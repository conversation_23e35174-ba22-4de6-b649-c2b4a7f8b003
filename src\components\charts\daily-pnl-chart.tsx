"use client"

import { format, startOfDay } from "date-fns"
import { ChartTooltip } from "@/components/ui/chart-tooltip"
import {
  Bar,
  BarChart,
  CartesianGrid,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
  Cell,
} from "recharts"

interface Trade {
  time_close: string
  profit: number
}

interface DailyPnLChartProps {
  trades: Trade[]
}

export function DailyPnLChart({ trades }: DailyPnLChartProps) {
  // Sort trades by date first to ensure chronological order
  const sortedTrades = [...trades].sort((a, b) =>
    new Date(a.time_close).getTime() - new Date(b.time_close).getTime()
  )

  // Group trades by day and calculate daily P&L
  const dailyPnL = sortedTrades.reduce((acc: { [key: string]: number }, trade) => {
    const day = format(startOfDay(new Date(trade.time_close)), "yyyy-MM-dd")
    acc[day] = (acc[day] || 0) + trade.profit
    return acc
  }, {})

  // Convert to array and sort by date
  const chartData = Object.entries(dailyPnL)
    .map(([date, pnl]) => ({
      date,
      pnl: parseFloat(pnl.toFixed(2)), // Limit decimal places
    }))
    .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime()) // Ensure chronological order

  return (
    <ResponsiveContainer width="100%" height="100%">
      <BarChart data={chartData} margin={{ top: 20, right: 20, bottom: 40, left: 60 }}>
        <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" opacity={0.4} />
        <XAxis
          dataKey="date"
          tickFormatter={(value) => format(new Date(value), "MMM d")}
          stroke="hsl(var(--muted-foreground))"
          tick={{
            fontSize: 11,
            fill: 'hsl(var(--muted-foreground))',
            opacity: 0.7,
            textAnchor: 'end'
          }}
          height={60}
          axisLine={{ stroke: 'hsl(var(--border))', opacity: 0.5 }}
        />
        <YAxis
          tickFormatter={(value) => `$${value.toLocaleString()}`}
          stroke="hsl(var(--muted-foreground))"
          tick={{ fontSize: 11, fill: 'hsl(var(--muted-foreground))', opacity: 0.7 }}
          axisLine={{ stroke: 'hsl(var(--border))', opacity: 0.5 }}
        />
        <Tooltip
          content={({ active, payload }) => (
            <ChartTooltip active={active} payload={payload}
              labelFormatter={(value) => format(new Date(value), "MMM d, yyyy")}
              formatter={(value) => `$${value.toLocaleString()}`}
            />
          )}
        />
        <Bar
          dataKey="pnl"
          // Remove the className and fill that might be overriding the Cell colors
        >
          {chartData.map((entry, index) => (
            <Cell
              key={`cell-${index}`}
              fill={entry.pnl >= 0 ? "#10b981" : "#ef4444"}
            />
          ))}
        </Bar>
      </BarChart>
    </ResponsiveContainer>
  )
}