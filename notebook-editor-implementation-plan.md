# Notebook Editor Implementation Plan

## Overview
This document outlines the plan for replacing the current notebook editor with the Tiptap SimpleEditor template. The goal is to create a more robust editor that allows users to view and edit notes selected from the notebook list.

## Current Implementation Analysis

### Data Flow
1. **Notebook Entries**: Stored in the `notebook_entries` table in Supabase
2. **Import Process**: 
   - When "Import Journal Entries" is clicked, the `/api/notebook/import` endpoint is called
   - Trade journal entries are imported from the `trades` table (with journal content)
   - Daily journal entries are imported from the `daily_journal_entries` table
   - Both are converted to notebook entries and stored in the `notebook_entries` table
   - System folders are created for organization (Trade Notes and Daily Journal)

3. **Data Fetching**:
   - Server-side: Initial data is fetched in `src/app/(dashboard)/notebook/page.tsx`
   - Client-side: React Query hooks in `src/hooks/use-notebook.ts` handle data fetching and mutations

4. **Current Editor**:
   - Located at `src/components/notebook/notebook-editor.tsx`
   - Uses Tiptap but lacks functionality to properly view and edit selected notes

### Current Issues
- The notebook editor doesn't properly display the content of selected notes
- Line 621 in `client.tsx` shows a placeholder message: "Viewing note with ID: {selectedNoteId}" instead of actual content
- No functionality to edit and save changes to existing notes

## Implementation Steps

### 1. Install Tiptap SimpleEditor Template

```bash
npx @tiptap/cli add simple-editor
```

This will scaffold the SimpleEditor template into our project.

### 2. Install Required Dependencies

```bash
npm install @tiptap/extension-task-item @tiptap/extension-task-list @tiptap/extension-typography
```

Note: Most dependencies are already installed in the project.

### 3. Import Global Styles

Create a CSS file to import the required styles and include it in our global CSS.

### 4. Create a New NotebookEditor Component

Create a new component that wraps the SimpleEditor template and adds the functionality to:
- Load note content when a note is selected
- Save changes to the note
- Handle tags and other metadata

### 5. Implement Note Loading and Saving

- Create a hook to fetch and update notebook entries
- Implement loading of note content into the editor
- Add save functionality to persist changes

### 6. Update the Client Component

Modify `src/app/(dashboard)/notebook/client.tsx` to use the new editor component and properly handle note selection and editing.

### 7. Test and Refine

Test the implementation to ensure it works correctly with:
- Note selection
- Content loading
- Editing and saving
- Folder and category organization

## Detailed Implementation Plan

### Step 1: Install Tiptap SimpleEditor Template

Run the Tiptap CLI command to add the SimpleEditor template to our project:

```bash
npx @tiptap/cli add simple-editor
```

### Step 2: Create CSS Imports

Create a file `src/styles/tiptap-imports.css` to import the required styles:

```css
@import '../components/templates/simple/styles/_variables.css';
@import '../components/templates/simple/styles/_keyframes-animations.css';
```

Update `src/app/globals.css` to include these imports.

### Step 3: Create a New NotebookEditor Component

Create a new file `src/components/notebook/new-notebook-editor.tsx` that:
1. Wraps the SimpleEditor template
2. Adds functionality to load and save note content
3. Integrates with our existing notebook system

### Step 4: Create a Hook for Note Editing

Enhance `src/hooks/use-notebook.ts` with a new hook for editing notes:

```typescript
export function useEditNotebookEntry(id: string | null) {
  // Implementation for fetching, editing, and saving notes
}
```

### Step 5: Update the Client Component

Modify `src/app/(dashboard)/notebook/client.tsx` to:
1. Use the new editor component
2. Properly handle note selection
3. Implement saving functionality

### Step 6: Test and Refine

Test the implementation with various scenarios:
- Selecting notes from different folders
- Editing and saving notes
- Creating new notes
- Organizing notes in folders and categories

## Success Criteria

The implementation will be considered successful when:

1. Users can select a note from the notebook list and view its content in the editor
2. Users can edit the note content and save changes
3. Users can create new notes and save them to folders or categories
4. The editor provides a rich text editing experience with all the features of the SimpleEditor template
5. All existing functionality of the notebook page is preserved

## Timeline

1. Setup and installation: 30 minutes
2. Component creation: 1 hour
3. Integration with existing code: 1 hour
4. Testing and refinement: 30 minutes
5. Total estimated time: 3 hours
