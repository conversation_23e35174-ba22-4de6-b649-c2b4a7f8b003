"use client"

import { useEffect } from 'react'

/**
 * A component that stops the Next.js loading indicator when mounted.
 * This is useful for ensuring the loading indicator disappears after
 * the page has fully loaded.
 */
export function LoadingIndicatorStopper() {
  useEffect(() => {
    // Function to stop the loading indicator
    const stopLoadingIndicator = () => {
      // Method 1: Push state to history
      window.history.pushState(null, '', window.location.href)
      
      // Method 2: Dispatch navigation end event
      window.dispatchEvent(new Event('routeChangeComplete'))
      
      // Method 3: Force any NProgress instances to complete
      if (typeof window !== 'undefined') {
        // Try to access NProgress directly
        const anyWindow = window as any
        if (anyWindow.NProgress) {
          anyWindow.NProgress.done()
        }
        
        // Try to find and manipulate the loading bar element directly
        const loadingBar = document.getElementById('nprogress')
        if (loadingBar) {
          loadingBar.style.display = 'none'
        }
      }
    }
    
    // Execute immediately
    stopLoadingIndicator()
    
    // Then try multiple times with increasing delays
    const timers = [
      setTimeout(stopLoadingIndicator, 100),
      setTimeout(stopLoadingIndicator, 300),
      setTimeout(stopLoadingIndicator, 500),
      setTimeout(stopLoadingIndicator, 1000),
      setTimeout(stopLoadingIndicator, 2000)
    ]
    
    // Clean up timers on unmount
    return () => {
      timers.forEach(timer => clearTimeout(timer))
    }
  }, [])

  // This component doesn't render anything
  return null
}
