"use client"

import { useState, useEffect } from "react"
import { Strategy } from "@/types/playbook"
import { getStrategies } from "@/lib/playbook-service"
import { Check, ChevronsUpDown } from "lucide-react"

import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"
import { Input } from "@/components/ui/input"

interface StrategySelector {
  userId: string
  selectedStrategyId: string | null
  onSelectStrategy: (strategyId: string | null) => void
  className?: string
}

export function StrategySelector({
  userId,
  selectedStrategyId,
  onSelectStrategy,
  className
}: StrategySelector) {
  const [open, setOpen] = useState(false)
  const [strategies, setStrategies] = useState<Strategy[]>([])
  const [loading, setLoading] = useState(false)
  const [searchQuery, setSearchQuery] = useState("")

  // Fetch strategies when component mounts
  useEffect(() => {
    const fetchStrategies = async () => {
      if (!userId) return

      setLoading(true)
      try {
        const strategiesData = await getStrategies(userId)
        setStrategies(strategiesData)
      } catch (error) {
        console.error("Error fetching strategies:", error)
      } finally {
        setLoading(false)
      }
    }

    fetchStrategies()
  }, [userId])

  // Find the selected strategy
  const selectedStrategy = strategies.find(s => s.id === selectedStrategyId)

  // Filter strategies based on search query
  const filteredStrategies = strategies.filter(strategy =>
    strategy.name.toLowerCase().includes(searchQuery.toLowerCase())
  )

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn("w-[200px] justify-between", className)}
          disabled={loading}
        >
          {selectedStrategy ? (
            <div className="flex items-center gap-2 truncate">
              <span className="truncate">{selectedStrategy.name}</span>
              {selectedStrategy.status && (
                <Badge
                  variant={
                    selectedStrategy.status === 'active' ? 'default' :
                    selectedStrategy.status === 'testing' ? 'secondary' :
                    'outline'
                  }
                  className="ml-auto text-xs"
                >
                  {selectedStrategy.status}
                </Badge>
              )}
            </div>
          ) : (
            "Select strategy"
          )}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[200px] p-2">
        <div className="flex flex-col space-y-2">
          {/* Search input */}
          <div className="flex items-center border-b pb-2">
            <Input
              placeholder="Search strategy..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="h-8 w-full"
            />
          </div>

          {/* Strategy list */}
          <div className="max-h-[300px] overflow-y-auto">
            {filteredStrategies.length === 0 ? (
              <div className="py-6 text-center text-sm text-muted-foreground">
                No strategy found.
              </div>
            ) : (
              <div className="flex flex-col space-y-1">
                {/* None option */}
                <Button
                  variant="ghost"
                  className="w-full justify-start text-left font-normal"
                  onClick={() => {
                    onSelectStrategy(null)
                    setOpen(false)
                  }}
                >
                  <Check
                    className={cn(
                      "mr-2 h-4 w-4",
                      !selectedStrategyId ? "opacity-100" : "opacity-0"
                    )}
                  />
                  <span>None</span>
                </Button>

                {/* Strategy options */}
                {filteredStrategies.map((strategy) => (
                  <Button
                    key={strategy.id}
                    variant="ghost"
                    className="w-full justify-start text-left font-normal"
                    onClick={() => {
                      onSelectStrategy(strategy.id)
                      setOpen(false)
                    }}
                  >
                    <Check
                      className={cn(
                        "mr-2 h-4 w-4",
                        selectedStrategyId === strategy.id ? "opacity-100" : "opacity-0"
                      )}
                    />
                    <div className="flex items-center justify-between w-full">
                      <span>{strategy.name}</span>
                      {strategy.status && (
                        <Badge
                          variant={
                            strategy.status === 'active' ? 'default' :
                            strategy.status === 'testing' ? 'secondary' :
                            'outline'
                          }
                          className="ml-auto text-xs"
                        >
                          {strategy.status}
                        </Badge>
                      )}
                    </div>
                  </Button>
                ))}
              </div>
            )}
          </div>
        </div>
      </PopoverContent>
    </Popover>
  )
}
