import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { createServerClient } from '@supabase/ssr';
import type { Database } from '@/types/supabase';
import { NotebookCategory } from '@/types/notebook';

// GET handler for fetching notebook categories
export async function GET(request: NextRequest) {
  try {
    // Get cookies for server-side Supabase client
    const cookieStore = await cookies();
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
          set(_name: string, _value: string, _options: any) {
            // API routes can't set cookies directly
          },
          remove(_name: string, _options: any) {
            // API routes can't remove cookies directly
          }
        },
      }
    );

    // Get authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get distinct categories
    const { data: categoriesData, error: categoriesError } = await supabase
      .from('notebook_entries')
      .select('category')
      .eq('user_id', user.id)
      .not('category', 'is', null);

    if (categoriesError) {
      console.error('Error fetching categories:', categoriesError);
      return NextResponse.json({ error: categoriesError.message }, { status: 500 });
    }

    // Count occurrences of each category
    const categoryCounts: Record<string, number> = {};
    categoriesData?.forEach(entry => {
      if (entry.category) {
        categoryCounts[entry.category] = (categoryCounts[entry.category] || 0) + 1;
      }
    });

    // Add system categories if they don't exist
    const systemCategories = ['Trade Notes', 'Daily Journal'];
    systemCategories.forEach(category => {
      if (!categoryCounts[category]) {
        categoryCounts[category] = 0;
      }
    });

    // Convert to array of category objects
    const categories: NotebookCategory[] = Object.entries(categoryCounts).map(([name, count]) => ({
      name,
      count
    }));

    return NextResponse.json(categories);
  } catch (error) {
    console.error('Error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
