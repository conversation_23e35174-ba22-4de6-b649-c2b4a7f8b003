-- Create tables for TradePivot

-- Enable Row Level Security
create extension if not exists "uuid-ossp";

-- Trading Accounts table
create table if not exists trading_accounts (
    id uuid default uuid_generate_v4() primary key,
    user_id uuid references auth.users(id) on delete cascade not null,
    name text,
    account_number text unique not null,
    broker text,
    created_at timestamp with time zone default timezone('utc'::text, now()) not null,
    updated_at timestamp with time zone default timezone('utc'::text, now()) not null
);

-- Trades table
create table if not exists trades (
    id uuid default uuid_generate_v4() primary key,
    user_id uuid references auth.users(id) on delete cascade not null,
    account_id uuid references trading_accounts(id) on delete cascade not null,
    position_id bigint not null,
    symbol text not null,
    type text not null,
    volume numeric not null,
    price_open numeric not null,
    price_close numeric not null,
    sl numeric,
    tp numeric,
    time_open timestamp with time zone not null,
    time_close timestamp with time zone not null,
    commission numeric default 0,
    swap numeric default 0,
    profit numeric not null,
    created_at timestamp with time zone default timezone('utc'::text, now()) not null
);

-- Trading Summaries table
create table if not exists trading_summaries (
    id uuid default uuid_generate_v4() primary key,
    user_id uuid references auth.users(id) on delete cascade not null,
    account_id uuid references trading_accounts(id) on delete cascade not null,
    total_net_profit numeric not null,
    gross_profit numeric not null,
    gross_loss numeric not null,
    profit_factor numeric not null,
    expected_payoff numeric not null,
    recovery_factor numeric not null,
    sharpe_ratio numeric not null,
    balance_drawdown_absolute numeric not null,
    balance_drawdown_maximal text not null,
    balance_drawdown_relative text not null,
    total_trades integer not null,
    short_trades_won text not null,
    long_trades_won text not null,
    profit_trades text not null,
    loss_trades text not null,
    largest_profit_trade numeric not null,
    largest_loss_trade numeric not null,
    average_profit_trade numeric not null,
    average_loss_trade numeric not null,
    maximum_consecutive_wins text not null,
    maximum_consecutive_losses text not null,
    maximal_consecutive_profit text not null,
    maximal_consecutive_loss text not null,
    average_consecutive_wins numeric not null,
    average_consecutive_losses numeric not null,
    created_at timestamp with time zone default timezone('utc'::text, now()) not null
);

-- Enable Row Level Security (RLS)
alter table trading_accounts enable row level security;
alter table trades enable row level security;
alter table trading_summaries enable row level security;

-- Create policies
create policy "Users can view their own trading accounts"
    on trading_accounts for select
    using (auth.uid() = user_id);

create policy "Users can insert their own trading accounts"
    on trading_accounts for insert
    with check (auth.uid() = user_id);

create policy "Users can update their own trading accounts"
    on trading_accounts for update
    using (auth.uid() = user_id);

create policy "Users can view their own trades"
    on trades for select
    using (auth.uid() = user_id);

create policy "Users can insert their own trades"
    on trades for insert
    with check (auth.uid() = user_id);

create policy "Users can view their own trading summaries"
    on trading_summaries for select
    using (auth.uid() = user_id);

create policy "Users can insert their own trading summaries"
    on trading_summaries for insert
    with check (auth.uid() = user_id);

-- Create indexes for better performance
create index if not exists trades_user_id_idx on trades(user_id);
create index if not exists trades_account_id_idx on trades(account_id);
create index if not exists trades_time_open_idx on trades(time_open);
create index if not exists trading_summaries_user_id_idx on trading_summaries(user_id);
create index if not exists trading_summaries_account_id_idx on trading_summaries(account_id); 