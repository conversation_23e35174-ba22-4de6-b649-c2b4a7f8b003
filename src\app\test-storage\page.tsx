'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';
import { uploadImageToStorage, deleteImageFromStorage } from '@/lib/image-service';
import { ImageUploadTest } from '@/components/test/image-upload-test';

export default function TestStoragePage() {
  const [testResults, setTestResults] = useState<any>(null);
  const [setupResults, setSetupResults] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isSettingUp, setIsSettingUp] = useState(false);
  const testStorage = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/test-storage');
      const data = await response.json();
      setTestResults(data);
      toast.success('Storage test completed');
    } catch (error) {
      console.error('Error testing storage:', error);
      toast.error('Failed to test storage');
    } finally {
      setIsLoading(false);
    }
  };

  const setupStorage = async () => {
    setIsSettingUp(true);
    try {
      const response = await fetch('/api/setup-storage-rls', {
        method: 'POST',
      });
      const data = await response.json();
      setSetupResults(data);

      if (response.ok) {
        toast.success('Storage setup completed');
        // Refresh the test results
        testStorage();
      } else {
        toast.error('Storage setup failed');
      }
    } catch (error) {
      console.error('Error setting up storage:', error);
      toast.error('Failed to set up storage');
    } finally {
      setIsSettingUp(false);
    }
  };

  useEffect(() => {
    // Run the test on page load
    testStorage();
  }, []);

  return (
    <div className="container py-10">
      <h1 className="text-3xl font-bold mb-6">Storage Test Page</h1>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Storage Test Results</CardTitle>
            <CardDescription>
              Check if the storage bucket and RLS policies are set up correctly
            </CardDescription>
          </CardHeader>
          <CardContent>
            {testResults ? (
              <div className="space-y-4">
                <div>
                  <h3 className="font-medium">Public Access:</h3>
                  {testResults.publicAccess ? (
                    <>
                      <p>Status: {testResults.publicAccess.success ? '✅ Working' : '❌ Not working'}</p>
                      {testResults.publicAccess.error && (
                        <p className="text-red-500">Error: {testResults.publicAccess.error}</p>
                      )}
                      <p>Files: {testResults.publicAccess.fileCount}</p>
                    </>
                  ) : (
                    <p>Public access information not available</p>
                  )}
                </div>

                <div>
                  <h3 className="font-medium">Admin Access:</h3>
                  {testResults.adminAccess ? (
                    <>
                      <p>Status: {testResults.adminAccess.success ? '✅ Working' : '❌ Not working'}</p>
                      {testResults.adminAccess.error && (
                        <p className="text-red-500">Error: {testResults.adminAccess.error}</p>
                      )}
                      <p>Files: {testResults.adminAccess.fileCount}</p>
                    </>
                  ) : (
                    <p>Admin access information not available</p>
                  )}
                </div>

                <div>
                  <h3 className="font-medium">RLS Policies:</h3>
                  {testResults.rlsPolicies ? (
                    <>
                      <p>Status: {testResults.rlsPolicies.success ? '✅ Working' : '❌ Not working'}</p>
                      {testResults.rlsPolicies.error && (
                        <p className="text-red-500">Error: {testResults.rlsPolicies.error}</p>
                      )}
                      {testResults.rlsPolicies.policies && (
                        <div className="mt-2">
                          <p>Policy Count: {testResults.rlsPolicies.policies.length}</p>
                          {testResults.rlsPolicies.policies.map((policy: any, index: number) => (
                            <div key={index} className="mt-1 text-sm">
                              <p>- {policy.policyname} ({policy.cmd})</p>
                            </div>
                          ))}
                        </div>
                      )}
                    </>
                  ) : (
                    <p>RLS Policies information not available</p>
                  )}
                </div>
              </div>
            ) : (
              <div className="flex justify-center items-center h-40">
                <div className="animate-pulse">Loading test results...</div>
              </div>
            )}
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button onClick={testStorage} disabled={isLoading}>
              {isLoading ? 'Testing...' : 'Run Test Again'}
            </Button>
            <Button onClick={setupStorage} disabled={isSettingUp}>
              {isSettingUp ? 'Setting up...' : 'Setup Storage RLS'}
            </Button>
          </CardFooter>
        </Card>

        <ImageUploadTest />
      </div>

      {setupResults && (
        <Card className="mt-6">
          <CardHeader>
            <CardTitle>Storage Setup Results</CardTitle>
          </CardHeader>
          <CardContent>
            <pre className="bg-muted p-4 rounded-md overflow-auto max-h-[300px]">
              {JSON.stringify(setupResults, null, 2)}
            </pre>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
