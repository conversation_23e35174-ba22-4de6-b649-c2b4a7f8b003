"use client"

import * as React from "react"
import { ChevronLeft, ChevronRight } from "lucide-react"
import { addMonths, format, startOfMonth, endOfMonth, startOfWeek, endOfWeek, addDays, isSameMonth, isSameDay, isToday, isAfter, isBefore } from "date-fns"
import { DateRange } from "react-day-picker"

import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"

interface TableCalendarProps {
  className?: string
  selected?: DateRange | undefined
  onSelect?: (range: DateRange | undefined) => void
  numberOfMonths?: number
  defaultMonth?: Date
}

export function TableCalendar({
  className,
  selected,
  onSelect,
  numberOfMonths = 2,
  defaultMonth = new Date()
}: TableCalendarProps) {
  const [currentMonth, setCurrentMonth] = React.useState(defaultMonth || new Date())

  const handlePrevMonth = () => {
    setCurrentMonth(prev => addMonths(prev, -1))
  }

  const handleNextMonth = () => {
    setCurrentMonth(prev => addMonths(prev, 1))
  }

  const handleDateClick = (day: Date) => {
    if (!onSelect) return

    if (!selected) {
      onSelect({ from: day, to: undefined })
      return
    }

    if (selected.from && !selected.to) {
      // If the clicked day is before the start date, swap them
      if (day < selected.from) {
        onSelect({ from: day, to: selected.from })
      } else {
        onSelect({ from: selected.from, to: day })
      }
      return
    }

    // If both dates are already selected, start a new selection
    onSelect({ from: day, to: undefined })
  }

  const isStartDate = (day: Date) => {
    return selected?.from && isSameDay(day, selected.from)
  }

  const isEndDate = (day: Date) => {
    return selected?.to && isSameDay(day, selected.to)
  }

  const isInBetween = (day: Date) => {
    if (!selected?.from || !selected?.to) return false
    return isAfter(day, selected.from) && isBefore(day, selected.to)
  }

  const renderMonth = (monthOffset: number) => {
    const monthToRender = addMonths(currentMonth, monthOffset)
    const monthStart = startOfMonth(monthToRender)
    const monthEnd = endOfMonth(monthToRender)
    const startDate = startOfWeek(monthStart, { weekStartsOn: 0 })
    const endDate = endOfWeek(monthEnd, { weekStartsOn: 0 })

    const rows = []
    let days = []
    let day = startDate

    // Create array of week rows
    while (day <= endDate) {
      for (let i = 0; i < 7; i++) {
        days.push(day)
        day = addDays(day, 1)
      }
      rows.push(days)
      days = []
    }

    return (
      <div className="month-container">
        <div className="text-center font-medium py-2 text-sm">
          {format(monthToRender, 'MMMM yyyy')}
        </div>
        <table className="w-full border-collapse">
          <thead>
            <tr className="border-b">
              <th className="p-2 text-center text-xs font-medium text-muted-foreground">Su</th>
              <th className="p-2 text-center text-xs font-medium text-muted-foreground">Mo</th>
              <th className="p-2 text-center text-xs font-medium text-muted-foreground">Tu</th>
              <th className="p-2 text-center text-xs font-medium text-muted-foreground">We</th>
              <th className="p-2 text-center text-xs font-medium text-muted-foreground">Th</th>
              <th className="p-2 text-center text-xs font-medium text-muted-foreground">Fr</th>
              <th className="p-2 text-center text-xs font-medium text-muted-foreground">Sa</th>
            </tr>
          </thead>
          <tbody>
            {rows.map((week, weekIndex) => (
              <tr key={weekIndex}>
                {week.map((day, dayIndex) => {
                  const isCurrentMonth = isSameMonth(day, monthToRender)
                  const isStart = isStartDate(day)
                  const isEnd = isEndDate(day)
                  const isBetween = isInBetween(day)
                  const isTodayDate = isToday(day)

                  return (
                    <td
                      key={dayIndex}
                      className={cn(
                        "p-0 text-center relative",
                        isBetween && isCurrentMonth && "bg-primary/15" // Light purple tint for in-between dates
                      )}
                    >
                      {/* Left connector */}
                      {(isStart && selected?.to) && (
                        <div className="absolute right-0 top-1/2 h-1/2 w-1/2 -translate-y-1/2 bg-primary/15" />
                      )}

                      {/* Right connector */}
                      {(isEnd && selected?.from) && (
                        <div className="absolute left-0 top-1/2 h-1/2 w-1/2 -translate-y-1/2 bg-primary/15" />
                      )}

                      <button
                        type="button"
                        onClick={() => handleDateClick(day)}
                        disabled={!isCurrentMonth}
                        className={cn(
                          "w-10 h-10 rounded-full mx-auto flex items-center justify-center text-sm relative z-10",
                          !isCurrentMonth && "text-muted-foreground opacity-30 cursor-not-allowed",
                          isStart && "bg-primary text-primary-foreground hover:bg-primary",
                          isEnd && "bg-primary text-primary-foreground hover:bg-primary",
                          isBetween && isCurrentMonth && "text-foreground hover:bg-primary/20", // Fixed contrast issue
                          isTodayDate && !isStart && !isEnd && !isBetween && "border border-accent-foreground",
                          isCurrentMonth && !isStart && !isEnd && !isTodayDate && !isBetween && "hover:bg-primary/10"
                        )}
                      >
                        {format(day, 'd')}
                      </button>
                    </td>
                  )
                })}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    )
  }

  return (
    <div className={cn("p-4 rounded-lg", className)}>
      <div className="flex items-center justify-between mb-6">
        <Button
          variant="outline"
          size="icon"
          onClick={handlePrevMonth}
          className="h-8 w-8 rounded-full bg-transparent p-0 hover:bg-accent/50"
        >
          <ChevronLeft className="h-4 w-4" />
          <span className="sr-only">Previous month</span>
        </Button>
        <div className="flex space-x-8">
          {Array.from({ length: numberOfMonths }).map((_, i) => (
            <div key={i} className="text-center font-medium">
              {format(addMonths(currentMonth, i), 'MMMM yyyy')}
            </div>
          ))}
        </div>
        <Button
          variant="outline"
          size="icon"
          onClick={handleNextMonth}
          className="h-8 w-8 rounded-full bg-transparent p-0 hover:bg-accent/50"
        >
          <ChevronRight className="h-4 w-4" />
          <span className="sr-only">Next month</span>
        </Button>
      </div>

      <div className="flex space-x-8">
        {Array.from({ length: numberOfMonths }).map((_, i) => (
          <div key={i} className="flex-1">
            {renderMonth(i)}
          </div>
        ))}
      </div>

      {selected?.from && (
        <div className="mt-4 pt-4 text-sm text-muted-foreground border-t">
          {selected.to ? (
            <div className="flex justify-between items-center">
              <span>
                {format(selected.from, 'MMM d, yyyy')} - {format(selected.to, 'MMM d, yyyy')}
              </span>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onSelect?.(undefined)}
                className="text-xs h-8"
              >
                Reset
              </Button>
            </div>
          ) : (
            <div className="flex justify-between items-center">
              <span>Start: {format(selected.from, 'MMM d, yyyy')}</span>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onSelect?.(undefined)}
                className="text-xs h-8"
              >
                Reset
              </Button>
            </div>
          )}
        </div>
      )}
    </div>
  )
}
