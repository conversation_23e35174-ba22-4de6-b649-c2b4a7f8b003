"use client"

import { useState, useEffect } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { toast } from "sonner"

import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { But<PERSON> } from "@/components/ui/button"
import { X, Plus, Image, FileText, BarChart3, Eye, CheckCircle, BookOpen } from "lucide-react"
import { cn } from "@/lib/utils"
import { Badge } from "@/components/ui/badge"

// Define the form schema
const formSchema = z.object({
  name: z.string().min(1, "Name is required").max(100, "Name must be less than 100 characters"),
  description: z.string().optional(),
  visual_cues: z.string().optional(),
  confirmation_criteria: z.string().optional(),
  image_urls: z.array(z.string().url("Must be a valid URL")).optional().default([]),
})

type FormValues = {
  name: string
  description?: string
  visual_cues?: string
  confirmation_criteria?: string
  image_urls?: string[]
}

interface SetupFormContentProps {
  onSubmit: (data: FormValues) => void
  initialData?: FormValues | null
  isSubmitting?: boolean
  strategyId: string
  strategyName: string
}

export function SetupFormContent({
  onSubmit,
  initialData = null,
  isSubmitting = false,
  strategyId,
  strategyName
}: SetupFormContentProps) {
  const [imageUrlInput, setImageUrlInput] = useState("")
  const [imageUrls, setImageUrls] = useState<string[]>(
    initialData?.image_urls || []
  )

  // Initialize form with default values or existing setup values
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: initialData || {
      name: "",
      description: "",
      visual_cues: "",
      confirmation_criteria: "",
      image_urls: [],
    },
  })

  // Update form when initialData changes
  useEffect(() => {
    if (initialData) {
      form.reset(initialData)
      setImageUrls(initialData.image_urls || [])
    }
  }, [initialData, form])

  // Handle adding an image URL
  const handleAddImageUrl = () => {
    if (imageUrlInput.trim() && !imageUrls.includes(imageUrlInput.trim())) {
      try {
        // Validate URL
        new URL(imageUrlInput.trim())

        const newImageUrls = [...imageUrls, imageUrlInput.trim()]
        setImageUrls(newImageUrls)
        form.setValue("image_urls", newImageUrls)
        setImageUrlInput("")
      } catch (error) {
        form.setError("image_urls", {
          type: "manual",
          message: "Please enter a valid URL"
        })
      }
    }
  }

  // Handle removing an image URL
  const handleRemoveImageUrl = async (url: string) => {
    try {
      // Import the deleteImageFromStorage function dynamically to avoid circular dependencies
      const { deleteImageFromStorage } = await import('@/lib/image-service')

      // Attempt to delete the image from storage
      const deleted = await deleteImageFromStorage(url)

      if (!deleted) {
        console.warn('Failed to delete image from storage:', url)
        // Continue with removing the URL from the form even if storage deletion fails
      } else {
        toast.success('Image removed successfully')
      }

      // Remove the URL from the form state
      const newImageUrls = imageUrls.filter(i => i !== url)

      // Force a refresh of the image list
      setTimeout(() => {
        setImageUrls([...newImageUrls])
        form.setValue("image_urls", [...newImageUrls])
      }, 100)
    } catch (error) {
      console.error('Error removing image:', error)
      toast.error('Failed to remove image')

      // Still remove the URL from the form state to maintain UI consistency
      const newImageUrls = imageUrls.filter(i => i !== url)
      setImageUrls([...newImageUrls])
      form.setValue("image_urls", [...newImageUrls])
    }
  }

  // Handle form submission
  const handleSubmit = (values: FormValues) => {
    // Ensure image_urls is always an array
    const formattedValues = {
      ...values,
      image_urls: values.image_urls || []
    }
    console.log("Submitting setup form with values:", formattedValues)
    onSubmit(formattedValues)
  }

  return (
    <div className="space-y-6">
      <div className="space-y-3 mb-6">
        <div className="flex items-center gap-2">
          <div className="p-2 rounded-full bg-blue-100 dark:bg-blue-950/50">
            <BookOpen className="h-5 w-5 text-blue-600 dark:text-blue-400" />
          </div>
          <div>
            <h3 className="text-lg font-medium">Setup Details for "{strategyName}"</h3>
            <p className="text-sm text-muted-foreground">
              Define a specific setup for your trading strategy
            </p>
          </div>
        </div>
        <div className="h-1 w-full bg-gradient-to-r from-blue-200 to-indigo-200 dark:from-blue-900/40 dark:to-indigo-900/40 rounded-full"></div>
      </div>

      <Form {...form}>
        <form id="setup-form" onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem className="bg-card/30 p-4 rounded-lg border border-border/50 shadow-sm">
                <div className="flex items-center gap-2 mb-2">
                  <FileText className="h-4 w-4 text-blue-500" />
                  <FormLabel className="text-base">Setup Name</FormLabel>
                </div>
                <FormControl>
                  <Input placeholder="Double Bottom Reversal" {...field} className="border-blue-200 dark:border-blue-900/50 focus-visible:ring-blue-500" />
                </FormControl>
                <FormDescription className="mt-2">
                  A clear, descriptive name for this setup
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="description"
            render={({ field }) => (
              <FormItem className="bg-card/30 p-4 rounded-lg border border-border/50 shadow-sm">
                <div className="flex items-center gap-2 mb-2">
                  <BarChart3 className="h-4 w-4 text-indigo-500" />
                  <FormLabel className="text-base">Description</FormLabel>
                </div>
                <FormControl>
                  <Textarea
                    placeholder="This setup occurs when price forms a double bottom pattern at a key support level..."
                    className="min-h-[100px] border-blue-200 dark:border-blue-900/50 focus-visible:ring-blue-500"
                    {...field}
                    value={field.value || ""}
                  />
                </FormControl>
                <FormDescription className="mt-2">
                  Describe this setup and when it appears
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="visual_cues"
            render={({ field }) => (
              <FormItem className="bg-card/30 p-4 rounded-lg border border-border/50 shadow-sm">
                <div className="flex items-center gap-2 mb-2">
                  <Eye className="h-4 w-4 text-purple-500" />
                  <FormLabel className="text-base">Visual Cues</FormLabel>
                </div>
                <FormControl>
                  <Textarea
                    placeholder="Price forms two bottoms at approximately the same level
The second bottom has lower volume than the first
RSI shows bullish divergence"
                    className="min-h-[100px] border-blue-200 dark:border-blue-900/50 focus-visible:ring-blue-500"
                    {...field}
                    value={field.value || ""}
                  />
                </FormControl>
                <FormDescription className="mt-2">
                  <span>Visual indicators to look for on your charts. <strong>Enter each cue on a separate line.</strong></span>
                  <div className="mt-2 p-2 bg-muted/50 rounded-md text-xs">
                    <div className="font-medium mb-1">Format guidelines:</div>
                    <div>• Enter one visual cue per line</div>
                    <div>• Keep each cue concise and specific</div>
                    <div>• Avoid numbering (1., 2., etc.) as the system will track them automatically</div>
                  </div>
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="confirmation_criteria"
            render={({ field }) => (
              <FormItem className="bg-card/30 p-4 rounded-lg border border-border/50 shadow-sm">
                <div className="flex items-center gap-2 mb-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <FormLabel className="text-base">Confirmation Criteria</FormLabel>
                </div>
                <FormControl>
                  <Textarea
                    placeholder="Price breaks above the neckline
Volume increases on the breakout
Price retests the neckline and holds"
                    className="min-h-[100px] border-blue-200 dark:border-blue-900/50 focus-visible:ring-blue-500"
                    {...field}
                    value={field.value || ""}
                  />
                </FormControl>
                <FormDescription className="mt-2">
                  <span>Conditions that must be met to confirm this setup is valid. <strong>Enter each criterion on a separate line.</strong></span>
                  <div className="mt-2 p-2 bg-muted/50 rounded-md text-xs">
                    <div className="font-medium mb-1">Format guidelines:</div>
                    <div>• Enter one confirmation criterion per line</div>
                    <div>• Make criteria clear and actionable</div>
                    <div>• These will appear as checkboxes in the trade details view</div>
                  </div>
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="image_urls"
            render={({ field }) => (
              <FormItem className="bg-card/30 p-4 rounded-lg border border-border/50 shadow-sm">
                <div className="flex items-center gap-2 mb-2">
                  <Image className="h-4 w-4 text-amber-500" />
                  <FormLabel className="text-base">Example Images</FormLabel>
                </div>
                <div className="flex gap-2">
                  <FormControl>
                    <Input
                      placeholder="https://example.com/image.jpg"
                      value={imageUrlInput}
                      onChange={(e) => setImageUrlInput(e.target.value)}
                      onKeyDown={(e) => {
                        if (e.key === "Enter") {
                          e.preventDefault()
                          handleAddImageUrl()
                        }
                      }}
                      className="border-blue-200 dark:border-blue-900/50 focus-visible:ring-blue-500"
                    />
                  </FormControl>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleAddImageUrl}
                    className="border-blue-200 hover:bg-blue-50 dark:border-blue-900/50 dark:hover:bg-blue-900/20"
                  >
                    <Plus className="mr-2 h-4 w-4" />
                    Add
                  </Button>
                </div>
                <div className="flex flex-wrap gap-2 mt-3 min-h-[40px] p-2 bg-muted/30 rounded-md border border-border/50">
                  {imageUrls.length === 0 ? (
                    <p className="text-xs text-muted-foreground w-full text-center py-1">No images added yet</p>
                  ) : (
                    imageUrls.map((url) => (
                      <Badge
                        key={url}
                        variant="secondary"
                        className="flex items-center gap-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300"
                      >
                        <Image className="h-3 w-3 mr-1" />
                        {url.substring(0, 20)}...
                        <Button
                          type="button"
                          variant="ghost"
                          size="icon"
                          className={cn(
                            "h-4 w-4 p-0 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                          )}
                          onClick={() => handleRemoveImageUrl(url)}
                        >
                          <X className="h-3 w-3" />
                          <span className="sr-only">Remove image</span>
                        </Button>
                      </Badge>
                    ))
                  )}
                </div>
                <FormDescription className="mt-2">
                  Add URLs to example images of this setup (optional)
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Submit button moved to wizard footer */}
        </form>
      </Form>
    </div>
  )
}
