"use client"

import React, { useEffect, useRef, useState } from "react"
import { motion } from "framer-motion"
import { cn } from "@/lib/utils"

type TextEffectProps = {
  children: React.ReactNode
  className?: string
  as?: React.ElementType
  preset?: "fade-in-blur" | "fade-in" | "slide-up"
  delay?: number
  duration?: number
  staggerChildren?: number
  speedSegment?: number
  per?: "word" | "line" | "character"
}

export function TextEffect({
  children,
  className,
  as: Component = "div",
  preset = "fade-in",
  delay = 0,
  duration = 0.5,
  staggerChildren = 0.05,
  speedSegment = 0.05,
  per = "word",
  ...props
}: TextEffectProps) {
  const text = useRef<HTMLDivElement>(null)
  const [splitted, setSplitted] = useState<React.ReactNode[]>([])

  useEffect(() => {
    if (!text.current) return

    if (typeof children !== "string") {
      setSplitted([children])
      return
    }

    if (per === "word") {
      const words = children.split(" ")
      setSplitted(
        words.map((word, i) => (
          <span key={i} className="inline-block">
            {word}
            {i !== words.length - 1 ? " " : ""}
          </span>
        ))
      )
    } else if (per === "character") {
      const chars = children.split("")
      setSplitted(
        chars.map((char, i) => (
          <span key={i} className="inline-block">
            {char}
          </span>
        ))
      )
    } else if (per === "line") {
      const lines = children.split("\n")
      setSplitted(
        lines.map((line, i) => (
          <span key={i} className="block">
            {line}
          </span>
        ))
      )
    }
  }, [children, per])

  const getVariants = () => {
    switch (preset) {
      case "fade-in-blur":
        return {
          hidden: { opacity: 0, filter: "blur(8px)" },
          visible: { opacity: 1, filter: "blur(0px)" },
        }
      case "slide-up":
        return {
          hidden: { opacity: 0, y: 20 },
          visible: { opacity: 1, y: 0 },
        }
      case "fade-in":
      default:
        return {
          hidden: { opacity: 0 },
          visible: { opacity: 1 },
        }
    }
  }

  return (
    <Component
      ref={text}
      className={cn(className)}
      {...props}
    >
      <motion.span
        initial="hidden"
        animate="visible"
        variants={{
          visible: {
            transition: {
              staggerChildren,
              delayChildren: delay,
            },
          },
        }}
      >
        {splitted.map((item, i) => (
          <motion.span
            key={i}
            variants={getVariants()}
            transition={{
              duration,
              delay: delay + i * speedSegment,
              ease: [0.2, 0.65, 0.3, 0.9],
            }}
          >
            {item}
          </motion.span>
        ))}
      </motion.span>
    </Component>
  )
}
