"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { toast } from "sonner"
import { Strategy, MARKET_CONDITIONS, TIMEFRAMES, STRATEGY_STATUSES } from "@/types/playbook"
import { createStrategy, updateStrategy } from "@/lib/server/playbook-service"

import { Button } from "@/components/ui/button"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { MultiSelect } from "@/components/ui/multi-select"
import { X } from "lucide-react"
import { cn } from "@/lib/utils"
import { Badge } from "@/components/ui/badge"

// Define the form schema
const formSchema = z.object({
  name: z.string().min(1, "Name is required").max(100, "Name must be less than 100 characters"),
  description: z.string().optional(),
  market_conditions: z.array(z.string()).min(1, "Select at least one market condition"),
  timeframes: z.array(z.string()).min(1, "Select at least one timeframe"),
  instruments: z.array(z.string()).min(1, "Enter at least one instrument"),
  risk_reward_ratio: z.coerce.number().positive().optional().nullable(),
  expected_win_rate: z.coerce.number().min(0).max(100).optional().nullable(),
  status: z.enum(["active", "testing", "archived"]).optional().default("active"),
})

type FormValues = {
  name: string
  description?: string
  market_conditions: string[]
  timeframes: string[]
  instruments: string[]
  risk_reward_ratio?: number | null
  expected_win_rate?: number | null
  status?: 'active' | 'testing' | 'archived'
}

interface StrategyFormProps {
  userId: string
  strategy?: Strategy
  onSuccess?: (strategy: Strategy) => void
  onCancel?: () => void
}

export function StrategyForm({ userId, strategy, onSuccess, onCancel }: StrategyFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [instrumentInput, setInstrumentInput] = useState("")
  const [selectedInstruments, setSelectedInstruments] = useState<string[]>(
    strategy?.instruments || []
  )

  const isEditing = !!strategy

  // Initialize form with default values or existing strategy values
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: strategy?.name || "",
      description: strategy?.description || "",
      market_conditions: strategy?.market_conditions || [],
      timeframes: strategy?.timeframes || [],
      instruments: strategy?.instruments || [],
      risk_reward_ratio: strategy?.risk_reward_ratio || null,
      expected_win_rate: strategy?.expected_win_rate || null,
      status: strategy?.status || "active",
    },
  })

  // Handle form submission
  const onSubmit = async (values: FormValues) => {
    setIsSubmitting(true)

    try {
      // Ensure status always has a value
      const submissionData = {
        ...values,
        status: values.status || "active"
      }

      let result: Strategy | null

      if (isEditing && strategy) {
        // Update existing strategy
        result = await updateStrategy(strategy.id, submissionData)
        if (result) {
          toast.success("Strategy updated successfully")
        }
      } else {
        // Create new strategy
        result = await createStrategy(submissionData)
        if (result) {
          toast.success("Strategy created successfully")
          form.reset() // Reset form after successful creation
          setSelectedInstruments([])
        }
      }

      if (result && onSuccess) {
        onSuccess(result)
      }
    } catch (error) {
      console.error("Error saving strategy:", error)
      toast.error("Failed to save strategy")
    } finally {
      setIsSubmitting(false)
    }
  }

  // Handle adding a new instrument
  const handleAddInstrument = () => {
    if (instrumentInput.trim() && !selectedInstruments.includes(instrumentInput.trim().toUpperCase())) {
      const newInstruments = [...selectedInstruments, instrumentInput.trim().toUpperCase()]
      setSelectedInstruments(newInstruments)
      form.setValue("instruments", newInstruments)
      setInstrumentInput("")
    }
  }

  // Handle removing an instrument
  const handleRemoveInstrument = (instrument: string) => {
    const newInstruments = selectedInstruments.filter(i => i !== instrument)
    setSelectedInstruments(newInstruments)
    form.setValue("instruments", newInstruments)
  }

  return (
    <Card className="w-full max-w-3xl mx-auto">
      <CardHeader>
        <CardTitle>{isEditing ? "Edit Strategy" : "Create New Strategy"}</CardTitle>
        <CardDescription>
          {isEditing
            ? "Update your trading strategy details"
            : "Document a new trading strategy to track its performance"}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Strategy Name</FormLabel>
                  <FormControl>
                    <Input placeholder="Breakout Strategy" {...field} />
                  </FormControl>
                  <FormDescription>
                    A clear, descriptive name for your strategy
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="This strategy focuses on trading breakouts from key support and resistance levels..."
                      className="min-h-[120px]"
                      {...field}
                      value={field.value || ""}
                    />
                  </FormControl>
                  <FormDescription>
                    Describe your strategy, its core principles, and when to use it
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="market_conditions"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Market Conditions</FormLabel>
                    <FormControl>
                      <MultiSelect
                        options={MARKET_CONDITIONS.map(condition => ({
                          value: condition,
                          label: condition.charAt(0).toUpperCase() + condition.slice(1).replace('_', ' ')
                        }))}
                        selected={field.value}
                        onChange={(selected) => form.setValue("market_conditions", selected)}
                        placeholder="Select market conditions"
                      />
                    </FormControl>
                    <FormDescription>
                      Market conditions where this strategy works best
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="timeframes"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Timeframes</FormLabel>
                    <FormControl>
                      <MultiSelect
                        options={TIMEFRAMES.map(timeframe => ({
                          value: timeframe,
                          label: timeframe.toUpperCase()
                        }))}
                        selected={field.value}
                        onChange={(selected) => form.setValue("timeframes", selected)}
                        placeholder="Select timeframes"
                      />
                    </FormControl>
                    <FormDescription>
                      Timeframes this strategy is designed for
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="instruments"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Instruments</FormLabel>
                  <div className="flex gap-2">
                    <FormControl>
                      <Input
                        placeholder="EURUSD"
                        value={instrumentInput}
                        onChange={(e) => setInstrumentInput(e.target.value)}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter') {
                            e.preventDefault()
                            handleAddInstrument()
                          }
                        }}
                      />
                    </FormControl>
                    <Button
                      type="button"
                      onClick={handleAddInstrument}
                      variant="secondary"
                    >
                      Add
                    </Button>
                  </div>
                  <FormDescription>
                    Currency pairs or instruments this strategy is applied to
                  </FormDescription>
                  <FormMessage />
                  {selectedInstruments.length > 0 && (
                    <div className="flex flex-wrap gap-1 mt-2">
                      {selectedInstruments.map((instrument) => (
                        <Badge key={instrument} variant="secondary" className="flex items-center gap-1">
                          {instrument}
                          <X
                            className="h-3 w-3 cursor-pointer"
                            onClick={() => handleRemoveInstrument(instrument)}
                          />
                        </Badge>
                      ))}
                    </div>
                  )}
                </FormItem>
              )}
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="risk_reward_ratio"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Risk/Reward Ratio</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.1"
                        placeholder="1.5"
                        {...field}
                        value={field.value === null ? "" : field.value}
                        onChange={(e) => {
                          const value = e.target.value === "" ? null : parseFloat(e.target.value)
                          field.onChange(value)
                        }}
                      />
                    </FormControl>
                    <FormDescription>
                      Target risk/reward ratio (e.g., 1:2 = 2)
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="expected_win_rate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Expected Win Rate (%)</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min="0"
                        max="100"
                        step="1"
                        placeholder="60"
                        {...field}
                        value={field.value === null ? "" : field.value}
                        onChange={(e) => {
                          const value = e.target.value === "" ? null : parseFloat(e.target.value)
                          field.onChange(value)
                        }}
                      />
                    </FormControl>
                    <FormDescription>
                      Expected percentage of winning trades
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="status"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Status</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select status" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {STRATEGY_STATUSES.map((status) => (
                        <SelectItem key={status.value} value={status.value}>
                          {status.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    Current status of this strategy
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <CardFooter className="px-0 pb-0 pt-6 flex justify-between">
              {onCancel && (
                <Button
                  type="button"
                  variant="outline"
                  onClick={onCancel}
                  disabled={isSubmitting}
                >
                  Cancel
                </Button>
              )}
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting
                  ? "Saving..."
                  : isEditing
                  ? "Update Strategy"
                  : "Create Strategy"}
              </Button>
            </CardFooter>
          </form>
        </Form>
      </CardContent>
    </Card>
  )
}
