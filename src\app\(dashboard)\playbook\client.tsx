"use client"

import { useState } from "react"
import { useRout<PERSON> } from "next/navigation"
import { toast } from "sonner"
import { Strategy } from "@/types/playbook"
import { deleteStrategy } from "@/lib/server/playbook-service"

import { StrategyList } from "@/components/playbook/strategy-list"
import { StrategyForm } from "@/components/playbook/strategy-form"
import { StrategyComparison } from "@/components/playbook/strategy-comparison"
import { StrategyWizard } from "@/components/playbook/strategy-wizard"
import { StrategyDetail } from "@/components/playbook/strategy-detail"

interface PlaybookClientProps {
  userId: string
  initialStrategies: Strategy[]
  prefetchedData?: any
}

export default function PlaybookClient({ userId, initialStrategies, prefetchedData = {} }: PlaybookClientProps) {
  const router = useRouter()

  const [strategies, setStrategies] = useState<Strategy[]>(initialStrategies)
  const [isLoading, setIsLoading] = useState<boolean>(false)

  const [view, setView] = useState<string>("strategies")
  const [selectedStrategy, setSelectedStrategy] = useState<Strategy | null>(null)

  // Handle strategy actions
  const handleSelectStrategy = (strategy: Strategy) => {
    setSelectedStrategy(strategy)
    setView("strategyDetail")
    // Clear any tab parameter from the URL
    const url = new URL(window.location.href)
    url.searchParams.delete('tab')
    window.history.pushState({}, '', url)
  }

  const handleAddStrategy = () => {
    setView("addStrategy")
  }

  const handleAddStrategyWizard = () => {
    setView("addStrategyWizard")
  }

  const handleEditStrategy = (strategy: Strategy) => {
    setSelectedStrategy(strategy)
    setView("editStrategy")
  }

  const handleDeleteStrategy = async (strategyId: string) => {
    try {
      const success = await deleteStrategy(strategyId)
      if (success) {
        setStrategies(strategies.filter(s => s.id !== strategyId))
        toast.success("Strategy deleted successfully")
        // If we're viewing the strategy that was deleted, go back to the list
        if (selectedStrategy?.id === strategyId) {
          setView("strategies")
          setSelectedStrategy(null)
        }
      } else {
        toast.error("Failed to delete strategy")
      }
    } catch (error) {
      console.error("Error deleting strategy:", error)
      toast.error("An error occurred while deleting the strategy")
    }
  }

  const handleStrategySuccess = (strategy: Strategy) => {
    if (view === "editStrategy") {
      setStrategies(strategies.map(s => s.id === strategy.id ? strategy : s))
    } else {
      setStrategies([strategy, ...strategies])
    }
    setView("strategies")

    // Refresh the data to ensure we have the latest
    router.refresh()
  }

  // These handlers redirect to Strategy Details with the appropriate tab
  const handleViewSetups = (strategy: Strategy) => {
    setSelectedStrategy(strategy)
    setView("strategyDetail")
    // Add a tab parameter to the URL
    const url = new URL(window.location.href)
    url.searchParams.set('tab', 'setups')
    window.history.pushState({}, '', url)
  }

  const handleViewRules = (strategy: Strategy) => {
    setSelectedStrategy(strategy)
    setView("strategyDetail")
    // Add a tab parameter to the URL
    const url = new URL(window.location.href)
    url.searchParams.set('tab', 'rules')
    window.history.pushState({}, '', url)
  }

  const handleViewPerformance = (strategy: Strategy) => {
    setSelectedStrategy(strategy)
    setView("strategyDetail")
    // Add a tab parameter to the URL
    const url = new URL(window.location.href)
    url.searchParams.set('tab', 'performance')
    window.history.pushState({}, '', url)
  }

  // Handle comparison view
  const handleCompareStrategies = () => {
    setView("compare")
  }

  // Handle back navigation
  const handleBack = () => {
    setView("strategies")
    setSelectedStrategy(null)
    // Clear any tab parameter from the URL
    const url = new URL(window.location.href)
    url.searchParams.delete('tab')
    window.history.pushState({}, '', url)
  }

  // Render the appropriate view
  const renderView = () => {
    if (!userId) return null

    // Show loading state
    if (isLoading) {
      return (
        <div className="flex justify-center items-center h-[400px]">
          <div className="animate-pulse flex flex-col items-center">
            <div className="h-12 w-12 rounded-full bg-muted mb-4"></div>
            <div className="h-4 w-48 bg-muted rounded mb-2"></div>
            <div className="h-3 w-32 bg-muted rounded"></div>
          </div>
        </div>
      )
    }

    switch (view) {
      case "strategies":
        return (
          <StrategyList
            userId={userId}
            strategies={strategies}
            onEdit={handleEditStrategy}
            onDelete={handleDeleteStrategy}
            onAdd={handleAddStrategy}
            onAddWizard={handleAddStrategyWizard}
            onViewSetups={handleViewSetups}
            onViewRules={handleViewRules}
            onViewPerformance={handleViewPerformance}
            onCompare={handleCompareStrategies}
            onSelectStrategy={handleSelectStrategy}
          />
        )

      case "strategyDetail":
        return (
          <StrategyDetail
            userId={userId}
            strategy={selectedStrategy!}
            onBack={handleBack}
            onEdit={() => handleEditStrategy(selectedStrategy!)}
            onDelete={() => handleDeleteStrategy(selectedStrategy!.id)}
            onViewSetups={() => handleViewSetups(selectedStrategy!)}
            onViewRules={() => handleViewRules(selectedStrategy!)}
            onViewPerformance={() => handleViewPerformance(selectedStrategy!)}
            prefetchedData={
              selectedStrategy && prefetchedData && strategies[0]?.id === selectedStrategy.id
                ? prefetchedData
                : undefined
            }
          />
        )

      case "addStrategy":
        return (
          <StrategyForm
            userId={userId}
            onSuccess={handleStrategySuccess}
            onCancel={handleBack}
          />
        )

      case "addStrategyWizard":
        return (
          <StrategyWizard
            userId={userId}
            onSuccess={handleStrategySuccess}
            onCancel={handleBack}
          />
        )

      case "editStrategy":
        return (
          <StrategyForm
            userId={userId}
            strategy={selectedStrategy!}
            onSuccess={handleStrategySuccess}
            onCancel={handleBack}
          />
        )

      case "compare":
        return (
          <StrategyComparison
            userId={userId}
            allStrategies={strategies}
            onBack={handleBack}
          />
        )

      default:
        return null
    }
  }

  return (
    <div className="container mx-auto py-6 relative">
      {renderView()}
    </div>
  )
}
