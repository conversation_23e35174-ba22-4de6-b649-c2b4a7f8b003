import { Suspense } from "react"
import { TradeDetailsClient } from "./trade-details-client"
import Loading from "./loading"
import { getSupabaseServer } from "@/lib/supabase-server"
import { getStrategiesServer } from "@/lib/playbook-service-server"
import { redirect } from "next/navigation"
import type { Strategy } from "@/types/playbook"

// Server Component
export default async function TradeDetailsPage({ params, searchParams }: {
  params: Promise<{ id: string }>,
  searchParams: Promise<{ source?: string, symbol?: string }>
}) {
  // Extract parameters
  let resolvedParams
  let tradeId

  try {
    resolvedParams = await params
    tradeId = resolvedParams?.id
  } catch (error) {
    console.error("Error resolving params:", error)
    redirect("/trades")
  }

  let resolvedSearchParams
  let source = null
  let symbol = null

  try {
    resolvedSearchParams = await searchParams
    source = resolvedSearchParams?.source || null
    symbol = resolvedSearchParams?.symbol || null
  } catch (error) {
    console.error("Error resolving searchParams:", error)
    // Continue with null values
  }

  // Validate trade ID exists and is a string
  if (!tradeId || typeof tradeId !== 'string') {
    console.error("Invalid trade ID:", {
      tradeId,
      type: typeof tradeId,
      resolvedParams
    })

    // Redirect based on source
    if (source === 'calendar') {
      redirect("/dashboard")
    } else if (source === 'symbol' && symbol) {
      redirect(`/symbols/${symbol}`)
    } else {
      redirect("/trades")
    }
  }

  // Validate trade ID format (should be a UUID)
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
  if (!uuidRegex.test(tradeId)) {
    console.error("Invalid trade ID format - expected UUID but received:", {
      tradeId,
      length: tradeId.length,
      type: typeof tradeId,
      isNumeric: /^\d+$/.test(tradeId),
      expectedFormat: "UUID (e.g., 123e4567-e89b-12d3-a456-************)",
      possibleCause: /^\d+$/.test(tradeId) ? "Using position_id instead of UUID id" : "Invalid format"
    })

    // Redirect based on source
    if (source === 'calendar') {
      redirect("/dashboard")
    } else if (source === 'symbol' && symbol) {
      redirect(`/symbols/${symbol}`)
    } else {
      redirect("/trades")
    }
  }

  // Create server-side Supabase client
  const supabase = await getSupabaseServer()

  // Get the current user
  const { data: { user }, error: authError } = await supabase.auth.getUser()

  if (authError) {
    console.error("Authentication error:", {
      error: authError,
      message: authError.message,
      tradeId
    })
    redirect("/login")
  }

  if (!user) {
    console.error("No authenticated user found:", { tradeId })
    redirect("/login")
  }

  // Fetch the trade data server-side
  const { data: trade, error: tradeError } = await supabase
    .from("trades")
    .select("*")
    .eq("id", tradeId)
    .single()

  // Handle errors
  if (tradeError) {
    console.error("Error fetching trade:", {
      error: tradeError,
      message: tradeError.message,
      details: tradeError.details,
      hint: tradeError.hint,
      code: tradeError.code,
      tradeId,
      userId: user.id
    })

    // Redirect based on source
    if (source === 'calendar') {
      redirect("/dashboard")
    } else if (source === 'symbol' && symbol) {
      redirect(`/symbols/${symbol}`)
    } else {
      redirect("/trades")
    }
  }

  // Check if trade exists
  if (!trade) {
    console.error("Trade not found:", { tradeId, userId: user.id })

    // Redirect based on source
    if (source === 'calendar') {
      redirect("/dashboard")
    } else if (source === 'symbol' && symbol) {
      redirect(`/symbols/${symbol}`)
    } else {
      redirect("/trades")
    }
  }

  // Ensure the trade belongs to the current user
  if (trade.user_id !== user.id) {
    console.error("User doesn't have permission to view this trade:", {
      tradeId,
      tradeUserId: trade.user_id,
      currentUserId: user.id
    })

    // Redirect based on source
    if (source === 'calendar') {
      redirect("/dashboard")
    } else if (source === 'symbol' && symbol) {
      redirect(`/symbols/${symbol}`)
    } else {
      redirect("/trades")
    }
  }

  // Fetch strategies server-side
  let strategies: Strategy[] = []
  try {
    strategies = await getStrategiesServer(user.id)
  } catch (error) {
    console.error("Error fetching strategies:", {
      error,
      userId: user.id,
      tradeId
    })
    // Continue with empty strategies array
  }

  return (
    <div className="container py-8 max-w-6xl">
      <Suspense fallback={<Loading />}>
        <TradeDetailsClient
          trade={trade}
          strategies={strategies}
          source={source}
          symbol={symbol}
          userId={user.id}
        />
      </Suspense>
    </div>
  )
}
