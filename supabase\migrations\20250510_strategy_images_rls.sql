-- Create storage bucket for strategy images if it doesn't exist
INSERT INTO storage.buckets (id, name, public)
VALUES ('strategyimages', 'strategyimages', true)
ON CONFLICT (id) DO NOTHING;

-- Set up storage policies for strategy images
DROP POLICY IF EXISTS "Strategy images are publicly accessible" ON storage.objects;
DROP POLICY IF EXISTS "Users can upload their own strategy images" ON storage.objects;
DROP POLICY IF EXISTS "Users can update their own strategy images" ON storage.objects;
DROP POLICY IF EXISTS "Users can delete their own strategy images" ON storage.objects;

-- Create policies for the strategyimages bucket
CREATE POLICY "Strategy images are publicly accessible"
  ON storage.objects FOR SELECT
  USING (bucket_id = 'strategyimages');

CREATE POLICY "Users can upload their own strategy images"
  ON storage.objects FOR INSERT
  WITH CHECK (bucket_id = 'strategyimages' AND auth.uid() = owner);

CREATE POLICY "Users can update their own strategy images"
  ON storage.objects FOR UPDATE
  USING (bucket_id = 'strategyimages' AND auth.uid() = owner);

CREATE POLICY "Users can delete their own strategy images"
  ON storage.objects FOR DELETE
  USING (bucket_id = 'strategyimages' AND auth.uid() = owner);
