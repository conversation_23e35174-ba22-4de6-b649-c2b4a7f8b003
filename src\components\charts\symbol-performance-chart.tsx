"use client"

import { useMemo } from "react"
import { format } from "date-fns"
import {
  LineChart,
  Line,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  ComposedChart,
  Legend,
  Cell,
} from "recharts"

interface Trade {
  time_close: string
  profit: number
}

interface SymbolPerformanceChartProps {
  trades: Trade[]
}

export function SymbolPerformanceChart({ trades }: SymbolPerformanceChartProps) {
  const chartData = useMemo(() => {
    // Sort trades by date
    const sortedTrades = [...trades].sort(
      (a, b) => new Date(a.time_close).getTime() - new Date(b.time_close).getTime()
    )
    
    // Group trades by day
    const tradesByDay: Record<string, { profit: number, trades: number, cumulative: number }> = {}
    let cumulative = 0
    
    sortedTrades.forEach(trade => {
      const day = format(new Date(trade.time_close), "yyyy-MM-dd")
      
      if (!tradesByDay[day]) {
        tradesByDay[day] = {
          profit: 0,
          trades: 0,
          cumulative: 0
        }
      }
      
      tradesByDay[day].profit += trade.profit
      tradesByDay[day].trades += 1
      cumulative += trade.profit
      tradesByDay[day].cumulative = cumulative
    })
    
    // Convert to array and ensure dates are in order
    return Object.entries(tradesByDay)
      .map(([date, data]) => ({
        date,
        profit: parseFloat(data.profit.toFixed(2)),
        trades: data.trades,
        cumulative: parseFloat(data.cumulative.toFixed(2))
      }))
      .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
  }, [trades])

  return (
    <ResponsiveContainer width="100%" height="100%">
      <ComposedChart
        data={chartData}
        margin={{ top: 20, right: 30, left: 20, bottom: 60 }}
      >
        <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" />
        <XAxis
          dataKey="date"
          angle={-45}
          textAnchor="end"
          height={60}
          tickFormatter={(value) => format(new Date(value), "MMM d")}
          stroke="hsl(var(--muted-foreground))"
        />
        <YAxis
          yAxisId="left"
          orientation="left"
          tickFormatter={(value) => `$${value}`}
          stroke="hsl(var(--muted-foreground))"
        />
        <YAxis
          yAxisId="right"
          orientation="right"
          tickFormatter={(value) => `$${value}`}
          stroke="hsl(var(--muted-foreground))"
        />
        <Tooltip
          content={({ active, payload }) => {
            if (!active || !payload?.length) return null
            const data = payload[0].payload
            return (
              <div className="rounded-lg border bg-card p-2 shadow-sm">
                <div className="grid grid-cols-2 gap-2">
                  <div className="font-medium">Date:</div>
                  <div>{format(new Date(data.date), "MMM d, yyyy")}</div>
                  <div className="font-medium">Daily P&L:</div>
                  <div className={data.profit >= 0 ? "text-emerald-500" : "text-rose-500"}>
                    ${data.profit}
                  </div>
                  <div className="font-medium">Cumulative:</div>
                  <div className={data.cumulative >= 0 ? "text-emerald-500" : "text-rose-500"}>
                    ${data.cumulative}
                  </div>
                  <div className="font-medium">Trades:</div>
                  <div>{data.trades}</div>
                </div>
              </div>
            )
          }}
        />
        <Legend />
        <Bar
          yAxisId="left"
          dataKey="profit"
          name="Daily P&L"
          fill="currentColor"
          className="fill-emerald-500 data-[value<0]:fill-rose-500"
        >
          {chartData.map((entry, index) => (
            <Cell 
              key={`cell-${index}`} 
              fill={entry.profit >= 0 ? "#10b981" : "#ef4444"} 
            />
          ))}
        </Bar>
        <Line
          yAxisId="right"
          type="monotone"
          dataKey="cumulative"
          name="Cumulative P&L"
          stroke="#2563eb"
          strokeWidth={2}
          dot={{ r: 3 }}
        />
      </ComposedChart>
    </ResponsiveContainer>
  )
}
