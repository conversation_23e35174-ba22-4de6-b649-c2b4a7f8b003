'use client';

import { useState, useEffect } from 'react';
import { Trade } from './types';
import { TradeJournalCard } from './trade-journal-card';

interface TradeJournalListProps {
  trades: Trade[];
  strategyMap: Record<string, string>;
  onEdit?: (trade: any) => void;
}

export function TradeJournalList({ trades, strategyMap, onEdit }: TradeJournalListProps) {
  // Keep a local copy of trades to handle updates efficiently
  const [localTrades, setLocalTrades] = useState<Trade[]>(trades);

  // Update local trades when the prop changes
  useEffect(() => {
    setLocalTrades(trades);
  }, [trades]);

  // Handle trade updates
  const handleTradeUpdate = (updatedTrade: any) => {
    // Update the local trades array
    setLocalTrades(prevTrades =>
      prevTrades.map(trade =>
        trade.id === updatedTrade.id ? updatedTrade : trade
      )
    );

    // Call the parent's onEdit callback if provided
    if (onEdit) {
      onEdit(updatedTrade);
    }
  };

  return (
    <div className="space-y-4">
      {localTrades.map((trade) => (
        <TradeJournalCard
          key={trade.id}
          trade={trade}
          strategyName={trade.strategy_id ? strategyMap[trade.strategy_id] : undefined}
          onEdit={handleTradeUpdate}
        />
      ))}
    </div>
  );
}
