export interface Strategy {
  id: string;
  user_id: string;
  name: string;
  description?: string | null;
  market_conditions: string[];
  timeframes: string[];
  instruments: string[];
  risk_reward_ratio?: number | null;
  expected_win_rate?: number | null;
  status: 'active' | 'testing' | 'archived';
  created_at: string;
  updated_at: string;
}

export interface Setup {
  id: string;
  strategy_id: string;
  user_id: string;
  name: string;
  description?: string | null;
  visual_cues?: string | null;
  confirmation_criteria?: string | null;
  image_urls: string[];
  created_at: string;
  updated_at: string;
  strategy?: Strategy;
}

export interface StrategyRule {
  id: string;
  strategy_id: string;
  user_id: string;
  rule_type: 'entry' | 'exit' | 'stop_loss' | 'take_profit' | 'position_sizing' | 'other';
  name: string;
  description?: string | null;
  priority: number;
  created_at: string;
  updated_at: string;
  strategy?: Strategy;
}

export interface StrategyPerformance {
  id: string;
  strategy_id: string;
  user_id: string;
  period_start: string;
  period_end: string;
  total_trades: number;
  winning_trades: number;
  losing_trades: number;
  win_rate: number;
  profit_loss: number;
  average_win: number;
  average_loss: number;
  largest_win: number;
  largest_loss: number;
  average_risk_reward: number;
  expectancy: number;
  profit_factor: number;
  created_at: string;
  updated_at: string;
  strategy?: Strategy;
}

export const MARKET_CONDITIONS = [
  'trending',
  'ranging',
  'volatile',
  'breakout',
  'reversal',
  'consolidation',
  'choppy',
  'low_volatility',
  'high_volatility',
  'news_driven',
  'seasonal',
  'other'
];

export const TIMEFRAMES = [
  '1m',
  '5m',
  '15m',
  '30m',
  '1h',
  '4h',
  'daily',
  'weekly',
  'monthly'
];

export const RULE_TYPES = [
  { value: 'entry', label: 'Entry Rules' },
  { value: 'exit', label: 'Exit Rules' },
  { value: 'stop_loss', label: 'Stop Loss Rules' },
  { value: 'take_profit', label: 'Take Profit Rules' },
  { value: 'position_sizing', label: 'Position Sizing Rules' },
  { value: 'other', label: 'Other Rules' }
];

export const STRATEGY_STATUSES = [
  { value: 'active', label: 'Active' },
  { value: 'testing', label: 'Testing' },
  { value: 'archived', label: 'Archived' }
];
