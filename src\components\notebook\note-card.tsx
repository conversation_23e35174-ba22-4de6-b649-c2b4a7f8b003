"use client"

import { format, formatDistanceToNow } from "date-fns"
import { NotebookEntry, NotebookViewMode, NotebookFolderWithMeta } from "@/types/notebook"
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Pin, Calendar, Tag, FileText, Bookmark, Folder } from "lucide-react"
import { cn } from "@/lib/utils"

interface NoteCardProps {
  entry: NotebookEntry & { is_selected?: boolean }
  viewMode: NotebookViewMode
  folders?: NotebookFolderWithMeta[]
  onClick: () => void
  className?: string
}

export function NoteCard({
  entry,
  viewMode,
  folders = [],
  onClick,
  className
}: NoteCardProps) {
  // Format dates
  const formattedDate = entry.updated_at
    ? format(new Date(entry.updated_at), "MMM d, yyyy")
    : ""

  const timeAgo = entry.updated_at
    ? formatDistanceToNow(new Date(entry.updated_at), { addSuffix: true })
    : ""

  // Extract a preview from the HTML content
  const getContentPreview = () => {
    if (!entry.html_content) return ""

    // Strip HTML tags and get first 120 characters (reduced from 150)
    const stripped = entry.html_content.replace(/<[^>]*>/g, " ")
    return stripped.length > 120
      ? stripped.substring(0, 120) + "..."
      : stripped
  }

  // Get folder information
  const getFolder = () => {
    if (!entry.folder_id || folders.length === 0) return null
    return folders.find(folder => folder.id === entry.folder_id)
  }

  // Helper function to get folder properties
  const getFolderColor = (folder: any) => {
    return folder?.color || '#725ac1'
  }

  const getFolderName = (folder: any) => {
    return folder?.name || 'Folder'
  }

  const folder = getFolder()

  // Create dynamic border styles using CSS classes
  const getBorderClasses = () => {
    const classes = [
      "rounded-lg", // 8px border radius
      "border", // Default border
      entry.is_selected ? "border-[#e2e8f0]" : "border-[rgba(226,232,240,0.5)]"
    ];

    if (entry.is_pinned || folder) {
      classes.push("border-l-[3px]");
    }

    return classes.join(" ");
  };

  // Get border left color
  const getBorderLeftColor = () => {
    if (entry.is_pinned) {
      return '#725ac1'; // Primary color
    } else if (folder) {
      return getFolderColor(folder);
    }
    return undefined;
  };

  // Render grid view card
  if (viewMode === "grid") {
    return (
      <Card
        className={cn(
          "overflow-hidden transition-all hover:shadow-sm cursor-pointer h-[140px] sm:h-[150px] flex flex-col",
          entry.is_selected ? "bg-[#f8f9fa] dark:bg-card/80" : "",
          getBorderClasses(),
          className
        )}
        style={{ borderLeftColor: getBorderLeftColor() }}
        onClick={onClick}
      >
        <CardHeader className="p-1.5 sm:p-2 pb-0.5 flex-shrink-0">
          <div className="flex items-start justify-between">
            <CardTitle className="text-xs sm:text-sm font-medium line-clamp-1">
              {entry.title}
            </CardTitle>
            {entry.is_pinned && (
              <Pin className="h-3 sm:h-3.5 w-3 sm:w-3.5 text-primary flex-shrink-0" />
            )}
          </div>
        </CardHeader>
        <CardContent className="p-1.5 sm:p-2 pt-0 pb-0.5 flex-grow overflow-hidden">
          <p className="text-[10px] sm:text-xs text-muted-foreground line-clamp-2 sm:line-clamp-3">
            {getContentPreview()}
          </p>
        </CardContent>
        <CardFooter className="p-1.5 sm:p-2 pt-0.5 flex-shrink-0 flex flex-col space-y-1 sm:space-y-1.5">
          <div className="w-full flex items-center justify-between text-[9px] sm:text-[10px] text-muted-foreground">
            <div className="flex items-center">
              <Calendar className="h-2.5 w-2.5 mr-0.5" />
              <span className="hidden xs:inline" title={formattedDate}>{formattedDate}</span>
              <span className="xs:hidden" title={formattedDate}>{entry.updated_at ? format(new Date(entry.updated_at), "MM/dd") : ""}</span>
            </div>
            {entry.is_template && (
              <Badge variant="outline" className="text-[8px] sm:text-[9px] h-3.5 sm:h-4 px-1">
                <Bookmark className="h-2 sm:h-2.5 w-2 sm:w-2.5 mr-0.5" />
                <span className="hidden xs:inline">Template</span>
                <span className="xs:hidden">Tmpl</span>
              </Badge>
            )}
          </div>

          <div className="flex flex-wrap items-center gap-0.5 sm:gap-1">
            {folder && (
              <Badge
                variant="outline"
                className="text-[8px] sm:text-[9px] h-3.5 sm:h-4 px-0.5 sm:px-1 rounded-md"
                style={{
                  borderColor: getFolderColor(folder),
                  color: getFolderColor(folder),
                  backgroundColor: `${getFolderColor(folder)}10`,
                  fontWeight: 'normal'
                }}
              >
                <Folder className="h-2 sm:h-2.5 w-2 sm:w-2.5 mr-0.5" />
                <span className="truncate max-w-[60px] sm:max-w-none">{getFolderName(folder)}</span>
              </Badge>
            )}
            {entry.category && !folder && (
              <Badge
                variant="secondary"
                className="text-[8px] sm:text-[9px] h-3.5 sm:h-4 px-0.5 sm:px-1 rounded-md"
                style={{ fontWeight: 'normal' }}
              >
                <FileText className="h-2 sm:h-2.5 w-2 sm:w-2.5 mr-0.5" />
                <span className="truncate max-w-[60px] sm:max-w-none">{entry.category}</span>
              </Badge>
            )}
            {entry.tags && entry.tags.map((tag: string) => (
              <Badge
                key={tag}
                variant="outline"
                className="text-[8px] sm:text-[9px] h-3.5 sm:h-4 px-0.5 sm:px-1 rounded-md bg-muted/50"
                style={{ fontWeight: 'normal' }}
              >
                <Tag className="h-1.5 sm:h-2 w-1.5 sm:w-2 mr-0.5" />
                <span className="truncate max-w-[40px] sm:max-w-none">{tag}</span>
              </Badge>
            ))}
          </div>
        </CardFooter>
      </Card>
    )
  }

  // Render list view card
  return (
    <Card
      className={cn(
        "overflow-hidden transition-all hover:shadow-sm cursor-pointer mb-2",
        entry.is_selected ? "bg-[#f8f9fa] dark:bg-card/80" : "",
        getBorderClasses(),
        className
      )}
      style={{ borderLeftColor: getBorderLeftColor() }}
      onClick={onClick}
    >
      <div className="p-1.5 sm:p-2 flex items-start gap-1 sm:gap-2">
        <div className="flex-grow min-w-0">
          <div className="flex items-center gap-1.5 mb-0.5">
            <h3 className="text-sm font-medium truncate">{entry.title}</h3>
            {entry.is_pinned && (
              <Pin className="h-3.5 w-3.5 text-primary flex-shrink-0" />
            )}
            {entry.is_template && (
              <Badge variant="outline" className="text-[9px] h-4 px-1 rounded-sm">
                <Bookmark className="h-2.5 w-2.5 mr-0.5" />
                Template
              </Badge>
            )}
          </div>
          <p className="text-xs text-muted-foreground line-clamp-2 mb-1.5">
            {getContentPreview()}
          </p>

          <div className="flex flex-wrap items-center gap-0.5 sm:gap-1">
            {folder && (
              <Badge
                variant="outline"
                className="text-[8px] sm:text-[9px] h-3.5 sm:h-4 px-0.5 sm:px-1 rounded-md"
                style={{
                  borderColor: getFolderColor(folder),
                  color: getFolderColor(folder),
                  backgroundColor: `${getFolderColor(folder)}10`,
                  fontWeight: 'normal'
                }}
              >
                <Folder className="h-2 sm:h-2.5 w-2 sm:w-2.5 mr-0.5" />
                <span className="truncate max-w-[80px] sm:max-w-none">{getFolderName(folder)}</span>
              </Badge>
            )}
            {entry.category && !folder && (
              <Badge
                variant="secondary"
                className="text-[8px] sm:text-[9px] h-3.5 sm:h-4 px-0.5 sm:px-1 rounded-md"
                style={{ fontWeight: 'normal' }}
              >
                <FileText className="h-2 sm:h-2.5 w-2 sm:w-2.5 mr-0.5" />
                <span className="truncate max-w-[80px] sm:max-w-none">{entry.category}</span>
              </Badge>
            )}
            {entry.tags && entry.tags.map((tag: string) => (
              <Badge
                key={tag}
                variant="outline"
                className="text-[8px] sm:text-[9px] h-3.5 sm:h-4 px-0.5 sm:px-1 rounded-md bg-muted/50"
                style={{ fontWeight: 'normal' }}
              >
                <Tag className="h-1.5 sm:h-2 w-1.5 sm:w-2 mr-0.5" />
                <span className="truncate max-w-[60px] sm:max-w-none">{tag}</span>
              </Badge>
            ))}
          </div>
        </div>
        <div className="flex-shrink-0 text-[10px] text-muted-foreground flex items-center">
          <Calendar className="h-2.5 w-2.5 mr-0.5" />
          <span className="hidden xs:inline" title={formattedDate}>{formattedDate}</span>
          <span className="xs:hidden" title={formattedDate}>{entry.updated_at ? format(new Date(entry.updated_at), "MM/dd") : ""}</span>
        </div>
      </div>
    </Card>
  )
}
