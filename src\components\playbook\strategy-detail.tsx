"use client"

import React, { useState, useEffect } from "react"
import { Strategy } from "@/types/playbook"
import { formatDistanceToNow } from "date-fns"
import { getCardGradient } from "@/lib/card-utils"

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  ArrowLeft,
  Edit,
  Trash,
  FileText,
  Layers,
  BarChart,
} from "lucide-react"
import { StrategyDynamicContent } from "./strategy-dynamic-content"
import { StrategySetupsSimple } from "./strategy-setups-simple"

interface StrategyDetailProps {
  userId: string
  strategy: Strategy
  onBack: () => void
  onEdit: (strategy: Strategy) => void
  onDelete: (strategy: Strategy) => void
  onViewSetups: (strategy: Strategy) => void
  onViewRules: (strategy: Strategy) => void
  onViewPerformance: (strategy: Strategy) => void
  prefetchedData?: any
}

export function StrategyDetail({
  userId,
  strategy,
  onBack,
  onEdit,
  onDelete,
  onViewSetups,
  onViewRules,
  onViewPerformance,
  prefetchedData,
}: StrategyDetailProps) {
  const [activeView, setActiveView] = useState<"summary" | "setups" | "rules" | "performance">("summary")
  const [refreshKey, setRefreshKey] = useState(0)

  // Add a way to detect which action was used to navigate to this component
  // We'll use a ref to track if we've already processed the initial tab selection
  const [initialTabProcessed, setInitialTabProcessed] = useState(false)

  // Set the active tab based on the action that was used to navigate here
  useEffect(() => {
    // Only process this once when the component mounts
    if (!initialTabProcessed) {
      // Check the URL to see if there's a tab parameter
      const url = new URL(window.location.href)
      const tab = url.searchParams.get('tab')

      if (tab) {
        // Set the active tab based on the URL parameter
        if (tab === 'setups' || tab === 'rules' || tab === 'performance') {
          setActiveView(tab as "setups" | "rules" | "performance")
        }
      }

      setInitialTabProcessed(true)
    }
  }, [initialTabProcessed])

  // Listen for image deletion events to refresh the setups view
  useEffect(() => {
    const handleImageDeleted = () => {
      // If we're currently viewing setups, refresh the data
      if (activeView === "setups") {
        console.log('Image deletion detected, refreshing setups data');
        // We'll trigger a refresh by changing the key on the StrategyDynamicContent component
        setRefreshKey(prev => prev + 1);
      }
    };

    // Listen for both regular and final image deletion events
    document.addEventListener('image-deleted', handleImageDeleted);
    document.addEventListener('image-deleted-final', handleImageDeleted);

    return () => {
      document.removeEventListener('image-deleted', handleImageDeleted);
      document.removeEventListener('image-deleted-final', handleImageDeleted);
    };
  }, [activeView]);

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-emerald-500 text-white font-medium">Active</Badge>
      case 'testing':
        return <Badge className="bg-amber-500 text-white font-medium">Testing</Badge>
      case 'archived':
        return <Badge variant="outline" className="text-muted-foreground font-medium">Archived</Badge>
      default:
        return <Badge variant="outline" className="font-medium">{status}</Badge>
    }
  }

  return (
    <div className="space-y-6 min-h-[calc(100vh-100px)]">
      <div className="flex items-center gap-2">
        <Button variant="ghost" size="icon" onClick={onBack}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <h2 className="text-2xl font-bold tracking-tight">Strategy Details</h2>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6" style={{ position: 'relative' }}>
        {/* Main Strategy Info */}
        <Card className="md:col-span-2">
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <CardTitle className="text-xl">{strategy.name}</CardTitle>
                {getStatusBadge(strategy.status)}
              </div>
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm" onClick={() => onEdit(strategy)}>
                  <Edit className="h-4 w-4 mr-2" />
                  Edit
                </Button>
                <Button variant="outline" size="sm" className="text-destructive" onClick={() => onDelete(strategy)}>
                  <Trash className="h-4 w-4 mr-2" />
                  Delete
                </Button>
              </div>
            </div>
            <CardDescription className="mt-2">
              {strategy.description || "No description provided"}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {/* Dynamic Content Area */}
            {activeView === "setups" ? (
              <StrategySetupsSimple
                key={`strategy-setups-${refreshKey}`}
                userId={userId}
                strategy={strategy}
              />
            ) : (
              <StrategyDynamicContent
                key={`strategy-content-${refreshKey}`}
                userId={userId}
                strategy={strategy}
                view={activeView}
                prefetchedData={prefetchedData}
              />
            )}
          </CardContent>
          <CardFooter className={`pt-2 text-xs text-muted-foreground border-t border-border/30 mt-auto ${getCardGradient(strategy.name).light.footerBg} dark:${getCardGradient(strategy.name).dark.footerBg} dark:border-border/10`}>
            <div className="flex items-center">
              <div
                className="w-2 h-2 rounded-full mr-2 animate-pulse dark:shadow-glow"
                style={{
                  backgroundColor: `var(--mode-light, ${getCardGradient(strategy.name).light.accent}) var(--mode-dark, ${getCardGradient(strategy.name).dark.accent})`,
                  '--mode-light': 'initial',
                  '--mode-dark': 'initial'
                } as React.CSSProperties}>
              </div>
              Updated {formatDistanceToNow(new Date(strategy.updated_at), { addSuffix: true })}
            </div>
          </CardFooter>
        </Card>

        {/* Actions Panel - Sticky */}
        <div className="md:col-span-1">
          <div className="md:sticky md:top-24">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Strategy Actions</CardTitle>
                <CardDescription>
                  Manage your strategy components and analyze performance
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <Button
                  className="w-full justify-start"
                  variant={activeView === "setups" ? "default" : "outline"}
                  onClick={() => {
                    setActiveView("setups");
                    // We'll no longer call the original handler since we're handling the view change internally
                  }}
                >
                  <Layers className="mr-2 h-4 w-4" />
                  View Setups
                </Button>
                <Button
                  className="w-full justify-start"
                  variant={activeView === "rules" ? "default" : "outline"}
                  onClick={() => {
                    setActiveView("rules");
                    // We'll no longer call the original handler since we're handling the view change internally
                  }}
                >
                  <FileText className="mr-2 h-4 w-4" />
                  View Rules
                </Button>
                <Button
                  className="w-full justify-start"
                  variant={activeView === "performance" ? "default" : "outline"}
                  onClick={() => {
                    setActiveView("performance");
                    // We'll no longer call the original handler since we're handling the view change internally
                  }}
                >
                  <BarChart className="mr-2 h-4 w-4" />
                  View Performance
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
