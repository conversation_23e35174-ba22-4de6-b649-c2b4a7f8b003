"use client"

import { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import { Strategy } from "@/types/playbook"
import { getStrategies } from "@/lib/playbook-service"

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bs<PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { BookMarked, Plus } from "lucide-react"

interface PlaybookDashboardProps {
  userId: string
}

export function PlaybookDashboard({ userId }: PlaybookDashboardProps) {
  const router = useRouter()
  const [strategies, setStrategies] = useState<Strategy[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const fetchStrategies = async () => {
      if (userId) {
        setIsLoading(true)
        try {
          const data = await getStrategies(userId)
          // Only show active strategies on the dashboard
          const activeStrategies = data.filter(s => s.status === 'active')
          setStrategies(activeStrategies.slice(0, 3)) // Show only the first 3 strategies
        } catch (error) {
          console.error("Error fetching strategies:", error)
        } finally {
          setIsLoading(false)
        }
      }
    }

    fetchStrategies()
  }, [userId])

  const handleViewAll = () => {
    router.push("/playbook")
  }

  const handleAddStrategy = () => {
    router.push("/playbook")
  }

  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <BookMarked className="mr-2 h-5 w-5" />
            <CardTitle>Trading Playbook</CardTitle>
          </div>
          <Button variant="outline" size="sm" onClick={handleViewAll}>
            View All
          </Button>
        </div>
        <CardDescription>
          Your active trading strategies and setups
        </CardDescription>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="flex justify-center items-center h-40">
            <div className="animate-pulse">Loading strategies...</div>
          </div>
        ) : strategies.length > 0 ? (
          <div className="space-y-4">
            {strategies.map((strategy) => (
              <div
                key={strategy.id}
                className="flex flex-col p-3 border rounded-lg hover:bg-accent/50 cursor-pointer transition-colors"
                onClick={() => router.push(`/playbook`)}
              >
                <div className="flex justify-between items-start mb-2">
                  <h3 className="font-medium">{strategy.name}</h3>
                  <Badge variant="outline" className="text-xs">
                    {strategy.instruments.length} instruments
                  </Badge>
                </div>
                <p className="text-sm text-muted-foreground line-clamp-2 mb-2">
                  {strategy.description || "No description provided"}
                </p>
                <div className="flex flex-wrap gap-1 mt-auto">
                  {strategy.market_conditions.slice(0, 3).map((condition) => (
                    <Badge key={condition} variant="secondary" className="text-xs">
                      {condition.charAt(0).toUpperCase() + condition.slice(1).replace('_', ' ')}
                    </Badge>
                  ))}
                  {strategy.market_conditions.length > 3 && (
                    <Badge variant="secondary" className="text-xs">
                      +{strategy.market_conditions.length - 3} more
                    </Badge>
                  )}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center h-40 text-center">
            <p className="text-muted-foreground mb-4">
              You haven't created any trading strategies yet.
            </p>
            <Button variant="outline" size="sm" onClick={handleAddStrategy}>
              <Plus className="mr-2 h-4 w-4" />
              Create Strategy
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
