"use client"

import { useState, useRef } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { toast } from "sonner"
import { Setup } from "@/types/playbook"
import { uploadImage } from "@/lib/image-uploader"

import { Button } from "@/components/ui/button"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { ImageDisplay } from "@/components/ui/image-display"
import { Image as ImageIcon, Loader2, Trash2 } from "lucide-react"

// Define the form schema
const formSchema = z.object({
  name: z.string().min(1, "Name is required").max(100, "Name must be less than 100 characters"),
  description: z.string().optional(),
  visual_cues: z.string().optional(),
  confirmation_criteria: z.string().optional(),
  image_urls: z.array(z.string()).optional().default([]),
})

export type SetupFormValues = {
  name: string
  description?: string
  visual_cues?: string
  confirmation_criteria?: string
  image_urls?: string[]
}

interface SetupFormSimpleProps {
  setup: Setup | null
  onSave: (data: SetupFormValues) => Promise<void>
  onCancel: () => void
}

export function SetupFormSimple({ setup, onSave, onCancel }: SetupFormSimpleProps) {
  const [isUploading, setIsUploading] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // Initialize form with default values or existing setup values
  const form = useForm<SetupFormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: setup?.name || "",
      description: setup?.description || "",
      visual_cues: setup?.visual_cues || "",
      confirmation_criteria: setup?.confirmation_criteria || "",
      image_urls: setup?.image_urls || [],
    },
  })

  // Handle form submission
  const onSubmit = async (values: SetupFormValues) => {
    try {
      setIsSubmitting(true)
      await onSave(values)
    } catch (error) {
      console.error("Error saving setup:", error)
      toast.error("Failed to save setup")
    } finally {
      setIsSubmitting(false)
    }
  }

  // Handle image upload
  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    // Validate file type
    if (!file.type.startsWith('image/')) {
      toast.error("Please upload an image file")
      return
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      toast.error("File size should not exceed 5MB")
      return
    }

    setIsUploading(true)

    try {
      const imageUrl = await uploadImage(file)

      if (imageUrl) {
        // Get current image URLs from form
        const currentUrls = form.getValues("image_urls") || []

        // Update form with new image URL
        form.setValue("image_urls", [...currentUrls, imageUrl])
        toast.success("Image uploaded successfully")
      }
    } catch (error) {
      console.error("Error uploading image:", error)
      toast.error("Failed to upload image")
    } finally {
      setIsUploading(false)
      // Clear the file input
      if (fileInputRef.current) {
        fileInputRef.current.value = ''
      }
    }
  }

  // Handle removing an image
  const removeImage = (index: number) => {
    const currentUrls = form.getValues("image_urls") || []
    const updatedUrls = [...currentUrls]
    updatedUrls.splice(index, 1)
    form.setValue("image_urls", updatedUrls)
    toast.success("Image removed")
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Setup Name</FormLabel>
              <FormControl>
                <Input placeholder="E.g., Double Bottom, Bull Flag, etc." {...field} />
              </FormControl>
              <FormDescription>
                Give your setup a clear, descriptive name that you'll recognize
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Description</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="E.g., A reversal pattern that forms after a downtrend and signals a potential change in direction..."
                  className="min-h-[100px]"
                  {...field}
                  value={field.value || ""}
                />
              </FormControl>
              <FormDescription>
                Provide a detailed explanation of what this setup is and when it typically appears
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="visual_cues"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Visual Cues</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="E.g., 1. Price makes a new low, then rebounds\n2. Price tests the low again but doesn't break it\n3. Volume decreases on the second low..."
                  className="min-h-[100px]"
                  {...field}
                  value={field.value || ""}
                />
              </FormControl>
              <FormDescription>
                List the specific visual patterns, indicators, or chart formations to look for
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="confirmation_criteria"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Confirmation Criteria</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="E.g., 1. Price breaks above the neckline\n2. Volume increases on the breakout\n3. RSI shows bullish divergence..."
                  className="min-h-[100px]"
                  {...field}
                  value={field.value || ""}
                />
              </FormControl>
              <FormDescription>
                Define the specific conditions that must be met before taking a trade based on this setup
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Image Upload */}
        <FormField
          control={form.control}
          name="image_urls"
          render={({ field }) => (
            <FormItem>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <FormLabel>Setup Images</FormLabel>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => fileInputRef.current?.click()}
                    disabled={isUploading}
                    className="h-8 text-xs"
                  >
                    {isUploading ? (
                      <>
                        <Loader2 className="h-3.5 w-3.5 mr-1.5 animate-spin" />
                        Uploading...
                      </>
                    ) : (
                      <>
                        <ImageIcon className="h-3.5 w-3.5 mr-1.5" />
                        Add Image
                      </>
                    )}
                  </Button>
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept="image/*"
                    className="hidden"
                    onChange={handleImageUpload}
                    disabled={isUploading}
                  />
                </div>

                <FormDescription>
                  Upload chart examples or diagrams of this setup (max 5MB)
                </FormDescription>

                {field.value && field.value.length > 0 ? (
                  <div className="grid grid-cols-1 gap-4 mt-2">
                    {field.value.map((imageUrl, index) => (
                      <div key={index} className="relative group">
                        <ImageDisplay
                          src={imageUrl}
                          alt={`Setup image ${index + 1}`}
                          className="w-full h-auto min-h-[300px]"
                          aspectRatio="auto"
                        />
                        <Button
                          variant="destructive"
                          size="icon"
                          className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity h-7 w-7"
                          onClick={() => removeImage(index)}
                          type="button"
                        >
                          <Trash2 className="h-3.5 w-3.5" />
                        </Button>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center p-8 border border-dashed rounded-md text-muted-foreground">
                    No images added yet. Click "Add Image" to upload a chart example.
                  </div>
                )}
              </div>
            </FormItem>
          )}
        />

        <div className="flex justify-end space-x-2 pt-6">
          <Button type="button" variant="outline" onClick={onCancel} disabled={isSubmitting || isUploading}>
            Cancel
          </Button>
          <Button type="submit" disabled={isSubmitting || isUploading}>
            {isSubmitting ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Saving...
              </>
            ) : (
              'Save Setup'
            )}
          </Button>
        </div>
      </form>
    </Form>
  )
}
