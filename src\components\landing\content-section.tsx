"use client"

import { motion } from 'framer-motion'
import { TrendingUp, Zap } from 'lucide-react'
import Image from 'next/image'

export default function ContentSection() {
    return (
        <section className="py-16 md:py-32 bg-background">
            <div className="mx-auto max-w-5xl space-y-8 px-6 md:space-y-16">
                <motion.h2
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true, margin: "-100px" }}
                    transition={{ duration: 0.6 }}
                    className="relative z-10 max-w-xl text-3xl font-bold md:text-4xl lg:text-5xl">
                    Make data-driven decisions with confidence
                </motion.h2>

                <div className="relative">
                    <div className="relative z-10 space-y-6 md:w-1/2">
                        <motion.p
                            initial={{ opacity: 0, y: 20 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            viewport={{ once: true, margin: "-100px" }}
                            transition={{ duration: 0.6, delay: 0.1 }}
                            className="text-lg">
                            TradePivot transforms your trading data into actionable insights. <span className="font-medium">It supports your entire trading journey</span> — from tracking individual trades to analyzing long-term performance trends.
                        </motion.p>

                        <motion.p
                            initial={{ opacity: 0, y: 20 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            viewport={{ once: true, margin: "-100px" }}
                            transition={{ duration: 0.6, delay: 0.2 }}
                        >
                            Our platform helps you identify what's working and what isn't, so you can refine your approach and improve your results over time.
                        </motion.p>

                        <motion.div
                            initial={{ opacity: 0, y: 20 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            viewport={{ once: true, margin: "-100px" }}
                            transition={{ duration: 0.6, delay: 0.3 }}
                            className="flex flex-col gap-4 pt-6">
                            <div className="flex items-start gap-3">
                                <div className="rounded-full bg-primary/10 p-1.5 text-primary">
                                    <TrendingUp className="size-4" />
                                </div>
                                <div>
                                    <h3 className="font-medium">Performance Tracking</h3>
                                    <p className="text-sm text-muted-foreground">Monitor your progress with detailed metrics and visualizations</p>
                                </div>
                            </div>
                            <div className="flex items-start gap-3">
                                <div className="rounded-full bg-primary/10 p-1.5 text-primary">
                                    <Zap className="size-4" />
                                </div>
                                <div>
                                    <h3 className="font-medium">Rapid Improvement</h3>
                                    <p className="text-sm text-muted-foreground">Identify patterns and optimize your trading strategy faster</p>
                                </div>
                            </div>
                        </motion.div>
                    </div>

                    <motion.div
                        initial={{ opacity: 0, scale: 0.95 }}
                        whileInView={{ opacity: 1, scale: 1 }}
                        viewport={{ once: true, margin: "-100px" }}
                        transition={{ duration: 0.8, delay: 0.4 }}
                        className="absolute right-0 top-0 hidden md:block md:w-1/2">
                        <div role="presentation" className="bg-gradient-to-l absolute inset-0 z-10 from-background to-transparent"></div>
                        <div className="relative rounded-2xl border border-border/50 border-dashed p-2">
                            <div className="aspect-video rounded-xl bg-muted flex items-center justify-center">
                                <p className="text-sm text-muted-foreground">Image placeholder for performance dashboard</p>
                            </div>
                        </div>
                    </motion.div>
                </div>
            </div>
        </section>
    )
}
