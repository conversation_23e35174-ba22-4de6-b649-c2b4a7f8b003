"use client"

import { useState, useEffect } from "react"
import { format, subDays, subMonths, startOfMonth, startOfYear, startOfWeek, endOfWeek, addDays } from "date-fns"
import { Calendar as CalendarIcon, Check, ChevronsUpDown, Filter, Search, X } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { DateRange } from "react-day-picker"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"
import { StrategySelector } from "@/components/strategy-selector"
import { Separator } from "@/components/ui/separator"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { TableDateRangePicker } from "@/components/ui/table-date-range-picker"

export type TimeFilterOption =
  | "today"
  | "this_week"
  | "this_month"
  | "last_30_days"
  | "last_quarter"
  | "ytd"
  | "custom"
  | "all"

export type TradeTypeFilter = "all" | "winning" | "losing"

export interface DashboardFilters {
  dateRange: DateRange | undefined
  timeFilter: TimeFilterOption
  strategyId: string | null
  tradeType: TradeTypeFilter
  symbols: string[]
  showWeekends: boolean
}

interface DashboardFiltersProps {
  userId?: string
  symbols: string[]
  onFilterChange: (filters: DashboardFilters) => void
  className?: string
}

export function DashboardFilters({
  userId,
  symbols,
  onFilterChange,
  className
}: DashboardFiltersProps) {
  // Filter states
  const [timeFilter, setTimeFilter] = useState<TimeFilterOption>("all")
  const [dateRange, setDateRange] = useState<DateRange | undefined>(undefined)
  const [strategyId, setStrategyId] = useState<string | null>(null)
  const [tradeType, setTradeType] = useState<TradeTypeFilter>("all")
  const [selectedSymbols, setSelectedSymbols] = useState<string[]>([])
  const [showWeekends, setShowWeekends] = useState(true)

  // UI states
  const [isFiltersOpen, setIsFiltersOpen] = useState(false)
  const [isSymbolsOpen, setIsSymbolsOpen] = useState(false)
  const [symbolSearch, setSymbolSearch] = useState("")
  const [activeFiltersCount, setActiveFiltersCount] = useState(0)

  // Calculate active filters count
  useEffect(() => {
    let count = 0
    if (timeFilter !== "all") count++
    if (strategyId) count++
    if (tradeType !== "all") count++
    if (selectedSymbols.length > 0) count++
    if (!showWeekends) count++
    setActiveFiltersCount(count)
  }, [timeFilter, strategyId, tradeType, selectedSymbols, showWeekends])

  // Apply date range based on time filter
  useEffect(() => {
    const now = new Date()
    let newRange: DateRange | undefined = undefined

    switch (timeFilter) {
      case "today":
        newRange = {
          from: new Date(new Date().setHours(0, 0, 0, 0)),
          to: new Date()
        }
        break
      case "this_week":
        newRange = {
          from: startOfWeek(now, { weekStartsOn: 0 }), // 0 = Sunday
          to: endOfWeek(now, { weekStartsOn: 0 })
        }
        break
      case "this_month":
        newRange = {
          from: startOfMonth(now),
          to: new Date()
        }
        break
      case "last_30_days":
        newRange = {
          from: subDays(now, 30),
          to: new Date()
        }
        break
      case "last_quarter":
        newRange = {
          from: subMonths(now, 3),
          to: new Date()
        }
        break
      case "ytd":
        newRange = {
          from: startOfYear(now),
          to: new Date()
        }
        break
      case "custom":
        // Keep the current dateRange
        return
      case "all":
        // Reset date range
        newRange = undefined
        break
    }

    setDateRange(newRange)
  }, [timeFilter])

  // Notify parent component when filters change
  useEffect(() => {
    onFilterChange({
      dateRange,
      timeFilter,
      strategyId,
      tradeType,
      symbols: selectedSymbols,
      showWeekends
    })
  }, [dateRange, timeFilter, strategyId, tradeType, selectedSymbols, showWeekends])

  // Filter symbols based on search
  const filteredSymbols = symbols.filter(symbol =>
    symbol.toLowerCase().includes(symbolSearch.toLowerCase())
  )

  // Reset all filters
  const resetFilters = () => {
    setTimeFilter("all")
    setDateRange(undefined)
    setStrategyId(null)
    setTradeType("all")
    setSelectedSymbols([])
    setShowWeekends(true)
  }

  // Toggle symbol selection
  const toggleSymbol = (symbol: string) => {
    if (selectedSymbols.includes(symbol)) {
      setSelectedSymbols(selectedSymbols.filter(s => s !== symbol))
    } else {
      setSelectedSymbols([...selectedSymbols, symbol])
    }
  }

  return (
    <div className={cn("flex flex-wrap items-center gap-2", className)}>
      {/* Time Period Filter */}
      <Select value={timeFilter} onValueChange={(value) => {
        setTimeFilter(value as TimeFilterOption)
        if (value === "custom") {
          setIsFiltersOpen(true)
        }
      }}>
        <SelectTrigger className="h-8 w-[130px] text-xs">
          <SelectValue placeholder="Time Period" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">All Time</SelectItem>
          <SelectItem value="today">Today</SelectItem>
          <SelectItem value="this_week">This Week</SelectItem>
          <SelectItem value="this_month">This Month</SelectItem>
          <SelectItem value="last_30_days">Last 30 Days</SelectItem>
          <SelectItem value="last_quarter">Last Quarter</SelectItem>
          <SelectItem value="ytd">Year to Date</SelectItem>
          <SelectItem value="custom">Custom Range...</SelectItem>
        </SelectContent>
      </Select>

      {/* Advanced Filters Button */}
      <Popover open={isFiltersOpen} onOpenChange={setIsFiltersOpen}>
        <PopoverTrigger asChild>
          <Button variant="outline" size="sm" className="h-8 gap-1 text-xs">
            <Filter className="h-3 w-3" />
            Filters
            {activeFiltersCount > 0 && (
              <Badge variant="secondary" className="ml-1 h-4 w-4 p-0 text-xs flex items-center justify-center">
                {activeFiltersCount}
              </Badge>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-80 p-4" align="start">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h4 className="font-medium">Dashboard Filters</h4>
              <Button variant="ghost" size="sm" onClick={resetFilters} className="h-7 px-2 text-xs">
                Reset
              </Button>
            </div>

            <Separator />

            {/* Date Range */}
            <div className="space-y-2">
              <h5 className="text-sm font-medium">Date Range</h5>
              <TableDateRangePicker
                dateRange={dateRange}
                onDateRangeChange={(range) => {
                  setDateRange(range);
                  if (range?.from) {
                    setTimeFilter("custom");
                  } else {
                    setTimeFilter("all");
                  }
                }}
              />
            </div>

            <Separator />

            {/* Strategy Filter */}
            {userId && (
              <div className="space-y-2">
                <h5 className="text-sm font-medium">Strategy</h5>
                <StrategySelector
                  userId={userId}
                  selectedStrategyId={strategyId}
                  onSelectStrategy={setStrategyId}
                  className="h-8 text-xs w-full"
                />
              </div>
            )}

            {/* Trade Type Filter */}
            <div className="space-y-2">
              <h5 className="text-sm font-medium">Trade Result</h5>
              <Select value={tradeType} onValueChange={(value) => setTradeType(value as TradeTypeFilter)}>
                <SelectTrigger className="h-8 text-xs w-full">
                  <SelectValue placeholder="Trade Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Trades</SelectItem>
                  <SelectItem value="winning">Winning Trades</SelectItem>
                  <SelectItem value="losing">Losing Trades</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Symbol Filter */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <h5 className="text-sm font-medium">Symbols</h5>
                {selectedSymbols.length > 0 && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setSelectedSymbols([])}
                    className="h-6 px-2 text-xs"
                  >
                    Clear
                  </Button>
                )}
              </div>
              <Popover open={isSymbolsOpen} onOpenChange={setIsSymbolsOpen}>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    role="combobox"
                    aria-expanded={isSymbolsOpen}
                    className="h-8 text-xs w-full justify-between"
                  >
                    {selectedSymbols.length > 0
                      ? `${selectedSymbols.length} selected`
                      : "Select symbols"}
                    <ChevronsUpDown className="ml-2 h-3 w-3 shrink-0 opacity-50" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-full p-0">
                  <div className="flex flex-col">
                    <div className="flex items-center border-b px-3">
                      <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
                      <input
                        type="text"
                        placeholder="Search symbols..."
                        className="h-8 text-xs w-full border-none focus:outline-none bg-transparent"
                        value={symbolSearch}
                        onChange={(e) => setSymbolSearch(e.target.value)}
                      />
                    </div>
                    <div className="max-h-[300px] overflow-y-auto p-1">
                      {filteredSymbols.length === 0 ? (
                        <div className="py-6 text-center text-sm">No symbols found.</div>
                      ) : (
                        <div className="flex flex-col gap-1">
                          {filteredSymbols.map((symbol) => (
                            <button
                              key={symbol}
                              type="button"
                              className="w-full text-left relative flex cursor-pointer select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none hover:bg-primary/20"
                              onClick={() => toggleSymbol(symbol)}
                            >
                              <Check
                                className={cn(
                                  "mr-2 h-3 w-3",
                                  selectedSymbols.includes(symbol) ? "opacity-100" : "opacity-0"
                                )}
                              />
                              {symbol}
                            </button>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                </PopoverContent>
              </Popover>
              {selectedSymbols.length > 0 && (
                <div className="flex flex-wrap gap-1 mt-2">
                  {selectedSymbols.map(symbol => (
                    <Badge key={symbol} variant="secondary" className="text-xs">
                      {symbol}
                      <X
                        className="ml-1 h-3 w-3 cursor-pointer"
                        onClick={() => toggleSymbol(symbol)}
                      />
                    </Badge>
                  ))}
                </div>
              )}
            </div>

            {/* Show Weekends Toggle */}
            <div className="flex items-center space-x-2">
              <Checkbox
                id="show-weekends"
                checked={showWeekends}
                onCheckedChange={(checked) => setShowWeekends(checked)}
              />
              <Label htmlFor="show-weekends" className="text-xs">Show weekends</Label>
            </div>
          </div>
        </PopoverContent>
      </Popover>

      {/* Active Filters Display */}
      {activeFiltersCount > 0 && (
        <div className="flex flex-wrap gap-1 ml-1">
          {timeFilter !== "all" && (
            <Badge variant="outline" className="text-xs flex items-center gap-1">
              {timeFilter === "today" && "Today"}
              {timeFilter === "this_week" && "This Week"}
              {timeFilter === "this_month" && "This Month"}
              {timeFilter === "last_30_days" && "Last 30 Days"}
              {timeFilter === "last_quarter" && "Last Quarter"}
              {timeFilter === "ytd" && "Year to Date"}
              {timeFilter === "custom" && "Custom Date Range"}
              <X
                className="h-3 w-3 cursor-pointer"
                onClick={() => setTimeFilter("all")}
              />
            </Badge>
          )}
          {strategyId && (
            <Badge variant="outline" className="text-xs flex items-center gap-1">
              Strategy
              <X
                className="h-3 w-3 cursor-pointer"
                onClick={() => setStrategyId(null)}
              />
            </Badge>
          )}
          {tradeType !== "all" && (
            <Badge variant="outline" className="text-xs flex items-center gap-1">
              {tradeType === "winning" ? "Winning Trades" : "Losing Trades"}
              <X
                className="h-3 w-3 cursor-pointer"
                onClick={() => setTradeType("all")}
              />
            </Badge>
          )}
          {selectedSymbols.length > 0 && (
            <Badge variant="outline" className="text-xs flex items-center gap-1">
              {selectedSymbols.length} Symbols
              <X
                className="h-3 w-3 cursor-pointer"
                onClick={() => setSelectedSymbols([])}
              />
            </Badge>
          )}
          {!showWeekends && (
            <Badge variant="outline" className="text-xs flex items-center gap-1">
              Hide Weekends
              <X
                className="h-3 w-3 cursor-pointer"
                onClick={() => setShowWeekends(true)}
              />
            </Badge>
          )}
          <Button
            variant="ghost"
            size="sm"
            onClick={resetFilters}
            className="h-6 px-2 text-xs"
          >
            Clear All
          </Button>
        </div>
      )}
    </div>
  )
}
