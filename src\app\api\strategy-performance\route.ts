import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { createServerClient } from '@supabase/ssr';
import type { Database } from '@/types/supabase';
import { StrategyPerformance } from '@/types/playbook';

// GET handler to fetch strategy performance
export async function GET(request: Request) {
  try {
    // Get URL parameters
    const url = new URL(request.url);
    const strategyId = url.searchParams.get('strategyId');
    const accountId = url.searchParams.get('accountId');

    if (!strategyId) {
      return NextResponse.json({ error: 'Strategy ID is required' }, { status: 400 });
    }

    // Get cookies for server-side Supabase client
    const cookieStore = await cookies();
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
          set(_name: string, _value: string, _options: any) {
            // API routes can't set cookies directly
          },
          remove(_name: string, _options: any) {
            // API routes can't remove cookies directly
          }
        },
      }
    );

    // Get authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const userId = user.id;

    // Build query for strategy performance
    let query = supabase
      .from('strategy_performance')
      .select('*')
      .eq('strategy_id', strategyId)
      .eq('user_id', userId);

    // Filter by account if specified
    if (accountId) {
      query = query.eq('account_id', accountId);
    } else {
      query = query.is('account_id', null);
    }

    // Execute query
    const { data, error } = await query.order('period_start', { ascending: false });

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json(data || []);
  } catch (error) {
    console.error('Error fetching strategy performance:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// POST handler to calculate and save strategy performance
export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { strategyId, periodStart, periodEnd, accountId } = body;

    if (!strategyId || !periodStart || !periodEnd) {
      return NextResponse.json({
        error: 'Missing required parameters',
        required: ['strategyId', 'periodStart', 'periodEnd']
      }, { status: 400 });
    }

    // Get cookies for server-side Supabase client
    const cookieStore = await cookies();
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
          set(_name: string, _value: string, _options: any) {
            // API routes can't set cookies directly
          },
          remove(_name: string, _options: any) {
            // API routes can't remove cookies directly
          }
        },
      }
    );

    // Get authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const userId = user.id;

    // First, check if the strategy exists
    const { data: strategy, error: strategyError } = await supabase
      .from('strategies')
      .select('name')
      .eq('id', strategyId)
      .eq('user_id', userId)
      .single();

    if (strategyError) {
      return NextResponse.json({ error: 'Strategy not found' }, { status: 404 });
    }

    // Get all trades for this strategy in the given period
    let query = supabase
      .from('trades')
      .select('*')
      .eq('user_id', userId)
      .eq('strategy_id', strategyId)
      .gte('time_open', periodStart)
      .lte('time_close', periodEnd);

    // Filter by account if specified
    if (accountId) {
      query = query.eq('account_id', accountId);
    }

    const { data: trades, error } = await query;

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    // If no trades found, return empty performance
    if (!trades || trades.length === 0) {
      return NextResponse.json({
        message: 'No trades found for this strategy in the specified period',
        data: null
      });
    }

    // Calculate performance metrics
    const winningTrades = trades.filter(trade => trade.profit > 0);
    const losingTrades = trades.filter(trade => trade.profit < 0);

    const totalTrades = trades.length;
    const winningTradesCount = winningTrades.length;
    const losingTradesCount = losingTrades.length;

    const winRate = totalTrades > 0 ? winningTradesCount / totalTrades : 0;
    const profitLoss = trades.reduce((sum, trade) => sum + trade.profit, 0);

    const averageWin = winningTradesCount > 0
      ? winningTrades.reduce((sum, trade) => sum + trade.profit, 0) / winningTradesCount
      : 0;

    const averageLoss = losingTradesCount > 0
      ? Math.abs(losingTrades.reduce((sum, trade) => sum + trade.profit, 0)) / losingTradesCount
      : 0;

    const largestWin = winningTradesCount > 0
      ? Math.max(...winningTrades.map(trade => trade.profit))
      : 0;

    const largestLoss = losingTradesCount > 0
      ? Math.abs(Math.min(...losingTrades.map(trade => trade.profit)))
      : 0;

    const averageRiskReward = averageLoss > 0 ? averageWin / averageLoss : 0;
    const expectancy = (winRate * averageWin) - ((1 - winRate) * averageLoss);

    // Calculate profit factor (gross profit / gross loss)
    const grossProfit = winningTrades.reduce((sum, trade) => sum + trade.profit, 0);
    const grossLoss = Math.abs(losingTrades.reduce((sum, trade) => sum + trade.profit, 0));
    // When gross loss is 0 but gross profit is positive, set profit factor to 999 (will display as ∞)
    const profitFactor = grossLoss > 0 ? grossProfit / grossLoss : grossProfit > 0 ? 999 : 0;

    // Create performance data object
    const performanceData = {
      strategy_id: strategyId,
      user_id: userId,
      account_id: accountId || null,
      period_start: periodStart,
      period_end: periodEnd,
      total_trades: totalTrades,
      winning_trades: winningTradesCount,
      losing_trades: losingTradesCount,
      win_rate: winRate,
      profit_loss: profitLoss,
      average_win: averageWin,
      average_loss: averageLoss,
      largest_win: largestWin,
      largest_loss: largestLoss,
      average_risk_reward: averageRiskReward,
      expectancy: expectancy,
      profit_factor: profitFactor
    };

    // Check if a performance record already exists for this period and account
    let lookupQuery = supabase
      .from('strategy_performance')
      .select('id')
      .eq('strategy_id', strategyId)
      .eq('period_start', periodStart)
      .eq('period_end', periodEnd);

    // Include account_id in the uniqueness check if specified
    if (accountId) {
      lookupQuery = lookupQuery.eq('account_id', accountId);
    } else {
      lookupQuery = lookupQuery.is('account_id', null);
    }

    const { data: existingPerformance, error: lookupError } = await lookupQuery.maybeSingle();

    if (lookupError) {
      return NextResponse.json({ error: lookupError.message }, { status: 500 });
    }

    let result;
    if (existingPerformance) {
      // Update existing record
      const { data, error: updateError } = await supabase
        .from('strategy_performance')
        .update(performanceData)
        .eq('id', existingPerformance.id)
        .eq('user_id', userId)
        .select()
        .single();

      if (updateError) {
        return NextResponse.json({ error: updateError.message }, { status: 500 });
      }

      result = data;
    } else {
      // Create new record
      const { data, error: insertError } = await supabase
        .from('strategy_performance')
        .insert([performanceData])
        .select()
        .single();

      if (insertError) {
        return NextResponse.json({ error: insertError.message }, { status: 500 });
      }

      result = data;
    }

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error calculating strategy performance:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
