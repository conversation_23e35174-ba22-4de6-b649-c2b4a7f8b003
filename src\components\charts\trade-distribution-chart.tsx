"use client"

import { type ProcessedData } from "@/lib/excel-processor"
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Legend,
} from "recharts"

interface TradeDistributionChartProps {
  trades: ProcessedData["trades"]
}

export function TradeDistributionChart({ trades }: TradeDistributionChartProps) {
  // Calculate trade distribution by symbol
  const tradesBySymbol = trades.reduce((acc, trade) => {
    const symbol = trade.symbol
    if (!acc[symbol]) {
      acc[symbol] = {
        symbol,
        totalTrades: 0,
        winningTrades: 0,
        losingTrades: 0,
        totalProfit: 0,
      }
    }

    acc[symbol].totalTrades++
    if (trade.profit > 0) {
      acc[symbol].winningTrades++
      acc[symbol].totalProfit += trade.profit
    } else if (trade.profit < 0) {
      acc[symbol].losingTrades++
      acc[symbol].totalProfit += trade.profit
    }

    return acc
  }, {} as Record<string, {
    symbol: string
    totalTrades: number
    winningTrades: number
    losingTrades: number
    totalProfit: number
  }>)

  const chartData = Object.values(tradesBySymbol)
    .sort((a, b) => b.totalTrades - a.totalTrades)
    .slice(0, 8) // Show top 8 most traded symbols
    .map(data => ({
      ...data,
      winRate: ((data.winningTrades / data.totalTrades) * 100).toFixed(1),
      avgProfit: (data.totalProfit / data.totalTrades).toFixed(2)
    }))

  return (
    <ResponsiveContainer width="100%" height="100%">
      <BarChart
        data={chartData}
        margin={{
          top: 20,
          right: 30,
          left: 20,
          bottom: 20,
        }}
        barGap={0}
        barCategoryGap={8}
      >
        <CartesianGrid
          strokeDasharray="3 3"
          vertical={false}
          stroke="rgba(255,255,255,0.1)"
        />
        <XAxis
          dataKey="symbol"
          tick={{ fill: "#9ca3af", fontSize: 12 }}
          stroke="rgba(255,255,255,0.1)"
        />
        <YAxis
          yAxisId="left"
          orientation="left"
          tick={{ fill: "#9ca3af", fontSize: 12 }}
          stroke="rgba(255,255,255,0.1)"
          tickFormatter={(value) => `${value}%`}
        />
        <YAxis
          yAxisId="right"
          orientation="right"
          tick={{ fill: "#9ca3af", fontSize: 12 }}
          stroke="rgba(255,255,255,0.1)"
          tickFormatter={(value) => value}
        />
        <Tooltip
          contentStyle={{
            backgroundColor: "#1C1C1C",
            border: "none",
            borderRadius: "6px",
            color: "#9ca3af",
          }}
          formatter={(value: number, name: string) => {
            switch (name) {
              case "winRate":
                return [`${value}%`, "Win Rate"]
              case "totalTrades":
                return [value, "Total Trades"]
              default:
                return [value, name]
            }
          }}
        />
        <Legend
          formatter={(value) => {
            switch (value) {
              case "winRate":
                return "Win Rate"
              case "totalTrades":
                return "Total Trades"
              default:
                return value
            }
          }}
          wrapperStyle={{ color: "#9ca3af" }}
        />
        <Bar
          yAxisId="left"
          dataKey="winRate"
          fill="#2563eb"
          radius={[4, 4, 0, 0]}
        />
        <Bar
          yAxisId="right"
          dataKey="totalTrades"
          fill="#10b981"
          radius={[4, 4, 0, 0]}
        />
      </BarChart>
    </ResponsiveContainer>
  )
} 