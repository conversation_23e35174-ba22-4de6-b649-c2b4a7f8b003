"use client"

import React from 'react'
import { cn } from '@/lib/utils'

interface EditorSkeletonProps {
  className?: string
  showHeader?: boolean
}

export function EditorSkeleton({ className, showHeader = true }: EditorSkeletonProps) {
  // Check for reduced motion preference
  const prefersReducedMotion = typeof window !== 'undefined' 
    ? window.matchMedia('(prefers-reduced-motion: reduce)').matches 
    : false

  return (
    <div className={cn("h-full flex flex-col p-3 space-y-3", className)}>
      {/* Header skeleton */}
      {showHeader && (
        <div className="flex items-center justify-between">
          <div className={cn(
            "h-5 bg-muted rounded w-48",
            !prefersReducedMotion && "animate-pulse"
          )} />
          <div className={cn(
            "h-7 bg-muted rounded w-16",
            !prefersReducedMotion && "animate-pulse"
          )} />
        </div>
      )}

      {/* Toolbar skeleton */}
      <div className="border rounded-lg p-2">
        <div className="flex items-center space-x-2">
          {/* Toolbar buttons */}
          {Array.from({ length: 8 }).map((_, i) => (
            <div
              key={i}
              className={cn(
                "h-8 w-8 bg-muted rounded",
                !prefersReducedMotion && "animate-pulse"
              )}
              style={{
                animationDelay: prefersReducedMotion ? '0ms' : `${i * 50}ms`
              }}
            />
          ))}
          
          {/* Separator */}
          <div className="w-px h-6 bg-border mx-2" />
          
          {/* Dropdown buttons */}
          {Array.from({ length: 3 }).map((_, i) => (
            <div
              key={`dropdown-${i}`}
              className={cn(
                "h-8 bg-muted rounded",
                i === 0 ? "w-20" : i === 1 ? "w-16" : "w-24",
                !prefersReducedMotion && "animate-pulse"
              )}
              style={{
                animationDelay: prefersReducedMotion ? '0ms' : `${(i + 8) * 50}ms`
              }}
            />
          ))}
        </div>
      </div>

      {/* Content skeleton */}
      <div className="flex-1 border rounded-lg p-4 space-y-3 overflow-hidden">
        {/* Title line */}
        <div className={cn(
          "h-6 bg-muted rounded w-3/4",
          !prefersReducedMotion && "animate-pulse"
        )} />
        
        {/* Content lines */}
        {Array.from({ length: 8 }).map((_, i) => (
          <div
            key={`line-${i}`}
            className={cn(
              "h-4 bg-muted rounded",
              // Vary the widths for more realistic skeleton
              i === 2 ? "w-1/2" : i === 5 ? "w-2/3" : i === 7 ? "w-3/4" : "w-full",
              !prefersReducedMotion && "animate-pulse"
            )}
            style={{
              animationDelay: prefersReducedMotion ? '0ms' : `${i * 100}ms`
            }}
          />
        ))}
        
        {/* Paragraph break */}
        <div className="h-4" />
        
        {/* More content lines */}
        {Array.from({ length: 5 }).map((_, i) => (
          <div
            key={`line2-${i}`}
            className={cn(
              "h-4 bg-muted rounded",
              i === 1 ? "w-5/6" : i === 3 ? "w-1/3" : "w-full",
              !prefersReducedMotion && "animate-pulse"
            )}
            style={{
              animationDelay: prefersReducedMotion ? '0ms' : `${(i + 8) * 100}ms`
            }}
          />
        ))}
      </div>
    </div>
  )
}
