import { cookies } from "next/headers"
import { createServerClient } from "@supabase/ssr"
import { redirect } from "next/navigation"
import type { Database } from "@/types/supabase"
import { NotebookEntry, NotebookFolderWithMeta, NotebookTag } from "@/types/notebook"
import NoteClientWrapper from "./client-wrapper"

interface NotePageProps {
  params: Promise<{
    id: string
  }>
}

export default async function NotePage({ params }: NotePageProps) {
  const { id } = await params

  // Get cookies for server-side Supabase client
  const cookieStore = await cookies()

  // Create a Supabase client with proper cookie handling
  const supabase = createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value
        },
        set(_name: string, _value: string, _options: any) {
          // Server components can't set cookies directly
        },
        remove(_name: string, _options: any) {
          // Server components can't remove cookies directly
        }
      },
    }
  )

  // Get authenticated user
  const { data: { user }, error: userError } = await supabase.auth.getUser()
  if (userError || !user) {
    redirect("/auth")
  }

  const userId = user.id

  // Fetch the notebook entry
  const { data: entry, error: entryError } = await supabase
    .from("notebook_entries")
    .select("*")
    .eq("id", id)
    .eq("user_id", userId)
    .single()

  if (entryError) {
    if (entryError.code === "PGRST116") {
      // Entry not found or doesn't belong to the user
      redirect("/notebook")
    }

    console.error("Error fetching notebook entry:", entryError)
    // Handle other errors
    redirect("/notebook")
  }

  // Fetch related entries (same category or tags)
  let relatedEntries: NotebookEntry[] = []

  if (entry.category || (entry.tags && entry.tags.length > 0)) {
    let query = supabase
      .from("notebook_entries")
      .select("*")
      .eq("user_id", userId)
      .neq("id", id) // Exclude current entry
      .limit(5)

    if (entry.category) {
      query = query.eq("category", entry.category)
    } else if (entry.tags && entry.tags.length > 0) {
      query = query.overlaps("tags", entry.tags)
    }

    const { data, error } = await query

    if (!error && data) {
      relatedEntries = data
    }
  }

  // Fetch folders
  const { data: folders, error: foldersError } = await supabase
    .from("notebook_folders")
    .select("*")
    .eq("user_id", userId)
    .order("name")

  if (foldersError) {
    console.error("Error fetching folders:", foldersError)
  }

  // Don't show any counts on server-side to avoid showing incorrect values
  // Client-side React Query will fetch and display accurate account-filtered counts
  const foldersWithCounts: NotebookFolderWithMeta[] = (folders || []).map(folder => ({
    ...folder,
    count: undefined // No count initially - client will populate with correct values
  }))

  // Don't fetch tags on server-side to avoid showing incorrect account-filtered data
  // Client-side React Query will fetch accurate account-filtered tags
  const tags: NotebookTag[] = []

  return (
    <NoteClientWrapper
      userId={userId}
      entry={entry}
      relatedEntries={relatedEntries}
      initialFolders={foldersWithCounts}
      initialTags={tags}
    />
  )
}
