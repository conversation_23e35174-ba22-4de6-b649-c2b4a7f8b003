"use client"

import { format, startOfDay } from "date-fns"
import { ChartTooltip } from "@/components/ui/chart-tooltip"
import {
  Bar,
  BarChart,
  CartesianGrid,
  ComposedChart,
  Line,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
  Legend,
  Cell,
  ReferenceLine,
} from "recharts"

interface Trade {
  time_close: string
  profit: number
}

interface DailyCumulativePnLChartProps {
  trades: Trade[]
}

export function DailyCumulativePnLChart({ trades }: DailyCumulativePnLChartProps) {
  // Handle empty trades array
  if (!trades || trades.length === 0) {
    return (
      <div className="flex items-center justify-center h-full w-full text-muted-foreground">
        No trade data available
      </div>
    )
  }

  // Sort trades by date first to ensure chronological order
  const sortedTrades = [...trades].sort((a, b) => {
    // Handle missing time_close values
    const aTime = a.time_close ? new Date(a.time_close).getTime() : new Date().getTime()
    const bTime = b.time_close ? new Date(b.time_close).getTime() : new Date().getTime()
    return aTime - bTime
  })

  // Group trades by day and calculate daily P&L, also count trades per day
  const dailyData = sortedTrades.reduce((acc: { [key: string]: { pnl: number; tradeCount: number } }, trade) => {
    if (!trade.time_close) return acc
    try {
      // Use same pattern as calendar component for consistent date handling
      const day = format(new Date(trade.time_close), "yyyy-MM-dd")
      if (!acc[day]) {
        acc[day] = { pnl: 0, tradeCount: 0 }
      }
      acc[day].pnl += (typeof trade.profit === 'number' ? trade.profit : 0)
      acc[day].tradeCount += 1
      return acc
    } catch (e) {
      console.warn('Invalid date in trade:', trade)
      return acc
    }
  }, {})

  // Convert to array and sort by date
  const sortedDays = Object.entries(dailyData)
    .map(([date, data]) => ({
      date,
      pnl: parseFloat(data.pnl.toFixed(2)), // Limit decimal places
      tradeCount: data.tradeCount,
    }))
    .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime()) // Ensure chronological order

  // Calculate cumulative P&L starting from 0
  let cumulativePnL = 0
  const chartData = [
    // Add initial point at 0
    {
      date: sortedDays.length > 0 ? sortedDays[0].date : format(new Date(), "yyyy-MM-dd"),
      pnl: 0,
      cumulativePnL: 0,
      tradeCount: 0
    },
    // Add all other points
    ...sortedDays.map(({ date, pnl, tradeCount }) => {
      cumulativePnL += pnl
      return {
        date,
        pnl,
        cumulativePnL: parseFloat(cumulativePnL.toFixed(2)), // Limit decimal places
        tradeCount,
      }
    })
  ] // Show all days

  return (
    <ResponsiveContainer width="100%" height="100%">
      <ComposedChart data={chartData} margin={{ top: 20, right: 30, bottom: 60, left: 20 }}>
        <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" opacity={0.4} />
        <XAxis
          dataKey="date"
          tickFormatter={(value: string) => format(new Date(value), "MMM d")}
          stroke="hsl(var(--muted-foreground))"
          tick={(props) => {
            const { x, y, payload } = props;
            return (
              <g transform={`translate(${x},${y})`}>
                <text
                  x={0}
                  y={0}
                  dy={16}
                  textAnchor="end"
                  fill="hsl(var(--muted-foreground))"
                  opacity={0.7}
                  fontSize={11}
                  transform="rotate(-45)"
                >
                  {format(new Date(payload.value), "MMM d")}
                </text>
              </g>
            );
          }}
          height={60}
          axisLine={{ stroke: 'hsl(var(--border))', opacity: 0.5 }}
        />
        <YAxis
          yAxisId="left"
          orientation="left"
          tickFormatter={(value: number) => `$${value.toLocaleString()}`}
          stroke="hsl(var(--muted-foreground))"
          tick={{ fontSize: 11, fill: 'hsl(var(--muted-foreground))', opacity: 0.7 }}
          axisLine={{ stroke: 'hsl(var(--border))', opacity: 0.5 }}
        />
        <ReferenceLine
          y={0}
          yAxisId="left"
          stroke="rgba(100, 100, 100, 0.5)"
          strokeDasharray="3 3"
        />
        <Tooltip
          content={({ active, payload }: { active?: boolean, payload?: any[] }) => {
            if (!active || !payload || !payload.length) return null;

            const data = payload[0].payload;
            const date = format(new Date(data.date), "MMM d, yyyy");

            return (
              <div className="rounded-lg border bg-card/95 backdrop-blur p-2 shadow-lg dark:shadow-none text-xs">
                <div className="font-medium text-muted-foreground mb-1">{date}</div>
                <div className="grid grid-cols-2 gap-x-3 gap-y-0.5">
                  <span className="text-muted-foreground">Daily P&L:</span>
                  <span className={`font-medium text-right ${
                    data.pnl >= 0 ? 'text-emerald-500' : 'text-rose-500'
                  }`}>
                    ${data.pnl.toLocaleString()}
                  </span>
                  <span className="text-muted-foreground">Cumulative:</span>
                  <span className={`font-medium text-right ${
                    data.cumulativePnL >= 0 ? 'text-emerald-500' : 'text-rose-500'
                  }`}>
                    ${data.cumulativePnL.toLocaleString()}
                  </span>
                  <span className="text-muted-foreground">Trades:</span>
                  <span className="font-medium text-right">
                    {data.tradeCount}
                  </span>
                </div>
              </div>
            );
          }}
        />
        <Legend />
        <Bar
          yAxisId="left"
          dataKey="pnl"
          name="Daily P&L"
        >
          {chartData.map((entry, index) => (
            <Cell
              key={`cell-${index}`}
              fill={entry.pnl >= 0 ? "#10b981" : "#ef4444"}
            />
          ))}
        </Bar>
        <Line
          yAxisId="left"
          type="monotone"
          dataKey="cumulativePnL"
          name="Cumulative P&L"
          stroke="#2563eb"
          strokeWidth={2}
          dot={false}
          activeDot={{ r: 4, strokeWidth: 0 }}
        />
      </ComposedChart>
    </ResponsiveContainer>
  )
}
