import * as XLSX from "xlsx"

export interface Trade {
  time_open: string
  position_id: number
  symbol: string
  type: "buy" | "sell"
  volume: number
  price_open: number
  sl: number | null
  tp: number | null
  time_close: string
  price_close: number
  commission: number
  swap: number
  profit: number
  strategy_id?: string | null
  setup_id?: string | null
  comment?: string | null
}

export interface TradingSummary {
  total_net_profit: number
  gross_profit: number
  gross_loss: number
  profit_factor: number
  expected_payoff: number
  recovery_factor: number
  sharpe_ratio: number
  balance_drawdown_absolute: number
  balance_drawdown_maximal: string
  balance_drawdown_relative: string
  total_trades: number
  short_trades_won: string
  long_trades_won: string
  profit_trades: string
  loss_trades: string
  largest_profit_trade: number
  largest_loss_trade: number
  average_profit_trade: number
  average_loss_trade: number
  maximum_consecutive_wins: string
  maximum_consecutive_losses: string
  maximal_consecutive_profit: string
  maximal_consecutive_loss: string
  average_consecutive_wins: number
  average_consecutive_losses: number
}

export interface ProcessedData {
  account: {
    name: string
    account: string
    company: string
    date: string
  }
  trades: Array<{
    position_id: number
    time_open: string
    time_close: string
    symbol: string
    type: string
    volume: number
    price_open: number
    price_close: number
    commission: number
    swap: number
    profit: number
    sl?: number | null
    tp?: number | null
    strategy_id?: string | null
    setup_id?: string | null
  }>
  summary: {
    total_trades: number
    profit_trades: string
    loss_trades: string
    total_net_profit: number
    gross_profit: number
    gross_loss: number
    profit_factor: number
    expected_payoff: number
    recovery_factor: number
    sharpe_ratio: number
    balance_drawdown_absolute: number
    balance_drawdown_maximal: string
    balance_drawdown_relative: string
    short_trades_won: string
    long_trades_won: string
    largest_profit_trade: number
    largest_loss_trade: number
    average_profit_trade: number
    average_loss_trade: number
    maximum_consecutive_wins: string
    maximum_consecutive_losses: string
    maximal_consecutive_profit: string
    maximal_consecutive_loss: string
    average_consecutive_wins: number
    average_consecutive_losses: number
    initial_balance?: number
  }
}

export async function processExcelFile(file: File): Promise<ProcessedData> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()

    reader.onload = (e) => {
      try {
        const data = new Uint8Array(e.target?.result as ArrayBuffer)
        const workbook = XLSX.read(data, { type: "array" })

        // Process trades sheet
        const tradesSheet = workbook.Sheets[workbook.SheetNames[0]]
        const trades = XLSX.utils.sheet_to_json(tradesSheet)

        // Calculate summary
        const totalTrades = trades.length
        const profitTrades = trades.filter((trade: any) => trade.Profit > 0)
        const lossTrades = trades.filter((trade: any) => trade.Profit < 0)
        const totalProfit = trades.reduce((sum: number, trade: any) => sum + trade.Profit, 0)

        // Process and format trades data
        const processedTrades = trades.map((trade: any) => ({
          position_id: trade["Position ID"] || trade.Position,
          time_open: trade["Time Open"] || trade.Open,
          time_close: trade["Time Close"] || trade.Close,
          symbol: trade.Symbol,
          type: trade.Type,
          volume: trade.Volume || trade.Lots,
          price_open: trade["Price Open"] || trade["Open Price"],
          price_close: trade["Price Close"] || trade["Close Price"],
          commission: trade.Commission || 0,
          swap: trade.Swap || 0,
          profit: trade.Profit,
          sl: trade.SL || trade["Stop Loss"] || null,
          tp: trade.TP || trade["Take Profit"] || null,
          strategy_id: null, // Default to null for imported trades
          setup_id: null, // Default to null for imported trades
        }))

        const processedData: ProcessedData = {
          account: {
            name: "Trading Account",
            account: "Demo Account",
            company: "MetaTrader 5",
            date: new Date().toISOString(),
          },
          trades: processedTrades,
          summary: {
            total_trades: totalTrades,
            profit_trades: ((profitTrades.length / totalTrades) * 100).toFixed(2) + '%',
            loss_trades: ((lossTrades.length / totalTrades) * 100).toFixed(2) + '%',
            total_net_profit: totalProfit,
            gross_profit: profitTrades.reduce((sum: number, trade: any) => sum + trade.Profit, 0),
            gross_loss: Math.abs(lossTrades.reduce((sum: number, trade: any) => sum + trade.Profit, 0)),
            profit_factor: lossTrades.length > 0 ?
              profitTrades.reduce((sum: number, trade: any) => sum + trade.Profit, 0) /
              Math.abs(lossTrades.reduce((sum: number, trade: any) => sum + trade.Profit, 0)) :
              profitTrades.length > 0 ? 999 : 0,
            expected_payoff: totalTrades > 0 ? totalProfit / totalTrades : 0,
            recovery_factor: 0, // Default value
            sharpe_ratio: 0, // Default value
            balance_drawdown_absolute: 0, // Default value
            balance_drawdown_maximal: calculateMaxDrawdown(processedTrades).toFixed(2) + ' (0.00%)',
            balance_drawdown_relative: '0.00% (0.00)',
            short_trades_won: '0 (0.00%)', // Default value
            long_trades_won: '0 (0.00%)', // Default value
            largest_profit_trade: profitTrades.length > 0 ?
              Math.max(...profitTrades.map((trade: any) => trade.Profit)) : 0,
            largest_loss_trade: lossTrades.length > 0 ?
              Math.min(...lossTrades.map((trade: any) => trade.Profit)) : 0,
            average_profit_trade: profitTrades.length > 0 ?
              profitTrades.reduce((sum: number, trade: any) => sum + trade.Profit, 0) / profitTrades.length : 0,
            average_loss_trade: lossTrades.length > 0 ?
              lossTrades.reduce((sum: number, trade: any) => sum + trade.Profit, 0) / lossTrades.length : 0,
            maximum_consecutive_wins: '0 (0.00)', // Default value
            maximum_consecutive_losses: '0 (0.00)', // Default value
            maximal_consecutive_profit: '0.00 (0)', // Default value
            maximal_consecutive_loss: '0.00 (0)', // Default value
            average_consecutive_wins: 0, // Default value
            average_consecutive_losses: 0, // Default value
            initial_balance: 10000, // Default initial balance
          },
        }

        resolve(processedData)
      } catch (error) {
        reject(new Error("Failed to process Excel file"))
      }
    }

    reader.onerror = () => {
      reject(new Error("Failed to read file"))
    }

    reader.readAsArrayBuffer(file)
  })
}

function calculateMaxDrawdown(trades: ProcessedData["trades"]): number {
  let balance = 0
  let peak = 0
  let maxDrawdown = 0

  trades.forEach((trade) => {
    balance += trade.profit
    peak = Math.max(peak, balance)
    const drawdown = ((peak - balance) / peak) * 100
    maxDrawdown = Math.max(maxDrawdown, drawdown)
  })

  return maxDrawdown
}