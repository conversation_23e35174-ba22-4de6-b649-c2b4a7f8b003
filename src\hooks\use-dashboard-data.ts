"use client"

import { useQuery, useQueryClient } from "@tanstack/react-query"
import { useEffect } from "react"
import { useToast } from "@/components/ui/toast-provider"

// Function to fetch dashboard stats
export async function fetchDashboardStats(accountId: string | null) {
  if (!accountId) return null

  // Add cache-busting timestamp to prevent browser caching
  const timestamp = new Date().getTime()
  const response = await fetch(`/api/dashboard-stats?accountId=${accountId}&_t=${timestamp}`, {
    next: { tags: ['dashboard-stats'] },
    cache: 'no-store' // Ensure we don't use cached responses
  })

  if (!response.ok) {
    throw new Error('Failed to fetch dashboard stats')
  }

  return response.json()
}

// Function to fetch dashboard trades
export async function fetchDashboardTrades(accountId: string | null) {
  if (!accountId) return []

  // Add cache-busting timestamp to prevent browser caching
  const timestamp = new Date().getTime()
  const response = await fetch(`/api/dashboard-trades?accountId=${accountId}&_t=${timestamp}`, {
    next: { tags: ['dashboard-trades'] },
    cache: 'no-store' // Ensure we don't use cached responses
  })

  if (!response.ok) {
    throw new Error('Failed to fetch dashboard trades')
  }

  return response.json()
}

// Function to fetch dashboard metrics
export async function fetchDashboardMetrics(accountId: string | null, type: string = 'metrics') {
  if (!accountId) return []

  // Add cache-busting timestamp to prevent browser caching
  const timestamp = new Date().getTime()
  const response = await fetch(`/api/dashboard-metrics?accountId=${accountId}&type=${type}&_t=${timestamp}`, {
    next: { tags: ['dashboard-metrics'] },
    cache: 'no-store' // Ensure we don't use cached responses
  })

  if (!response.ok) {
    throw new Error('Failed to fetch dashboard metrics')
  }

  return response.json()
}

// Hook to fetch dashboard stats with account change detection
export function useDashboardStats(accountId: string | null, initialData: any = null) {
  const queryClient = useQueryClient()
  const { showErrorToast } = useToast()

  // Effect to invalidate cache when account changes
  useEffect(() => {
    if (accountId) {
      // Force refetch when account changes by invalidating the query
      queryClient.invalidateQueries({ queryKey: ['dashboard-stats', accountId] })
    }
  }, [accountId, queryClient])

  const query = useQuery({
    queryKey: ['dashboard-stats', accountId],
    queryFn: () => fetchDashboardStats(accountId),
    enabled: !!accountId,
    initialData: accountId ? undefined : initialData, // Only use initialData if no accountId
    staleTime: 0, // Always consider data stale to force refetch on account change
    refetchOnMount: true, // Always refetch when component mounts
    // Add error handling to prevent crashes if the API returns an error
    retry: 1, // Only retry once to avoid excessive retries on schema issues
  })

  // Handle errors using the query result
  if (query.error) {
    console.error('Error fetching dashboard stats:', query.error)
    showErrorToast(query.error, 'Failed to load dashboard statistics')
  }

  return query
}

// Hook to fetch dashboard trades with account change detection
export function useDashboardTrades(accountId: string | null, initialData: any[] = []) {
  const queryClient = useQueryClient()
  const { showErrorToast } = useToast()

  // Effect to invalidate cache when account changes
  useEffect(() => {
    if (accountId) {
      // Force refetch when account changes by invalidating the query
      queryClient.invalidateQueries({ queryKey: ['dashboard-trades', accountId] })
    }
  }, [accountId, queryClient])

  const query = useQuery({
    queryKey: ['dashboard-trades', accountId],
    queryFn: () => fetchDashboardTrades(accountId),
    enabled: !!accountId,
    initialData: accountId ? undefined : initialData, // Only use initialData if no accountId
    staleTime: 0, // Always consider data stale to force refetch on account change
    refetchOnMount: true, // Always refetch when component mounts
    // Add error handling to prevent crashes if the API returns an error
    retry: 1, // Only retry once to avoid excessive retries on schema issues
  })

  // Handle errors using the query result
  if (query.error) {
    console.error('Error fetching dashboard trades:', query.error)
    showErrorToast(query.error, 'Failed to load recent trades')
  }

  return query
}

// Hook to fetch dashboard metrics with account change detection
export function useDashboardMetrics(accountId: string | null, type: string = 'metrics', initialData: any[] = []) {
  const queryClient = useQueryClient()
  const { showErrorToast } = useToast()

  // Effect to invalidate cache when account changes
  useEffect(() => {
    if (accountId) {
      // Force refetch when account changes by invalidating the query
      queryClient.invalidateQueries({ queryKey: ['dashboard-metrics', accountId, type] })
    }
  }, [accountId, type, queryClient])

  const query = useQuery({
    queryKey: ['dashboard-metrics', accountId, type],
    queryFn: () => fetchDashboardMetrics(accountId, type),
    enabled: !!accountId,
    initialData: accountId ? undefined : initialData, // Only use initialData if no accountId
    staleTime: 0, // Always consider data stale to force refetch on account change
    refetchOnMount: true, // Always refetch when component mounts
    // Add error handling to prevent crashes if the API returns an error
    retry: 1, // Only retry once to avoid excessive retries on schema issues
  })

  // Handle errors using the query result
  if (query.error) {
    console.error(`Error fetching dashboard ${type}:`, query.error)
    showErrorToast(query.error, `Failed to load ${type} data`)
  }

  return query
}

// Hook to fetch dashboard goals with account change detection
export function useDashboardGoals(accountId: string | null, initialData: any[] = []) {
  const queryClient = useQueryClient()
  const { showErrorToast } = useToast()

  // Effect to invalidate cache when account changes
  useEffect(() => {
    if (accountId) {
      // Force refetch when account changes by invalidating the query
      queryClient.invalidateQueries({ queryKey: ['dashboard-metrics', accountId, 'goals'] })
    }
  }, [accountId, queryClient])

  const query = useQuery({
    queryKey: ['dashboard-metrics', accountId, 'goals'],
    queryFn: () => fetchDashboardMetrics(accountId, 'goals'),
    enabled: !!accountId,
    initialData: accountId ? undefined : initialData, // Only use initialData if no accountId
    staleTime: 0, // Always consider data stale to force refetch on account change
    refetchOnMount: true, // Always refetch when component mounts
    // Add error handling to prevent crashes if the API returns an error
    retry: 1, // Only retry once to avoid excessive retries on schema issues
  })

  // Handle errors using the query result
  if (query.error) {
    console.error('Error fetching dashboard goals:', query.error)
    showErrorToast(query.error, 'Failed to load goals data')
  }

  return query
}
