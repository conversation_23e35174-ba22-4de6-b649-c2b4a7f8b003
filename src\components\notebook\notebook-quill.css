/* Notebook Quill Editor Styles */

.quill-notebook-editor {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  border-radius: 0.375rem;
  overflow: hidden;
}

/* Make the editor take up the full height */
.quill-notebook-editor .quill {
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
}

/* Style the toolbar */
.quill-notebook-editor .ql-toolbar {
  border-top-left-radius: 0.375rem;
  border-top-right-radius: 0.375rem;
  background-color: var(--background);
  border-color: var(--border);
  position: sticky;
  top: 0;
  z-index: 30;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(8px);
  padding: 0.5rem;
  flex-shrink: 0;
}

/* Style the editor container */
.quill-notebook-editor .ql-container {
  flex: 1;
  overflow: hidden;
  font-size: 1rem;
  border-bottom-left-radius: 0.375rem;
  border-bottom-right-radius: 0.375rem;
  background-color: var(--background);
  border-color: var(--border);
  display: flex;
  flex-direction: column;
}

/* Style the editor content area */
.quill-notebook-editor .ql-editor {
  min-height: 200px;
  flex: 1;
  overflow-y: auto;
  color: var(--foreground);
  font-family: var(--font-sans);
  padding: 1rem;
  line-height: 1.6;
  /* Light mode: White background to match journal note cards */
  background-color: hsl(var(--card));
  border-radius: 0.375rem;
  border: 1px solid hsl(var(--border) / 0.3);
  transition: all 0.2s ease-in-out;
}

/* Focus state for general quill editor */
.quill-notebook-editor .ql-editor:focus-within {
  border-color: hsl(var(--ring));
  box-shadow: 0 0 0 2px hsl(var(--ring) / 0.2);
}

/* Style the placeholder */
.quill-notebook-editor .ql-editor.ql-blank::before {
  color: var(--muted-foreground);
  font-style: italic;
  opacity: 0.6;
}

/* Style toolbar buttons */
.quill-notebook-editor .ql-toolbar button {
  color: var(--foreground);
  width: 28px;
  height: 28px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.25rem;
  margin: 0 1px;
  transition: all 0.2s ease-in-out;
  background-color: transparent;
  border: 1px solid transparent;
}

.quill-notebook-editor .ql-toolbar button:hover {
  color: var(--primary);
  background-color: var(--accent);
  border-color: var(--border);
}

.quill-notebook-editor .ql-toolbar button.ql-active {
  color: var(--primary-foreground);
  background-color: var(--primary);
  border-color: var(--primary);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Style dropdown menus */
.quill-notebook-editor .ql-toolbar .ql-picker {
  color: var(--foreground);
  height: 28px;
}

.quill-notebook-editor .ql-toolbar .ql-picker-label {
  border-radius: 0.25rem;
  padding: 0 0.5rem;
  display: flex;
  align-items: center;
  height: 28px;
  transition: all 0.2s ease-in-out;
  background-color: transparent;
  border: 1px solid var(--border);
  color: var(--foreground);
}

.quill-notebook-editor .ql-toolbar .ql-picker-label:hover {
  background-color: var(--accent);
  border-color: var(--border);
  color: var(--primary);
}

.quill-notebook-editor .ql-toolbar .ql-picker.ql-expanded .ql-picker-label {
  background-color: var(--primary);
  border-color: var(--primary);
  color: var(--primary-foreground);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Style dropdown options */
.quill-notebook-editor .ql-toolbar .ql-picker-options {
  background-color: var(--popover);
  border: 1px solid var(--border);
  border-radius: 0.375rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  padding: 0.25rem;
  margin-top: 0.25rem;
  animation: dropdown-open 0.15s ease-out;
  transform-origin: top;
}

.quill-notebook-editor .ql-toolbar .ql-picker-item {
  padding: 0.375rem 0.75rem;
  border-radius: 0.25rem;
  color: var(--foreground);
  transition: all 0.15s ease-in-out;
  cursor: pointer;
}

.quill-notebook-editor .ql-toolbar .ql-picker-item:hover {
  background-color: var(--accent);
  color: var(--primary);
}

.quill-notebook-editor .ql-toolbar .ql-picker-item.ql-selected {
  background-color: var(--primary);
  color: var(--primary-foreground);
}

/* Dropdown animation */
@keyframes dropdown-open {
  from {
    opacity: 0;
    transform: scaleY(0.95);
  }
  to {
    opacity: 1;
    transform: scaleY(1);
  }
}



/* Dark mode adjustments */
.dark .quill-notebook-editor .ql-snow .ql-stroke {
  stroke: hsl(var(--foreground) / 0.8);
}

.dark .quill-notebook-editor .ql-snow .ql-fill {
  fill: hsl(var(--foreground) / 0.8);
}

/* Ensure SVG icons are visible in dark mode */
.dark .ql-snow .ql-picker.ql-header .ql-picker-label::before,
.dark .ql-snow .ql-picker.ql-font .ql-picker-label::before,
.dark .ql-snow .ql-picker.ql-size .ql-picker-label::before,
.dark .ql-snow .ql-picker.ql-header .ql-picker-item::before,
.dark .ql-snow .ql-picker.ql-font .ql-picker-item::before,
.dark .ql-snow .ql-picker.ql-size .ql-picker-item::before,
.dark .ql-snow .ql-color .ql-picker-label::before,
.dark .ql-snow .ql-align .ql-picker-label::before {
  color: hsl(var(--foreground) / 0.8);
}

.dark .quill-notebook-editor .ql-snow.ql-toolbar button:hover .ql-stroke,
.dark .quill-notebook-editor .ql-snow.ql-toolbar button.ql-active .ql-stroke {
  stroke: var(--primary);
}

.dark .quill-notebook-editor .ql-snow.ql-toolbar button:hover .ql-fill,
.dark .quill-notebook-editor .ql-snow.ql-toolbar button.ql-active .ql-fill {
  fill: var(--primary);
}

.dark .quill-notebook-editor .ql-toolbar {
  background-color: hsl(var(--card) / 0.8);
  border-color: hsl(var(--border) / 0.5);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.dark .quill-notebook-editor .ql-container {
  background-color: var(--card);
  border-color: var(--border);
}

/* Dark mode: Contrasting background for general quill editor content area */
.dark .quill-notebook-editor .ql-editor {
  background-color: hsl(var(--muted) / 0.3);
  border-color: hsl(var(--border) / 0.4);
  color: var(--foreground);
}

/* Dark mode focus state for general quill editor */
.dark .quill-notebook-editor .ql-editor:focus-within {
  background-color: hsl(var(--muted) / 0.4);
  border-color: hsl(var(--ring));
  box-shadow: 0 0 0 2px hsl(var(--ring) / 0.3);
}

/* Improve toolbar button visibility in dark mode */
.dark .quill-notebook-editor .ql-toolbar button {
  background-color: hsl(var(--secondary) / 0.2);
  border: 1px solid hsl(var(--border) / 0.3);
  color: hsl(var(--foreground) / 0.9);
}

.dark .quill-notebook-editor .ql-toolbar button:hover {
  background-color: hsl(var(--accent) / 0.8);
  border-color: hsl(var(--border) / 0.6);
  color: var(--primary);
}

.dark .quill-notebook-editor .ql-toolbar button.ql-active {
  background-color: var(--primary) !important;
  border-color: var(--primary) !important;
  color: var(--primary-foreground) !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3) !important;
  opacity: 1 !important;
}

/* Ensure SVG icons in active buttons are visible in dark mode */
.dark .quill-notebook-editor .ql-toolbar button.ql-active .ql-stroke {
  stroke: var(--primary-foreground) !important;
  stroke-width: 2 !important;
}

.dark .quill-notebook-editor .ql-toolbar button.ql-active .ql-fill {
  fill: var(--primary-foreground) !important;
}

/* Additional specificity for Quill Snow theme active buttons in dark mode */
.dark .ql-snow .ql-toolbar button.ql-active .ql-stroke {
  stroke: var(--primary-foreground) !important;
  stroke-width: 2 !important;
}

.dark .ql-snow .ql-toolbar button.ql-active .ql-fill {
  fill: var(--primary-foreground) !important;
}

/* Target specific Quill button types in dark mode */
.dark .ql-snow .ql-toolbar button.ql-active svg .ql-stroke {
  stroke: var(--primary-foreground) !important;
  stroke-width: 2 !important;
}

.dark .ql-snow .ql-toolbar button.ql-active svg .ql-fill {
  fill: var(--primary-foreground) !important;
}

/* Comprehensive dark mode active button icon visibility fixes */
.dark .ql-snow .ql-toolbar button.ql-active,
.dark .quill-notebook-editor .ql-toolbar button.ql-active,
.dark .notebook-editor-instance .ql-toolbar button.ql-active {
  /* Ensure the button itself has proper styling */
  background-color: var(--primary) !important;
  color: var(--primary-foreground) !important;
}

/* Target all possible SVG icon variations in active buttons */
.dark .ql-snow .ql-toolbar button.ql-active svg,
.dark .quill-notebook-editor .ql-toolbar button.ql-active svg,
.dark .notebook-editor-instance .ql-toolbar button.ql-active svg {
  color: var(--primary-foreground) !important;
}

.dark .ql-snow .ql-toolbar button.ql-active svg *,
.dark .quill-notebook-editor .ql-toolbar button.ql-active svg *,
.dark .notebook-editor-instance .ql-toolbar button.ql-active svg * {
  stroke: var(--primary-foreground) !important;
  fill: var(--primary-foreground) !important;
  stroke-width: 2 !important;
}

/* Specific targeting for Quill's icon classes */
.dark .ql-snow .ql-toolbar button.ql-active .ql-stroke,
.dark .quill-notebook-editor .ql-toolbar button.ql-active .ql-stroke,
.dark .notebook-editor-instance .ql-toolbar button.ql-active .ql-stroke {
  stroke: var(--primary-foreground) !important;
  stroke-width: 2 !important;
}

.dark .ql-snow .ql-toolbar button.ql-active .ql-fill,
.dark .quill-notebook-editor .ql-toolbar button.ql-active .ql-fill,
.dark .notebook-editor-instance .ql-toolbar button.ql-active .ql-fill {
  fill: var(--primary-foreground) !important;
}

/* Ultimate override for any stubborn Quill default styles in dark mode */
.dark .ql-snow.ql-toolbar button.ql-active .ql-stroke,
.dark .ql-snow.ql-toolbar button.ql-active .ql-fill,
.dark .ql-snow.ql-toolbar button.ql-active svg .ql-stroke,
.dark .ql-snow.ql-toolbar button.ql-active svg .ql-fill {
  stroke: white !important;
  fill: white !important;
  stroke-width: 2 !important;
  color: white !important;
}

/* Force white color for all SVG elements in active buttons - dark mode */
.dark .ql-toolbar button.ql-active svg,
.dark .ql-toolbar button.ql-active svg *,
.dark .ql-snow .ql-toolbar button.ql-active svg,
.dark .ql-snow .ql-toolbar button.ql-active svg * {
  stroke: white !important;
  fill: white !important;
  color: white !important;
  stroke-width: 2 !important;
}

/* Specific targeting for common Quill button types in dark mode */
.dark .ql-toolbar button.ql-active[title*="Bold"] svg,
.dark .ql-toolbar button.ql-active[title*="Italic"] svg,
.dark .ql-toolbar button.ql-active[title*="Underline"] svg,
.dark .ql-toolbar button.ql-active[title*="Strike"] svg,
.dark .ql-toolbar button.ql-active[title*="List"] svg,
.dark .ql-toolbar button.ql-active[title*="Header"] svg {
  stroke: white !important;
  fill: white !important;
  color: white !important;
}

/* Target by button content/class in dark mode */
.dark .ql-toolbar .ql-bold.ql-active svg,
.dark .ql-toolbar .ql-italic.ql-active svg,
.dark .ql-toolbar .ql-underline.ql-active svg,
.dark .ql-toolbar .ql-strike.ql-active svg,
.dark .ql-toolbar .ql-list.ql-active svg,
.dark .ql-toolbar .ql-header.ql-active svg {
  stroke: white !important;
  fill: white !important;
  color: white !important;
  stroke-width: 2 !important;
}

/* Nuclear option - force all active button SVG elements to be white in dark mode */
.dark .ql-toolbar button.ql-active svg path,
.dark .ql-toolbar button.ql-active svg circle,
.dark .ql-toolbar button.ql-active svg rect,
.dark .ql-toolbar button.ql-active svg line,
.dark .ql-toolbar button.ql-active svg polyline,
.dark .ql-toolbar button.ql-active svg polygon {
  stroke: white !important;
  fill: white !important;
  color: white !important;
}

/* Target all possible nested SVG elements */
.dark .ql-toolbar button.ql-active svg > *,
.dark .ql-toolbar button.ql-active svg > * > *,
.dark .ql-toolbar button.ql-active svg > * > * > * {
  stroke: white !important;
  fill: white !important;
  color: white !important;
}

/* Ensure visibility for both stroke and fill elements */
.dark .ql-toolbar button.ql-active .ql-stroke,
.dark .ql-toolbar button.ql-active .ql-fill {
  stroke: white !important;
  fill: white !important;
  color: white !important;
  opacity: 1 !important;
  visibility: visible !important;
}

/* Final comprehensive override - target any possible SVG in active buttons */
.dark [class*="ql-"] button.ql-active svg,
.dark [class*="ql-"] button.ql-active svg *,
.dark [class*="quill"] button.ql-active svg,
.dark [class*="quill"] button.ql-active svg *,
.dark [class*="notebook"] button.ql-active svg,
.dark [class*="notebook"] button.ql-active svg * {
  stroke: white !important;
  fill: white !important;
  color: white !important;
  stroke-width: 2 !important;
  opacity: 1 !important;
  visibility: visible !important;
}

/* Ensure active state is clearly visible with high contrast */
.dark .ql-snow .ql-toolbar button.ql-active:hover .ql-stroke,
.dark .ql-snow .ql-toolbar button.ql-active:hover .ql-fill,
.dark .quill-notebook-editor .ql-toolbar button.ql-active:hover .ql-stroke,
.dark .quill-notebook-editor .ql-toolbar button.ql-active:hover .ql-fill,
.dark .notebook-editor-instance .ql-toolbar button.ql-active:hover .ql-stroke,
.dark .notebook-editor-instance .ql-toolbar button.ql-active:hover .ql-fill {
  stroke: var(--primary-foreground) !important;
  fill: var(--primary-foreground) !important;
  stroke-width: 2 !important;
}

/* Enhanced hover states for non-active buttons in light mode */
.ql-snow .ql-toolbar button:not(.ql-active):hover,
.quill-notebook-editor .ql-toolbar button:not(.ql-active):hover,
.notebook-editor-instance .ql-toolbar button:not(.ql-active):hover {
  background-color: var(--accent) !important;
  border-radius: 4px !important;
  transition: all 0.2s ease-in-out !important;
}

.ql-snow .ql-toolbar button:not(.ql-active):hover .ql-stroke,
.ql-snow .ql-toolbar button:not(.ql-active):hover svg .ql-stroke,
.quill-notebook-editor .ql-toolbar button:not(.ql-active):hover .ql-stroke,
.quill-notebook-editor .ql-toolbar button:not(.ql-active):hover svg .ql-stroke,
.notebook-editor-instance .ql-toolbar button:not(.ql-active):hover .ql-stroke,
.notebook-editor-instance .ql-toolbar button:not(.ql-active):hover svg .ql-stroke {
  stroke: var(--accent-foreground) !important;
  stroke-width: 1.5 !important;
}

.ql-snow .ql-toolbar button:not(.ql-active):hover .ql-fill,
.ql-snow .ql-toolbar button:not(.ql-active):hover svg .ql-fill,
.quill-notebook-editor .ql-toolbar button:not(.ql-active):hover .ql-fill,
.quill-notebook-editor .ql-toolbar button:not(.ql-active):hover svg .ql-fill,
.notebook-editor-instance .ql-toolbar button:not(.ql-active):hover .ql-fill,
.notebook-editor-instance .ql-toolbar button:not(.ql-active):hover svg .ql-fill {
  fill: var(--accent-foreground) !important;
}

/* Enhanced hover states for non-active buttons in dark mode */
.dark .ql-snow .ql-toolbar button:not(.ql-active):hover,
.dark .quill-notebook-editor .ql-toolbar button:not(.ql-active):hover,
.dark .notebook-editor-instance .ql-toolbar button:not(.ql-active):hover {
  background-color: var(--accent) !important;
  border-radius: 4px !important;
  transition: all 0.2s ease-in-out !important;
}

.dark .ql-snow .ql-toolbar button:not(.ql-active):hover .ql-stroke,
.dark .ql-snow .ql-toolbar button:not(.ql-active):hover svg .ql-stroke,
.dark .quill-notebook-editor .ql-toolbar button:not(.ql-active):hover .ql-stroke,
.dark .quill-notebook-editor .ql-toolbar button:not(.ql-active):hover svg .ql-stroke,
.dark .notebook-editor-instance .ql-toolbar button:not(.ql-active):hover .ql-stroke,
.dark .notebook-editor-instance .ql-toolbar button:not(.ql-active):hover svg .ql-stroke {
  stroke: var(--accent-foreground) !important;
  stroke-width: 1.5 !important;
}

.dark .ql-snow .ql-toolbar button:not(.ql-active):hover .ql-fill,
.dark .ql-snow .ql-toolbar button:not(.ql-active):hover svg .ql-fill,
.dark .quill-notebook-editor .ql-toolbar button:not(.ql-active):hover .ql-fill,
.dark .quill-notebook-editor .ql-toolbar button:not(.ql-active):hover svg .ql-fill,
.dark .notebook-editor-instance .ql-toolbar button:not(.ql-active):hover .ql-fill,
.dark .notebook-editor-instance .ql-toolbar button:not(.ql-active):hover svg .ql-fill {
  fill: var(--accent-foreground) !important;
}

/* Focus states for accessibility */
.ql-snow .ql-toolbar button:not(.ql-active):focus,
.quill-notebook-editor .ql-toolbar button:not(.ql-active):focus,
.notebook-editor-instance .ql-toolbar button:not(.ql-active):focus {
  background-color: var(--accent) !important;
  border-radius: 4px !important;
  outline: 2px solid var(--ring) !important;
  outline-offset: 2px !important;
}

.dark .ql-snow .ql-toolbar button:not(.ql-active):focus,
.dark .quill-notebook-editor .ql-toolbar button:not(.ql-active):focus,
.dark .notebook-editor-instance .ql-toolbar button:not(.ql-active):focus {
  background-color: var(--accent) !important;
  border-radius: 4px !important;
  outline: 2px solid var(--ring) !important;
  outline-offset: 2px !important;
}

/* Pressed/active state for better feedback */
.ql-snow .ql-toolbar button:not(.ql-active):active,
.quill-notebook-editor .ql-toolbar button:not(.ql-active):active,
.notebook-editor-instance .ql-toolbar button:not(.ql-active):active {
  background-color: var(--muted) !important;
  transform: scale(0.95) !important;
  transition: all 0.1s ease-in-out !important;
}

.dark .ql-snow .ql-toolbar button:not(.ql-active):active,
.dark .quill-notebook-editor .ql-toolbar button:not(.ql-active):active,
.dark .notebook-editor-instance .ql-toolbar button:not(.ql-active):active {
  background-color: var(--muted) !important;
  transform: scale(0.95) !important;
  transition: all 0.1s ease-in-out !important;
}

/* Enhanced hover states for dropdown buttons */
.ql-snow .ql-toolbar .ql-picker:not(.ql-expanded):hover .ql-picker-label,
.quill-notebook-editor .ql-toolbar .ql-picker:not(.ql-expanded):hover .ql-picker-label,
.notebook-editor-instance .ql-toolbar .ql-picker:not(.ql-expanded):hover .ql-picker-label {
  background-color: var(--accent) !important;
  color: var(--accent-foreground) !important;
  border-radius: 4px !important;
  transition: all 0.2s ease-in-out !important;
}

.dark .ql-snow .ql-toolbar .ql-picker:not(.ql-expanded):hover .ql-picker-label,
.dark .quill-notebook-editor .ql-toolbar .ql-picker:not(.ql-expanded):hover .ql-picker-label,
.dark .notebook-editor-instance .ql-toolbar .ql-picker:not(.ql-expanded):hover .ql-picker-label {
  background-color: var(--accent) !important;
  color: var(--accent-foreground) !important;
  border-radius: 4px !important;
  transition: all 0.2s ease-in-out !important;
}

/* Hover states for dropdown arrows */
.ql-snow .ql-toolbar .ql-picker:not(.ql-expanded):hover .ql-picker-label::before,
.quill-notebook-editor .ql-toolbar .ql-picker:not(.ql-expanded):hover .ql-picker-label::before,
.notebook-editor-instance .ql-toolbar .ql-picker:not(.ql-expanded):hover .ql-picker-label::before {
  color: var(--accent-foreground) !important;
}

.dark .ql-snow .ql-toolbar .ql-picker:not(.ql-expanded):hover .ql-picker-label::before,
.dark .quill-notebook-editor .ql-toolbar .ql-picker:not(.ql-expanded):hover .ql-picker-label::before,
.dark .notebook-editor-instance .ql-toolbar .ql-picker:not(.ql-expanded):hover .ql-picker-label::before {
  color: var(--accent-foreground) !important;
}

/* Smooth transitions for all toolbar elements */
.ql-snow .ql-toolbar button,
.ql-snow .ql-toolbar .ql-picker-label,
.quill-notebook-editor .ql-toolbar button,
.quill-notebook-editor .ql-toolbar .ql-picker-label,
.notebook-editor-instance .ql-toolbar button,
.notebook-editor-instance .ql-toolbar .ql-picker-label {
  transition: all 0.2s ease-in-out !important;
}

/* Ensure SVG icons have smooth transitions */
.ql-snow .ql-toolbar button svg,
.ql-snow .ql-toolbar button .ql-stroke,
.ql-snow .ql-toolbar button .ql-fill,
.quill-notebook-editor .ql-toolbar button svg,
.quill-notebook-editor .ql-toolbar button .ql-stroke,
.quill-notebook-editor .ql-toolbar button .ql-fill,
.notebook-editor-instance .ql-toolbar button svg,
.notebook-editor-instance .ql-toolbar button .ql-stroke,
.notebook-editor-instance .ql-toolbar button .ql-fill {
  transition: all 0.2s ease-in-out !important;
}

/* Improve dropdown visibility in dark mode */
.dark .quill-notebook-editor .ql-toolbar .ql-picker-label {
  background-color: hsl(var(--secondary) / 0.2);
  border-color: hsl(var(--border) / 0.3);
  color: hsl(var(--foreground) / 0.9);
}

.dark .quill-notebook-editor .ql-toolbar .ql-picker-label:hover {
  background-color: hsl(var(--accent) / 0.8);
  border-color: hsl(var(--border) / 0.6);
  color: var(--primary);
}

.dark .quill-notebook-editor .ql-toolbar .ql-picker.ql-expanded .ql-picker-label {
  background-color: var(--primary);
  border-color: var(--primary);
  color: var(--primary-foreground);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.dark .quill-notebook-editor .ql-toolbar .ql-picker-options {
  background-color: hsl(var(--popover) / 0.98);
  border-color: hsl(var(--border));
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
}

.dark .quill-notebook-editor .ql-toolbar .ql-picker-item {
  color: hsl(var(--foreground) / 0.9);
}

.dark .quill-notebook-editor .ql-toolbar .ql-picker-item:hover {
  background-color: hsl(var(--accent) / 0.8);
  color: var(--primary);
}

.dark .quill-notebook-editor .ql-toolbar .ql-picker-item.ql-selected {
  background-color: var(--primary);
  color: var(--primary-foreground);
}

/* Ensure proper spacing for lists */
.quill-notebook-editor .ql-editor ul,
.quill-notebook-editor .ql-editor ol {
  padding-left: 1.5em;
  margin-bottom: 0.75rem;
}

/* Style blockquotes */
.quill-notebook-editor .ql-editor blockquote {
  border-left: 4px solid var(--border);
  padding-left: 1em;
  margin: 0.75rem 0;
  color: var(--muted-foreground);
}

/* Style code blocks */
.quill-notebook-editor .ql-editor pre.ql-syntax {
  background-color: var(--muted);
  color: var(--foreground);
  border-radius: 0.25rem;
  padding: 0.75em;
  margin: 0.75rem 0;
  overflow: auto;
  font-family: var(--font-mono);
}

/* Notebook-specific styles */
.notebook-quill-editor .ql-toolbar {
  border-top: none;
  border-left: none;
  border-right: none;
  padding: 8px 12px;
  background-color: var(--background);
}

.notebook-quill-editor .ql-container {
  border-left: none;
  border-right: none;
  border-bottom: none;
}

/* Ensure the editor fits in the notebook layout */
.notebook-quill-editor {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* Specific styles for the notebook editor instance */
.notebook-editor-instance {
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
}

.notebook-editor-instance .ql-toolbar {
  border-top: none;
  border-left: none;
  border-right: none;
  border-bottom: 1px solid var(--border);
  background-color: var(--card);
  position: sticky;
  top: 0;
  z-index: 30;
  padding: 8px 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(8px);
  flex-shrink: 0;
}

.notebook-editor-instance .ql-container {
  flex: 1;
  border: none;
  background-color: transparent;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.notebook-editor-instance .ql-editor {
  padding: 1rem;
  min-height: 200px;
  flex: 1;
  overflow-y: auto;
  /* Light mode: White background to match journal note cards */
  background-color: hsl(var(--card));
  border-radius: 0.375rem;
  border: 1px solid hsl(var(--border) / 0.3);
  transition: all 0.2s ease-in-out;
}

/* Focus state for better visual feedback */
.notebook-editor-instance .ql-editor:focus-within {
  border-color: hsl(var(--ring));
  box-shadow: 0 0 0 2px hsl(var(--ring) / 0.2);
}

/* Dark mode specific styles for notebook editor instance */
.dark .notebook-editor-instance .ql-toolbar {
  background-color: hsl(var(--card) / 0.9);
  border-bottom: 1px solid hsl(var(--border) / 0.7);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

/* Dark mode: Contrasting background for editor content area */
.dark .notebook-editor-instance .ql-editor {
  background-color: hsl(var(--muted) / 0.3);
  border-color: hsl(var(--border) / 0.4);
  color: var(--foreground);
}

/* Dark mode focus state */
.dark .notebook-editor-instance .ql-editor:focus-within {
  background-color: hsl(var(--muted) / 0.4);
  border-color: hsl(var(--ring));
  box-shadow: 0 0 0 2px hsl(var(--ring) / 0.3);
}

/* Improve toolbar button visibility in notebook editor instance */
.dark .notebook-editor-instance .ql-toolbar button {
  background-color: hsl(var(--secondary) / 0.2);
  border: 1px solid hsl(var(--border) / 0.3);
  color: hsl(var(--foreground) / 0.9);
  transition: all 0.2s ease-in-out;
}

.dark .notebook-editor-instance .ql-toolbar button:hover {
  background-color: hsl(var(--accent) / 0.8);
  border-color: hsl(var(--border) / 0.6);
  color: var(--primary);
}

.dark .notebook-editor-instance .ql-toolbar button.ql-active {
  background-color: var(--primary) !important;
  border-color: var(--primary) !important;
  color: var(--primary-foreground) !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3) !important;
  opacity: 1 !important;
}

/* Ensure SVG icons in active buttons are visible in notebook editor instance */
.dark .notebook-editor-instance .ql-toolbar button.ql-active .ql-stroke {
  stroke: var(--primary-foreground) !important;
  stroke-width: 2 !important;
}

.dark .notebook-editor-instance .ql-toolbar button.ql-active .ql-fill {
  fill: var(--primary-foreground) !important;
}

/* Additional specificity for notebook editor instance in dark mode */
.dark .notebook-editor-instance .ql-snow .ql-toolbar button.ql-active .ql-stroke {
  stroke: var(--primary-foreground) !important;
  stroke-width: 2 !important;
}

.dark .notebook-editor-instance .ql-snow .ql-toolbar button.ql-active .ql-fill {
  fill: var(--primary-foreground) !important;
}

/* Target specific Quill button types in notebook editor instance dark mode */
.dark .notebook-editor-instance .ql-snow .ql-toolbar button.ql-active svg .ql-stroke {
  stroke: var(--primary-foreground) !important;
  stroke-width: 2 !important;
}

.dark .notebook-editor-instance .ql-snow .ql-toolbar button.ql-active svg .ql-fill {
  fill: white !important;
}

/* Apply the same nuclear option to notebook editor instance */
.dark .notebook-editor-instance .ql-toolbar button.ql-active svg,
.dark .notebook-editor-instance .ql-toolbar button.ql-active svg *,
.dark .notebook-editor-instance .ql-toolbar button.ql-active svg path,
.dark .notebook-editor-instance .ql-toolbar button.ql-active svg circle,
.dark .notebook-editor-instance .ql-toolbar button.ql-active svg rect,
.dark .notebook-editor-instance .ql-toolbar button.ql-active svg line,
.dark .notebook-editor-instance .ql-toolbar button.ql-active svg polyline,
.dark .notebook-editor-instance .ql-toolbar button.ql-active svg polygon {
  stroke: white !important;
  fill: white !important;
  color: white !important;
  stroke-width: 2 !important;
}

/* Ensure visibility for notebook editor instance */
.dark .notebook-editor-instance .ql-toolbar button.ql-active .ql-stroke,
.dark .notebook-editor-instance .ql-toolbar button.ql-active .ql-fill {
  stroke: white !important;
  fill: white !important;
  color: white !important;
  opacity: 1 !important;
  visibility: visible !important;
}

/* Improve dropdown visibility in notebook editor instance */
.dark .notebook-editor-instance .ql-toolbar .ql-picker-label {
  background-color: hsl(var(--secondary) / 0.2);
  border-color: hsl(var(--border) / 0.3);
  color: hsl(var(--foreground) / 0.9);
  transition: all 0.2s ease-in-out;
}

.dark .notebook-editor-instance .ql-toolbar .ql-picker-label:hover {
  background-color: hsl(var(--accent) / 0.8);
  border-color: hsl(var(--border) / 0.6);
  color: var(--primary);
}

.dark .notebook-editor-instance .ql-toolbar .ql-picker.ql-expanded .ql-picker-label {
  background-color: var(--primary);
  border-color: var(--primary);
  color: var(--primary-foreground);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.dark .notebook-editor-instance .ql-toolbar .ql-picker-options {
  background-color: hsl(var(--popover) / 0.98);
  border-color: hsl(var(--border));
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
  animation: dropdown-open 0.15s ease-out;
}

.dark .notebook-editor-instance .ql-toolbar .ql-picker-item {
  color: hsl(var(--foreground) / 0.9);
  transition: all 0.15s ease-in-out;
}

.dark .notebook-editor-instance .ql-toolbar .ql-picker-item:hover {
  background-color: hsl(var(--accent) / 0.8);
  color: var(--primary);
}

.dark .notebook-editor-instance .ql-toolbar .ql-picker-item.ql-selected {
  background-color: var(--primary);
  color: var(--primary-foreground);
}

/* View mode styles */
.notebook-editor-instance[contenteditable="false"] .ql-editor {
  cursor: default;
}

/* Make headings look better */
.quill-notebook-editor .ql-editor h1 {
  font-size: 1.75rem;
  font-weight: 600;
  margin-top: 1.5rem;
  margin-bottom: 0.75rem;
  color: var(--foreground);
}

.quill-notebook-editor .ql-editor h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-top: 1.25rem;
  margin-bottom: 0.75rem;
  color: var(--foreground);
}

.quill-notebook-editor .ql-editor h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-top: 1rem;
  margin-bottom: 0.5rem;
  color: var(--foreground);
}

/* Improve paragraph spacing */
.quill-notebook-editor .ql-editor p {
  margin-bottom: 0.75rem;
  line-height: 1.6;
}

/* Fix link styling */
.quill-notebook-editor .ql-editor a {
  color: var(--primary);
  text-decoration: underline;
  transition: color 0.2s ease;
}

.quill-notebook-editor .ql-editor a:hover {
  color: var(--primary-foreground);
}
