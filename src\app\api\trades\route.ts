import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { createServerClient } from '@supabase/ssr';
import type { Database } from '@/types/supabase';
import { Trade } from '@/types/trade';

// GET handler to fetch trades
export async function GET(request: Request) {
  try {
    // Get URL parameters
    const url = new URL(request.url);
    const tradeId = url.searchParams.get('id');
    const strategyId = url.searchParams.get('strategyId');
    const accountId = url.searchParams.get('accountId');

    // Get cookies for server-side Supabase client
    const cookieStore = await cookies();
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
          set(_name: string, _value: string, _options: any) {
            // API routes can't set cookies directly
          },
          remove(_name: string, _options: any) {
            // API routes can't remove cookies directly
          }
        },
      }
    );

    // Get authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const userId = user.id;

    // If tradeId is provided, fetch a single trade
    if (tradeId) {
      const { data, error } = await supabase
        .from('trades')
        .select('*')
        .eq('id', tradeId)
        .eq('user_id', userId)
        .single();

      if (error) {
        return NextResponse.json({ error: error.message }, { status: 500 });
      }

      return NextResponse.json(data);
    }

    // Build query for trades
    let query = supabase
      .from('trades')
      .select('*')
      .eq('user_id', userId);

    // Filter by strategy if specified
    if (strategyId) {
      query = query.eq('strategy_id', strategyId);
    }

    // Filter by account if specified
    if (accountId) {
      query = query.eq('account_id', accountId);
    }

    // Execute query
    const { data, error } = await query.order('time_close', { ascending: false });

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json(data || []);
  } catch (error) {
    console.error('Error fetching trades:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// PATCH handler to update a trade
export async function PATCH(request: Request) {
  try {
    const body = await request.json();

    // Get cookies for server-side Supabase client
    const cookieStore = await cookies();
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
          set(_name: string, _value: string, _options: any) {
            // API routes can't set cookies directly
          },
          remove(_name: string, _options: any) {
            // API routes can't remove cookies directly
          }
        },
      }
    );

    // Get authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const userId = user.id;

    // Validate required fields
    if (!body.id) {
      return NextResponse.json({ error: 'Trade ID is required' }, { status: 400 });
    }

    // Update trade
    const { data, error } = await supabase
      .from('trades')
      .update({
        strategy_id: body.strategy_id,
        updated_at: new Date().toISOString()
      })
      .eq('id', body.id)
      .eq('user_id', userId)
      .select()
      .single();

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error('Error updating trade:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
