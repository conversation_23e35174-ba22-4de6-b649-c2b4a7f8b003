import { serve } from 'https://deno.land/std@0.177.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.39.3';
import * as XLSX from 'https://esm.sh/xlsx@0.18.5';

// Maximum file size (9MB)
const MAX_FILE_SIZE = 9 * 1024 * 1024; // 9MB in bytes

interface AccountInfo {
  name: string;
  account: string;
  company: string;
  date: string;
}

interface Trade {
  position_id: number;
  time_open: string;
  time_close: string;
  symbol: string;
  type: string;
  volume: number;
  price_open: number;
  price_close: number;
  commission: number;
  swap: number;
  profit: number;
  sl?: number | null;
  tp?: number | null;
}

interface TradingSummary {
  total_trades: number;
  profit_trades: string;
  loss_trades: string;
  total_net_profit: number;
  gross_profit: number;
  gross_loss: number;
  profit_factor: number;
  expected_payoff: number;
  recovery_factor: number;
  sharpe_ratio: number;
  balance_drawdown_absolute: number;
  balance_drawdown_maximal: string;
  balance_drawdown_relative: string;
  short_trades_won: string;
  long_trades_won: string;
  largest_profit_trade: number;
  largest_loss_trade: number;
  average_profit_trade: number;
  average_loss_trade: number;
  maximum_consecutive_wins: string;
  maximum_consecutive_losses: string;
  maximal_consecutive_profit: string;
  maximal_consecutive_loss: string;
  average_consecutive_wins: number;
  average_consecutive_losses: number;
}

interface ProcessedData {
  account: AccountInfo;
  trades: Trade[];
  summary: Partial<TradingSummary>;
}

serve(async (req) => {
  // CORS headers
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  };

  // Handle OPTIONS request for CORS
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers });
  }

  try {
    // Check if request is multipart/form-data
    const contentType = req.headers.get('content-type') || '';
    if (!contentType.includes('multipart/form-data')) {
      return new Response(
        JSON.stringify({ error: 'Request must be multipart/form-data' }),
        { status: 400, headers: { ...headers, 'Content-Type': 'application/json' } }
      );
    }

    // Get the form data
    const formData = await req.formData();
    const file = formData.get('file') as File;
    
    if (!file) {
      return new Response(
        JSON.stringify({ error: 'No file provided' }),
        { status: 400, headers: { ...headers, 'Content-Type': 'application/json' } }
      );
    }

    // Check file size
    if (file.size > MAX_FILE_SIZE) {
      return new Response(
        JSON.stringify({ error: 'File size exceeds the 9MB limit' }),
        { status: 400, headers: { ...headers, 'Content-Type': 'application/json' } }
      );
    }

    // Check file type
    const fileType = file.type;
    const validTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
      'application/vnd.ms-excel', // .xls
      'text/csv', // .csv
    ];

    if (!validTypes.includes(fileType)) {
      return new Response(
        JSON.stringify({ error: 'Invalid file type. Only Excel (.xlsx, .xls) and CSV files are supported' }),
        { status: 400, headers: { ...headers, 'Content-Type': 'application/json' } }
      );
    }

    // Process the file
    const arrayBuffer = await file.arrayBuffer();
    const data = new Uint8Array(arrayBuffer);
    
    // Parse the Excel file
    const processedData = await processExcelFile(data);

    // Return the processed data
    return new Response(
      JSON.stringify({ data: processedData }),
      { status: 200, headers: { ...headers, 'Content-Type': 'application/json' } }
    );
  } catch (error) {
    console.error('Error processing file:', error);
    
    return new Response(
      JSON.stringify({ 
        error: 'Failed to process file', 
        details: error instanceof Error ? error.message : 'Unknown error' 
      }),
      { status: 500, headers: { ...headers, 'Content-Type': 'application/json' } }
    );
  }
});

async function processExcelFile(data: Uint8Array): Promise<ProcessedData> {
  try {
    const workbook = XLSX.read(data, { type: 'array' });
    
    // Extract account information
    const accountInfo = extractAccountInfo(workbook);
    
    // Extract trades
    const trades = extractTrades(workbook);
    
    // Extract summary
    const summary = extractSummary(workbook, trades);
    
    return {
      account: accountInfo,
      trades,
      summary,
    };
  } catch (error) {
    console.error('Error in processExcelFile:', error);
    throw new Error('Failed to process Excel file: ' + (error instanceof Error ? error.message : 'Unknown error'));
  }
}

function extractAccountInfo(workbook: XLSX.WorkBook): AccountInfo {
  try {
    // Get the first sheet
    const firstSheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[firstSheetName];
    
    // Convert to JSON to easily access the data
    const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 }) as any[][];
    
    let name = 'Trading Account';
    let account = 'Demo Account';
    let company = 'MetaTrader 5';
    let date = new Date().toISOString();
    
    // Look for account information in the first few rows
    for (let i = 0; i < Math.min(10, jsonData.length); i++) {
      const row = jsonData[i];
      if (!row || row.length === 0) continue;
      
      const firstCell = String(row[0] || '').trim();
      
      if (firstCell === 'Name:' && row.length >= 4) {
        name = String(row[3] || '').trim();
      } else if (firstCell === 'Account:' && row.length >= 4) {
        account = String(row[3] || '').trim();
      } else if (firstCell === 'Company:' && row.length >= 4) {
        company = String(row[3] || '').trim();
      } else if (firstCell === 'Date:' && row.length >= 4) {
        date = String(row[3] || '').trim();
      }
    }
    
    return { name, account, company, date };
  } catch (error) {
    console.error('Error extracting account info:', error);
    return {
      name: 'Trading Account',
      account: 'Demo Account',
      company: 'MetaTrader 5',
      date: new Date().toISOString(),
    };
  }
}

function extractTrades(workbook: XLSX.WorkBook): Trade[] {
  try {
    // Find the positions section
    const firstSheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[firstSheetName];
    
    // Convert to JSON with headers
    const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 }) as any[][];
    
    let positionsStartRow = -1;
    let positionsHeaderRow = -1;
    
    // Find the "Positions" section and its header row
    for (let i = 0; i < jsonData.length; i++) {
      const row = jsonData[i];
      if (!row || row.length === 0) continue;
      
      const firstCell = String(row[0] || '').trim();
      
      if (firstCell === 'Positions') {
        positionsStartRow = i;
      } else if (positionsStartRow !== -1 && firstCell === 'Time') {
        positionsHeaderRow = i;
        break;
      }
    }
    
    if (positionsStartRow === -1 || positionsHeaderRow === -1) {
      throw new Error('Could not find Positions section in the Excel file');
    }
    
    // Extract header columns
    const headers = jsonData[positionsHeaderRow].map((h: any) => String(h || '').trim());
    
    // Find column indices
    const timeIndex = headers.indexOf('Time');
    const positionIndex = headers.indexOf('Position');
    const symbolIndex = headers.indexOf('Symbol');
    const typeIndex = headers.indexOf('Type');
    const volumeIndex = headers.indexOf('Volume');
    const priceOpenIndex = headers.indexOf('Price');
    const slIndex = headers.indexOf('S / L');
    const tpIndex = headers.indexOf('T / P');
    const timeCloseIndex = headers.findIndex((h: string) => h === 'Time' && headers.indexOf(h) !== timeIndex);
    const priceCloseIndex = headers.findIndex((h: string) => h === 'Price' && headers.indexOf(h) !== priceOpenIndex);
    const commissionIndex = headers.indexOf('Commission');
    const swapIndex = headers.indexOf('Swap');
    const profitIndex = headers.indexOf('Profit');
    
    // Validate required columns
    if (timeIndex === -1 || positionIndex === -1 || symbolIndex === -1 || typeIndex === -1 || 
        volumeIndex === -1 || priceOpenIndex === -1 || timeCloseIndex === -1 || 
        priceCloseIndex === -1 || profitIndex === -1) {
      throw new Error('Missing required columns in the Positions section');
    }
    
    const trades: Trade[] = [];
    
    // Extract trades data
    for (let i = positionsHeaderRow + 1; i < jsonData.length; i++) {
      const row = jsonData[i];
      if (!row || row.length === 0) continue;
      
      // Check if we've reached the end of the Positions section
      if (String(row[0] || '').trim() === 'Orders') break;
      
      // Skip empty rows
      if (!row[timeIndex] || !row[positionIndex]) continue;
      
      const trade: Trade = {
        position_id: parseInt(String(row[positionIndex] || '0'), 10),
        time_open: String(row[timeIndex] || ''),
        time_close: String(row[timeCloseIndex] || ''),
        symbol: String(row[symbolIndex] || ''),
        type: String(row[typeIndex] || '').toLowerCase(),
        volume: parseFloat(String(row[volumeIndex] || '0')),
        price_open: parseFloat(String(row[priceOpenIndex] || '0').replace(/\s+/g, '')),
        price_close: parseFloat(String(row[priceCloseIndex] || '0').replace(/\s+/g, '')),
        commission: commissionIndex !== -1 ? parseFloat(String(row[commissionIndex] || '0')) : 0,
        swap: swapIndex !== -1 ? parseFloat(String(row[swapIndex] || '0')) : 0,
        profit: parseFloat(String(row[profitIndex] || '0').replace(/\s+/g, '')),
      };
      
      // Add optional fields if they exist
      if (slIndex !== -1 && row[slIndex]) {
        trade.sl = parseFloat(String(row[slIndex]).replace(/\s+/g, ''));
      }
      
      if (tpIndex !== -1 && row[tpIndex]) {
        trade.tp = parseFloat(String(row[tpIndex]).replace(/\s+/g, ''));
      }
      
      trades.push(trade);
    }
    
    return trades;
  } catch (error) {
    console.error('Error extracting trades:', error);
    throw new Error('Failed to extract trades: ' + (error instanceof Error ? error.message : 'Unknown error'));
  }
}

function extractSummary(workbook: XLSX.WorkBook, trades: Trade[]): Partial<TradingSummary> {
  try {
    // Find the Results section
    const firstSheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[firstSheetName];
    
    // Convert to JSON with headers
    const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 }) as any[][];
    
    let resultsStartRow = -1;
    
    // Find the "Results" section
    for (let i = 0; i < jsonData.length; i++) {
      const row = jsonData[i];
      if (!row || row.length === 0) continue;
      
      const firstCell = String(row[0] || '').trim();
      
      if (firstCell === 'Results') {
        resultsStartRow = i;
        break;
      }
    }
    
    if (resultsStartRow === -1) {
      // If we can't find the Results section, calculate basic summary from trades
      return calculateBasicSummary(trades);
    }
    
    const summary: Partial<TradingSummary> = {};
    
    // Extract summary data
    for (let i = resultsStartRow + 1; i < jsonData.length; i++) {
      const row = jsonData[i];
      if (!row || row.length === 0) continue;
      
      const label = String(row[0] || '').trim();
      
      if (label === 'Total Net Profit:' && row.length >= 4) {
        summary.total_net_profit = parseFloat(String(row[3] || '0').replace(/\s+/g, ''));
        
        if (row.length >= 7) {
          summary.gross_profit = parseFloat(String(row[6] || '0').replace(/\s+/g, ''));
        }
        
        if (row.length >= 12) {
          summary.gross_loss = parseFloat(String(row[11] || '0').replace(/\s+/g, ''));
        }
      } else if (label === 'Profit Factor:' && row.length >= 4) {
        summary.profit_factor = parseFloat(String(row[3] || '0'));
        
        if (row.length >= 7) {
          summary.expected_payoff = parseFloat(String(row[6] || '0').replace(/\s+/g, ''));
        }
      } else if (label === 'Recovery Factor:' && row.length >= 4) {
        summary.recovery_factor = parseFloat(String(row[3] || '0'));
        
        if (row.length >= 7) {
          summary.sharpe_ratio = parseFloat(String(row[6] || '0'));
        }
      } else if (label === 'Balance Drawdown Absolute:' && row.length >= 4) {
        summary.balance_drawdown_absolute = parseFloat(String(row[3] || '0').replace(/\s+/g, ''));
        
        if (row.length >= 7) {
          summary.balance_drawdown_maximal = String(row[6] || '0');
        }
        
        if (row.length >= 12) {
          summary.balance_drawdown_relative = String(row[11] || '0');
        }
      } else if (label === 'Total Trades:' && row.length >= 4) {
        summary.total_trades = parseInt(String(row[3] || '0'), 10);
        
        if (row.length >= 7) {
          summary.short_trades_won = String(row[6] || '0');
        }
        
        if (row.length >= 12) {
          summary.long_trades_won = String(row[11] || '0');
        }
      } else if (label === '' && row.length >= 7 && String(row[4] || '').includes('Profit Trades')) {
        summary.profit_trades = String(row[6] || '0');
        
        if (row.length >= 12) {
          summary.loss_trades = String(row[11] || '0');
        }
      } else if (label === '' && row.length >= 7 && String(row[4] || '').includes('Largest profit trade')) {
        summary.largest_profit_trade = parseFloat(String(row[6] || '0'));
        
        if (row.length >= 12) {
          summary.largest_loss_trade = parseFloat(String(row[11] || '0'));
        }
      } else if (label === '' && row.length >= 7 && String(row[4] || '').includes('Average profit trade')) {
        summary.average_profit_trade = parseFloat(String(row[6] || '0'));
        
        if (row.length >= 12) {
          summary.average_loss_trade = parseFloat(String(row[11] || '0'));
        }
      } else if (label === '' && row.length >= 7 && String(row[4] || '').includes('Maximum consecutive wins')) {
        summary.maximum_consecutive_wins = String(row[6] || '0');
        
        if (row.length >= 12) {
          summary.maximum_consecutive_losses = String(row[11] || '0');
        }
      } else if (label === '' && row.length >= 7 && String(row[4] || '').includes('Maximal consecutive profit')) {
        summary.maximal_consecutive_profit = String(row[6] || '0');
        
        if (row.length >= 12) {
          summary.maximal_consecutive_loss = String(row[11] || '0');
        }
      }
    }
    
    // If we couldn't extract some essential fields, calculate them from trades
    if (!summary.total_trades || !summary.profit_trades || !summary.loss_trades || !summary.total_net_profit) {
      const basicSummary = calculateBasicSummary(trades);
      
      summary.total_trades = summary.total_trades || basicSummary.total_trades;
      summary.profit_trades = summary.profit_trades || basicSummary.profit_trades;
      summary.loss_trades = summary.loss_trades || basicSummary.loss_trades;
      summary.total_net_profit = summary.total_net_profit || basicSummary.total_net_profit;
      summary.balance_drawdown_maximal = summary.balance_drawdown_maximal || basicSummary.balance_drawdown_maximal;
    }
    
    return summary;
  } catch (error) {
    console.error('Error extracting summary:', error);
    // Fallback to calculating basic summary from trades
    return calculateBasicSummary(trades);
  }
}

function calculateBasicSummary(trades: Trade[]): Partial<TradingSummary> {
  const totalTrades = trades.length;
  const profitTrades = trades.filter(trade => trade.profit > 0);
  const lossTrades = trades.filter(trade => trade.profit < 0);
  const totalProfit = trades.reduce((sum, trade) => sum + trade.profit, 0);
  
  // Calculate max drawdown
  let balance = 0;
  let peak = 0;
  let maxDrawdown = 0;
  
  trades.forEach(trade => {
    balance += trade.profit;
    peak = Math.max(peak, balance);
    const drawdown = peak > 0 ? ((peak - balance) / peak) * 100 : 0;
    maxDrawdown = Math.max(maxDrawdown, drawdown);
  });
  
  return {
    total_trades: totalTrades,
    profit_trades: totalTrades > 0 ? ((profitTrades.length / totalTrades) * 100).toFixed(2) + '%' : '0.00%',
    loss_trades: totalTrades > 0 ? ((lossTrades.length / totalTrades) * 100).toFixed(2) + '%' : '0.00%',
    total_net_profit: totalProfit,
    balance_drawdown_maximal: maxDrawdown.toFixed(2) + '%',
  };
}
