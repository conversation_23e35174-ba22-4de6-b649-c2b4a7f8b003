"use client"

import { useState, useEffect, useMemo } from "react"
import { ImageOff, ZoomIn } from "lucide-react"
import { cn } from "@/lib/utils"
import { ImageLightbox } from "@/components/ui/image-lightbox"
import { Button } from "@/components/ui/button"

interface ImageDisplayProps {
  src: string
  alt: string
  className?: string
  aspectRatio?: "square" | "video" | "auto"
  lightboxEnabled?: boolean
  lightboxGroup?: string[]
  lightboxIndex?: number
}

export function ImageDisplay({
  src,
  alt,
  className = "",
  aspectRatio = "video",
  lightboxEnabled = true,
  lightboxGroup,
  lightboxIndex = 0
}: ImageDisplayProps) {
  const [error, setError] = useState(false)
  const [loading, setLoading] = useState(true)
  const [lightboxOpen, setLightboxOpen] = useState(false)
  const [finalSrc, setFinalSrc] = useState<string | null>(null)

  // Extract the filename from the URL
  const getFilename = (url: string): string => {
    try {
      // Check if it's already just a filename (no slashes or protocol)
      if (!url.includes('/') && !url.includes('://')) {
        console.log('URL is already a filename:', url);
        return url;
      }

      // Remove query parameters
      const cleanUrl = url.split('?')[0];
      // Get the last part of the path
      const filename = cleanUrl.split('/').pop() || '';
      console.log('Extracted filename from URL:', filename);
      return filename;
    } catch (e) {
      console.error('Error extracting filename:', e);
      return '';
    }
  };

  // Create a proxy URL that will serve the image through our API
  const createProxyUrl = (url: string): string => {
    const filename = getFilename(url);
    if (!filename) {
      console.error('Failed to extract filename from:', url);
      return url;
    }

    // Use our server-side API to proxy the image
    const proxyUrl = `/api/image-proxy?filename=${encodeURIComponent(filename)}`;
    console.log('Created proxy URL:', proxyUrl, 'from original:', url);
    return proxyUrl;
  };

  // Try to load an image with the given URL
  const tryLoadImage = (url: string): Promise<string> => {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => resolve(url);
      img.onerror = () => reject(new Error(`Failed to load image: ${url}`));
      img.src = url;
    });
  };

  // Effect to handle image loading
  useEffect(() => {
    if (!src) {
      setError(true);
      setLoading(false);
      return;
    }

    const loadImage = async () => {
      setLoading(true);
      setError(false);

      // Create a proxy URL that will serve the image through our API
      const proxyUrl = createProxyUrl(src);
      console.log('Using proxy URL:', proxyUrl);

      try {
        // Add a cache-busting parameter to force a fresh load
        const cacheBustUrl = `${proxyUrl}&_t=${Date.now()}`;
        console.log('Using cache-busting URL:', cacheBustUrl);

        // Try loading the image through our proxy
        await tryLoadImage(cacheBustUrl);
        setFinalSrc(cacheBustUrl);
        setLoading(false);
      } catch (e) {
        console.error('Failed to load image through proxy:', e);

        // Try one more time with a delay
        setTimeout(async () => {
          try {
            const retryUrl = `${proxyUrl}&_retry=true&_t=${Date.now()}`;
            console.log('Retrying with URL:', retryUrl);
            await tryLoadImage(retryUrl);
            setFinalSrc(retryUrl);
            setLoading(false);
          } catch (retryError) {
            console.error('Retry failed:', retryError);
            setError(true);
            setLoading(false);
          }
        }, 1000);
      }
    };

    loadImage();
  }, [src]);

  // Process lightbox images to use the proxy
  const processedLightboxGroup = useMemo(() => {
    if (!lightboxGroup) {
      return finalSrc ? [finalSrc] : [];
    }

    // Convert all lightbox images to use the proxy
    return lightboxGroup.map(createProxyUrl);
  }, [lightboxGroup, finalSrc]);

  // Skip old format URLs
  if (src.includes('/setup_')) {
    return (
      <div className={cn(
        "flex items-center justify-center bg-muted/30 rounded-lg",
        className,
        aspectRatio === "square" ? "aspect-square" :
        aspectRatio === "video" ? "aspect-video" : ""
      )}>
        <div className="flex flex-col items-center text-muted-foreground">
          <ImageOff className="h-8 w-8 mb-2" />
          <span className="text-sm">Image not available</span>
        </div>
      </div>
    )
  }

  // Show loading state
  if (loading) {
    return (
      <div className={cn(
        "flex items-center justify-center bg-muted/30 rounded-lg",
        className,
        aspectRatio === "square" ? "aspect-square" :
        aspectRatio === "video" ? "aspect-video" : ""
      )}>
        <div className="flex flex-col items-center text-muted-foreground">
          <div className="h-8 w-8 mb-2 rounded-full border-2 border-purple-500 border-t-transparent animate-spin" />
          <span className="text-sm">Loading image...</span>
        </div>
      </div>
    )
  }

  // Show error state
  if (error || !finalSrc) {
    return (
      <div className={cn(
        "flex items-center justify-center bg-muted/30 rounded-lg",
        className,
        aspectRatio === "square" ? "aspect-square" :
        aspectRatio === "video" ? "aspect-video" : ""
      )}>
        <div className="flex flex-col items-center text-muted-foreground">
          <ImageOff className="h-8 w-8 mb-2" />
          <span className="text-sm">Failed to load image</span>
          {/* Add a retry button */}
          <Button
            variant="outline"
            size="sm"
            className="mt-2"
            onClick={() => {
              // Force a reload of the image through our proxy
              const timestamp = Date.now();
              const filename = getFilename(src);
              const proxyUrl = `/api/image-proxy?filename=${encodeURIComponent(filename)}&_t=${timestamp}&_force=true`;

              // Try loading again
              setLoading(true);
              setError(false);

              // Log the retry attempt
              console.log('Manual retry with URL:', proxyUrl);

              // Use a different approach for manual retries
              fetch(proxyUrl, {
                method: 'HEAD',
                cache: 'no-cache',
                headers: {
                  'Cache-Control': 'no-cache, no-store, must-revalidate',
                  'Pragma': 'no-cache',
                  'Expires': '0'
                }
              }).then(() => {
                // If the HEAD request succeeds, try loading the image
                const img = new Image();
                img.onload = () => {
                  console.log('Manual retry successful for:', proxyUrl);
                  setFinalSrc(proxyUrl);
                  setLoading(false);
                };
                img.onerror = () => {
                  console.error('Manual retry failed for:', proxyUrl);
                  setError(true);
                  setLoading(false);
                };
                img.src = proxyUrl;
              }).catch(() => {
                // If the HEAD request fails, try one more approach
                const finalRetryUrl = `/api/image-proxy?filename=${encodeURIComponent(filename)}&_t=${timestamp}&_final=true`;
                console.log('Final retry attempt with:', finalRetryUrl);

                const img = new Image();
                img.onload = () => {
                  console.log('Final retry successful for:', finalRetryUrl);
                  setFinalSrc(finalRetryUrl);
                  setLoading(false);
                };
                img.onerror = () => {
                  console.error('All retry attempts failed for image');
                  setError(true);
                  setLoading(false);
                };
                img.src = finalRetryUrl;
              });
            }}
          >
            Retry
          </Button>
        </div>
      </div>
    )
  }

  // Show the successfully loaded image
  return (
    <>
      <div
        className={cn(
          "relative bg-muted/30 rounded-lg overflow-hidden group",
          className,
          aspectRatio === "square" ? "aspect-square" :
          aspectRatio === "video" ? "aspect-video" : "",
          lightboxEnabled && "cursor-pointer"
        )}
        onClick={lightboxEnabled ? () => setLightboxOpen(true) : undefined}
      >
        <img
          src={finalSrc}
          alt={alt}
          className={cn(
            "w-full object-contain",
            aspectRatio === "auto" ? "h-auto" : "h-full"
          )}
        />

        {/* Zoom indicator overlay */}
        {lightboxEnabled && (
          <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 dark:group-hover:bg-black/40 transition-colors flex items-center justify-center opacity-0 group-hover:opacity-100">
            <Button
              variant="secondary"
              size="icon"
              className="h-9 w-9 rounded-full shadow-lg bg-background/80 backdrop-blur-sm"
            >
              <ZoomIn className="h-5 w-5" />
            </Button>
          </div>
        )}
      </div>

      {/* Image Lightbox */}
      {lightboxEnabled && finalSrc && (
        <ImageLightbox
          images={processedLightboxGroup}
          initialIndex={lightboxIndex}
          open={lightboxOpen}
          onOpenChange={setLightboxOpen}
        />
      )}
    </>
  )
}
