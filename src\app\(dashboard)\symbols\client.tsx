"use client"

import { useState, useEffect, useMemo } from "react"
import { useRouter } from "next/navigation"
import { format } from "date-fns"
import { getUserTrades } from "@/lib/trade-service"
import { useAccount } from "@/contexts/account-context"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { cn } from "@/lib/utils"
import { SymbolComparison } from "@/components/symbol-comparison"
import { DailyCumulativePnLBySymbol } from "@/components/daily-cumulative-pnl-by-symbol"
import { Trade } from "@/types/trade"

type SymbolMetric = {
  symbol: string
  totalTrades: number
  winningTrades: number
  losingTrades: number
  winRate: number
  totalProfit: number
  averageProfit: number
  profitFactor: number
  riskReward: number
  avgDuration: number
  volume: number
}

interface SymbolsClientProps {
  userId: string;
  initialTrades: Trade[];
  initialAccountId: string | null;
}

export default function SymbolsClient({
  userId,
  initialTrades,
  initialAccountId
}: SymbolsClientProps) {
  const router = useRouter()
  const [trades, setTrades] = useState<Trade[]>(initialTrades)
  const [loading, setLoading] = useState(false)
  const [selectedSymbol, setSelectedSymbol] = useState<string>("all")
  const [timeFilter, setTimeFilter] = useState<string>("all")
  const [selectedSymbols, setSelectedSymbols] = useState<string[]>([])
  const { selectedAccountId } = useAccount()

  useEffect(() => {
    const fetchTrades = async () => {
      try {
        setLoading(true)
        // Only fetch if the selected account is different from the initial account
        if (selectedAccountId !== initialAccountId) {
          const tradesData = await getUserTrades(userId, selectedAccountId)
          setTrades(tradesData)
        }
      } catch (error) {
        console.error("Error fetching trades:", error)
      } finally {
        setLoading(false)
      }
    }

    fetchTrades()
  }, [userId, selectedAccountId, initialAccountId])

  // Filter trades based on selected symbol and time period
  const filteredTrades = useMemo(() => {
    let filtered = [...trades]

    // Filter by symbol
    if (selectedSymbol !== "all") {
      filtered = filtered.filter(trade => trade.symbol === selectedSymbol)
    }

    // Filter by time period
    if (timeFilter !== "all") {
      const now = new Date()
      let startDate = new Date()

      switch (timeFilter) {
        case "week":
          startDate.setDate(now.getDate() - 7)
          break
        case "month":
          startDate.setMonth(now.getMonth() - 1)
          break
        case "quarter":
          startDate.setMonth(now.getMonth() - 3)
          break
        case "year":
          startDate.setFullYear(now.getFullYear() - 1)
          break
      }

      filtered = filtered.filter(trade => new Date(trade.time_close) >= startDate)
    }

    return filtered
  }, [trades, selectedSymbol, timeFilter])

  // Calculate symbol metrics
  const symbolMetrics = useMemo(() => {
    const metrics: SymbolMetric[] = []
    const symbolMap = new Map<string, Trade[]>()

    // Group trades by symbol
    filteredTrades.forEach(trade => {
      if (!symbolMap.has(trade.symbol)) {
        symbolMap.set(trade.symbol, [])
      }
      symbolMap.get(trade.symbol)?.push(trade)
    })

    // Calculate metrics for each symbol
    symbolMap.forEach((symbolTrades, symbol) => {
      const totalTrades = symbolTrades.length
      const winningTrades = symbolTrades.filter(t => t.profit > 0).length
      const losingTrades = symbolTrades.filter(t => t.profit <= 0).length
      const winRate = (winningTrades / totalTrades) * 100
      const totalProfit = symbolTrades.reduce((sum, t) => sum + t.profit, 0)
      const averageProfit = totalProfit / totalTrades

      // Calculate profit factor (gross profit / gross loss)
      const grossProfit = symbolTrades.filter(t => t.profit > 0).reduce((sum, t) => sum + t.profit, 0)
      const grossLoss = Math.abs(symbolTrades.filter(t => t.profit < 0).reduce((sum, t) => sum + t.profit, 0))
      const profitFactor = grossLoss === 0 ? grossProfit : grossProfit / grossLoss

      // Calculate risk/reward ratio
      const avgWin = symbolTrades.filter(t => t.profit > 0).reduce((sum, t) => sum + t.profit, 0) / Math.max(winningTrades, 1)
      const avgLoss = Math.abs(symbolTrades.filter(t => t.profit < 0).reduce((sum, t) => sum + t.profit, 0)) / Math.max(losingTrades, 1)
      const riskReward = avgLoss === 0 ? avgWin : avgWin / avgLoss

      // Calculate average duration in minutes
      const avgDuration = symbolTrades.reduce((sum, t) => {
        const openTime = new Date(t.time_open).getTime()
        const closeTime = new Date(t.time_close).getTime()
        return sum + (closeTime - openTime) / (1000 * 60) // Convert ms to minutes
      }, 0) / totalTrades

      // Calculate total volume
      const volume = symbolTrades.reduce((sum, t) => sum + t.volume, 0)

      metrics.push({
        symbol,
        totalTrades,
        winningTrades,
        losingTrades,
        winRate,
        totalProfit,
        averageProfit,
        profitFactor,
        riskReward,
        avgDuration,
        volume
      })
    })

    // Sort by total profit (descending)
    return metrics.sort((a, b) => b.totalProfit - a.totalProfit)
  }, [filteredTrades])

  // Get unique symbols from trades
  const symbols = useMemo(() => {
    const uniqueSymbols = new Set<string>()
    uniqueSymbols.add("all")
    trades.forEach(trade => uniqueSymbols.add(trade.symbol))
    return Array.from(uniqueSymbols)
  }, [trades])

  if (loading) {
    return (
      <div className="flex justify-center items-center h-[calc(100vh-200px)]">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    )
  }

  return (
    <div className="container py-6">
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-end mb-6">

        <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2 w-full sm:w-auto">
          <Select
            value={selectedSymbol}
            onValueChange={setSelectedSymbol}
          >
            <SelectTrigger className="w-full sm:w-[180px] bg-card/50 hover:bg-card/80 transition-colors">
              <SelectValue placeholder="Select Symbol" />
            </SelectTrigger>
            <SelectContent className="bg-card/50">
              <SelectItem value="all">All Symbols</SelectItem>
              {symbols.filter(s => s !== "all").map(symbol => (
                <SelectItem key={symbol} value={symbol}>{symbol}</SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select
            value={timeFilter}
            onValueChange={setTimeFilter}
          >
            <SelectTrigger className="w-full sm:w-[180px] bg-card/50 hover:bg-card/80 transition-colors">
              <SelectValue placeholder="Time Period" />
            </SelectTrigger>
            <SelectContent className="bg-card/50">
              <SelectItem value="all">All Time</SelectItem>
              <SelectItem value="week">Last Week</SelectItem>
              <SelectItem value="month">Last Month</SelectItem>
              <SelectItem value="quarter">Last Quarter</SelectItem>
              <SelectItem value="year">Last Year</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {selectedAccountId === null ? (
        <Card className="p-8 text-center">
          <div className="flex flex-col items-center justify-center space-y-4">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="h-12 w-12 text-muted-foreground mb-2"
            >
              <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
              <circle cx="9" cy="7" r="4" />
              <path d="M22 21v-2a4 4 0 0 0-3-3.87" />
              <path d="M16 3.13a4 4 0 0 1 0 7.75" />
            </svg>
            <h2 className="text-xl font-semibold">No Account Selected</h2>
            <p className="text-muted-foreground max-w-md">
              Please select an account from the dashboard to view your symbol performance. No data will be displayed until an account is selected.
            </p>
            <Button
              variant="outline"
              onClick={() => router.push('/dashboard')}
              className="mt-4"
            >
              Go to Dashboard
            </Button>
          </div>
        </Card>
      ) : (
        <>
          {filteredTrades.length === 0 ? (
            <Card className="bg-card/50">
              <CardContent className="pt-6">
                <div className="text-center py-6">
                  <p className="text-muted-foreground">No trades found for the selected filters.</p>
                </div>
              </CardContent>
            </Card>
          ) : (
            <>
              {/* Symbol Overview */}
              <div className="grid gap-4 grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 mb-6">
                <Card className="dashboard-card bg-card/50 hover:bg-card/80 transition-colors">
                  <CardHeader className="p-4 pb-2">
                    <CardTitle className="text-sm font-medium text-muted-foreground">Symbols</CardTitle>
                  </CardHeader>
                  <CardContent className="p-4 pt-0">
                    <div className="text-2xl font-bold">{symbolMetrics.length}</div>
                  </CardContent>
                </Card>

                <Card className="dashboard-card bg-card/50 hover:bg-card/80 transition-colors">
                  <CardHeader className="p-4 pb-2">
                    <CardTitle className="text-sm font-medium text-muted-foreground">Total Trades</CardTitle>
                  </CardHeader>
                  <CardContent className="p-4 pt-0">
                    <div className="text-2xl font-bold">{filteredTrades.length}</div>
                  </CardContent>
                </Card>

                <Card className="dashboard-card bg-card/50 hover:bg-card/80 transition-colors">
                  <CardHeader className="p-4 pb-2">
                    <CardTitle className="text-sm font-medium text-muted-foreground">Total Profit</CardTitle>
                  </CardHeader>
                  <CardContent className="p-4 pt-0">
                    <div className={cn(
                      "text-2xl font-bold",
                      filteredTrades.reduce((sum, t) => sum + t.profit, 0) >= 0
                        ? "text-emerald-500"
                        : "text-rose-500"
                    )}>
                      ${filteredTrades.reduce((sum, t) => sum + t.profit, 0).toFixed(2)}
                    </div>
                  </CardContent>
                </Card>

                <Card className="dashboard-card bg-card/50 hover:bg-card/80 transition-colors">
                  <CardHeader className="p-4 pb-2">
                    <CardTitle className="text-sm font-medium text-muted-foreground">Avg. Profit/Symbol</CardTitle>
                  </CardHeader>
                  <CardContent className="p-4 pt-0">
                    <div className={cn(
                      "text-2xl font-bold",
                      symbolMetrics.reduce((sum, m) => sum + m.totalProfit, 0) / Math.max(symbolMetrics.length, 1) >= 0
                        ? "text-emerald-500"
                        : "text-rose-500"
                    )}>
                      ${(symbolMetrics.reduce((sum, m) => sum + m.totalProfit, 0) / Math.max(symbolMetrics.length, 1)).toFixed(2)}
                    </div>
                  </CardContent>
                </Card>

                <Card className="dashboard-card bg-card/50 hover:bg-card/80 transition-colors">
                  <CardHeader className="p-4 pb-2">
                    <CardTitle className="text-sm font-medium text-muted-foreground">Total Volume</CardTitle>
                  </CardHeader>
                  <CardContent className="p-4 pt-0">
                    <div className="text-2xl font-bold">{symbolMetrics.reduce((sum, m) => sum + m.volume, 0).toFixed(2)}</div>
                  </CardContent>
                </Card>
              </div>

              {/* Charts */}
              <div className="grid gap-4 md:grid-cols-2 mb-6">
                <SymbolComparison
                  symbolMetrics={symbolMetrics}
                  allSymbols={symbols.filter(s => s !== "all")}
                  selectedSymbols={selectedSymbols}
                  setSelectedSymbols={setSelectedSymbols}
                />

                <DailyCumulativePnLBySymbol
                  trades={filteredTrades}
                  selectedSymbols={selectedSymbols}
                />
              </div>

              {/* Symbol Metrics Table */}
              <Card className="dashboard-card bg-card/50 hover:bg-card/80 transition-colors">
                <CardHeader className="p-4 pb-2">
                  <CardTitle className="text-base font-medium">Symbol Metrics</CardTitle>
                </CardHeader>
                <CardContent className="p-0">
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead>
                        <tr className="border-b">
                          <th className="text-left p-4">Symbol</th>
                          <th className="text-left p-4">Trades</th>
                          <th className="text-left p-4">Win Rate</th>
                          <th className="text-left p-4">Profit</th>
                          <th className="text-left p-4">Avg. Profit</th>
                          <th className="text-left p-4">Profit Factor</th>
                          <th className="text-left p-4">Risk/Reward</th>
                          <th className="text-left p-4">Avg. Duration</th>
                          <th className="text-left p-4">Volume</th>
                        </tr>
                      </thead>
                      <tbody>
                        {symbolMetrics.map((metric) => (
                          <tr key={metric.symbol} className="border-b hover:bg-muted/50 cursor-pointer" onClick={() => router.push(`/symbols/${metric.symbol}`)}>
                            <td className="p-4 font-medium text-blue-500 hover:underline">{metric.symbol}</td>
                            <td className="p-4">{metric.totalTrades}</td>
                            <td className="p-4">
                              <div className="flex items-center">
                                <div
                                  className="w-16 h-2 rounded-full bg-muted overflow-hidden mr-2"
                                >
                                  <div
                                    className="h-full bg-blue-500"
                                    style={{ width: `${metric.winRate}%` }}
                                  />
                                </div>
                                {metric.winRate.toFixed(1)}%
                              </div>
                            </td>
                            <td className={cn(
                              "p-4",
                              metric.totalProfit >= 0 ? "text-emerald-500" : "text-rose-500"
                            )}>
                              ${metric.totalProfit.toFixed(2)}
                            </td>
                            <td className={cn(
                              "p-4",
                              metric.averageProfit >= 0 ? "text-emerald-500" : "text-rose-500"
                            )}>
                              ${metric.averageProfit.toFixed(2)}
                            </td>
                            <td className="p-4">{metric.profitFactor.toFixed(2)}</td>
                            <td className="p-4">{metric.riskReward.toFixed(2)}</td>
                            <td className="p-4">{metric.avgDuration.toFixed(2)} min</td>
                            <td className="p-4">{metric.volume.toFixed(2)}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </CardContent>
              </Card>
            </>
          )}
        </>
      )}
    </div>
  )
}
