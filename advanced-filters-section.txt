        {showAdvancedFilters && (
          <div className="space-y-4 pt-2">
            <div className="grid grid-cols-4 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Date From</label>
                <div className="relative">
                  <Calendar className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="date"
                    value={filterDateFrom}
                    onChange={(e) => setFilterDateFrom(e.target.value)}
                    className="pl-8"
                  />
                </div>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Date To</label>
                <div className="relative">
                  <Calendar className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="date"
                    value={filterDateTo}
                    onChange={(e) => setFilterDateTo(e.target.value)}
                    className="pl-8"
                  />
                </div>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Min Profit</label>
                <div className="relative">
                  <span className="absolute left-2.5 top-2.5 text-muted-foreground">$</span>
                  <Input
                    type="number"
                    placeholder="Min"
                    value={filterProfitMin}
                    onChange={(e) => setFilterProfitMin(e.target.value)}
                    className="pl-7"
                  />
                </div>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Max Profit</label>
                <div className="relative">
                  <span className="absolute left-2.5 top-2.5 text-muted-foreground">$</span>
                  <Input
                    type="number"
                    placeholder="Max"
                    value={filterProfitMax}
                    onChange={(e) => setFilterProfitMax(e.target.value)}
                    className="pl-7"
                  />
                </div>
              </div>
            </div>
            <div className="flex justify-end">
              <Button 
                variant="outline" 
                size="sm" 
                onClick={resetFilters}
              >
                Reset Filters
              </Button>
            </div>
          </div>
        )}
