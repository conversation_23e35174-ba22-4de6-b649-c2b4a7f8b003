"use client"

import React, { useState, useEffect } from 'react'
import { DndContext, closestCenter, KeyboardSensor, PointerSensor, useSensor, useSensors, DragEndEvent } from '@dnd-kit/core'
import { arrayMove, SortableContext, sortableKeyboardCoordinates, useSortable, verticalListSortingStrategy } from '@dnd-kit/sortable'
import { CSS } from '@dnd-kit/utilities'
import { GripVertical } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { cn } from '@/lib/utils'

interface SortableItemProps {
  id: string
  children: React.ReactNode
  title: string
}

function SortableItem({ id, children, title }: SortableItemProps) {
  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({ id })

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    zIndex: isDragging ? 10 : 1,
    opacity: isDragging ? 0.8 : 1,
  }

  return (
    <div
      ref={setNodeRef}
      style={style}
      className="mb-6"
    >
      <Card className={cn(
        "relative transition-all duration-200",
        isDragging && "border-primary shadow-lg scale-[1.02]"
      )}>
        <CardHeader className="flex flex-row items-center justify-between py-3">
          <CardTitle className="text-base font-medium">{title}</CardTitle>
          <div
            className={cn(
              "flex items-center justify-center w-10 h-10 rounded-md",
              "cursor-grab active:cursor-grabbing",
              "hover:bg-muted/80 active:bg-muted transition-colors",
              "touch-none select-none",
              "border-2 border-dashed border-transparent hover:border-muted-foreground/30"
            )}
            {...attributes}
            {...listeners}
            title="Drag to reorder"
          >
            <GripVertical className="h-5 w-5 text-muted-foreground hover:text-foreground transition-colors" />
          </div>
        </CardHeader>
        <CardContent>
          {children}
        </CardContent>
      </Card>
    </div>
  )
}

interface DashboardSection {
  id: string
  title: string
  component: React.ReactNode
}

interface CustomizableDashboardProps {
  sections: DashboardSection[]
  onOrderChange?: (newOrder: string[]) => void
  savedOrder?: string[]
}

export function CustomizableDashboard({ sections, onOrderChange, savedOrder }: CustomizableDashboardProps) {
  const [items, setItems] = useState<string[]>([])

  useEffect(() => {
    console.log('CustomizableDashboard: sections received:', sections.length, sections.map(s => s.id))
    console.log('CustomizableDashboard: savedOrder:', savedOrder)

    // Initialize with saved order or default order
    if (savedOrder && savedOrder.length === sections.length) {
      console.log('CustomizableDashboard: using saved order')
      setItems(savedOrder)
    } else {
      console.log('CustomizableDashboard: using default order')
      setItems(sections.map(section => section.id))
    }
  }, [sections, savedOrder])

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8, // Require 8px of movement before activating
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  )

  function handleDragEnd(event: DragEndEvent) {
    const { active, over } = event
    console.log('Drag ended:', { activeId: active.id, overId: over?.id })

    if (over && active.id !== over.id) {
      setItems((items) => {
        const oldIndex = items.indexOf(active.id.toString())
        const newIndex = items.indexOf(over.id.toString())

        console.log('Moving item:', { activeId: active.id, oldIndex, newIndex })

        const newOrder = arrayMove(items, oldIndex, newIndex)
        console.log('New order:', newOrder)

        // Call the callback if provided
        if (onOrderChange) {
          onOrderChange(newOrder)
        }

        return newOrder
      })
    }
  }

  return (
    <div className="space-y-4">
      {/* Customizable Mode Indicator */}
      <div className="bg-blue-50 dark:bg-blue-950 border border-blue-200 dark:border-blue-800 rounded-lg p-3">
        <div className="flex items-center gap-2 text-sm text-blue-700 dark:text-blue-300">
          <GripVertical className="h-4 w-4" />
          <span className="font-medium">Customizable Layout Mode</span>
          <span className="text-blue-600 dark:text-blue-400">- Drag the grip handles to reorder sections</span>
        </div>
      </div>

      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragEnd={handleDragEnd}
        onDragStart={() => console.log('Drag started')}
        onDragOver={() => console.log('Drag over')}
      >
        <SortableContext
          items={items}
          strategy={verticalListSortingStrategy}
        >
          <div className="space-y-6">
            {items.map((id) => {
              const section = sections.find(s => s.id === id)
              if (!section) return null

              return (
                <SortableItem key={id} id={id} title={section.title}>
                  {section.component}
                </SortableItem>
              )
            })}
          </div>
        </SortableContext>
      </DndContext>
    </div>
  )
}
