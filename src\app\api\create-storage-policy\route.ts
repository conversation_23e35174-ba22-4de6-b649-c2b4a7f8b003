import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

export async function POST(request: Request) {
  try {
    const { policyName, bucketName, operation, definition } = await request.json();
    
    if (!policyName || !bucketName || !operation || !definition) {
      return NextResponse.json({ 
        error: 'Missing required parameters', 
        required: ['policyName', 'bucketName', 'operation', 'definition'] 
      }, { status: 400 });
    }
    
    // Create a Supabase client with admin privileges
    const supabaseAdmin = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL || '',
      process.env.SUPABASE_SERVICE_ROLE_KEY || ''
    );
    
    // Create the SQL statement to create the policy
    const sql = `
      CREATE POLICY "${policyName}"
      ON storage.objects
      FOR ${operation}
      TO authenticated
      USING (${definition});
    `;
    
    // Execute the SQL statement
    const { error } = await supabaseAdmin.rpc('exec_sql', { sql });
    
    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }
    
    return NextResponse.json({
      success: true,
      message: `Policy "${policyName}" created successfully`,
      details: {
        policyName,
        bucketName,
        operation,
        definition
      }
    });
  } catch (error) {
    console.error('Error in create-storage-policy route:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
