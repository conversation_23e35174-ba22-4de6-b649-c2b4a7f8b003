# TradePivot Development Prompt

## Project Overview
You are in a blank Next.js project and I am on a windows PC, Create a modern, responsive Forex trading journal SaaS application called "TradePivot" using Next.js, The application will allow traders to upload their MT5 Excel trading history reports, analyze their trading performance, and gain insights into their trading behaviors.

## Tech Stack
- **Frontend**: Next.js with App Router 
- **UI Components**: Shadcn/UI
- **Styling**: Tailwind CSS
- **State Management**: Zustand or React Query
- **Authentication & Database**: Supabase
- **File Processing**: SheetJS (Excel.js) for parsing Excel files
- **Charts & Visualizations**: Recharts or D3.js
- **Forms**: React Hook Form with Zod validation
- **Calendar**: React-Big-Calendar for trading activity visualization
- **Analytics**: Tremor for data visualization components
- **Animations**: Framer Motion for smooth UI transitions

## Application Structure

### 1. Landing Page
- Modern, sleek design with gradient accents
- Responsive header with logo, navigation, and CTA
- Hero section highlighting key features
- Features section with iconography and brief descriptions
- Testimonials/social proof section
- Pricing plans (if applicable)
- Footer with links to resource pages, terms, privacy policy, etc.
- Dark mode as default with intuitive gradients and animations

### 2. Authentication System (Supabase)
- Sign up
- Sign in
- Password reset
- Profile management
- OAuth integrations (Google, GitHub)

### 3. Dashboard
- Beautiful and collapsible side panel navigation
- When collapsed, only icons remain visible
- Custom theme with dark mode
- Key performance indicators prominently displayed:
  - Total trades
  - Winrate
  - Total P&L
  - Risk/Reward
  - Account balance
  - Max drawdown

### 4. Excel Report Upload & Processing
- Drag and drop interface for Excel files
- Progress indicator during upload and processing
- Error handling for invalid or corrupt files
- Preview of detected data before confirmation
- Use SheetJS to parse MT5 Excel trading reports
- Match the Excel structure to the provided JSON schema

### 5. Trade Log
- Tabular view of all trades with the following columns:
  - Entry date
  - Symbol
  - Type (buy/sell)
  - Volume
  - Duration
  - NET P/L
  - Status (closed/open)
- Sorting and filtering capabilities
- Pagination for large datasets
- Export functionality
- Detailed view on row click

### 6. Performance Analytics
- Bar charts for daily P&L
- Pie charts for trade distributions
- Equity curves showing account balance over time
- Win/loss ratio visualizations
- Trade duration analysis
- Symbol performance comparison
- Time-of-day analysis

### 7. Calendar View
- Modern calendar interface showing trading activity
- Heat map style highlighting based on P&L or number of trades
- Drill-down capability when clicking on a specific date
- Detailed view of trades taken on selected date
- Month/week/day view options

### 8. Journal Notes
- Ability to add notes to specific trades
- General journal entries for trading days
- Rich text editor for formatting
- Tag system for categorization
- Search functionality

## Data Models

### User
- ID
- Email
- Password (hashed)
- Name
- Profile information
- Created_at
- Updated_at

### Trading Account
- ID
- User_ID (foreign key)
- Account name
- Broker
- Account number
- Currency
- Initial balance
- Created_at
- Updated_at

### Trade
- ID
- Account_ID (foreign key)
- Symbol
- Type (buy/sell)
- Volume
- Entry price
- Exit price
- Entry time
- Exit time
- SL (stop loss)
- TP (take profit)
- Commission
- Swap
- Profit/Loss
- Status (open/closed)
- Created_at
- Updated_at

### Journal Entry
- ID
- User_ID (foreign key)
- Trade_ID (foreign key, optional)
- Date
- Content
- Tags
- Created_at
- Updated_at

## Excel File Structure Understanding
The application should be able to process MT5 Excel reports with the following structure (represented as markdown for clarity @Sample_markdown_for_excel_file.md)


## Functionality Requirements

### File Upload and Processing
1. Allow users to upload MT5 Excel reports
2. Parse the Excel file using SheetJS
3. Extract and store relevant trading data
4. Handle various Excel formats and potential errors
5. Display import summary after processing

### Performance Metrics Calculation
1. Calculate win rate from imported data
2. Determine total P&L
3. Compute risk/reward ratios
4. Track account balance changes
5. Calculate drawdown metrics
6. Analyze trade durations and patterns

### Data Visualization
1. Create responsive, interactive charts
2. Implement filtering options for all visualizations
3. Allow date range selection for analysis
4. Enable comparison between different time periods
5. Provide export options for charts and data

### Calendar Integration
1. Display trading activity across months
2. Highlight profitable vs losing days with color coding
3. Show trade volume through visual indicators
4. Provide detailed view of trades on date selection

## UI/UX Requirements

### General
- Dark mode by default
- Responsive design for all device sizes
- Intuitive navigation with clear hierarchies
- Smooth animations and transitions
- Consistent color scheme throughout

### Dashboard
- Clean, uncluttered layout
- Card-based components for metrics
- Easy-to-understand visualizations
- Quick access to most important data

### Mobile Responsiveness
- All features must be fully functional on mobile devices
- Appropriate touch targets for mobile users
- Optimized layouts for different screen sizes
- Consideration for limited bandwidth (lazy loading)

## Implementation Guidelines

### Code Structure
- Utilize Next.js 14.2.24 App Router for routing
- Implement proper folder structure following Next.js conventions
- Create reusable components for common UI elements
- Use TypeScript for type safety
- Follow best practices for state management

### Performance Considerations
- Optimize for large datasets
- Implement pagination where appropriate
- Use caching strategies for expensive calculations
- Lazy load non-critical components
- Optimize bundle size for faster loading

### Security
- Implement proper authentication and authorization with Supabase
- Secure API endpoints
- Validate all user inputs
- Handle sensitive data appropriately
- Implement CSRF protection

## Development Phases

### Phase 1: Setup and Base Structure
1. Initialize Next.js 14.2.24 project
2. Configure Supabase auth and database
3. Create basic layouts and navigation
4. Implement dark mode theme
5. Set up responsive design framework

### Phase 2: Core Functionality
1. Develop file upload and processing system
2. Implement trade logging functionality
3. Create basic analytics calculations
4. Build calendar view integration
5. Develop journal note system

### Phase 3: Data Visualization
1. Implement performance dashboard
2. Create interactive charts and graphs
3. Develop filtering and sorting capabilities
4. Build date range selectors
5. Integrate all visualizations with data sources

### Phase 4: Refinement
1. Optimize performance
2. Enhance UI with animations
3. Improve mobile experience
4. Add export functionality
5. Implement comprehensive error handling

## Testing Requirements
1. Unit tests for core functionality
2. Integration tests for data flow
3. UI tests for responsive design
4. Performance testing with large datasets
5. Security testing for authentication and authorization

## Deployment Considerations
1. Configure CI/CD pipeline
2. Set up proper error logging
3. Implement analytics tracking
4. Create backup and recovery system
5. Plan for scaling as user base grows

## Additional Notes
- Focus on creating an intuitive, user-friendly interface
- Prioritize data accuracy in calculations
- Ensure smooth file upload experience
- Design for trader workflows and habits
- Consider future extensibility for planned features