import { NextResponse } from 'next/server'

/**
 * Health check endpoint for Docker containers and load balancers
 * Returns 200 OK with basic application status
 */
export async function GET() {
  try {
    // Basic health check - you can add more sophisticated checks here
    const healthStatus = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: process.env.NODE_ENV || 'development',
      version: process.env.npm_package_version || '1.0.0'
    }

    return NextResponse.json(healthStatus, { status: 200 })
  } catch (error) {
    // Return unhealthy status if something goes wrong
    return NextResponse.json(
      { 
        status: 'unhealthy', 
        error: 'Health check failed',
        timestamp: new Date().toISOString()
      }, 
      { status: 503 }
    )
  }
}

// Support for HEAD requests (common for health checks)
export async function HEAD() {
  try {
    return new NextResponse(null, { status: 200 })
  } catch (error) {
    return new NextResponse(null, { status: 503 })
  }
}
