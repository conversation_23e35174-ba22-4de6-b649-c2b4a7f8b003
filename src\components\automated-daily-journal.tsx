"use client"

import { useState, useRef, useEffect } from "react"
import { format } from "date-fns"

// Helper function to compare dates (ignoring time)
const areDatesEqual = (date1: Date, date2: Date): boolean => {
  // Set both dates to UTC midnight to ensure proper comparison
  const date1UTC = new Date(Date.UTC(
    date1.getFullYear(),
    date1.getMonth(),
    date1.getDate()
  ));

  const date2UTC = new Date(Date.UTC(
    date2.getFullYear(),
    date2.getMonth(),
    date2.getDate()
  ));

  // Compare the UTC dates
  return date1UTC.getTime() === date2UTC.getTime();
}
import {
  ChevronDown,
  ChevronUp,
  Calendar,
  Image as ImageIcon,
  Save,
  X,
  Trash2,
  BarChart3,
  DollarSign,
  TrendingUp,
  TrendingDown,
  BookOpen,
  Tag
} from "lucide-react"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { ImageDisplay } from "@/components/ui/image-display"
import { JournalPnLChart } from "@/components/journal-pnl-chart"
import { TagInput } from "@/components/tag-input"
import { Badge } from "@/components/ui/badge"
import { uploadImage } from "@/lib/image-uploader"
import { getSupabaseBrowser } from "@/lib/supabase"
import { cn } from "@/lib/utils"
import { getAllTags } from "@/lib/journal-service"
import { getDailyJournalEntry, saveDailyJournalEntry, migrateLocalStorageJournalEntries } from "@/lib/daily-journal-service"
import { toast } from "sonner"
import { useAccount } from "@/contexts/account-context"

interface AutomatedDailyJournalProps {
  date: Date
  trades: any // Can be an array of trades or an object with trades, metrics, and strategyMap
  className?: string
  userId: string
  accountId: string | null
  onUpdate: () => void
  tradeDate?: string // Optional date string for fetching trades
  initialJournalEntry?: any // Optional pre-fetched journal entry
}

interface JournalEntry {
  note: string
  screenshots: string[]
  tags: string[]
}

export function AutomatedDailyJournal({ date, trades, className, userId, accountId, onUpdate, tradeDate, initialJournalEntry }: AutomatedDailyJournalProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [isEditing, setIsEditing] = useState(false)
  const [isUploading, setIsUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [availableTags, setAvailableTags] = useState<string[]>([])
  const [directlyFetchedTrades, setDirectlyFetchedTrades] = useState<any[]>([])
  const [isLoadingTrades, setIsLoadingTrades] = useState(false)

  // Debounced save function to reduce database calls
  const debouncedSave = useRef<NodeJS.Timeout | null>(null)

  // Cleanup effect to cancel any pending debounced saves when the component unmounts
  useEffect(() => {
    return () => {
      if (debouncedSave.current) {
        clearTimeout(debouncedSave.current);
      }
    };
  }, [])

  // Initialize state based on the type of trades prop
  const [resolvedTrades, setResolvedTrades] = useState<any[]>(() => {
    // If trades is an object with a trades property, use that
    if (trades && typeof trades === 'object' && 'trades' in trades) {
      return trades.trades || [];
    }
    // Otherwise, assume it's an array
    return Array.isArray(trades) ? trades : [];
  })

  // Initialize strategyMap from trades prop if available
  const [strategyMap, setStrategyMap] = useState<Record<string, string>>(() => {
    // If trades is an object with a strategyMap property, use that
    if (trades && typeof trades === 'object' && 'strategyMap' in trades) {
      return trades.strategyMap || {};
    }
    return {};
  })

  // Initialize metrics from trades prop if available
  const [dailyMetrics, setDailyMetrics] = useState<any>(() => {
    // If trades is an object with a metrics property, use that
    if (trades && typeof trades === 'object' && 'metrics' in trades) {
      return trades.metrics || null;
    }
    return null;
  })

  const fileInputRef = useRef<HTMLInputElement>(null)
  const supabase = getSupabaseBrowser()
  const { selectedAccountId } = useAccount()

  // Fetch trades directly for this date using the server action
  const fetchTradesForDate = async (userId: string) => {
    // Skip if we already have data from the parent component
    if (trades && typeof trades === 'object' && 'trades' in trades) {
      console.log('Using trades data from parent component');
      return;
    }

    try {
      setIsLoadingTrades(true);

      // Use tradeDate if provided, otherwise format from date
      const dateStr = tradeDate || format(date, "yyyy-MM-dd");

      // Import the server action
      const { fetchTradesForDate: fetchTradesAction } = await import("@/app/(dashboard)/journal/actions");

      // Call the server action to get trades and pre-calculated metrics
      const result = await fetchTradesAction(
        userId,
        selectedAccountId,
        dateStr,
        false // Don't apply filters for now
      );

      if (result && result.trades) {
        setDirectlyFetchedTrades(result.trades);
        setResolvedTrades(result.trades);

        // Set strategy map from server
        if (result.strategyMap) {
          setStrategyMap(result.strategyMap);
        }

        // Store the pre-calculated metrics
        setDailyMetrics(result.metrics);
      } else {
        // Clear trades if none found
        setDirectlyFetchedTrades([]);
        setResolvedTrades([]);
      }
    } catch (error) {
      console.error(`Error in fetchTradesForDate:`, error);
    } finally {
      setIsLoadingTrades(false);
    }
  }

  // Fetch tags and trades when component mounts or when account/date changes
  useEffect(() => {
    let isMounted = true;

    const fetchTagsAndTrades = async () => {
      if (!userId) return;

      try {
        setIsLoadingTrades(true);

        // Migrate any localStorage journal entries to Supabase (only once)
        if (isMounted) {
          await migrateLocalStorageJournalEntries(userId, selectedAccountId || undefined)
        }

        // Fetch tags
        if (isMounted) {
          const tags = await getAllTags(userId)
          setAvailableTags(tags)
        }

        // Fetch trades for this date
        if (isMounted) {
          await fetchTradesForDate(userId)
        }
      } catch (error) {
        console.error("Error fetching tags and trades:", error)
      } finally {
        if (isMounted) {
          setIsLoadingTrades(false);
        }
      }
    }

    fetchTagsAndTrades()

    // Cleanup function to prevent state updates after unmount
    return () => {
      isMounted = false;
    }
  }, [userId, selectedAccountId, date, tradeDate])

  // Update state when trades prop changes
  useEffect(() => {
    // If trades is an object with the expected properties, update state
    if (trades && typeof trades === 'object') {
      if ('trades' in trades) {
        setResolvedTrades(trades.trades || []);
      }
      if ('strategyMap' in trades) {
        setStrategyMap(trades.strategyMap || {});
      }
      if ('metrics' in trades) {
        setDailyMetrics(trades.metrics || null);
      }
    } else if (Array.isArray(trades)) {
      // If trades is an array, update resolvedTrades
      setResolvedTrades(trades);
    }
  }, [trades]);

  // Fetch strategy names for all strategy IDs in the trades
  useEffect(() => {
    // Skip if we already have strategy map from parent
    if (trades && typeof trades === 'object' && 'strategyMap' in trades && Object.keys(trades.strategyMap).length > 0) {
      return;
    }

    const fetchStrategyNames = async () => {
      // Combine directly fetched trades with initial trades
      const allTrades = [...resolvedTrades, ...directlyFetchedTrades];

      // Remove duplicates
      const uniqueTradeIds = new Set();
      const uniqueTrades = allTrades.filter(trade => {
        if (!trade.id || uniqueTradeIds.has(trade.id)) return false;
        uniqueTradeIds.add(trade.id);
        return true;
      });

      if (uniqueTrades.length === 0) return;

      // Get unique strategy IDs from trades
      const strategyIds = [...new Set(
        uniqueTrades
          .filter(trade => trade.strategy_id)
          .map(trade => trade.strategy_id)
      )];

      if (strategyIds.length === 0) return;

      try {
        const supabase = getSupabaseBrowser()
        const { data, error } = await supabase
          .from('strategies')
          .select('id, name')
          .in('id', strategyIds)

        if (error) {
          console.error('Error fetching strategy names:', error)
          return
        }

        // Create a map of strategy ID to name
        const newStrategyMap: Record<string, string> = {}
        data?.forEach(strategy => {
          newStrategyMap[strategy.id] = strategy.name
        })

        setStrategyMap(newStrategyMap)
      } catch (error) {
        console.error('Error in fetchStrategyNames:', error)
      }
    }

    fetchStrategyNames()
  }, [resolvedTrades, directlyFetchedTrades])

  // Get journal entry from Supabase or create a new one
  const getJournalEntry = async (): Promise<JournalEntry> => {
    const defaultEntry = { note: "", screenshots: [], tags: [] }

    // If we have initialJournalEntry, use it instead of fetching
    if (initialJournalEntry) {
      console.log('Using initialJournalEntry in getJournalEntry');
      return {
        note: initialJournalEntry.note || "",
        screenshots: Array.isArray(initialJournalEntry.screenshots) ? initialJournalEntry.screenshots : [],
        tags: Array.isArray(initialJournalEntry.tags) ? initialJournalEntry.tags : []
      };
    }

    if (!userId) return defaultEntry;

    try {
      const entry = await getDailyJournalEntry(userId, date, selectedAccountId || undefined);

      if (entry) {
        // Convert from database format to component format
        return {
          note: entry.note || "",
          screenshots: Array.isArray(entry.screenshots) ? entry.screenshots : [],
          tags: Array.isArray(entry.tags) ? entry.tags : []
        };
      }

      return defaultEntry;
    } catch (error) {
      console.error('Error fetching journal entry from Supabase:', error);
      return defaultEntry;
    }
  }

  // Helper function to save changes to the database
  const saveChangesToDatabase = async (entry: JournalEntry) => {
    if (!userId) {
      console.error('Cannot save journal entry: No user ID');
      return false;
    }

    try {
      // Ensure tags is always an array
      const updatedEntry = {
        ...entry,
        tags: Array.isArray(entry.tags) ? entry.tags : []
      };

      // Save to Supabase
      await saveDailyJournalEntry(userId, {
        date,
        note: updatedEntry.note,
        screenshots: updatedEntry.screenshots,
        tags: updatedEntry.tags,
        accountId: accountId || selectedAccountId || undefined
      });

      // Dispatch an event to notify that tags have been updated
      // This will trigger a refresh of the journal page
      const event = new CustomEvent('journal-tags-updated', {
        detail: { date: format(date, "yyyy-MM-dd"), tags: updatedEntry.tags }
      });
      document.dispatchEvent(event);

      // Also update the tags for all trades on this day to make them filterable
      // Combine directly fetched trades with resolved trades
      const allTrades = [...resolvedTrades, ...directlyFetchedTrades];
      // Remove duplicates
      const uniqueTradeIds = new Set();
      const uniqueTrades = allTrades.filter(trade => {
        if (!trade.id || uniqueTradeIds.has(trade.id)) return false;
        uniqueTradeIds.add(trade.id);
        return true;
      });

      if (uniqueTrades.length > 0 && updatedEntry.tags.length > 0) {
        await updateTradeTagsForDay(updatedEntry.tags, uniqueTrades);
      }

      return true;
    } catch (error) {
      console.error('Error saving journal entry to Supabase:', error);
      toast.error('Failed to save journal entry');
      return false;
    }
  }

  // Debounced save function
  const debouncedSaveToDatabase = (entry: JournalEntry) => {
    // Clear any existing timeout
    if (debouncedSave.current) {
      clearTimeout(debouncedSave.current);
    }

    // Set a new timeout
    debouncedSave.current = setTimeout(async () => {
      await saveChangesToDatabase(entry);
    }, 500); // 500ms debounce time
  };

  // Save journal entry to Supabase
  const saveJournalEntry = async (entry: JournalEntry) => {
    return await saveChangesToDatabase(entry);
  }

  // Helper function to update tags for all trades on this day
  const updateTradeTagsForDay = async (tags: string[], tradesToUpdate?: any[]) => {
    if (!tags || tags.length === 0) {
      return;
    }

    // Use provided trades or fall back to resolved trades
    const tradesForUpdate = tradesToUpdate || resolvedTrades;

    if (!tradesForUpdate || tradesForUpdate.length === 0) {
      return;
    }

    try {
      // For each trade on this day, update its tags to include the daily journal tags
      for (const trade of tradesForUpdate) {
        // Get current trade tags or initialize as empty array
        const currentTradeTags = Array.isArray(trade.tags) ? trade.tags : [];

        // Create a new set of unique tags by combining current trade tags with daily journal tags
        const uniqueTags = Array.from(new Set([...currentTradeTags, ...tags]));

        // Only update if tags have actually changed
        if (JSON.stringify(uniqueTags) !== JSON.stringify(currentTradeTags)) {
          // Update the trade with the combined tags
          await supabase
            .from("trades")
            .update({
              tags: uniqueTags,
              updated_at: new Date().toISOString()
            })
            .eq("id", trade.id);
        }
      }

      // Dispatch a global event to notify that trade data has changed
      const globalEvent = new CustomEvent('global-data-change', {
        detail: { type: 'trade-tags-updated', date: format(date, "yyyy-MM-dd") }
      });
      window.dispatchEvent(globalEvent);
    } catch (error) {
      console.error("Error updating trade tags:", error);
    }
  }

  // Initialize with initialJournalEntry if provided, otherwise default empty entry
  const [journalEntry, setJournalEntry] = useState<JournalEntry>(() => {
    const dateStr = format(date, 'yyyy-MM-dd');
    console.log(`Initializing journal entry for date ${dateStr}, initialJournalEntry:`, initialJournalEntry);

    if (initialJournalEntry) {
      console.log(`Using initialJournalEntry for date ${dateStr}`);
      return {
        note: initialJournalEntry.note || "",
        screenshots: Array.isArray(initialJournalEntry.screenshots) ? initialJournalEntry.screenshots : [],
        tags: Array.isArray(initialJournalEntry.tags) ? initialJournalEntry.tags : []
      };
    } else {
      console.log(`No initialJournalEntry for date ${dateStr}, using default`);
    }
    return { note: "", screenshots: [], tags: [] };
  })

  // Load journal entry from Supabase when component mounts or userId/date/account changes
  useEffect(() => {
    let isMounted = true;

    const loadJournalEntry = async () => {
      if (!userId) return;

      // Skip fetching if we already have initialJournalEntry
      if (initialJournalEntry) {
        console.log('Using initialJournalEntry, skipping fetch');
        return;
      }

      try {
        // Set a loading state if needed
        // setIsLoading(true);

        const entry = await getJournalEntry();

        // Only update state if component is still mounted
        if (isMounted) {
          setJournalEntry(entry);
          // setIsLoading(false);
        }
      } catch (error) {
        if (isMounted) {
          console.error("Error loading journal entry:", error);
          // setIsLoading(false);

          // Set default empty entry on error
          setJournalEntry({ note: "", screenshots: [], tags: [] });
        }
      }
    };

    // Load the journal entry
    loadJournalEntry();

    // Cleanup function to prevent state updates after unmount
    return () => {
      isMounted = false;
    };
  }, [userId, date, selectedAccountId, initialJournalEntry]);



  const handleSave = async () => {
    try {
      await saveJournalEntry(journalEntry);
      setIsEditing(false);
      toast.success("Journal entry saved");
      // Call the onUpdate callback to refresh data in parent component
      onUpdate();
    } catch (error) {
      console.error("Error saving journal entry:", error);
      toast.error("Failed to save journal entry");
    }
  }

  const handleCancel = async () => {
    try {
      const entry = await getJournalEntry();
      setJournalEntry(entry);
      setIsEditing(false);
    } catch (error) {
      console.error("Error loading journal entry:", error);
      setIsEditing(false);
    }
  }

  const handleNoteChange = (note: string) => {
    setJournalEntry({ ...journalEntry, note })
  }

  const handleTagsChange = async (tags: string[]) => {
    const updatedEntry = { ...journalEntry, tags };
    setJournalEntry(updatedEntry);

    // Save the updated entry immediately to ensure tags are persisted
    try {
      await saveJournalEntry(updatedEntry);
    } catch (error) {
      console.error("Error saving tags to Supabase:", error);
      toast.error("Failed to save tags");
    }
  }

  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    // Validate file type
    if (!file.type.startsWith('image/')) {
      toast.error("Please upload an image file")
      return
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      toast.error("File size should not exceed 5MB")
      return
    }

    // Set uploading state
    setIsUploading(true)
    setUploadProgress(0)

    try {
      // Simulate progress for better UX
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          const newProgress = prev + Math.random() * 15;
          return newProgress > 90 ? 90 : newProgress;
        });
      }, 150);

      // Upload the image
      const imageUrl = await uploadImage(file)

      // Clear progress interval
      clearInterval(progressInterval)

      // Set progress to 100% when upload is complete
      setUploadProgress(100)

      if (imageUrl) {
        // Update local state immediately for responsive UI
        const updatedEntry = {
          ...journalEntry,
          screenshots: [...journalEntry.screenshots, imageUrl]
        }

        // Update the UI immediately
        setJournalEntry(updatedEntry)

        // Show success message
        toast.success("Screenshot uploaded successfully")

        // Save to database in the background
        debouncedSaveToDatabase(updatedEntry)
      }
    } catch (error) {
      console.error("Error uploading screenshot:", error)
      toast.error("Failed to upload screenshot")
    } finally {
      // Reset uploading state
      setIsUploading(false)
      setUploadProgress(0)

      // Clear the file input
      if (fileInputRef.current) {
        fileInputRef.current.value = ''
      }
    }
  }

  const removeScreenshot = (index: number) => {
    try {
      // Update the local state immediately for a responsive UI
      const updatedScreenshots = [...journalEntry.screenshots]
      updatedScreenshots.splice(index, 1)

      const updatedEntry = {
        ...journalEntry,
        screenshots: updatedScreenshots
      }

      // Update the UI immediately
      setJournalEntry(updatedEntry)

      // Show success message
      toast.success("Screenshot removed")

      // Save to database in the background
      debouncedSaveToDatabase(updatedEntry)
    } catch (error) {
      console.error("Error removing screenshot:", error)
      toast.error("Failed to remove screenshot")
    }
  }

  // Get trading metrics for the day - use pre-calculated metrics if available
  const calculateDailyMetrics = () => {
    // If we have pre-calculated metrics from the server, use those
    if (dailyMetrics) {
      return dailyMetrics;
    }

    // Otherwise, calculate metrics on the client (fallback)
    // Combine resolved trades with directly fetched trades
    const allTrades = [...resolvedTrades, ...directlyFetchedTrades];

    // Remove duplicates by trade ID
    const uniqueTradeIds = new Set();
    const combinedTrades = allTrades.filter(trade => {
      if (!trade.id || uniqueTradeIds.has(trade.id)) return false;
      uniqueTradeIds.add(trade.id);
      return true;
    });

    if (combinedTrades.length === 0) {
      return {
        totalTrades: 0,
        winningTrades: 0,
        losingTrades: 0,
        winRate: 0,
        totalProfit: 0,
        largestWin: 0,
        largestLoss: 0,
        averageWin: 0,
        averageLoss: 0,
        profitFactor: 0
      }
    }

    // Ensure all trades have a valid profit value and are for this date
    const validTrades = combinedTrades.filter(trade => {
      try {
        // Check if profit is valid
        if (trade.profit === undefined || trade.profit === null || isNaN(Number(trade.profit))) {
          return false;
        }

        // Verify the trade date matches our date
        // Create a new Date object from the trade's time_close
        const tradeDate = new Date(trade.time_close);

        // Use our helper function to compare dates
        const isForThisDate = areDatesEqual(tradeDate, date);

        return isForThisDate;
      } catch (error) {
        return false;
      }
    });

    if (validTrades.length === 0) {
      return {
        totalTrades: 0,
        winningTrades: 0,
        losingTrades: 0,
        winRate: 0,
        totalProfit: 0,
        largestWin: 0,
        largestLoss: 0,
        averageWin: 0,
        averageLoss: 0,
        profitFactor: 0
      }
    }

    const winningTrades = validTrades.filter(trade => trade.profit > 0)
    const losingTrades = validTrades.filter(trade => trade.profit < 0)

    const totalProfit = validTrades.reduce((sum, trade) => sum + Number(trade.profit), 0)
    const totalWinnings = winningTrades.reduce((sum, trade) => sum + Number(trade.profit), 0)
    const totalLosses = Math.abs(losingTrades.reduce((sum, trade) => sum + Number(trade.profit), 0))

    const largestWin = winningTrades.length > 0
      ? Math.max(...winningTrades.map(trade => Number(trade.profit)))
      : 0

    const largestLoss = losingTrades.length > 0
      ? Math.abs(Math.min(...losingTrades.map(trade => Number(trade.profit))))
      : 0

    const averageWin = winningTrades.length > 0
      ? totalWinnings / winningTrades.length
      : 0

    const averageLoss = losingTrades.length > 0
      ? totalLosses / losingTrades.length
      : 0

    const profitFactor = totalLosses > 0
      ? totalWinnings / totalLosses
      : totalWinnings > 0 ? Infinity : 0

    // Log the calculated metrics for debugging
    const metrics = {
      totalTrades: validTrades.length,
      winningTrades: winningTrades.length,
      losingTrades: losingTrades.length,
      winRate: validTrades.length > 0 ? (winningTrades.length / validTrades.length) * 100 : 0,
      totalProfit,
      largestWin,
      largestLoss,
      averageWin,
      averageLoss,
      profitFactor
    };

    console.log(`Calculated metrics for ${format(date, "yyyy-MM-dd")}:`, metrics);
    return metrics;
  }

  // Calculate metrics and get valid trades for the chart
  const { metrics, validTrades } = (() => {
    const calculatedMetrics = calculateDailyMetrics();

    // Get valid trades for this date - combine and deduplicate
    const allTradesSet = new Set();
    const allTrades = [...resolvedTrades, ...directlyFetchedTrades].filter(trade => {
      if (!trade.id || allTradesSet.has(trade.id)) return false;
      allTradesSet.add(trade.id);
      return true;
    });

    // Remove duplicates by trade ID
    const uniqueTradeIds = new Set();
    const combinedTrades = allTrades.filter(trade => {
      if (!trade.id || uniqueTradeIds.has(trade.id)) return false;
      uniqueTradeIds.add(trade.id);
      return true;
    });

    // Filter trades for this date
    const filteredTrades = combinedTrades.filter(trade => {
      try {
        // Check if profit is valid
        if (trade.profit === undefined || trade.profit === null || isNaN(Number(trade.profit))) {
          return false;
        }

        // Verify the trade date matches our date
        const tradeDate = new Date(trade.time_close);
        return areDatesEqual(tradeDate, date);
      } catch (error) {
        return false;
      }
    });

    return { metrics: calculatedMetrics, validTrades: filteredTrades };
  })()

  // Format currency values
  const formatCurrency = (value: number) => {
    if (value === 0) return "$0.00"

    return value > 0
      ? `$${value.toFixed(2)}`
      : `$-${Math.abs(value).toFixed(2)}`
  }

  return (
    <Card className={cn(
      "w-full relative overflow-hidden rounded-lg border-l-[3px]",
      "border-l-purple-600/80 dark:border-l-white/20",
      className
    )}>
      <Collapsible open={isOpen} onOpenChange={setIsOpen}>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center space-x-2">
              <CollapsibleTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0 text-muted-foreground hover:text-foreground transition-colors"
                >
                  {isOpen ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                </Button>
              </CollapsibleTrigger>
              <div className="flex items-center space-x-2">
                <Calendar className="h-4 w-4 text-purple-500 dark:text-muted-foreground" />
                <div>
                  <CardTitle className="text-base flex items-center">
                    Trading Journal: {format(date, "MMMM d, yyyy")}
                    {isLoadingTrades && (
                      <div className="ml-2 animate-spin text-muted-foreground">
                        <svg className="h-3 w-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                      </div>
                    )}
                  </CardTitle>
                  <div className="text-xs text-muted-foreground mt-0.5 flex items-center">
                    {!isOpen && (
                      <span className="flex items-center">
                        <ChevronDown className="h-3 w-3 mr-1 inline" />
                        Expand to {(journalEntry.note || journalEntry.screenshots.length > 0) ? "view details" : "add notes & screenshots"}
                      </span>
                    )}
                    {isOpen && (journalEntry.note || journalEntry.screenshots.length > 0) && (
                      <span>
                        {journalEntry.note && journalEntry.screenshots.length > 0
                          ? "Contains notes and screenshots"
                          : journalEntry.note
                            ? "Contains notes"
                            : journalEntry.screenshots.length > 0
                              ? `Contains ${journalEntry.screenshots.length} screenshot${journalEntry.screenshots.length > 1 ? 's' : ''}`
                              : ""}
                      </span>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {isEditing && (
              <div className="flex space-x-1">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleSave}
                  className="h-8 text-emerald-500"
                >
                  <Save className="h-4 w-4 mr-1" />
                  Save
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleCancel}
                  className="h-8 text-rose-500"
                >
                  <X className="h-4 w-4 mr-1" />
                  Cancel
                </Button>
              </div>
            )}
          </div>
        </CardHeader>

        <CardContent className="pb-3">
          {/* Daily Summary with Chart */}
          <div className="flex flex-col md:flex-row gap-4">
            {/* P&L Chart on the left */}
            <div className="md:w-1/3 h-[120px] bg-muted/20 rounded-md overflow-hidden">
              <JournalPnLChart trades={validTrades} />
            </div>

            {/* Metrics on the right */}
            <div className="md:w-2/3 grid grid-cols-2 md:grid-cols-3 gap-3">
              <div className="dark:bg-muted/30 bg-background p-3 rounded-md">
                <div className="flex items-center justify-between">
                  <div className="text-xs text-muted-foreground">Total Trades</div>
                  <BarChart3 className="h-3.5 w-3.5 text-muted-foreground" />
                </div>
                <div className="text-lg font-medium">{metrics.totalTrades}</div>
              </div>

              <div className="dark:bg-muted/30 bg-background p-3 rounded-md">
                <div className="flex items-center justify-between">
                  <div className="text-xs text-muted-foreground">Win Rate</div>
                  <TrendingUp className="h-3.5 w-3.5 text-muted-foreground" />
                </div>
                <div className="text-lg font-medium">{metrics.winRate.toFixed(1)}%</div>
              </div>

              <div className="dark:bg-muted/30 bg-background p-3 rounded-md">
                <div className="flex items-center justify-between">
                  <div className="text-xs text-muted-foreground">Total P&L</div>
                  <DollarSign className="h-3.5 w-3.5 text-muted-foreground" />
                </div>
                <div className={cn(
                  "text-lg font-medium",
                  metrics.totalProfit > 0 ? "text-emerald-500" :
                  metrics.totalProfit < 0 ? "text-rose-500" : ""
                )}>
                  {formatCurrency(metrics.totalProfit)}
                </div>
              </div>

              <div className="dark:bg-muted/30 bg-background p-3 rounded-md">
                <div className="flex items-center justify-between">
                  <div className="text-xs text-muted-foreground">Avg Win/Loss</div>
                  <TrendingDown className="h-3.5 w-3.5 text-muted-foreground" />
                </div>
                <div className="text-lg font-medium">
                  {metrics.averageLoss > 0
                    ? (metrics.averageWin / metrics.averageLoss).toFixed(2)
                    : metrics.averageWin > 0 ? "∞" : "0.00"}
                </div>
              </div>

              <div className="dark:bg-muted/30 bg-background p-3 rounded-md">
                <div className="flex items-center justify-between">
                  <div className="text-xs text-muted-foreground">Volume</div>
                  <BarChart3 className="h-3.5 w-3.5 text-muted-foreground" />
                </div>
                <div className="text-lg font-medium">
                  {validTrades.reduce((sum, trade) => {
                    const volume = Number(trade.volume || 0);
                    return isNaN(volume) ? sum : sum + volume;
                  }, 0).toFixed(2)}
                </div>
              </div>

              <div className="dark:bg-muted/30 bg-background p-3 rounded-md">
                <div className="flex items-center justify-between">
                  <div className="text-xs text-muted-foreground">Profit Factor</div>
                  <TrendingUp className="h-3.5 w-3.5 text-muted-foreground" />
                </div>
                <div className="text-lg font-medium">
                  {metrics.profitFactor.toFixed(2)}
                </div>
              </div>
            </div>
          </div>
        </CardContent>

        <CollapsibleContent>
          <div className="border-t px-4 py-3">
            {/* Journal Notes */}
            {isEditing ? (
              <div className="space-y-4 mb-6">
                <div className="space-y-2">
                  <div className="text-sm font-medium">Trading Notes</div>
                  <Textarea
                    value={journalEntry.note}
                    onChange={(e) => handleNoteChange(e.target.value)}
                    placeholder="Add your trading notes, observations, and lessons learned..."
                    className="min-h-[100px]"
                  />
                </div>

                <div className="space-y-2">
                  <div className="text-sm font-medium flex items-center">
                    <Tag className="h-3.5 w-3.5 mr-1.5 text-purple-500" />
                    Tags
                  </div>
                  <TagInput
                    value={journalEntry.tags}
                    onChange={handleTagsChange}
                    suggestions={availableTags}
                    placeholder="Add tags..."
                    className="w-full"
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    Tags added here will be used to filter both daily journals and trade-specific journals.
                  </p>
                  <p className="text-xs text-muted-foreground">
                    Add tags to categorize your journal entries (e.g., "breakout", "trend-following", "psychology")
                  </p>
                </div>
              </div>
            ) : (
              <div className="space-y-4 mb-6">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="text-sm font-medium">Trading Notes</div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setIsEditing(true)}
                      className="h-8"
                    >
                      {journalEntry.note ? "Edit Notes" : "Add Notes"}
                    </Button>
                  </div>

                  {journalEntry.note ? (
                    <div className="text-sm bg-muted/30 p-3 rounded-md whitespace-pre-wrap">
                      {journalEntry.note}
                    </div>
                  ) : (
                    <div className="text-sm text-muted-foreground italic p-3 border border-dashed border-muted rounded-md">
                      No notes added yet. Click "Add Notes" to record your observations about this trading day.
                    </div>
                  )}
                </div>

                {journalEntry.tags && journalEntry.tags.length > 0 && (
                  <div className="space-y-2">
                    <div className="flex items-center">
                      <Tag className="h-3.5 w-3.5 mr-1.5 text-purple-500" />
                      <div className="text-sm font-medium">Tags</div>
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {journalEntry.tags.map((tag, index) => (
                        <Badge
                          key={index}
                          variant="outline"
                          className="bg-purple-50 text-purple-700 border-purple-200 hover:bg-purple-100 dark:bg-purple-900/20 dark:text-purple-300 dark:border-purple-800/50"
                        >
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Screenshots */}
            <div className="space-y-2 mb-6">
              <div className="flex items-center justify-between">
                <div className="text-sm font-medium">Screenshots</div>
                <div className="flex items-center">
                  {isUploading && (
                    <div className="mr-2 w-24 h-2 bg-muted rounded-full overflow-hidden">
                      <div
                        className="h-full bg-primary transition-all duration-300 ease-out"
                        style={{ width: `${uploadProgress}%` }}
                      />
                    </div>
                  )}
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => fileInputRef.current?.click()}
                    disabled={isUploading}
                    className="h-8 text-xs"
                  >
                    {isUploading ? (
                      <>
                        <div className="h-3.5 w-3.5 mr-1.5 animate-spin rounded-full border-2 border-current border-t-transparent" />
                        {Math.round(uploadProgress)}%
                      </>
                    ) : (
                      <>
                        <ImageIcon className="h-3.5 w-3.5 mr-1.5" />
                        Add Screenshot
                      </>
                    )}
                  </Button>
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept="image/*"
                    className="hidden"
                    onChange={handleImageUpload}
                    disabled={isUploading}
                  />
                </div>
              </div>

              {journalEntry.screenshots.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {journalEntry.screenshots.map((screenshot, index) => (
                    <div key={index} className="relative group">
                      <ImageDisplay
                        src={screenshot}
                        alt={`Trading screenshot ${index + 1}`}
                        aspectRatio="video"
                        lightboxGroup={journalEntry.screenshots}
                        lightboxIndex={index}
                      />
                      <Button
                        variant="destructive"
                        size="icon"
                        className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity h-7 w-7 z-10"
                        onClick={(e) => {
                          e.stopPropagation();
                          removeScreenshot(index);
                        }}
                      >
                        <Trash2 className="h-3.5 w-3.5" />
                      </Button>
                    </div>
                  ))}
                </div>
              ) : null}
            </div>

            <h4 className="text-sm font-medium mb-3">Detailed Trading Activity</h4>

            {/* Display the valid trades in the detailed trading activity */}
            {(() => {
              return validTrades.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="w-full text-sm">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left py-2 px-2 font-medium">Symbol</th>
                        <th className="text-left py-2 px-2 font-medium">Type</th>
                        <th className="text-left py-2 px-2 font-medium">Strategy</th>
                        <th className="text-left py-2 px-2 font-medium">Entry Time</th>
                        <th className="text-left py-2 px-2 font-medium">Exit Time</th>
                        <th className="text-right py-2 px-2 font-medium">Size</th>
                        <th className="text-right py-2 px-2 font-medium">Entry</th>
                        <th className="text-right py-2 px-2 font-medium">Exit</th>
                        <th className="text-right py-2 px-2 font-medium">P&L</th>
                      </tr>
                    </thead>
                    <tbody>
                      {validTrades.map((trade, index) => (
                      <tr key={index} className="border-b last:border-b-0 hover:bg-muted/30">
                        <td className="py-2 px-2">{trade.symbol}</td>
                        <td className="py-2 px-2">
                          <span className={cn(
                            "px-2 py-0.5 rounded-full text-xs",
                            trade.type === "buy" ? "bg-emerald-100 text-emerald-700 dark:bg-emerald-900/30 dark:text-emerald-400" :
                            "bg-rose-100 text-rose-700 dark:bg-rose-900/30 dark:text-rose-400"
                          )}>
                            {trade.type === "buy" ? "Long" : "Short"}
                          </span>
                        </td>
                        <td className="py-2 px-2">
                          {trade.strategy_id ? (
                            <div className="inline-flex items-center px-2 py-0.5 rounded-md text-xs font-medium bg-gradient-to-r from-purple-50 to-indigo-50 dark:from-purple-900/30 dark:to-indigo-900/30 text-purple-700 dark:text-foreground border border-purple-200 dark:border-purple-700/50 shadow-sm">
                              <BookOpen className="h-3 w-3 mr-1 text-purple-500 dark:text-purple-400" />
                              {strategyMap[trade.strategy_id] || "Unknown Strategy"}
                            </div>
                          ) : (
                            <span className="text-xs text-muted-foreground">—</span>
                          )}
                        </td>
                        <td className="py-2 px-2">{format(new Date(trade.time_open), "HH:mm:ss")}</td>
                        <td className="py-2 px-2">{format(new Date(trade.time_close), "HH:mm:ss")}</td>
                        <td className="py-2 px-2 text-right">{parseFloat(trade.volume).toFixed(2)}</td>
                        <td className="py-2 px-2 text-right">{trade.price_open.toFixed(2)}</td>
                        <td className="py-2 px-2 text-right">{trade.price_close.toFixed(2)}</td>
                        <td className={cn(
                          "py-2 px-2 text-right font-medium",
                          trade.profit > 0 ? "text-emerald-500" :
                          trade.profit < 0 ? "text-rose-500" : ""
                        )}>
                          {formatCurrency(trade.profit)}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                No trading activity on this day
              </div>
            );
            })()}
          </div>
        </CollapsibleContent>
      </Collapsible>
    </Card>
  )
}
