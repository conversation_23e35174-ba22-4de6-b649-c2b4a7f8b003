'use server'

import { cookies } from 'next/headers';
import { createServerClient } from '@supabase/ssr';
import type { Database } from '@/types/supabase';
import { format } from 'date-fns';
import { DailyJournalEntry } from './types';

// Cache storage with expiration
type CacheEntry = {
  data: any;
  expiry: number;
};

// Cache duration in milliseconds (5 minutes)
const CACHE_DURATION = 5 * 60 * 1000;
const cacheStore: Map<string, CacheEntry> = new Map();

// Helper function to get cached response
async function getCachedResponse(key: string) {
  const cached = cacheStore.get(key);
  const now = Date.now();

  if (cached && cached.expiry > now) {
    console.log(`Cache hit for key: ${key}`);
    return cached.data;
  }

  // Clean up expired entries
  if (cached && cached.expiry <= now) {
    cacheStore.delete(key);
  }

  return null;
}

// Helper function to set cached response
async function setCachedResponse(key: string, data: any) {
  cacheStore.set(key, {
    data,
    expiry: Date.now() + CACHE_DURATION
  });

  // Limit cache size to prevent memory issues
  if (cacheStore.size > 100) {
    // Delete oldest entry
    const oldestKey = cacheStore.keys().next().value;
    if (oldestKey) {
      cacheStore.delete(oldestKey);
    }
  }
}

// Helper function to calculate trading statistics
function calculateTradingStats(trades: any[]) {
  if (!trades || trades.length === 0) {
    return {
      totalTrades: 0,
      winningTrades: 0,
      losingTrades: 0,
      winRate: 0,
      totalProfit: 0,
      averageProfit: 0,
      largestWin: 0,
      largestLoss: 0,
      totalVolume: 0,
      avgWinLossRatio: 0,
      profitFactor: 0,
      grossProfit: 0,
      grossLoss: 0,
      avgWin: 0,
      avgLoss: 0,
    };
  }

  const winningTrades = trades.filter(trade => Number(trade.profit) > 0);
  const losingTrades = trades.filter(trade => Number(trade.profit) < 0);
  const totalProfit = trades.reduce((sum, trade) => sum + (Number(trade.profit) || 0), 0);
  const totalVolume = trades.reduce((sum, trade) => sum + (Number(trade.volume) || 0), 0);

  const grossProfit = winningTrades.reduce((sum, trade) => sum + (Number(trade.profit) || 0), 0);
  const grossLoss = Math.abs(losingTrades.reduce((sum, trade) => sum + (Number(trade.profit) || 0), 0));

  const avgWin = winningTrades.length > 0 ? grossProfit / winningTrades.length : 0;
  const avgLoss = losingTrades.length > 0 ? grossLoss / losingTrades.length : 0;

  const largestWin = winningTrades.length > 0
    ? Math.max(...winningTrades.map(trade => Number(trade.profit) || 0))
    : 0;
  const largestLoss = losingTrades.length > 0
    ? Math.min(...losingTrades.map(trade => Number(trade.profit) || 0))
    : 0;

  // Calculate profit factor (gross profit / gross loss)
  const profitFactor = grossLoss > 0 ? grossProfit / grossLoss : grossProfit > 0 ? Infinity : 0;

  // Calculate average win/loss ratio
  const avgWinLossRatio = avgLoss > 0 ? avgWin / avgLoss : avgWin > 0 ? Infinity : 0;

  return {
    totalTrades: trades.length,
    winningTrades: winningTrades.length,
    losingTrades: losingTrades.length,
    winRate: trades.length > 0 ? (winningTrades.length / trades.length) * 100 : 0,
    totalProfit,
    averageProfit: trades.length > 0 ? totalProfit / trades.length : 0,
    largestWin,
    largestLoss,
    totalVolume,
    avgWinLossRatio,
    profitFactor,
    grossProfit,
    grossLoss,
    avgWin,
    avgLoss,
  };
}

/**
 * Fetch a daily journal entry for a specific date
 */
export async function fetchDailyJournalEntry(
  userId: string,
  date: string,
  accountId: string | null
) {
  // Create a cache key based on the parameters
  const cacheKey = `daily-journal-entry-${userId}-${date}-${accountId}`;

  // Check if we have a cached response
  const cachedResponse = await getCachedResponse(cacheKey);
  if (cachedResponse) {
    return cachedResponse;
  }

  const cookieStore = await cookies();
  const supabase = createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name) {
          return cookieStore.get(name)?.value;
        },
        set(_name, _value, _options) {
          // Server actions can't set cookies directly
        },
        remove(_name, _options) {
          // Server actions can't remove cookies directly
        }
      }
    }
  );

  console.log(`Fetching journal entry for date: ${date}, accountId: ${accountId || 'none'}`);

  // Build the query to fetch the journal entry
  let query = supabase
    .from('daily_journal_entries')
    .select('*')
    .eq('user_id', userId)
    .eq('date', date);

  // Add account filter if provided
  if (accountId && accountId !== 'null' && accountId !== 'undefined') {
    query = query.eq('account_id', accountId);
  } else {
    query = query.is('account_id', null);
  }

  // Execute the query
  const { data: journalEntry, error: entryError } = await query.maybeSingle();

  if (entryError) {
    console.error('Error fetching journal entry:', entryError);
    throw new Error(entryError.message);
  }

  // Fetch trades for this date to include in the response, with strategy information
  const tradesQuery = supabase
    .from('trades')
    .select(`
      *,
      strategies (
        id,
        name
      )
    `)
    .eq('user_id', userId)
    .gte('time_close', `${date}T00:00:00`)
    .lt('time_close', `${date}T23:59:59.999`);

  // Add account filter if provided
  if (accountId && accountId !== 'null' && accountId !== 'undefined') {
    tradesQuery.eq('account_id', accountId);
  }

  const { data: trades, error: tradesError } = await tradesQuery;

  if (tradesError) {
    console.error('Error fetching trades:', tradesError);
    // Continue without trades data
  }

  // Calculate trading statistics
  const stats = calculateTradingStats(trades || []);

  const result = {
    entry: journalEntry || null,
    trades: trades || [],
    stats,
  };

  // Cache the result
  await setCachedResponse(cacheKey, result);

  return result;
}

/**
 * Save a daily journal entry
 */
export async function saveDailyJournalEntry(
  userId: string,
  date: string,
  accountId: string | null,
  note: string,
  tags: string[],
  screenshots: string[]
) {
  const cookieStore = await cookies();
  const supabase = createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name) {
          return cookieStore.get(name)?.value;
        },
        set(_name, _value, _options) {
          // Server actions can't set cookies directly
        },
        remove(_name, _options) {
          // Server actions can't remove cookies directly
        }
      }
    }
  );

  // Check if a daily journal entry already exists for this date and account
  const { data: existingEntry, error: fetchError } = await supabase
    .from('daily_journal_entries')
    .select('id')
    .eq('user_id', userId)
    .eq('date', date)
    .eq('account_id', accountId || null)
    .maybeSingle();

  if (fetchError) {
    console.error('Error checking for existing daily journal entry:', fetchError);
    throw new Error(fetchError.message);
  }

  let result;
  if (existingEntry) {
    // Update existing entry
    const { data, error } = await supabase
      .from('daily_journal_entries')
      .update({
        note,
        screenshots,
        tags,
        updated_at: new Date().toISOString()
      })
      .eq('id', existingEntry.id)
      .select()
      .single();

    if (error) {
      console.error('Error updating daily journal entry:', error);
      throw new Error(error.message);
    }

    result = data;
  } else {
    // Create new entry
    const { data, error } = await supabase
      .from('daily_journal_entries')
      .insert({
        user_id: userId,
        date,
        account_id: accountId || null,
        note,
        screenshots,
        tags
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating daily journal entry:', error);
      throw new Error(error.message);
    }

    result = data;
  }

  // Invalidate cache
  const cacheKey = `daily-journal-entry-${userId}-${date}-${accountId}`;
  cacheStore.delete(cacheKey);

  return result;
}
