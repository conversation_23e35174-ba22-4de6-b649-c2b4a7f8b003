import { redirect } from 'next/navigation';
import { getSupabaseServer } from '@/lib/supabase-server';
import { getUserAccounts } from '@/lib/data/account-data';
import { getUserTrades } from '@/lib/data/trade-data';
import SymbolDetailClient from './client';

type SymbolDetailPageProps = {
  params: Promise<{
    symbol: string;
  }>;
};

export default async function SymbolDetailPage({ params }: SymbolDetailPageProps) {
  const { symbol } = await params;

  // Get authenticated user
  const supabase = await getSupabaseServer();
  const { data: { user }, error: userError } = await supabase.auth.getUser();
  if (userError || !user) {
    redirect('/login');
  }

  const userId = user.id;

  // Fetch user accounts using our standardized data access layer
  const accounts = await getUserAccounts(userId, { useServer: true });
  if (!accounts || accounts.length === 0) {
    console.log('No trading accounts found for user');
  }

  // Get the first account ID (if any)
  // Note: The actual selected account will be determined by the client component
  // using the account context, but we provide initial data for the first account
  const initialAccountId = accounts && accounts.length > 0 ? accounts[0].id : null;

  // Fetch initial trades for the first account and the specific symbol
  let initialTrades: any[] = [];
  if (initialAccountId) {
    initialTrades = await getUserTrades(userId, {
      accountId: initialAccountId,
      symbol,
      useServer: true
    });
  }

  // Fetch strategies
  const { data: strategies, error: strategiesError } = await supabase
    .from("strategies")
    .select("*")
    .eq("user_id", userId);

  if (strategiesError) {
    console.error('Error fetching strategies:', strategiesError);
  }

  // Create a map of strategy IDs to names for quick lookup
  const strategyMap = new Map();
  if (strategies) {
    strategies.forEach(strategy => {
      strategyMap.set(strategy.id, strategy.name);
    });
  }

  // Add strategy name to each trade
  const tradesWithStrategyNames = initialTrades.map((trade, index) => ({
    ...trade,
    id: trade.id?.toString() || trade.position_id?.toString() || `trade-${Date.now()}-${index}-${Math.floor(Math.random() * 1000000)}`,
    strategy_name: trade.strategy_id ? strategyMap.get(trade.strategy_id) : null
  }));

  // Pass the fetched data to the client component
  return (
    <SymbolDetailClient
      userId={userId}
      symbol={symbol}
      initialTrades={tradesWithStrategyNames}
      initialAccountId={initialAccountId}
    />
  );
}
