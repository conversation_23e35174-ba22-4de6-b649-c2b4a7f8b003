"use client"

/**
 * This utility fixes the issue with aria-hidden being applied to elements that contain focused elements.
 * It monitors focus events and removes aria-hidden attributes from ancestors of focused elements.
 */
export function initFocusTrapFix() {
  if (typeof window === 'undefined') return;

  // Function to check if an element is hidden by aria-hidden
  const isHiddenByAriaHidden = (element: HTMLElement): boolean => {
    return element.getAttribute('aria-hidden') === 'true' || 
           element.getAttribute('data-aria-hidden') === 'true';
  };

  // Function to find all ancestors with aria-hidden="true"
  const findHiddenAncestors = (element: HTMLElement): HTMLElement[] => {
    const ancestors: HTMLElement[] = [];
    let current = element.parentElement;
    
    while (current) {
      if (isHiddenByAriaHidden(current)) {
        ancestors.push(current);
      }
      current = current.parentElement;
    }
    
    return ancestors;
  };

  // Function to temporarily remove aria-hidden from ancestors
  const removeAriaHiddenFromAncestors = (ancestors: HTMLElement[]): (() => void) => {
    // Store original values to restore later
    const originalValues = ancestors.map(ancestor => ({
      element: ancestor,
      ariaHidden: ancestor.getAttribute('aria-hidden'),
      dataAriaHidden: ancestor.getAttribute('data-aria-hidden')
    }));
    
    // Remove aria-hidden attributes
    ancestors.forEach(ancestor => {
      if (ancestor.getAttribute('aria-hidden') === 'true') {
        ancestor.setAttribute('aria-hidden', 'false');
      }
      if (ancestor.getAttribute('data-aria-hidden') === 'true') {
        ancestor.setAttribute('data-aria-hidden', 'false');
      }
    });
    
    // Return function to restore original values
    return () => {
      originalValues.forEach(({ element, ariaHidden, dataAriaHidden }) => {
        if (ariaHidden !== null) {
          element.setAttribute('aria-hidden', ariaHidden);
        }
        if (dataAriaHidden !== null) {
          element.setAttribute('data-aria-hidden', dataAriaHidden);
        }
      });
    };
  };

  // Handle focus events
  const handleFocus = (event: FocusEvent) => {
    const target = event.target as HTMLElement;
    if (!target) return;
    
    // Find ancestors with aria-hidden="true"
    const hiddenAncestors = findHiddenAncestors(target);
    
    if (hiddenAncestors.length > 0) {
      console.log('Found focused element inside aria-hidden ancestor, fixing...', target);
      
      // Remove aria-hidden from ancestors
      const restoreFunction = removeAriaHiddenFromAncestors(hiddenAncestors);
      
      // Set up a one-time blur handler to restore aria-hidden
      const handleBlur = () => {
        // Small delay to allow for focus to move to another element
        setTimeout(() => {
          // Only restore if the document.activeElement is not inside any of the ancestors
          const activeElement = document.activeElement as HTMLElement;
          const isInsideAncestor = hiddenAncestors.some(ancestor => 
            ancestor.contains(activeElement)
          );
          
          if (!isInsideAncestor) {
            restoreFunction();
            target.removeEventListener('blur', handleBlur);
          }
        }, 0);
      };
      
      target.addEventListener('blur', handleBlur, { once: true });
    }
  };

  // Add focus event listener to document
  document.addEventListener('focus', handleFocus, true);
  
  // Return cleanup function
  return () => {
    document.removeEventListener('focus', handleFocus, true);
  };
}
