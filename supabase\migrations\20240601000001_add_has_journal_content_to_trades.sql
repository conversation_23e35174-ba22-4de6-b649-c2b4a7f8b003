-- Add has_journal_content and tags columns to trades table
DO $$
BEGIN
    -- Check if has_journal_content column exists, if not add it
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_schema = 'public'
        AND table_name = 'trades'
        AND column_name = 'has_journal_content'
    ) THEN
        ALTER TABLE public.trades ADD COLUMN has_journal_content BOOLEAN DEFAULT FALSE;
    END IF;

    -- Check if tags column exists, if not add it
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_schema = 'public'
        AND table_name = 'trades'
        AND column_name = 'tags'
    ) THEN
        ALTER TABLE public.trades ADD COLUMN tags TEXT[] DEFAULT '{}';
    END IF;
END
$$;

-- Update the update_trade_details function to include the new parameters
CREATE OR REPLACE FUNCTION public.update_trade_details(
  p_trade_id UUID,
  p_strategy_id UUID,
  p_setup_id UUID,
  p_notes TEXT,
  p_screenshots TEXT[],
  p_followed_rules TEXT[],
  p_followed_setup_criteria TEXT[],
  p_tags TEXT[],
  p_has_journal_content BOOLEAN
) RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  -- Update the trade with the provided details
  UPDATE public.trades
  SET
    strategy_id = p_strategy_id,
    setup_id = p_setup_id,
    notes = p_notes,
    screenshots = p_screenshots,
    followed_rules = p_followed_rules,
    followed_setup_criteria = p_followed_setup_criteria,
    tags = p_tags,
    has_journal_content = p_has_journal_content,
    updated_at = NOW()
  WHERE id = p_trade_id;

  -- Return true if the update was successful
  RETURN FOUND;
END;
$$;
