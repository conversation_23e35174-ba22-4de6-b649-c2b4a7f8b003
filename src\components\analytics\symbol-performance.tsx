"use client"

import { useState, useMemo } from "react"
import { Trade } from "@/types/trade"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { BarChart3, DollarSign, <PERSON>h, <PERSON>cent as PercentIcon, <PERSON><PERSON><PERSON> as Pie<PERSON>hartIcon, Scale, TrendingUp } from "lucide-react"
import { Ta<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, Cell, ReferenceLine } from "recharts"
import { Pie<PERSON>hart, Pie, Sector } from "recharts"
import { cn } from "@/lib/utils"

interface SymbolPerformanceProps {
  trades: Trade[]
}

type MetricType = "profit" | "winRate" | "tradeCount" | "averageProfit" | "profitFactor"

export function SymbolPerformance({ trades }: SymbolPerformanceProps) {
  const [selectedMetric, setSelectedMetric] = useState<MetricType>("profit")
  const [chartType, setChartType] = useState<"bar" | "pie">("bar")
  const [activeIndex, setActiveIndex] = useState(0)
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc")
  const [timeRange, setTimeRange] = useState<"all" | "1m" | "3m" | "6m" | "1y">("all")

  // Filter trades by time range
  const filteredTrades = useMemo(() => {
    if (timeRange === "all") return trades

    const now = new Date()
    let startDate = new Date()

    switch (timeRange) {
      case "1m":
        startDate.setMonth(now.getMonth() - 1)
        break
      case "3m":
        startDate.setMonth(now.getMonth() - 3)
        break
      case "6m":
        startDate.setMonth(now.getMonth() - 6)
        break
      case "1y":
        startDate.setFullYear(now.getFullYear() - 1)
        break
    }

    return trades.filter(trade => new Date(trade.time_close) >= startDate)
  }, [trades, timeRange])

  // Helper function to get the value for the selected metric
  const getMetricValue = (data: any, metric: MetricType) => {
    switch (metric) {
      case "profit":
        return data.totalProfit
      case "winRate":
        return data.winRate
      case "tradeCount":
        return data.tradeCount
      case "averageProfit":
        return data.averageProfit
      case "profitFactor":
        return data.profitFactor === Infinity ? 999999 : data.profitFactor
      default:
        return 0
    }
  }

  // Group trades by symbol
  const symbolData = useMemo(() => {
    const symbolMap = new Map<string, {
      symbol: string
      totalProfit: number
      wins: number
      losses: number
      tradeCount: number
      totalVolume: number
    }>()

    filteredTrades.forEach(trade => {
      const symbol = trade.symbol
      const isWin = trade.profit > 0

      if (!symbolMap.has(symbol)) {
        symbolMap.set(symbol, {
          symbol,
          totalProfit: 0,
          wins: 0,
          losses: 0,
          tradeCount: 0,
          totalVolume: 0
        })
      }

      const data = symbolMap.get(symbol)!
      data.totalProfit += trade.profit
      data.tradeCount += 1
      data.totalVolume += trade.volume

      if (isWin) {
        data.wins += 1
      } else {
        data.losses += 1
      }
    })

    // Convert map to array and calculate additional metrics
    return Array.from(symbolMap.values())
      .map(data => ({
        ...data,
        winRate: data.tradeCount > 0 ? (data.wins / data.tradeCount) * 100 : 0,
        averageProfit: data.tradeCount > 0 ? data.totalProfit / data.tradeCount : 0,
        profitFactor: (() => {
          // Calculate gross profit and gross loss
          const grossProfit = filteredTrades
            .filter(t => t.symbol === data.symbol && t.profit > 0)
            .reduce((sum, t) => sum + t.profit, 0);

          const grossLoss = Math.abs(filteredTrades
            .filter(t => t.symbol === data.symbol && t.profit < 0)
            .reduce((sum, t) => sum + t.profit, 0));

          // Calculate profit factor correctly
          const profitFactor = grossLoss > 0 ? grossProfit / grossLoss : grossProfit > 0 ? Infinity : 0;

          return profitFactor;
        })()
      }))
      .sort((a, b) => {
        const aValue = getMetricValue(a, selectedMetric)
        const bValue = getMetricValue(b, selectedMetric)
        return sortOrder === "desc" ? bValue - aValue : aValue - bValue
      })
      .slice(0, 10) // Top 10 symbols
  }, [filteredTrades, selectedMetric, sortOrder])



  // Format the metric value for display
  const formatMetricValue = (value: number, metric: MetricType) => {
    switch (metric) {
      case "profit":
        return `$${value.toFixed(2)}`
      case "winRate":
        return `${value.toFixed(2)}%`
      case "tradeCount":
        // For trade count, we want whole numbers
        return Math.round(value).toString()
      case "averageProfit":
        return `$${value.toFixed(2)}`
      case "profitFactor":
        return value === Infinity ? "∞" : value.toFixed(2)
      default:
        return value.toFixed(2)
    }
  }

  // Get the label for the selected metric
  const getMetricLabel = (metric: MetricType) => {
    switch (metric) {
      case "profit":
        return "Total Profit"
      case "winRate":
        return "Win Rate (%)"
      case "tradeCount":
        return "Number of Trades"
      case "averageProfit":
        return "Average Profit per Trade"
      case "profitFactor":
        return "Profit Factor"
      default:
        return ""
    }
  }

  // Custom tooltip for the bar chart
  const CustomTooltip = ({ active, payload }: { active?: boolean, payload?: any[] }) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload

      // Ensure all values are formatted with exactly 2 decimal places
      const formatValue = (value: number, prefix: string = '') => {
        if (value === Infinity) return '∞';
        return `${prefix}${value.toFixed(2)}`;
      };

      return (
        <div className="bg-background border rounded-md p-3 shadow-md">
          <p className="font-medium">{data.symbol}</p>
          <p className="text-sm text-muted-foreground">
            {getMetricLabel(selectedMetric)}: {
              selectedMetric === "profit" || selectedMetric === "averageProfit"
                ? `$${formatValue(data[selectedMetric === "profit" ? "totalProfit" : "averageProfit"])}`
                : selectedMetric === "winRate"
                  ? `${formatValue(data.winRate)}%`
                  : selectedMetric === "tradeCount"
                    ? Math.round(data.tradeCount)
                    : formatValue(data[selectedMetric])
            }
          </p>
          <p className="text-sm text-muted-foreground">Win Rate: {formatValue(data.winRate)}%</p>
          <p className="text-sm text-muted-foreground">Trades: {Math.round(data.tradeCount)}</p>
          <p className="text-sm text-muted-foreground">Avg Profit: ${formatValue(data.averageProfit)}</p>
          <p className="text-sm text-muted-foreground">Profit Factor: {formatValue(data.profitFactor)}</p>
        </div>
      )
    }
    return null
  }

  // Custom active shape for the pie chart
  const renderActiveShape = (props: any) => {
    const { cx, cy, midAngle, innerRadius, outerRadius, startAngle, endAngle, fill, payload, percent, value } = props
    const sin = Math.sin(-midAngle * Math.PI / 180)
    const cos = Math.cos(-midAngle * Math.PI / 180)
    const sx = cx + (outerRadius + 10) * cos
    const sy = cy + (outerRadius + 10) * sin
    const mx = cx + (outerRadius + 30) * cos
    const my = cy + (outerRadius + 30) * sin
    const ex = mx + (cos >= 0 ? 1 : -1) * 22
    const ey = my
    const textAnchor = cos >= 0 ? 'start' : 'end'

    return (
      <g>
        <Sector
          cx={cx}
          cy={cy}
          innerRadius={innerRadius}
          outerRadius={outerRadius}
          startAngle={startAngle}
          endAngle={endAngle}
          fill={fill}
        />
        <Sector
          cx={cx}
          cy={cy}
          startAngle={startAngle}
          endAngle={endAngle}
          innerRadius={outerRadius + 6}
          outerRadius={outerRadius + 10}
          fill={fill}
        />
        <path d={`M${sx},${sy}L${mx},${my}L${ex},${ey}`} stroke={fill} fill="none" />
        <circle cx={ex} cy={ey} r={2} fill={fill} stroke="none" />
        <text x={ex + (cos >= 0 ? 1 : -1) * 12} y={ey} textAnchor={textAnchor} fill="#333" className="text-xs">
          {payload.symbol}
        </text>
        <text x={ex + (cos >= 0 ? 1 : -1) * 12} y={ey} dy={18} textAnchor={textAnchor} fill="#999" className="text-xs">
          {(() => {
            // Format the value consistently with 2 decimal places
            let formattedValue;
            if (selectedMetric === "profit" || selectedMetric === "averageProfit") {
              formattedValue = `$${Number(value).toFixed(2)}`;
            } else if (selectedMetric === "winRate") {
              formattedValue = `${Number(value).toFixed(2)}%`;
            } else if (selectedMetric === "profitFactor") {
              formattedValue = Number(value) === Infinity ? "∞" : Number(value).toFixed(2);
            } else if (selectedMetric === "tradeCount") {
              formattedValue = Math.round(Number(value)).toString();
            } else {
              formattedValue = Number(value).toFixed(2);
            }

            // Add negative sign if needed
            const prefix = payload.isNegative && (selectedMetric === "profit" || selectedMetric === "averageProfit") ? "-" : "";
            return `${prefix}${formattedValue} (${(percent * 100).toFixed(2)}%)`;
          })()}
        </text>
      </g>
    )
  }

  // Get the data for the selected chart type
  const chartData = useMemo(() => {
    // For pie charts with profit metrics, we need to handle negative values
    if (chartType === "pie" && (selectedMetric === "profit" || selectedMetric === "averageProfit")) {
      // Create separate datasets for positive and negative values
      const positiveData = symbolData
        .filter(data => {
          const value = selectedMetric === "profit" ? data.totalProfit : data.averageProfit;
          return value > 0;
        })
        .map(data => ({
          symbol: data.symbol,
          totalProfit: data.totalProfit,
          winRate: data.winRate,
          tradeCount: data.tradeCount,
          averageProfit: data.averageProfit,
          profitFactor: data.profitFactor === Infinity ? 999999 : data.profitFactor,
          wins: data.wins,
          losses: data.losses
        }));

      const negativeData = symbolData
        .filter(data => {
          const value = selectedMetric === "profit" ? data.totalProfit : data.averageProfit;
          return value < 0;
        })
        .map(data => ({
          symbol: data.symbol,
          totalProfit: Math.abs(data.totalProfit), // Use absolute value for pie chart
          winRate: data.winRate,
          tradeCount: data.tradeCount,
          averageProfit: Math.abs(data.averageProfit), // Use absolute value for pie chart
          profitFactor: data.profitFactor === Infinity ? 999999 : data.profitFactor,
          wins: data.wins,
          losses: data.losses,
          isNegative: true // Flag to identify negative values
        }));

      return [...positiveData, ...negativeData];
    }

    // For bar charts or non-profit metrics, return the data as is
    return symbolData.map(data => ({
      symbol: data.symbol,
      totalProfit: data.totalProfit,
      winRate: data.winRate,
      tradeCount: data.tradeCount,
      averageProfit: data.averageProfit,
      profitFactor: data.profitFactor === Infinity ? 999999 : data.profitFactor,
      wins: data.wins,
      losses: data.losses
    }));
  }, [symbolData, chartType, selectedMetric])

  // Get colors for the bars/pie slices based on profit
  const getBarColor = (data: any) => {
    // For pie charts with profit metrics, use the isNegative flag
    if (chartType === "pie" && (selectedMetric === "profit" || selectedMetric === "averageProfit")) {
      return data.isNegative ? "#ef4444" : "#10b981";
    }

    // For bar charts or other metrics
    const value = getMetricValue(data, selectedMetric);
    if (selectedMetric === "profit" || selectedMetric === "averageProfit") {
      return value >= 0 ? "#10b981" : "#ef4444";
    }
    return "#3b82f6";
  }

  // Handle pie chart sector click
  const onPieEnter = (_: any, index: number) => {
    setActiveIndex(index)
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row gap-4 justify-between">
        <div className="flex flex-col sm:flex-row gap-2">
          <Tabs value={selectedMetric} onValueChange={(value) => setSelectedMetric(value as MetricType)}>
            <TabsList className="grid w-[550px] grid-cols-5 rounded-none border-b bg-transparent">
              <TabsTrigger
                value="profit"
                className="rounded-none data-[state=active]:bg-background data-[state=active]:border-b data-[state=active]:border-primary data-[state=active]:font-medium"
              >
                <DollarSign className="mr-2 h-4 w-4" />
                Total Profit
              </TabsTrigger>
              <TabsTrigger
                value="winRate"
                className="rounded-none data-[state=active]:bg-background data-[state=active]:border-b data-[state=active]:border-primary data-[state=active]:font-medium"
              >
                <PercentIcon className="mr-2 h-4 w-4" />
                Win Rate
              </TabsTrigger>
              <TabsTrigger
                value="tradeCount"
                className="rounded-none data-[state=active]:bg-background data-[state=active]:border-b data-[state=active]:border-primary data-[state=active]:font-medium"
              >
                <Hash className="mr-2 h-4 w-4" />
                Trade Count
              </TabsTrigger>
              <TabsTrigger
                value="averageProfit"
                className="rounded-none data-[state=active]:bg-background data-[state=active]:border-b data-[state=active]:border-primary data-[state=active]:font-medium"
              >
                <TrendingUp className="mr-2 h-4 w-4" />
                Avg Profit
              </TabsTrigger>
              <TabsTrigger
                value="profitFactor"
                className="rounded-none data-[state=active]:bg-background data-[state=active]:border-b data-[state=active]:border-primary data-[state=active]:font-medium"
              >
                <Scale className="mr-2 h-4 w-4" />
                Profit Factor
              </TabsTrigger>
            </TabsList>
          </Tabs>

          <Select value={timeRange} onValueChange={(value) => setTimeRange(value as any)}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Time Range" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Time</SelectItem>
              <SelectItem value="1m">Last Month</SelectItem>
              <SelectItem value="3m">Last 3 Months</SelectItem>
              <SelectItem value="6m">Last 6 Months</SelectItem>
              <SelectItem value="1y">Last Year</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="flex gap-2">
          <Select value={sortOrder} onValueChange={(value) => setSortOrder(value as "asc" | "desc")}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Sort Order" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="desc">Highest First</SelectItem>
              <SelectItem value="asc">Lowest First</SelectItem>
            </SelectContent>
          </Select>

          <Tabs value={chartType} onValueChange={(value) => setChartType(value as "bar" | "pie")}>
            <TabsList className="grid w-[140px] grid-cols-2 rounded-none border-b bg-transparent">
              <TabsTrigger
                value="bar"
                className="rounded-none data-[state=active]:bg-background data-[state=active]:border-b data-[state=active]:border-primary data-[state=active]:font-medium"
              >
                <BarChart3 className="mr-2 h-4 w-4" />
                Bar
              </TabsTrigger>
              <TabsTrigger
                value="pie"
                className="rounded-none data-[state=active]:bg-background data-[state=active]:border-b data-[state=active]:border-primary data-[state=active]:font-medium"
              >
                <PieChartIcon className="mr-2 h-4 w-4" />
                Pie
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
      </div>

      {symbolData.length === 0 ? (
        <div className="flex justify-center items-center h-[400px] border rounded-md">
          <p className="text-muted-foreground">No data available for the selected time range</p>
        </div>
      ) : (
        <div className="h-[500px]">
          {chartType === "bar" ? (
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={chartData}
                margin={{ top: 20, right: 30, left: 20, bottom: 70 }}
              >
                <CartesianGrid
                  strokeDasharray="3 3"
                  stroke="hsl(var(--border))"
                  opacity={0.4}
                  vertical={true}
                />
                <XAxis
                  dataKey="symbol"
                  angle={-45}
                  textAnchor="end"
                  height={70}
                  tick={{
                    fontSize: 11,
                    fill: 'hsl(var(--muted-foreground))',
                    opacity: 0.7
                  }}
                  axisLine={{ stroke: 'hsl(var(--border))', opacity: 0.5 }}
                />
                <YAxis
                  tickFormatter={(value) => formatMetricValue(value, selectedMetric)}
                  label={{
                    value: getMetricLabel(selectedMetric),
                    angle: -90,
                    position: 'insideLeft',
                    offset: -10,
                    style: {
                      textAnchor: 'middle',
                      fill: 'hsl(var(--muted-foreground))',
                      fontSize: 12
                    }
                  }}
                  tick={{
                    fontSize: 11,
                    fill: 'hsl(var(--muted-foreground))',
                    opacity: 0.7
                  }}
                  axisLine={{ stroke: 'hsl(var(--border))', opacity: 0.5 }}
                  // Add some padding to prevent label overlap
                  width={60}
                />
                <Tooltip content={<CustomTooltip />} />
                <Legend
                  verticalAlign="bottom"
                  height={36}
                  wrapperStyle={{
                    paddingBottom: '10px',
                    fontSize: '12px'
                  }}
                />
                {/* Add reference line for profit factor at 1.0 */}
                {selectedMetric === "profitFactor" && (
                  <ReferenceLine y={1} stroke="#666" />
                )}
                {/* Add reference line for profit metrics at 0 */}
                {(selectedMetric === "profit" || selectedMetric === "averageProfit") && (
                  <ReferenceLine y={0} stroke="#666" />
                )}
                <Bar
                  dataKey={selectedMetric === "profit" ? "totalProfit" :
                          selectedMetric === "averageProfit" ? "averageProfit" :
                          selectedMetric}
                  name={getMetricLabel(selectedMetric)}
                >
                  {chartData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={getBarColor(entry)} />
                  ))}
                </Bar>
              </BarChart>
            </ResponsiveContainer>
          ) : (
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  activeIndex={activeIndex}
                  activeShape={renderActiveShape}
                  data={chartData}
                  cx="50%"
                  cy="50%"
                  innerRadius={100}
                  outerRadius={140}
                  dataKey={selectedMetric === "profit" ? "totalProfit" :
                          selectedMetric === "averageProfit" ? "averageProfit" :
                          selectedMetric}
                  onMouseEnter={onPieEnter}
                >
                  {chartData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={getBarColor(entry)} />
                  ))}
                </Pie>
                <Tooltip
                  formatter={(value) => {
                    // Format the value based on the selected metric
                    if (selectedMetric === "profit" || selectedMetric === "averageProfit") {
                      return [`$${Number(value).toFixed(2)}`, getMetricLabel(selectedMetric)];
                    } else if (selectedMetric === "winRate") {
                      return [`${Number(value).toFixed(2)}%`, getMetricLabel(selectedMetric)];
                    } else if (selectedMetric === "profitFactor") {
                      return [Number(value) === Infinity ? "∞" : Number(value).toFixed(2), getMetricLabel(selectedMetric)];
                    } else if (selectedMetric === "tradeCount") {
                      return [Math.round(Number(value)), getMetricLabel(selectedMetric)];
                    } else {
                      return [Number(value).toFixed(2), getMetricLabel(selectedMetric)];
                    }
                  }}
                  contentStyle={{
                    backgroundColor: 'hsl(var(--background))',
                    borderColor: 'hsl(var(--border))',
                    borderRadius: '0.375rem',
                    padding: '0.5rem'
                  }}
                />
              </PieChart>
            </ResponsiveContainer>
          )}
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {symbolData.slice(0, 6).map((data) => (
          <Card key={data.symbol} className={cn(
            "overflow-hidden",
            data.totalProfit >= 0 ? "border-emerald-500/20" : "border-rose-500/20"
          )}>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg flex justify-between items-center">
                <span>{data.symbol}</span>
                <span className={data.totalProfit >= 0 ? "text-emerald-500" : "text-rose-500"}>
                  ${data.totalProfit.toFixed(2)}
                </span>
              </CardTitle>
              <CardDescription>
                {data.tradeCount} trades ({data.wins} wins, {data.losses} losses)
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Win Rate:</span>
                  <span className="font-medium">{data.winRate.toFixed(2)}%</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Avg Profit:</span>
                  <span className={cn(
                    "font-medium",
                    data.averageProfit >= 0 ? "text-emerald-500" : "text-rose-500"
                  )}>
                    ${data.averageProfit.toFixed(2)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Profit Factor:</span>
                  <span className="font-medium">
                    {data.profitFactor === Infinity ? "∞" : data.profitFactor.toFixed(2)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Volume:</span>
                  <span className="font-medium">{data.totalVolume.toFixed(2)}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}
