import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { createServerClient } from '@supabase/ssr';
import type { Database } from '@/types/supabase';

// GET handler for fetching all journal tags
export async function GET(request: NextRequest) {
  try {
    // Get the URL search params
    const searchParams = request.nextUrl.searchParams;
    const accountId = searchParams.get('accountId');

    // Get cookies for server-side Supabase client
    const cookieStore = await cookies();
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name) {
            return cookieStore.get(name)?.value;
          },
          set(_name, _value, _options) {
            // API routes can't set cookies directly
          },
          remove(_name, _options) {
            // API routes can't remove cookies directly
          }
        },
      }
    );

    // Get authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Fetch tags from journal entries
    const { data: journalTags, error: journalError } = await supabase
      .from('journal_entries')
      .select('tags')
      .eq('user_id', user.id);

    if (journalError) {
      console.error('Error fetching journal tags:', journalError);
      return NextResponse.json({ error: journalError.message }, { status: 500 });
    }

    // Fetch tags from daily journal entries
    const { data: dailyJournalEntries, error: dailyJournalError } = await supabase.rpc(
      'get_daily_journal_entries',
      {
        p_user_id: user.id,
        p_account_id: accountId || null,
        p_start_date: null,
        p_end_date: null,
        p_tags: null
      }
    );

    if (dailyJournalError) {
      console.error('Error fetching daily journal entries:', dailyJournalError);
      return NextResponse.json({ error: dailyJournalError.message }, { status: 500 });
    }

    // Extract all tags from journal entries
    const allJournalTags = journalTags?.flatMap((entry: any) => entry.tags || []) || [];

    // Extract all tags from daily journal entries
    const allDailyJournalTags = dailyJournalEntries?.flatMap((entry: any) => entry.tags || []) || [];

    // Combine and deduplicate all tags
    const combinedTags = [...new Set([...allJournalTags, ...allDailyJournalTags])];

    // Sort tags alphabetically
    combinedTags.sort();

    return NextResponse.json(combinedTags);
  } catch (error) {
    console.error('Error in journal tags API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
