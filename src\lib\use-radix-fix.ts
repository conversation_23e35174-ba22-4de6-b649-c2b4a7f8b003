"use client"

import { useEffect } from 'react'

/**
 * This hook patches the Radix UI components to prevent aria-hidden issues.
 * It directly modifies the DOM to remove aria-hidden attributes from elements
 * that contain focused elements.
 */
export function useRadixFix() {
  useEffect(() => {
    if (typeof window === 'undefined') return

    // Function to check if an element is hidden by aria-hidden
    const isHiddenByAriaHidden = (element: HTMLElement): boolean => {
      return element.getAttribute('aria-hidden') === 'true' || 
             element.getAttribute('data-aria-hidden') === 'true' ||
             element.getAttribute('data-state') === 'closed'
    }

    // Function to find all ancestors with aria-hidden="true"
    const findHiddenAncestors = (element: HTMLElement): HTMLElement[] => {
      const ancestors: HTMLElement[] = []
      let current = element.parentElement
      
      while (current) {
        if (isHiddenByAriaHidden(current)) {
          ancestors.push(current)
        }
        current = current.parentElement
      }
      
      return ancestors
    }

    // Function to temporarily remove aria-hidden from ancestors
    const removeAriaHiddenFromAncestors = (ancestors: HTMLElement[]): void => {
      ancestors.forEach(ancestor => {
        if (ancestor.getAttribute('aria-hidden') === 'true') {
          ancestor.setAttribute('aria-hidden', 'false')
        }
        if (ancestor.getAttribute('data-aria-hidden') === 'true') {
          ancestor.setAttribute('data-aria-hidden', 'false')
        }
      })
    }

    // Patch Radix UI's focus management
    const patchRadixUI = () => {
      // Find all Radix UI components
      const radixElements = document.querySelectorAll('[data-radix-popper-content-wrapper], [role="dialog"], [role="alertdialog"]')
      
      radixElements.forEach(element => {
        // Remove aria-hidden from the element itself
        if (element instanceof HTMLElement) {
          if (element.getAttribute('aria-hidden') === 'true') {
            element.setAttribute('aria-hidden', 'false')
          }
          
          // Also remove aria-hidden from all ancestors
          const ancestors = findHiddenAncestors(element)
          removeAriaHiddenFromAncestors(ancestors)
        }
      })
    }

    // Create a MutationObserver to watch for changes to the DOM
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        // Check if the mutation is related to aria-hidden
        if (mutation.type === 'attributes' && 
            (mutation.attributeName === 'aria-hidden' || 
             mutation.attributeName === 'data-aria-hidden' ||
             mutation.attributeName === 'data-state')) {
          patchRadixUI()
        }
        
        // Check if new Radix UI components were added to the DOM
        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
          mutation.addedNodes.forEach(node => {
            if (node instanceof HTMLElement) {
              if (node.matches('[data-radix-popper-content-wrapper], [role="dialog"], [role="alertdialog"]')) {
                patchRadixUI()
              }
              
              // Also check children of the added node
              const radixChildren = node.querySelectorAll('[data-radix-popper-content-wrapper], [role="dialog"], [role="alertdialog"]')
              if (radixChildren.length > 0) {
                patchRadixUI()
              }
            }
          })
        }
      })
    })

    // Start observing the document
    observer.observe(document.body, { 
      attributes: true, 
      childList: true, 
      subtree: true,
      attributeFilter: ['aria-hidden', 'data-aria-hidden', 'data-state']
    })

    // Also handle focus events to fix aria-hidden issues
    const handleFocus = (event: FocusEvent) => {
      const target = event.target as HTMLElement
      if (!target) return
      
      // Find ancestors with aria-hidden="true"
      const hiddenAncestors = findHiddenAncestors(target)
      
      if (hiddenAncestors.length > 0) {
        console.log('Found focused element inside aria-hidden ancestor, fixing...', target)
        removeAriaHiddenFromAncestors(hiddenAncestors)
      }
    }

    // Add focus event listener to document
    document.addEventListener('focus', handleFocus, true)
    
    // Run the patch immediately
    patchRadixUI()
    
    // Return cleanup function
    return () => {
      observer.disconnect()
      document.removeEventListener('focus', handleFocus, true)
    }
  }, [])
}
