import { NextRequest, NextResponse } from "next/server"
import { getSupabaseServer } from "@/lib/supabase-server"

export async function POST(request: NextRequest) {
  try {
    console.log('=== MANUAL TRADE TO NOTEBOOK MIGRATION API CALLED ===')

    const { tradeId } = await request.json()

    if (!tradeId) {
      return NextResponse.json(
        { error: "Trade ID is required" },
        { status: 400 }
      )
    }

    // Get authenticated user
    const supabase = await getSupabaseServer()
    const { data: { user }, error: userError } = await supabase.auth.getUser()

    if (userError || !user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    // Fetch the trade to get current data
    const { data: trade, error: fetchError } = await supabase
      .from("trades")
      .select("*")
      .eq("id", tradeId)
      .eq("user_id", user.id)
      .single()

    if (fetchError || !trade) {
      return NextResponse.json(
        { error: "Trade not found" },
        { status: 404 }
      )
    }

    // Call the auto-migrate Edge Function manually
    const edgeFunctionUrl = `${process.env.NEXT_PUBLIC_SUPABASE_URL}/functions/v1/auto-migrate-to-notebook`
    const serviceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

    if (!serviceKey) {
      console.error('SUPABASE_SERVICE_ROLE_KEY not found in environment variables')
      return NextResponse.json(
        { error: "Service key not configured - check environment variables" },
        { status: 500 }
      )
    }

    console.log('Calling auto-migrate Edge Function:', edgeFunctionUrl)
    console.log('Trade data:', { 
      id: trade.id, 
      symbol: trade.symbol,
      hasNotes: !!trade.notes,
      hasJournalContent: !!trade.journal_content
    })

    const migrateResponse = await fetch(edgeFunctionUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${serviceKey}`,
      },
      body: JSON.stringify({
        type: 'INSERT',
        table: 'trades',
        record: trade,
        old_record: null
      })
    })

    if (!migrateResponse.ok) {
      const errorText = await migrateResponse.text()
      console.error('Auto-migrate function failed:', errorText)
      return NextResponse.json(
        { error: "Migration failed", details: errorText },
        { status: 500 }
      )
    }

    const migrateResult = await migrateResponse.json()
    console.log('Manual migration completed successfully:', migrateResult)

    return NextResponse.json({
      success: true,
      message: "Trade migrated to notebook successfully",
      migrated: migrateResult.migrated,
      notebookEntryId: migrateResult.notebookEntryId
    })

  } catch (error) {
    console.error('Error in manual trade migration:', error)
    return NextResponse.json(
      { error: "Internal server error", details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}
