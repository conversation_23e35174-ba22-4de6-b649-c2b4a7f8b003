"use client"

import { Card } from "@/components/ui/card"
import { Info } from "lucide-react"
import { cn } from "@/lib/utils"
import { motion } from "framer-motion"
import { SemiCircularProgress } from "@/components/ui/semi-circular-progress"
import { DualColorGauge } from "@/components/ui/dual-color-gauge"

interface StatsCardProps {
  title: string
  value: string | number
  info?: string
  progress?: number
  progressColor?: string
  className?: string
  valueClassName?: string
  prefix?: string
  suffix?: string
  // New props for gauge
  gaugeValue?: number
  gaugeMax?: number
  gaugeColor?: string
  leftLabel?: string
  rightLabel?: string
  // Dual color gauge props
  useDualColorGauge?: boolean
  leftValue?: number
  rightValue?: number
  leftColor?: string
  rightColor?: string
}

export function StatsCard({
  title,
  value,
  info,
  progress = 0,
  progressColor = "bg-emerald-500",
  className,
  valueClassName = "",
  prefix,
  suffix,
  gaugeValue,
  gaugeMax = 100,
  gaugeColor = "stroke-emerald-500",
  leftLabel,
  rightLabel,
  // Dual color gauge props
  useDualColorGauge = false,
  leftValue = 0,
  rightValue = 0,
  leftColor = "stroke-emerald-500",
  rightColor = "stroke-rose-500"
}: StatsCardProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Card className={cn("dashboard-card p-4 relative overflow-hidden transition-all duration-300 hover:shadow-[0_8px_20px_rgba(0,0,0,0.08)] dark:hover:shadow-none h-[110px]", className)}>
        <div className="flex justify-between items-start mb-2">
          <h3 className="text-sm font-medium text-muted-foreground">{title}</h3>
          {info && (
            <Info className="h-4 w-4 text-muted-foreground hover:text-foreground cursor-help transition-colors duration-200" />
          )}
        </div>

        {/* Value display with optional gauge */}
        {gaugeValue !== undefined ? (
          <div className="flex items-center justify-between">
            <motion.div
              initial={{ scale: 0.9 }}
              animate={{ scale: 1 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className={cn("text-2xl font-bold", valueClassName)}
            >
              {prefix}{value}{suffix}
            </motion.div>

            <div className="ml-2 flex-shrink-0">
              {useDualColorGauge ? (
                <DualColorGauge
                  leftValue={leftValue}
                  rightValue={rightValue}
                  size={60}
                  strokeWidth={6}
                  className="sm:scale-100 scale-75"
                  leftColor={leftColor}
                  rightColor={rightColor}
                  showLabels={leftLabel !== undefined || rightLabel !== undefined}
                  leftLabel={leftLabel}
                  rightLabel={rightLabel}
                />
              ) : (
                <SemiCircularProgress
                  value={gaugeValue}
                  max={gaugeMax}
                  size={60}
                  strokeWidth={6}
                  className="sm:scale-100 scale-75"
                  progressColor={gaugeColor}
                  showLabels={leftLabel !== undefined || rightLabel !== undefined}
                  leftLabel={leftLabel}
                  rightLabel={rightLabel}
                  showPercentage={false}
                />
              )}
            </div>
          </div>
        ) : (
          <motion.div
            initial={{ scale: 0.9 }}
            animate={{ scale: 1 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className={cn("text-2xl font-bold mb-1", valueClassName)}
          >
            {prefix}{value}{suffix}
          </motion.div>
        )}

        {/* Legacy progress bar support */}
        {progress > 0 && gaugeValue === undefined && (
          <div className="w-full h-1.5 bg-muted rounded-full overflow-hidden mt-2">
            <motion.div
              initial={{ width: 0 }}
              animate={{ width: `${progress}%` }}
              transition={{ duration: 1, delay: 0.3, ease: "easeOut" }}
              className={`h-full ${progressColor}`}
            />
          </div>
        )}
      </Card>
    </motion.div>
  )
}
