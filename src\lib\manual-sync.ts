/**
 * Manual sync utilities for bidirectional notebook-journal synchronization
 * These functions trigger sync only when explicitly called (on save)
 */

/**
 * Manually sync a notebook entry to its linked trades
 */
export async function syncNotebookToTrades(notebookEntryId: string): Promise<{ success: boolean; error?: string }> {
  try {
    console.log(`Manually syncing notebook entry ${notebookEntryId} to trades`)
    
    const response = await fetch('/api/sync/notebook-to-trades', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ notebookEntryId })
    })

    if (!response.ok) {
      const errorData = await response.json()
      console.error('Notebook to trades sync failed:', errorData)
      return { success: false, error: errorData.error || 'Sync failed' }
    }

    const result = await response.json()
    console.log('Notebook to trades sync successful:', result)
    return { success: true }

  } catch (error) {
    console.error('Error in syncNotebookToTrades:', error)
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    }
  }
}

/**
 * Manually sync a trade to its linked notebook entries
 */
export async function syncTradeToNotebooks(tradeId: string): Promise<{ success: boolean; error?: string }> {
  try {
    console.log(`Manually syncing trade ${tradeId} to notebooks`)
    
    const response = await fetch('/api/sync/trade-to-notebooks', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ tradeId })
    })

    if (!response.ok) {
      const errorData = await response.json()
      console.error('Trade to notebooks sync failed:', errorData)
      return { success: false, error: errorData.error || 'Sync failed' }
    }

    const result = await response.json()
    console.log('Trade to notebooks sync successful:', result)
    return { success: true }

  } catch (error) {
    console.error('Error in syncTradeToNotebooks:', error)
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    }
  }
}

/**
 * Sync a daily journal entry to its linked notebook entries
 */
export async function syncDailyJournalToNotebooks(dailyJournalId: string): Promise<{ success: boolean; error?: string }> {
  try {
    console.log(`Manually syncing daily journal ${dailyJournalId} to notebooks`)
    
    // For now, daily journal sync can use the same pattern
    // You might want to create a separate endpoint if daily journals have different sync logic
    const response = await fetch('/api/sync/daily-journal-to-notebooks', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ dailyJournalId })
    })

    if (!response.ok) {
      const errorData = await response.json()
      console.error('Daily journal to notebooks sync failed:', errorData)
      return { success: false, error: errorData.error || 'Sync failed' }
    }

    const result = await response.json()
    console.log('Daily journal to notebooks sync successful:', result)
    return { success: true }

  } catch (error) {
    console.error('Error in syncDailyJournalToNotebooks:', error)
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    }
  }
}

/**
 * Helper function to handle save + sync operations
 * This ensures the save happens first, then sync, with proper error handling
 */
export async function saveAndSync<T>(
  saveOperation: () => Promise<T>,
  syncOperation: () => Promise<{ success: boolean; error?: string }>,
  entityName: string = 'entry'
): Promise<{ success: boolean; data?: T; error?: string }> {
  try {
    console.log(`Starting save and sync operation for ${entityName}`)
    
    // Step 1: Save the changes to the database
    console.log(`Saving ${entityName}...`)
    const savedData = await saveOperation()
    console.log(`${entityName} saved successfully`)
    
    // Step 2: Trigger manual sync
    console.log(`Syncing ${entityName}...`)
    const syncResult = await syncOperation()
    
    if (!syncResult.success) {
      console.warn(`${entityName} saved but sync failed:`, syncResult.error)
      // Return success for save but include sync warning
      return { 
        success: true, 
        data: savedData, 
        error: `Saved successfully but sync failed: ${syncResult.error}` 
      }
    }
    
    console.log(`${entityName} saved and synced successfully`)
    return { success: true, data: savedData }
    
  } catch (error) {
    console.error(`Error in save and sync operation for ${entityName}:`, error)
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    }
  }
}

/**
 * Batch sync multiple entries (useful for bulk operations)
 */
export async function batchSync(
  syncOperations: Array<() => Promise<{ success: boolean; error?: string }>>,
  entityName: string = 'entries'
): Promise<{ success: boolean; errors: string[] }> {
  console.log(`Starting batch sync for ${syncOperations.length} ${entityName}`)
  
  const errors: string[] = []
  
  for (let i = 0; i < syncOperations.length; i++) {
    try {
      const result = await syncOperations[i]()
      if (!result.success) {
        errors.push(`${entityName} ${i + 1}: ${result.error}`)
      }
    } catch (error) {
      errors.push(`${entityName} ${i + 1}: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }
  
  const success = errors.length === 0
  console.log(`Batch sync completed. Success: ${success}, Errors: ${errors.length}`)
  
  return { success, errors }
}
