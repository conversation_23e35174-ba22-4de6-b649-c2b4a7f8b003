"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { NotebookEntry, NotebookFolderWithMeta, NotebookTag, EditorContent } from "@/types/notebook"
import { NotebookEditor } from "@/components/notebook/notebook-editor"
import { useNotebookEntry, useNotebookFolders } from "@/hooks/use-notebook"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { TagInput } from "@/components/tag-input"
import { FolderDropdown } from "@/components/notebook/folder-dropdown"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from "@/components/ui/dialog"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu"
import { toast } from "sonner"
import {
  ArrowLeft,
  MoreVertical,
  Tag,
  X,
  Plus,
  Trash,
  Copy,
  Pin,
  Calendar,
  Bookmark,
  Folder
} from "lucide-react"
import { format } from "date-fns"
import { RelatedNotes } from "@/components/notebook/related-notes"

interface NoteClientWrapperProps {
  userId: string
  entry: NotebookEntry
  relatedEntries: NotebookEntry[]
  initialFolders?: NotebookFolderWithMeta[]
  initialTags?: NotebookTag[]
}

export default function NoteClientWrapper({
  userId,
  entry,
  relatedEntries,
  initialFolders = [],
  initialTags = []
}: NoteClientWrapperProps) {
  const router = useRouter()

  // State for editing
  const [isEditing, setIsEditing] = useState(false)
  const [title, setTitle] = useState(entry.title)
  const [category, setCategory] = useState(entry.category || "")
  const [tags, setTags] = useState<string[]>(entry.tags || [])
  const [content, setContent] = useState<EditorContent | null>(
    entry.content ? JSON.parse(entry.content as string) : null
  )
  const [htmlContent, setHtmlContent] = useState(entry.html_content || "")
  const [isPinned, setIsPinned] = useState(entry.is_pinned)
  const [isTemplate, setIsTemplate] = useState(entry.is_template)
  const [folderId, setFolderId] = useState<string | null>(entry.folder_id)

  // Available tags for suggestions
  const [availableTags, setAvailableTags] = useState<string[]>([])

  // State for delete confirmation
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)

  // Use custom hooks
  const { updateEntry, isUpdating, deleteEntry, isDeleting } = useNotebookEntry(entry.id, entry)
  const { folders } = useNotebookFolders(initialFolders)

  // Extract available tags from initialTags
  useEffect(() => {
    if (initialTags && initialTags.length > 0) {
      setAvailableTags(initialTags.map(tag => tag.name))
    }
  }, [initialTags])

  // Format dates
  const createdAt = entry.created_at ? new Date(entry.created_at) : new Date()
  const updatedAt = entry.updated_at ? new Date(entry.updated_at) : new Date()

  // Handle editor content change
  const handleEditorChange = (newContent: any, newHtmlContent: string) => {
    setContent(newContent)
    setHtmlContent(newHtmlContent)
  }

  // We don't need these functions anymore as they're handled by the TagInput component

  // Handle save changes with manual sync
  const handleSaveChanges = async () => {
    if (!title.trim()) {
      toast.error("Please enter a title for your note")
      return
    }

    try {
      // Convert editor content to JSON string for storage
      const contentToSave = content
        ? JSON.stringify(content)
        : JSON.stringify({ type: 'doc', content: [] });

      // Step 1: Save the notebook entry
      await updateEntry({
        title,
        content: contentToSave,
        html_content: htmlContent,
        category: category || null,
        tags,
        folder_id: folderId,
        is_pinned: isPinned,
        is_template: isTemplate
      })

      // Step 2: Manually trigger sync to linked trades
      try {
        const { syncNotebookToTrades } = await import('@/lib/manual-sync')
        const syncResult = await syncNotebookToTrades(entry.id)

        if (syncResult.success) {
          toast.success("Note updated and synced successfully")
        } else {
          toast.success("Note updated (sync warning: " + syncResult.error + ")")
        }
      } catch (syncError) {
        console.warn("Sync failed but note was saved:", syncError)
        toast.success("Note updated (sync failed)")
      }

      setIsEditing(false)
      router.refresh()
    } catch (error) {
      console.error("Error updating note:", error)
      toast.error("Failed to update note")
    }
  }

  // Handle delete note
  const handleDeleteNote = async () => {
    try {
      await deleteEntry()
      toast.success("Note deleted successfully")
      router.push("/notebook")
    } catch (error) {
      console.error("Error deleting note:", error)
      toast.error("Failed to delete note")
    }
  }

  // Handle duplicate note
  const handleDuplicateNote = async () => {
    try {
      // For duplicating, we need to use the API directly since our hook doesn't support this operation
      const response = await fetch(`/api/notebook/duplicate/${entry.id}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          title: `${entry.title} (Copy)`
        })
      });

      if (!response.ok) {
        throw new Error('Failed to duplicate note');
      }

      const duplicatedEntry = await response.json();

      toast.success("Note duplicated successfully")
      router.push(`/notebook/${duplicatedEntry.id}`)
    } catch (error) {
      console.error("Error duplicating note:", error)
      toast.error("Failed to duplicate note")
    }
  }

  // Handle toggle pin
  const handleTogglePin = async () => {
    try {
      await updateEntry({
        is_pinned: !isPinned
      })

      setIsPinned(!isPinned)
      toast.success(isPinned ? "Note unpinned" : "Note pinned")
      router.refresh()
    } catch (error) {
      console.error("Error toggling pin:", error)
      toast.error("Failed to update note")
    }
  }

  // Handle toggle template
  const handleToggleTemplate = async () => {
    try {
      await updateEntry({
        is_template: !isTemplate
      })

      setIsTemplate(!isTemplate)
      toast.success(isTemplate ? "Removed from templates" : "Saved as template")
      router.refresh()
    } catch (error) {
      console.error("Error toggling template:", error)
      toast.error("Failed to update note")
    }
  }

  return (
    <div className="h-[calc(100vh-4rem)] flex flex-col p-6 overflow-hidden">
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => router.push("/notebook")}
          className="gap-1"
        >
          <ArrowLeft className="h-4 w-4" />
          Back to Notebook
        </Button>

        <div className="flex items-center gap-2">
          {isEditing ? (
            <>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsEditing(false)}
              >
                Cancel
              </Button>
              <Button
                size="sm"
                onClick={handleSaveChanges}
                disabled={isUpdating}
              >
                {isUpdating ? "Saving..." : "Save Changes"}
              </Button>
            </>
          ) : (
            <>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsEditing(true)}
              >
                Edit
              </Button>

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="icon">
                    <MoreVertical className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={handleTogglePin}>
                    <Pin className="h-4 w-4 mr-2" />
                    {isPinned ? "Unpin Note" : "Pin Note"}
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={handleToggleTemplate}>
                    <Bookmark className="h-4 w-4 mr-2" />
                    {isTemplate ? "Remove from Templates" : "Save as Template"}
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={handleDuplicateNote}>
                    <Copy className="h-4 w-4 mr-2" />
                    Duplicate
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() => setIsDeleteDialogOpen(true)}
                    className="text-destructive"
                  >
                    <Trash className="h-4 w-4 mr-2" />
                    Delete
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </>
          )}
        </div>
      </div>

      {/* Title and Metadata */}
      <div className="mb-4">
        {isEditing ? (
          <Input
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            className="text-xl font-semibold mb-2"
            placeholder="Note title"
          />
        ) : (
          <h1 className="text-2xl font-semibold mb-2 flex items-center gap-2">
            {title}
            {isPinned && <Pin className="h-4 w-4 text-primary" />}
            {isTemplate && <Bookmark className="h-4 w-4 text-primary" />}
          </h1>
        )}

        <div className="flex items-center text-sm text-muted-foreground gap-4">
          <div className="flex items-center gap-1">
            <Calendar className="h-3 w-3" />
            <span>Created: {format(createdAt, "MMM d, yyyy")}</span>
          </div>
          {updatedAt.getTime() !== createdAt.getTime() && (
            <div>
              <span>Updated: {format(updatedAt, "MMM d, yyyy")}</span>
            </div>
          )}
        </div>
      </div>

      {/* Folder, Category and Tags */}
      <div className="mb-4">
        {isEditing ? (
          <div className="grid grid-cols-2 gap-4 mb-4">
            <div className="space-y-4">
              <div>
                <Label htmlFor="folder">Folder</Label>
                <FolderDropdown
                  folders={folders}
                  selectedFolderId={folderId}
                  onSelect={setFolderId}
                  className="mt-1"
                />
              </div>

              <div>
                <Label htmlFor="category">Category (optional)</Label>
                <Input
                  id="category"
                  placeholder="Category"
                  value={category}
                  onChange={(e) => setCategory(e.target.value)}
                />
              </div>

              <div className="flex items-center gap-2 mt-4">
                <input
                  type="checkbox"
                  id="is-template"
                  checked={isTemplate}
                  onChange={(e) => setIsTemplate(e.target.checked)}
                  className="mr-1"
                />
                <Label htmlFor="is-template" className="text-xs cursor-pointer">
                  Save as template
                </Label>
              </div>
            </div>

            <div>
              <Label htmlFor="tags">Tags</Label>
              <TagInput
                value={tags}
                onChange={setTags}
                suggestions={availableTags}
                placeholder="Add tags..."
                className="mt-1"
              />
              <p className="text-xs text-muted-foreground mt-2">
                Add tags to categorize your note (e.g., "important", "idea", "todo")
              </p>
            </div>
          </div>
        ) : (
          <div className="flex flex-wrap gap-2 mb-4">
            {/* Display folder if set */}
            {folderId && (
              <Badge variant="outline" className="gap-1 text-sm">
                <Folder className="h-3 w-3" />
                {folders.find(f => f.id === folderId)?.name || "Unknown folder"}
              </Badge>
            )}

            {/* Display category if set */}
            {category && (
              <Badge variant="outline" className="text-sm">
                {category}
              </Badge>
            )}

            {/* Display template status */}
            {isTemplate && (
              <Badge variant="outline" className="gap-1 text-sm">
                <Bookmark className="h-3 w-3" />
                Template
              </Badge>
            )}

            {/* Display tags */}
            {tags && tags.length > 0 && tags.map(tag => (
              <Badge key={tag} variant="secondary" className="gap-1 text-sm">
                <Tag className="h-3 w-3" />
                {tag}
              </Badge>
            ))}
          </div>
        )}
      </div>

      <Separator className="mb-4" />

      {/* Content */}
      <div className="flex-grow overflow-auto">
        <NotebookEditor
          content={content}
          onChange={handleEditorChange}
          readOnly={!isEditing}
          className="h-full"
        />
      </div>

      {/* Related Notes */}
      {relatedEntries.length > 0 && (
        <div className="mt-4">
          <h3 className="text-lg font-medium mb-2">Related Notes</h3>
          <RelatedNotes entries={relatedEntries} onSelect={(id) => router.push(`/notebook/${id}`)} />
        </div>
      )}

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Note</DialogTitle>
            <DialogDescription>
              This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <p>Are you sure you want to delete this note?</p>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteNote}
              disabled={isDeleting}
            >
              {isDeleting ? "Deleting..." : "Delete"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
