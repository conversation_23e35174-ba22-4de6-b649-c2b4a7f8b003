import { getSupabase<PERSON>rowser } from "@/lib/supabase-browser"
import { getSupabaseServer } from "@/lib/supabase-server"
import {
  NotebookEntry,
  NotebookEntryInsert,
  NotebookEntryUpdate,
  NotebookFilterOptions,
  NotebookSortOptions,
  NotebookCategory,
  NotebookTag
} from "@/types/notebook"
import { handleError } from "@/lib/error-handler"

/**
 * Get all notebook entries for a user
 */
export async function getNotebookEntries(
  userId: string,
  options?: {
    useServer?: boolean
    filters?: NotebookFilterOptions
    sort?: NotebookSortOptions
    limit?: number
    offset?: number
  }
): Promise<{ entries: NotebookEntry[], count: number }> {
  try {
    const supabase = options?.useServer
      ? await getSupabaseServer()
      : getSupabaseBrowser()

    // Start building the query
    let query = supabase
      .from("notebook_entries")
      .select("*", { count: "exact" })
      .eq("user_id", userId)

    // Apply filters
    if (options?.filters) {
      const { searchTerm, tags, category, isTemplate, accountId } = options.filters

      if (searchTerm) {
        query = query.or(`title.ilike.%${searchTerm}%,html_content.ilike.%${searchTerm}%`)
      }

      if (tags && tags.length > 0) {
        query = query.overlaps("tags", tags)
      }

      if (category) {
        query = query.eq("category", category)
      }

      if (isTemplate !== undefined) {
        query = query.eq("is_template", isTemplate)
      }

      if (accountId !== undefined) {
        if (accountId === null) {
          query = query.is("account_id", null)
        } else {
          query = query.eq("account_id", accountId)
        }
      }
    }

    // Apply sorting
    if (options?.sort) {
      const { field, order } = options.sort
      query = query.order(field, { ascending: order === "asc" })
    } else {
      // Default sorting: pinned first, then by updated_at
      query = query.order("is_pinned", { ascending: false }).order("updated_at", { ascending: false })
    }

    // Apply pagination
    if (options?.limit) {
      query = query.limit(options.limit)
    }

    if (options?.offset) {
      query = query.range(options.offset, options.offset + (options.limit || 10) - 1)
    }

    // Execute the query
    const { data, error, count } = await query

    if (error) {
      throw error
    }

    return {
      entries: data || [],
      count: count || 0
    }
  } catch (error) {
    return handleError(error, { entries: [], count: 0 }, 'getNotebookEntries')
  }
}

/**
 * Get a notebook entry by ID
 */
export async function getNotebookEntryById(
  userId: string,
  entryId: string,
  options?: { useServer?: boolean }
): Promise<NotebookEntry | null> {
  try {
    const supabase = options?.useServer
      ? await getSupabaseServer()
      : getSupabaseBrowser()

    const { data, error } = await supabase
      .from("notebook_entries")
      .select("*")
      .eq("id", entryId)
      .eq("user_id", userId)
      .single()

    if (error) {
      throw error
    }

    return data
  } catch (error) {
    return handleError(error, null, 'getNotebookEntryById')
  }
}

/**
 * Create a new notebook entry
 */
export async function createNotebookEntry(
  userId: string,
  entry: NotebookEntryInsert,
  options?: { useServer?: boolean }
): Promise<NotebookEntry | null> {
  try {
    const supabase = options?.useServer
      ? await getSupabaseServer()
      : getSupabaseBrowser()

    const { data, error } = await supabase
      .from("notebook_entries")
      .insert({
        ...entry,
        user_id: userId,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single()

    if (error) {
      throw error
    }

    return data
  } catch (error) {
    return handleError(error, null, 'createNotebookEntry')
  }
}

/**
 * Update a notebook entry
 */
export async function updateNotebookEntry(
  userId: string,
  entryId: string,
  updates: NotebookEntryUpdate,
  options?: { useServer?: boolean }
): Promise<NotebookEntry | null> {
  try {
    const supabase = options?.useServer
      ? await getSupabaseServer()
      : getSupabaseBrowser()

    const { data, error } = await supabase
      .from("notebook_entries")
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq("id", entryId)
      .eq("user_id", userId)
      .select()
      .single()

    if (error) {
      throw error
    }

    return data
  } catch (error) {
    return handleError(error, null, 'updateNotebookEntry')
  }
}

/**
 * Delete a notebook entry
 */
export async function deleteNotebookEntry(
  userId: string,
  entryId: string,
  options?: { useServer?: boolean }
): Promise<boolean> {
  try {
    const supabase = options?.useServer
      ? await getSupabaseServer()
      : getSupabaseBrowser()

    const { error } = await supabase
      .from("notebook_entries")
      .delete()
      .eq("id", entryId)
      .eq("user_id", userId)

    if (error) {
      throw error
    }

    return true
  } catch (error) {
    return handleError(error, false, 'deleteNotebookEntry')
  }
}

/**
 * Get all categories used in notebook entries
 */
export async function getNotebookCategories(
  userId: string,
  options?: { useServer?: boolean }
): Promise<NotebookCategory[]> {
  try {
    const supabase = options?.useServer
      ? await getSupabaseServer()
      : getSupabaseBrowser()

    const { data, error } = await supabase
      .from("notebook_entries")
      .select("category")
      .eq("user_id", userId)
      .not("category", "is", null)

    if (error) {
      throw error
    }

    // Count occurrences of each category
    const categories: Record<string, number> = {}
    data.forEach(entry => {
      if (entry.category) {
        categories[entry.category] = (categories[entry.category] || 0) + 1
      }
    })

    // Convert to array of category objects
    return Object.entries(categories).map(([name, count]) => ({
      name,
      count
    }))
  } catch (error) {
    return handleError(error, [], 'getNotebookCategories')
  }
}

/**
 * Get all tags used in notebook entries
 */
export async function getNotebookTags(
  userId: string,
  options?: { useServer?: boolean; accountId?: string | null }
): Promise<NotebookTag[]> {
  try {
    const supabase = options?.useServer
      ? await getSupabaseServer()
      : getSupabaseBrowser()

    let query = supabase
      .from("notebook_entries")
      .select("tags")
      .eq("user_id", userId)

    // Apply account filtering if specified
    if (options?.accountId !== undefined) {
      if (options.accountId === null) {
        query = query.is('account_id', null)
      } else {
        query = query.eq('account_id', options.accountId)
      }
    }

    const { data, error } = await query

    if (error) {
      throw error
    }

    // Count occurrences of each tag
    const tags: Record<string, number> = {}
    data.forEach(entry => {
      if (entry.tags && Array.isArray(entry.tags)) {
        entry.tags.forEach(tag => {
          tags[tag] = (tags[tag] || 0) + 1
        })
      }
    })

    // Convert to array of tag objects
    return Object.entries(tags).map(([name, count]) => ({
      name,
      count
    }))
  } catch (error) {
    return handleError(error, [], 'getNotebookTags')
  }
}
