"use client"

import * as React from "react"
import { Check } from "lucide-react"
import { cn } from "@/lib/utils"

interface SimpleCheckboxProps {
  id: string
  checked: boolean
  onChange: (checked: boolean) => void
  className?: string
  disabled?: boolean
}

export function SimpleCheckbox({
  id,
  checked,
  onChange,
  className,
  disabled = false
}: SimpleCheckboxProps) {
  // Use internal state to ensure immediate visual feedback
  const [isChecked, setIsChecked] = React.useState(checked)

  // Update internal state when prop changes
  React.useEffect(() => {
    setIsChecked(checked)
  }, [checked])

  const handleClick = () => {
    const newChecked = !isChecked
    setIsChecked(newChecked)
    onChange(newChecked)
  }

  // Use a button instead of an input for more reliable rendering
  return (
    <button
      id={id}
      type="button"
      role="checkbox"
      aria-checked={isChecked}
      disabled={disabled}
      className={cn(
        "flex h-4 w-4 shrink-0 items-center justify-center rounded-sm border border-primary transition-colors duration-200",
        isChecked ? "bg-primary border-primary" : "bg-transparent",
        disabled && "opacity-50 cursor-not-allowed",
        className
      )}
      onClick={handleClick}
    >
      {isChecked && (
        <Check className="h-3 w-3 text-white dark:text-black" />
      )}
    </button>
  )
}
