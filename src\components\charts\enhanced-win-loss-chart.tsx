"use client"

import { ChartTooltip } from "@/components/ui/chart-tooltip"
import { ChartContainer } from "@/components/ui/chart-container"
import {
  <PERSON><PERSON>hart,
  Pie,
  Cell,
  ResponsiveContainer,
  Legend,
  Tooltip,
} from "recharts"

interface Trade {
  profit: number
}

interface WinLossChartProps {
  trades: Trade[]
}

export function EnhancedWinLossChart({ trades }: WinLossChartProps) {
  const winningTrades = trades.filter((trade) => trade.profit > 0).length
  const losingTrades = trades.filter((trade) => trade.profit < 0).length
  const breakEvenTrades = trades.filter((trade) => trade.profit === 0).length

  const data = [
    { name: "Winning", value: winningTrades, color: "#10b981" },
    { name: "Losing", value: losingTrades, color: "#ef4444" },
    { name: "Break Even", value: breakEvenTrades, color: "#6b7280" },
  ].filter((item) => item.value > 0)

  // Calculate win rate
  const totalTrades = winningTrades + losingTrades + breakEvenTrades
  const winRate = totalTrades > 0 
    ? Math.round((winningTrades / totalTrades) * 100) 
    : 0

  return (
    <ChartContainer>
      <ResponsiveContainer width="100%" height="100%">
        <PieChart>
          <text
            x="50%"
            y="50%"
            textAnchor="middle"
            dominantBaseline="middle"
            className="text-2xl font-semibold fill-foreground"
          >
            {winRate}%
          </text>
          <Pie
            data={data}
            cx="50%"
            cy="50%"
            innerRadius={60}
            outerRadius={80}
            paddingAngle={2}
            dataKey="value"
            animationDuration={1000}
            animationEasing="ease-out"
          >
            {data.map((entry, index) => (
              <Cell
                key={`cell-${index}`}
                fill={entry.color}
                stroke="rgba(0,0,0,0.1)"
                strokeWidth={2}
              />
            ))}
          </Pie>
          <Tooltip
            content={({ active, payload }) => (
              <ChartTooltip active={active} payload={payload}
                formatter={(value, name) => `${value} Trades (${Math.round((Number(value) / totalTrades) * 100)}%)`}
              />
            )}
          />
          <Legend
            verticalAlign="bottom"
            height={36}
            formatter={(value, entry, index) => (
              <span className="text-foreground">{value}</span>
            )}
          />
        </PieChart>
      </ResponsiveContainer>
    </ChartContainer>
  )
}
