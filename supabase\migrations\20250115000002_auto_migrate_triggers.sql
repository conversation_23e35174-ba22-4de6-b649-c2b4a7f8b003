-- Create triggers for automatic migration to notebook entries
-- This enables immediate bidirectional sync without requiring manual import

-- Create queue table for auto-migration processing
CREATE TABLE IF NOT EXISTS public.auto_migrate_queue (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    table_name TEXT NOT NULL,
    record_id UUID NOT NULL,
    operation_type TEXT NOT NULL,
    record_data JSONB NOT NULL,
    old_record_data JSONB,
    processed BOOLEAN DEFAULT FALSE,
    processed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    error_message TEXT
);

-- Create index for efficient processing
CREATE INDEX IF NOT EXISTS idx_auto_migrate_queue_unprocessed
ON public.auto_migrate_queue (processed, created_at)
WHERE processed = FALSE;

-- Create index for cleanup
CREATE INDEX IF NOT EXISTS idx_auto_migrate_queue_processed_at
ON public.auto_migrate_queue (processed_at)
WHERE processed = TRUE;



-- Function to call the auto-migrate Edge Function for trades
CREATE OR REPLACE FUNCTION public.trigger_auto_migrate_trade()
R<PERSON>URNS TRIGGER AS $$
BEGIN
    -- Only process trades that have journal content
    IF NEW.notes IS NOT NULL OR NEW.journal_content IS NOT NULL OR NEW.has_journal_content = true THEN
        -- Log the trigger activation
        RAISE NOTICE 'Auto-migrate trigger activated for trade % with journal content', NEW.id;

        -- Insert a record into a queue table for processing by the Edge Function
        -- This approach is more reliable than direct HTTP calls from triggers
        INSERT INTO public.auto_migrate_queue (
            table_name,
            record_id,
            operation_type,
            record_data,
            old_record_data,
            created_at
        ) VALUES (
            'trades',
            NEW.id,
            TG_OP,
            to_jsonb(NEW),
            CASE WHEN TG_OP = 'UPDATE' THEN to_jsonb(OLD) ELSE NULL END,
            NOW()
        );

        RAISE NOTICE 'Queued trade % for auto-migration', NEW.id;
    ELSE
        RAISE NOTICE 'Skipping auto-migrate for trade % - no journal content', NEW.id;
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to call the auto-migrate Edge Function for daily journal entries
CREATE OR REPLACE FUNCTION public.trigger_auto_migrate_daily_journal()
RETURNS TRIGGER AS $$
BEGIN
    -- Only process daily journals that have content
    IF NEW.note IS NOT NULL AND trim(NEW.note) != '' THEN
        -- Log the trigger activation
        RAISE NOTICE 'Auto-migrate trigger activated for daily journal % with content', NEW.id;

        -- Insert a record into a queue table for processing by the Edge Function
        INSERT INTO public.auto_migrate_queue (
            table_name,
            record_id,
            operation_type,
            record_data,
            old_record_data,
            created_at
        ) VALUES (
            'daily_journal_entries',
            NEW.id,
            TG_OP,
            to_jsonb(NEW),
            CASE WHEN TG_OP = 'UPDATE' THEN to_jsonb(OLD) ELSE NULL END,
            NOW()
        );

        RAISE NOTICE 'Queued daily journal % for auto-migration', NEW.id;
    ELSE
        RAISE NOTICE 'Skipping auto-migrate for daily journal % - no content', NEW.id;
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for trades table
DROP TRIGGER IF EXISTS auto_migrate_trade_trigger ON public.trades;
CREATE TRIGGER auto_migrate_trade_trigger
    AFTER INSERT OR UPDATE ON public.trades
    FOR EACH ROW
    EXECUTE FUNCTION public.trigger_auto_migrate_trade();

-- Create triggers for daily_journal_entries table
DROP TRIGGER IF EXISTS auto_migrate_daily_journal_trigger ON public.daily_journal_entries;
CREATE TRIGGER auto_migrate_daily_journal_trigger
    AFTER INSERT OR UPDATE ON public.daily_journal_entries
    FOR EACH ROW
    EXECUTE FUNCTION public.trigger_auto_migrate_daily_journal();

-- Add comments to document the purpose
COMMENT ON FUNCTION public.trigger_auto_migrate_trade() IS
'Trigger function that calls the auto-migrate Edge Function when trades are created or updated to automatically create corresponding notebook entries';

COMMENT ON FUNCTION public.trigger_auto_migrate_daily_journal() IS
'Trigger function that calls the auto-migrate Edge Function when daily journal entries are created or updated to automatically create corresponding notebook entries';

COMMENT ON TRIGGER auto_migrate_trade_trigger ON public.trades IS
'Automatically creates notebook entries for trades with journal content to enable immediate bidirectional sync';

COMMENT ON TRIGGER auto_migrate_daily_journal_trigger ON public.daily_journal_entries IS
'Automatically creates notebook entries for daily journal entries to enable immediate bidirectional sync';
