"use client"

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import dynamic from 'next/dynamic';
import { useAccount } from '@/contexts/account-context';
import { Skeleton } from '@/components/ui/skeleton';

// Dynamically import the client component
const TradesClient = dynamic(() => import('./client'), {
  loading: () => <TradesLoadingState />
});

// Loading state component that matches the exact UI of the trades page
function TradesLoadingState() {
  return (
    <div className="space-y-6">
      {/* Header skeleton */}
      <div className="flex justify-between items-center">
        <Skeleton className="h-8 w-48" />
        <Skeleton className="h-10 w-32" />
      </div>

      {/* Stats cards skeleton */}
      <div className="grid grid-cols-4 gap-4">
        {Array(4).fill(0).map((_, i) => (
          <div key={i} className="p-4 rounded-lg border border-border bg-card">
            <Skeleton className="h-4 w-24 mb-2" />
            <Skeleton className="h-8 w-16" />
          </div>
        ))}
      </div>

      {/* Filters skeleton */}
      <div className="flex flex-wrap gap-2 items-center">
        {Array(5).fill(0).map((_, i) => (
          <Skeleton key={i} className="h-10 w-32" />
        ))}
      </div>

      {/* Table skeleton */}
      <div className="rounded-md border border-border">
        <div className="bg-card/50 p-4">
          <div className="grid grid-cols-9 gap-4">
            {Array(9).fill(0).map((_, i) => (
              <Skeleton key={i} className="h-6 w-full" />
            ))}
          </div>
        </div>
        <div className="divide-y divide-border">
          {Array(10).fill(0).map((_, i) => (
            <div key={i} className="p-4">
              <div className="grid grid-cols-9 gap-4">
                {Array(9).fill(0).map((_, j) => (
                  <Skeleton key={j} className="h-6 w-full" />
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Pagination skeleton */}
      <div className="flex justify-between items-center">
        <Skeleton className="h-8 w-24" />
        <div className="flex gap-2">
          {Array(3).fill(0).map((_, i) => (
            <Skeleton key={i} className="h-10 w-10" />
          ))}
        </div>
      </div>
    </div>
  );
}

interface ClientWrapperProps {
  userId: string;
  initialTrades: any[];
  initialFilteredTrades: any[];
  initialPaginatedTrades: any[];
  initialStrategies: any[];
  initialStats: {
    totalTrades: number;
    winRate: string;
    totalProfit: string;
    avgWin: string;
    avgLoss: string;
    winLossRatio: string;
    winningTradesCount: number;
    losingTradesCount: number;
  };
  initialFilters: {
    symbol: string;
    type: string | null;
    dateFrom: string;
    dateTo: string;
    profitMin: string;
    profitMax: string;
    volumeMin: string;
    volumeMax: string;
    durationMin: string;
    durationMax: string;
    status: string;
    strategy: string;
  };
  initialSortField: string;
  initialSortOrder: 'asc' | 'desc';
  initialPage: number;
  initialPageSize: number;
  totalPages: number;
  totalItems: number;
  selectedAccountId: string | null;
}

export default function ClientWrapper({
  userId,
  initialTrades,
  initialFilteredTrades,
  initialPaginatedTrades,
  initialStrategies,
  initialStats,
  initialFilters,
  initialSortField,
  initialSortOrder,
  initialPage,
  initialPageSize,
  totalPages,
  totalItems,
  selectedAccountId
}: ClientWrapperProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { selectedAccountId: contextAccountId } = useAccount();

  // If the account context has a different account ID than what was used for initial data,
  // we need to update the URL to trigger a refetch with the new account
  useEffect(() => {
    if (contextAccountId && contextAccountId !== selectedAccountId && searchParams) {
      // Create a new URLSearchParams object
      const params = new URLSearchParams(searchParams.toString());

      // Add the account ID parameter
      params.set('accountId', contextAccountId);

      // Update the URL without refreshing the page
      router.push(`/trades?${params.toString()}`);
    }
  }, [contextAccountId, selectedAccountId, router, searchParams]);

  return (
    <TradesClient
      userId={userId}
      initialTrades={initialTrades}
      initialFilteredTrades={initialFilteredTrades}
      initialPaginatedTrades={initialPaginatedTrades}
      initialStrategies={initialStrategies}
      initialStats={initialStats}
      initialFilters={initialFilters}
      initialSortField={initialSortField}
      initialSortOrder={initialSortOrder}
      initialPage={initialPage}
      initialPageSize={initialPageSize}
      totalPages={totalPages}
      totalItems={totalItems}
      selectedAccountId={selectedAccountId}
    />
  );
}
