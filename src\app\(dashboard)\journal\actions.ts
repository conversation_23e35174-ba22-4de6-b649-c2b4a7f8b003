'use server'

import { cookies } from 'next/headers';
import { createServerClient } from '@supabase/ssr';
import type { Database } from '@/types/supabase';
import { format } from 'date-fns';

// Cache storage with expiration
type CacheEntry = {
  data: any;
  expiry: number;
};

const CACHE_DURATION = 30 * 1000; // 30 seconds in milliseconds
const cacheStore: Map<string, CacheEntry> = new Map();

// Helper function to get cached response
async function getCachedResponse(key: string) {
  const cached = cacheStore.get(key);
  const now = Date.now();

  if (cached && cached.expiry > now) {
    console.log(`Cache hit for key: ${key}`);
    return cached.data;
  }

  // Clean up expired entries
  if (cached && cached.expiry <= now) {
    cacheStore.delete(key);
  }

  return null;
}

// Helper function to set cached response
async function setCachedResponse(key: string, data: any) {
  cacheStore.set(key, {
    data,
    expiry: Date.now() + CACHE_DURATION
  });

  // Limit cache size to prevent memory issues
  if (cacheStore.size > 100) {
    // Delete oldest entry
    const oldestKey = cacheStore.keys().next().value;
    if (oldestKey) {
      cacheStore.delete(oldestKey);
    }
  }
}

// Helper function to safely get search params
export async function getSearchParams(request: Request) {
  const url = new URL(request.url);
  return {
    searchTerm: url.searchParams.get('searchTerm') || "",
    tags: url.searchParams.getAll('tags'),
    startDate: url.searchParams.get('startDate') || undefined,
    endDate: url.searchParams.get('endDate') || undefined,
    tab: url.searchParams.get('tab') || "all"
  };
}

// Helper function to format a date to YYYY-MM-DD
function formatDateToYYYYMMDD(date: Date): string {
  return format(date, 'yyyy-MM-dd');
}

// Cache the journal data for 30 seconds to improve performance
// This is especially useful for repeated requests with the same parameters
/**
 * Calculate trading metrics for a set of trades
 */
function calculateTradeMetrics(trades: any[]) {
  if (!trades || trades.length === 0) {
    return {
      totalTrades: 0,
      winningTrades: 0,
      losingTrades: 0,
      winRate: 0,
      totalProfit: 0,
      largestWin: 0,
      largestLoss: 0,
      averageWin: 0,
      averageLoss: 0,
      profitFactor: 0,
      volume: 0
    };
  }

  // Ensure all trades have a valid profit value
  const validTrades = trades.filter(trade => {
    return trade.profit !== undefined && trade.profit !== null && !isNaN(Number(trade.profit));
  });

  if (validTrades.length === 0) {
    return {
      totalTrades: 0,
      winningTrades: 0,
      losingTrades: 0,
      winRate: 0,
      totalProfit: 0,
      largestWin: 0,
      largestLoss: 0,
      averageWin: 0,
      averageLoss: 0,
      profitFactor: 0,
      volume: 0
    };
  }

  const winningTrades = validTrades.filter(trade => trade.profit > 0);
  const losingTrades = validTrades.filter(trade => trade.profit < 0);
  const breakEvenTrades = validTrades.filter(trade => trade.profit === 0);

  const totalProfit = validTrades.reduce((sum, trade) => sum + trade.profit, 0);
  const grossProfit = winningTrades.reduce((sum, trade) => sum + trade.profit, 0);
  const grossLoss = Math.abs(losingTrades.reduce((sum, trade) => sum + trade.profit, 0));

  const largestWin = winningTrades.length > 0
    ? Math.max(...winningTrades.map(trade => trade.profit))
    : 0;

  const largestLoss = losingTrades.length > 0
    ? Math.abs(Math.min(...losingTrades.map(trade => trade.profit)))
    : 0;

  const averageWin = winningTrades.length > 0
    ? grossProfit / winningTrades.length
    : 0;

  const averageLoss = losingTrades.length > 0
    ? grossLoss / losingTrades.length
    : 0;

  // Calculate profit factor (gross profit / gross loss)
  // If gross loss is 0, profit factor is either 0 (if gross profit is 0) or infinity (if gross profit > 0)
  let profitFactor = 0;
  if (grossLoss > 0) {
    profitFactor = grossProfit / grossLoss;
  } else if (grossProfit > 0) {
    profitFactor = 999; // Represent infinity with a high number
  }

  // Calculate total volume
  const volume = validTrades.reduce((sum, trade) => {
    const tradeVolume = Number(trade.volume || 0);
    return isNaN(tradeVolume) ? sum : sum + tradeVolume;
  }, 0);

  return {
    totalTrades: validTrades.length,
    winningTrades: winningTrades.length,
    losingTrades: losingTrades.length,
    breakEvenTrades: breakEvenTrades.length,
    winRate: winningTrades.length / validTrades.length * 100,
    totalProfit,
    grossProfit,
    grossLoss,
    largestWin,
    largestLoss,
    averageWin,
    averageLoss,
    profitFactor,
    volume
  };
}

/**
 * Batch fetch trades and metrics for multiple dates
 * This optimizes performance by fetching data for multiple dates in a single request
 */
export async function batchFetchTradesForDates(
  userId: string,
  accountId: string | null,
  dates: string[],
  applyFilters: boolean = false,
  searchTerm?: string,
  tags?: string[]
) {
  if (!dates || dates.length === 0) {
    return {};
  }

  // Create a cache key based on the parameters
  const datesKey = dates.sort().join(',');
  const cacheKey = `batch-trades-for-dates-${userId}-${accountId}-${datesKey}-${applyFilters}-${searchTerm}-${tags?.join(',')}`;

  // Check if we have a cached response
  const cachedResponse = await getCachedResponse(cacheKey);
  if (cachedResponse) {
    return cachedResponse;
  }

  const cookieStore = await cookies();
  const supabase = createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name) {
          return cookieStore.get(name)?.value;
        },
        set(_name, _value, _options) {
          // Server actions can't set cookies directly
        },
        remove(_name, _options) {
          // Server actions can't remove cookies directly
        }
      }
    }
  );

  // Instead of using a complex OR condition, we'll use a simpler approach
  // We'll fetch all trades within the date range from the earliest to the latest date
  // and then filter them in memory

  // Find the earliest and latest dates
  const sortedDates = [...dates].sort((a, b) => new Date(a).getTime() - new Date(b).getTime());
  const earliestDate = sortedDates[0];
  const latestDate = sortedDates[sortedDates.length - 1];

  // Add one day to the latest date to include it fully
  const latestDateObj = new Date(latestDate);
  latestDateObj.setDate(latestDateObj.getDate() + 1);

  // Start building the query
  let query = supabase
    .from("trades")
    .select("*")
    .eq("user_id", userId)
    .gte('time_close', `${earliestDate}T00:00:00`)
    .lt('time_close', `${format(latestDateObj, 'yyyy-MM-dd')}T00:00:00`)
    .order("time_close", { ascending: false });

  // Apply account filter
  if (accountId) {
    query = query.eq("account_id", accountId);
  }

  // If we're applying additional filters
  if (applyFilters) {
    // Apply tag filters if provided
    if (tags && tags.length > 0) {
      query = query.contains('tags', tags);
    }

    // Apply search term filter if provided
    if (searchTerm && searchTerm.trim() !== '') {
      const term = searchTerm.toLowerCase().trim();
      query = query.or(`notes.ilike.%${term}%,symbol.ilike.%${term}%,tags.cs.{${term}}`);
    }
  }

  // Execute the query
  const { data: allTrades, error } = await query;

  if (error) {
    console.error('Error batch fetching trades for dates:', error);
    throw new Error(error.message);
  }

  // Group trades by date
  const tradesByDate: Record<string, any[]> = {};

  // Initialize with empty arrays for all requested dates
  dates.forEach(date => {
    tradesByDate[date] = [];
  });

  // Group trades by date, but only include trades for the requested dates
  (allTrades || []).forEach(trade => {
    const tradeDate = format(new Date(trade.time_close), 'yyyy-MM-dd');
    // Only add trades for the requested dates
    if (dates.includes(tradeDate)) {
      if (!tradesByDate[tradeDate]) {
        tradesByDate[tradeDate] = [];
      }
      tradesByDate[tradeDate].push(trade);
    }
  });

  // Collect all strategy IDs from all trades
  const allStrategyIds = new Set<string>();
  (allTrades || []).forEach(trade => {
    if (trade.strategy_id) {
      allStrategyIds.add(trade.strategy_id);
    }
  });

  // Fetch all strategies in one request
  let strategyMap: Record<string, string> = {};
  if (allStrategyIds.size > 0) {
    const { data: strategies, error: strategiesError } = await supabase
      .from('strategies')
      .select('id, name')
      .in('id', Array.from(allStrategyIds));

    if (strategiesError) {
      console.error('Error fetching strategies:', strategiesError);
    } else if (strategies) {
      strategies.forEach(strategy => {
        strategyMap[strategy.id] = strategy.name;
      });
    }
  }

  // Calculate metrics for each date
  const result: Record<string, any> = {};

  for (const date of dates) {
    const trades = tradesByDate[date] || [];
    const metrics = calculateTradeMetrics(trades);

    result[date] = {
      trades,
      metrics,
      strategyMap
    };
  }

  // Cache the result
  await setCachedResponse(cacheKey, result);

  return result;
}

/**
 * Fetch trades and metrics for a specific date
 * This is an optimized server action specifically for getting trades and metrics for a single date
 */
export async function fetchTradesForDate(
  userId: string,
  accountId: string | null,
  date: string,
  applyFilters: boolean = false,
  searchTerm?: string,
  tags?: string[]
) {
  // Create a cache key based on the parameters
  const cacheKey = `trades-for-date-${userId}-${accountId}-${date}-${applyFilters}-${searchTerm}-${tags?.join(',')}`;

  // Check if we have a cached response
  const cachedResponse = await getCachedResponse(cacheKey);
  if (cachedResponse) {
    return cachedResponse;
  }

  const cookieStore = await cookies();
  const supabase = createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name) {
          return cookieStore.get(name)?.value;
        },
        set(_name, _value, _options) {
          // Server actions can't set cookies directly
        },
        remove(_name, _options) {
          // Server actions can't remove cookies directly
        }
      }
    }
  );

  // Parse the date
  const dateObj = new Date(date);
  const nextDay = new Date(date);
  nextDay.setDate(nextDay.getDate() + 1);

  // Format dates for query
  const dateStr = format(dateObj, 'yyyy-MM-dd');
  const nextDayStr = format(nextDay, 'yyyy-MM-dd');

  // Start building the query
  let query = supabase
    .from("trades")
    .select("*")
    .eq("user_id", userId)
    .gte('time_close', `${dateStr}T00:00:00`)
    .lt('time_close', `${nextDayStr}T00:00:00`)
    .order("time_close", { ascending: false });

  // Apply account filter
  if (accountId) {
    query = query.eq("account_id", accountId);
  }

  // If we're applying additional filters
  if (applyFilters) {
    // Apply tag filters if provided
    if (tags && tags.length > 0) {
      query = query.contains('tags', tags);
    }

    // Apply search term filter if provided
    if (searchTerm && searchTerm.trim() !== '') {
      const term = searchTerm.toLowerCase().trim();
      query = query.or(`notes.ilike.%${term}%,symbol.ilike.%${term}%,tags.cs.{${term}}`);
    }
  }

  // Execute the query
  const { data: trades, error } = await query;

  if (error) {
    console.error('Error fetching trades for date:', error);
    throw new Error(error.message);
  }

  // Fetch strategies for trades with strategy_id
  const tradesWithStrategy = (trades || []).filter(trade => trade.strategy_id);
  const strategyIds = [...new Set(tradesWithStrategy.map(trade => trade.strategy_id))];

  let strategies: Array<{ id: string, name: string }> = [];
  let strategyMap: Record<string, string> = {};

  if (strategyIds.length > 0) {
    const { data: strategiesData, error: strategiesError } = await supabase
      .from('strategies')
      .select('id, name')
      .in('id', strategyIds);

    if (strategiesError) {
      console.error('Error fetching strategies:', strategiesError);
    } else {
      strategies = strategiesData || [];

      // Create strategy map
      strategies.forEach(strategy => {
        strategyMap[strategy.id] = strategy.name;
      });
    }
  }

  // Calculate metrics on the server
  const metrics = calculateTradeMetrics(trades || []);

  // Prepare the result with trades and pre-calculated metrics
  const result = {
    trades: trades || [],
    metrics,
    strategyMap
  };

  // Cache the result
  await setCachedResponse(cacheKey, result);

  return result;
}

/**
 * Main function to fetch filtered journal data
 */
export async function fetchFilteredJournalData(
  userId: string,
  accountId: string | null,
  searchTerm?: string,
  tags?: string[],
  startDate?: string,
  endDate?: string,
  activeTab?: string,
  page: number = 1,
  pageSize: number = 50,
  loadMore: boolean = false,
  specificDate?: string // New parameter for filtering by a specific date
) {
  // Create a cache key based on the parameters
  const cacheKey = `journal-data-${userId}-${accountId}-${searchTerm}-${tags?.join(',')}-${startDate}-${endDate}-${activeTab}-${specificDate}-${page}-${pageSize}`;

  // Check if we have a cached response
  const cachedResponse = await getCachedResponse(cacheKey);
  if (cachedResponse) {
    return cachedResponse;
  }
  const cookieStore = await cookies();
  const supabase = createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name) {
          return cookieStore.get(name)?.value;
        },
        set(_name, _value, _options) {
          // Server actions can't set cookies directly
        },
        remove(_name, _options) {
          // Server actions can't remove cookies directly
        }
      }
    }
  );

  // Start building the trades query with pagination
  let tradesQuery = supabase
    .from("trades")
    .select("*", { count: 'exact' }) // Get total count for pagination
    .eq("user_id", userId)
    .order("time_close", { ascending: false })
    .range((page - 1) * pageSize, page * pageSize - 1); // Apply pagination

  // Apply account filter
  if (accountId) {
    tradesQuery = tradesQuery.eq("account_id", accountId);
  }

  // Apply specific date filter if provided (takes precedence over date range)
  if (specificDate) {
    const nextDay = new Date(specificDate);
    nextDay.setDate(nextDay.getDate() + 1);
    const nextDayStr = format(nextDay, 'yyyy-MM-dd');

    tradesQuery = tradesQuery
      .gte('time_close', `${specificDate}T00:00:00`)
      .lt('time_close', `${nextDayStr}T00:00:00`);
  } else {
    // Apply date range filters if provided
    if (startDate) {
      tradesQuery = tradesQuery.gte('time_close', `${startDate}T00:00:00`);
    }

    if (endDate) {
      tradesQuery = tradesQuery.lte('time_close', `${endDate}T23:59:59.999`);
    }
  }

  // Apply tag filters if provided - using a completely new approach
  if (tags && tags.length > 0) {
    // Log the tags we're filtering by
    console.log(`Applying tag filters on server: ${tags.join(', ')}`);

    // IMPORTANT: Special handling for the "impulsive" tag
    const hasImpulsiveTag = tags.some(tag =>
      tag.toLowerCase() === 'impulsive'
    );

    if (hasImpulsiveTag) {
      console.log(`Special handling for "impulsive" tag detected`);

      // Use a direct approach for the impulsive tag
      // This ensures we find trades with this specific tag
      tradesQuery = tradesQuery.or(`tags::text ILIKE '%impulsive%'`);

      // Also try with the array contains operator
      tradesQuery = tradesQuery.or(`tags @> ARRAY['impulsive']`);
      tradesQuery = tradesQuery.or(`tags @> ARRAY['Impulsive']`);

      console.log(`Added special filters for "impulsive" tag`);
    } else {
      // For other tags, use the standard approach
      // IMPORTANT: Instead of trying to use complex PostgreSQL operators,
      // we'll fetch ALL trades and then filter them in post-processing.
      // This is more reliable than trying to use complex SQL queries.

      // We'll still add a basic filter to reduce the number of trades we need to process
      // But we won't rely on it for accurate filtering

      // Add a simple filter that will match trades with any of the specified tags
      // This is just to reduce the number of trades we need to process
      tradesQuery = tradesQuery.filter('tags', 'not.is', null);
    }

    console.log(`Using simplified tag filtering with post-processing for reliable results`);
  }

  // Apply search term filter if provided
  if (searchTerm && searchTerm.trim() !== '') {
    const term = searchTerm.toLowerCase().trim();

    // Check if the search term might be a tag
    const isLikelyTag = term.length > 2 && !term.includes(' ');

    if (isLikelyTag) {
      // If it looks like a tag, treat it like a tag filter
      console.log(`Search term "${term}" looks like a tag, treating it as a tag filter`);

      // Similar to tag filtering, we'll fetch all trades with tags
      // and then filter them in post-processing
      tradesQuery = tradesQuery.filter('tags', 'not.is', null);

      // Also search in notes and symbol for broader results
      tradesQuery = tradesQuery.or(
        `notes.ilike.%${term}%,symbol.ilike.%${term}%`
      );
    } else {
      // Regular search in notes and symbol
      tradesQuery = tradesQuery.or(
        `notes.ilike.%${term}%,symbol.ilike.%${term}%`
      );
    }
  }

  // Execute the query
  const { data: tradesData, error: tradesError, count: totalCount } = await tradesQuery;

  if (tradesError) {
    console.error('Error fetching trades:', tradesError);
    throw new Error(tradesError.message);
  }

  // Create pagination metadata
  const pagination = {
    currentPage: page,
    totalPages: totalCount ? Math.ceil(totalCount / pageSize) : 1,
    pageSize,
    totalCount: totalCount || 0,
    hasMore: page < (totalCount ? Math.ceil(totalCount / pageSize) : 1)
  };

  // Post-process for tag search if needed
  let filteredTrades = tradesData || [];

  // If we have a search term or tags, we need to do more comprehensive tag matching
  if ((searchTerm && searchTerm.trim() !== '') || (tags && tags.length > 0)) {
    const term = searchTerm?.toLowerCase().trim() || '';

    // Create a list of search terms, removing any empty strings
    const searchTerms = [...(tags || [])];
    if (term) searchTerms.push(term);
    const searchTermsLower = searchTerms.map(t => t.toLowerCase()).filter(t => t);

    if (searchTermsLower.length > 0) {
      console.log(`Post-processing for tags/terms: ${searchTermsLower.join(', ')}`);

      // Log all trades with their tags for debugging
      console.log(`All trades before filtering (${tradesData?.length || 0}):`);
      (tradesData || []).slice(0, 10).forEach((trade: any, index: number) => {
        console.log(`Trade ${index + 1}:`, {
          id: trade.id,
          symbol: trade.symbol,
          tags: trade.tags,
          hasNotes: !!trade.notes,
          hasScreenshots: !!(trade.screenshots && trade.screenshots.length > 0)
        });
      });

      // DIRECT TAG MATCHING - no prioritization, just exact matching
      // This is the most reliable approach
      const matchedTrades: any[] = [];
      const unmatchedTrades: any[] = [];

      // Process each trade
      (tradesData || []).forEach(trade => {
        // Skip trades without tags if we're looking for tags
        if (!Array.isArray(trade.tags) || trade.tags.length === 0) {
          // Only add to unmatched if we're not specifically looking for tags
          if (!tags || tags.length === 0) {
            unmatchedTrades.push(trade);
          }
          return;
        }

        // Convert trade tags to lowercase for case-insensitive comparison
        const tradeTags = trade.tags.map((tag: string) =>
          typeof tag === 'string' ? tag.toLowerCase() : ''
        ).filter((t: string) => t);

        // Log trade tags for debugging
        console.log(`Trade ${trade.id} has tags:`, tradeTags);

        // Check if any of the search terms match this trade's tags
        let hasMatch = false;

        // Special handling for "impulsive" tag
        const isLookingForImpulsive = searchTermsLower.some(term => term === 'impulsive');
        if (isLookingForImpulsive && tradeTags.includes('impulsive')) {
          console.log(`Found trade with "impulsive" tag: ${trade.id}`);
          hasMatch = true;
        } else {
          // Check for matches with each search term
          for (const termLower of searchTermsLower) {
            // Check for exact matches
            if (tradeTags.includes(termLower)) {
              hasMatch = true;
              break;
            }
          }
        }

        // Add to the appropriate category based on match
        if (hasMatch) {
          matchedTrades.push(trade);
        } else if (!tags || tags.length === 0) {
          // Only add to unmatched if we're not specifically looking for tags
          unmatchedTrades.push(trade);
        }
      });

      // If we're specifically looking for tag matches, only include trades that match tags
      // Otherwise, include other matches too (for general search)
      if (tags && tags.length > 0) {
        // For tag filtering, we only want trades that match the tags exactly
        filteredTrades = matchedTrades;
      } else {
        // For general search, include all matches
        filteredTrades = [...matchedTrades, ...unmatchedTrades];
      }

      // Remove duplicates
      const uniqueIds = new Set();
      filteredTrades = filteredTrades.filter(trade => {
        if (uniqueIds.has(trade.id)) return false;
        uniqueIds.add(trade.id);
        return true;
      });

      console.log(`Post-processed tag search: Found ${filteredTrades.length} trades`);
      console.log(`  - Tag matches: ${matchedTrades.length}`);
      console.log(`  - Other matches: ${unmatchedTrades.length}`);

      // Log matched trades for debugging
      if (matchedTrades.length > 0) {
        console.log(`Matched trades (${matchedTrades.length}):`);
        matchedTrades.slice(0, 5).forEach((trade: any, index: number) => {
          console.log(`Matched Trade ${index + 1}:`, {
            id: trade.id,
            symbol: trade.symbol,
            tags: trade.tags
          });
        });
      }

      // If we're specifically looking for trades with tags but found none,
      // log a warning to help with debugging
      if (filteredTrades.length === 0 && ((tags && tags.length > 0) || (term && term.length > 0))) {
        console.warn(`No trades found with tags/terms: ${searchTermsLower.join(', ')}`);
      }
    }
  }

  // Calculate trading days
  const tradingDaysSet = new Set<string>();
  filteredTrades.forEach(trade => {
    try {
      const tradeDate = new Date(trade.time_close);
      const dateStr = format(tradeDate, 'yyyy-MM-dd');
      tradingDaysSet.add(dateStr);
    } catch (error) {
      console.error('Error processing trade date:', error);
    }
  });

  // Only fetch daily journal entries if we're on the daily journal tab
  if (activeTab === "without-trades") {
    // Fetch daily journal entries with matching tags if needed
    if (tags && tags.length > 0 || searchTerm && searchTerm.trim() !== '') {
      try {
        // Determine if we need to search by term in daily journals
        const term = searchTerm?.trim().toLowerCase();
        const searchByTerm = term && term.length > 0;

        console.log(`Fetching daily journal entries with ${tags?.length || 0} tags and searchTerm: ${searchByTerm ? term : 'none'}`);

        // Fetch daily journal entries with matching tags
        const { data: dailyJournalEntries } = await supabase.rpc(
          'get_daily_journal_entries',
          {
            p_user_id: userId,
            p_account_id: accountId || null,
            p_start_date: startDate || null,
            p_end_date: endDate || null,
            p_tags: tags && tags.length > 0 ? tags : null
          }
        );

        // Process the daily journal entries
        if (dailyJournalEntries) {
          console.log(`Retrieved ${dailyJournalEntries.length} daily journal entries`);

          // Filter entries if we're searching by term
          let filteredEntries = dailyJournalEntries;

          if (searchByTerm) {
            filteredEntries = dailyJournalEntries.filter((entry: any) => {
              // Check if the note contains the search term
              if (entry.notes && entry.notes.toLowerCase().includes(term)) {
                return true;
              }

              // Check if any tag matches the search term
              if (Array.isArray(entry.tags)) {
                // First check for exact matches (case insensitive)
                const hasExactMatch = entry.tags.some((tag: string) =>
                  tag.toLowerCase() === term
                );

                if (hasExactMatch) {
                  console.log(`Found exact tag match for "${term}" in daily journal entry for ${entry.date}`);
                  return true;
                }

                // Then check for starts-with matches
                const hasStartsWithMatch = entry.tags.some((tag: string) =>
                  tag.toLowerCase().startsWith(term)
                );

                if (hasStartsWithMatch) {
                  console.log(`Found starts-with tag match for "${term}" in daily journal entry for ${entry.date}`);
                  return true;
                }

                // Finally check for contains matches
                const hasContainsMatch = entry.tags.some((tag: string) =>
                  tag.toLowerCase().includes(term)
                );

                if (hasContainsMatch) {
                  console.log(`Found contains tag match for "${term}" in daily journal entry for ${entry.date}`);
                  return true;
                }

                return false;
              }

              return false;
            });

            console.log(`Filtered to ${filteredEntries.length} daily journal entries matching term "${term}"`);
          }

          // Add dates from matching daily journal entries
          filteredEntries.forEach((entry: any) => {
            const dateStr = format(new Date(entry.date), 'yyyy-MM-dd');
            tradingDaysSet.add(dateStr);
          });
        }
      } catch (error) {
        console.error('Error fetching daily journal entries with matching tags:', error);
      }
    }
  } else {
    console.log(`Skipping daily journal entries fetch because active tab is "${activeTab}"`);
  }

  // Convert to array and sort in descending order (newest first)
  const tradingDays = Array.from(tradingDaysSet).sort((a, b) => {
    return new Date(b).getTime() - new Date(a).getTime();
  });

  // Process trades with journal content
  const tradesWithJournalContent = filteredTrades.filter(trade =>
    (trade.notes && trade.notes.trim().length > 0) ||
    (Array.isArray(trade.screenshots) && trade.screenshots.length > 0) ||
    trade.has_journal_content === true
  );

  // Fetch strategies for trades with strategy_id
  const tradesWithStrategy = filteredTrades.filter(trade => trade.strategy_id);
  const strategyIds = [...new Set(tradesWithStrategy.map(trade => trade.strategy_id))];

  let strategies: Array<{ id: string, name: string }> = [];
  let strategyMap: Record<string, string> = {};

  if (strategyIds.length > 0) {
    const { data: strategiesData, error: strategiesError } = await supabase
      .from('strategies')
      .select('id, name')
      .in('id', strategyIds);

    if (strategiesError) {
      console.error('Error fetching strategies:', strategiesError);
    } else {
      strategies = strategiesData || [];

      // Create strategy map
      strategies.forEach(strategy => {
        strategyMap[strategy.id] = strategy.name;
      });
    }
  }

  // Fetch all available tags for the user
  const { data: allTagsData } = await supabase
    .from("journal_entries")
    .select("tags")
    .eq("user_id", userId);

  // Extract all tags from journal entries
  const journalTags = allTagsData?.flatMap(entry => entry.tags || []) || [];

  // Extract all tags from trades
  const tradeTags = filteredTrades.flatMap(trade =>
    Array.isArray(trade.tags) ? trade.tags : []
  );

  // Combine and deduplicate all tags
  const availableTags = [...new Set([...journalTags, ...tradeTags])];

  // Get trades for a specific date if requested
  let tradesForSpecificDate: any[] = [];
  if (specificDate) {
    // This is already filtered by the query if specificDate was provided
    tradesForSpecificDate = filteredTrades;
  }

  // Convert trading days to ISO strings for consistent format
  const tradingDaysISO = tradingDays.map(dateStr => {
    const date = new Date(dateStr);
    return format(date, 'yyyy-MM-dd');
  });

  // Prepare the result with enhanced data
  const result = {
    // If loading more, we're only interested in the new trades
    // Otherwise, return all filtered trades
    trades: filteredTrades,
    tradingDays: tradingDaysISO, // Use ISO format for consistency
    // If loading more, we only want trades with journal content from the current page
    // This avoids duplicating entries when appending to the existing list
    tradesWithJournalContent: loadMore
      ? filteredTrades.filter(trade =>
          (trade.notes && trade.notes.trim().length > 0) ||
          (Array.isArray(trade.screenshots) && trade.screenshots.length > 0) ||
          trade.has_journal_content === true
        )
      : tradesWithJournalContent,
    strategies,
    strategyMap,
    pagination,
    // Add new data for client optimization
    availableTags,
    tradesForSpecificDate,
    // Add metadata for client
    metadata: {
      totalTrades: totalCount || 0,
      tradesWithJournalCount: tradesWithJournalContent.length,
      uniqueDaysCount: tradingDaysISO.length,
      tagsCount: availableTags.length
    }
  };

  // Cache the result for future requests
  // Only cache if not loading more, as we want to cache the complete initial result
  if (!loadMore) {
    await setCachedResponse(cacheKey, result);
  }

  return result;
}
