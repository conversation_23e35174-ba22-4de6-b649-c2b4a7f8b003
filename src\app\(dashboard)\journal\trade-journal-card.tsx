'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { format } from 'date-fns';
import { ChevronDown, ChevronUp, Tag, Edit, ArrowUpRight } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { ImageDisplay } from '@/components/ui/image-display';
import { cn } from '@/lib/utils';
import { getSupabaseBrowser } from '@/lib/supabase';
import { TradeJournalEditDialog } from '@/components/trade-journal-edit-dialog';

interface TradeJournalCardProps {
  trade: any;
  strategyName?: string;
  onEdit?: (trade: any) => void;
}

export function TradeJournalCard({ trade, strategyName, onEdit }: TradeJournalCardProps) {
  // Function to validate image URLs
  const validateImageUrl = (url: string): boolean => {
    // Check if the URL is empty
    if (!url) return false;

    // Basic URL validation
    try {
      new URL(url);
      return true;
    } catch (e) {
      console.error('Invalid image URL:', url);
      return false;
    }
  };

  // State to track the current trade data
  const [currentTrade, setCurrentTrade] = useState(trade);

  // Update currentTrade when the trade prop changes
  useEffect(() => {
    setCurrentTrade(trade);
  }, [trade]);

  // Listen for image deletion events
  useEffect(() => {
    const handleImageDeleted = () => {
      console.log('Trade journal card detected image deletion, refreshing...');
      // Force a refresh of the current trade data
      setCurrentTrade({...currentTrade});
    };

    // Add event listeners
    document.addEventListener('image-deleted', handleImageDeleted);
    document.addEventListener('image-deleted-final', handleImageDeleted);

    // Clean up
    return () => {
      document.removeEventListener('image-deleted', handleImageDeleted);
      document.removeEventListener('image-deleted-final', handleImageDeleted);
    };
  }, [currentTrade]);

  // Filter out invalid screenshot URLs
  const validScreenshots = Array.isArray(currentTrade.screenshots)
    ? currentTrade.screenshots.filter(validateImageUrl)
    : [];

  const [isOpen, setIsOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);

  // Format date and time
  const tradeDate = new Date(currentTrade.time_close);
  const formattedDate = format(tradeDate, 'MMM d, yyyy');
  const formattedTime = format(tradeDate, 'h:mm a');

  // Check if the trade has notes, screenshots, or tags
  const hasNotes = currentTrade.notes && currentTrade.notes.trim().length > 0;
  const hasScreenshots = validScreenshots.length > 0;
  const hasTags = Array.isArray(currentTrade.tags) && currentTrade.tags.length > 0;
  const hasAnyContent = hasNotes || hasScreenshots || hasTags;

  // Calculate profit and loss metrics
  const profit = Number(currentTrade.profit);
  const isProfitable = profit > 0;
  const volume = Number(currentTrade.volume) || 0;
  const entryPrice = Number(currentTrade.price_open) || 0;
  const exitPrice = Number(currentTrade.price_close) || 0;
  const priceDifference = exitPrice - entryPrice;
  const percentChange = entryPrice > 0 ? (priceDifference / entryPrice) * 100 : 0;

  // Calculate trade duration
  const entryTime = new Date(currentTrade.time_open);
  const exitTime = new Date(currentTrade.time_close);
  const durationMs = exitTime.getTime() - entryTime.getTime();
  const durationMinutes = Math.floor(durationMs / (1000 * 60));
  const durationHours = Math.floor(durationMinutes / 60);
  const remainingMinutes = durationMinutes % 60;
  const durationFormatted = durationHours > 0
    ? `${durationHours}h ${remainingMinutes}m`
    : `${durationMinutes}m`;



  return (
    <Card className="overflow-hidden">
      <Collapsible
        open={isOpen}
        onOpenChange={setIsOpen}>
        <CardHeader className="pb-3">
          <div className="flex justify-between items-start">
            <div>
              <CardTitle className="text-lg flex items-center">
                <span className="font-mono">{currentTrade.symbol}</span>
                <Badge
                  variant={isProfitable ? "default" : "destructive"}
                  className="ml-2"
                >
                  ${profit.toFixed(2)}
                </Badge>
              </CardTitle>
              <CardDescription>
                {formattedDate} at {formattedTime}
                {strategyName && (
                  <span className="ml-2">• {strategyName}</span>
                )}
              </CardDescription>
            </div>

            <div className="flex items-center gap-1">
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0"
                onClick={() => setIsEditDialogOpen(true)}
                title="Edit trade journal"
              >
                <Edit className="h-4 w-4" />
              </Button>
              <Link href={`/trades/${currentTrade.id}`} passHref>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0"
                  title="View trade details"
                >
                  <ArrowUpRight className="h-4 w-4" />
                </Button>
              </Link>
              {/* Expand/Collapse Button */}
              <CollapsibleTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0"
                  title={isOpen ? "Collapse" : "Expand"}
                >
                  {isOpen ? (
                    <ChevronUp className="h-4 w-4" />
                  ) : (
                    <ChevronDown className="h-4 w-4" />
                  )}
                </Button>
              </CollapsibleTrigger>
            </div>
          </div>
        </CardHeader>

        <CardContent className="pb-4 pt-0">
          {/* Content indicator for collapsed state */}
          {!isOpen && hasAnyContent && (
            <div className="flex items-center justify-center mb-2 text-xs text-muted-foreground">
              {[
                hasNotes ? "notes" : null,
                hasScreenshots ? `${validScreenshots.length} screenshot${validScreenshots.length > 1 ? 's' : ''}` : null,
                hasTags ? `${currentTrade.tags.length} tag${currentTrade.tags.length > 1 ? 's' : ''}` : null
              ].filter(Boolean).join(", ")}
            </div>
          )}

          {/* Trade Statistics Summary */}
          <div className="grid grid-cols-3 md:grid-cols-6 gap-2 mb-4">
            {/* Entry Price */}
            <div className="bg-muted/50 p-2 rounded-md border border-border">
              <div className="text-xs text-muted-foreground">Entry</div>
              <div className="text-sm font-semibold">
                ${entryPrice.toFixed(2)}
              </div>
            </div>

            {/* Exit Price */}
            <div className="bg-muted/50 p-2 rounded-md border border-border">
              <div className="text-xs text-muted-foreground">Exit</div>
              <div className="text-sm font-semibold">
                ${exitPrice.toFixed(2)}
              </div>
            </div>

            {/* Volume */}
            <div className="bg-muted/50 p-2 rounded-md border border-border">
              <div className="text-xs text-muted-foreground">Volume</div>
              <div className="text-sm font-semibold">
                {volume.toFixed(2)}
              </div>
            </div>

            {/* Duration */}
            <div className="bg-muted/50 p-2 rounded-md border border-border">
              <div className="text-xs text-muted-foreground">Duration</div>
              <div className="text-sm font-semibold">
                {durationFormatted}
              </div>
            </div>

            {/* P&L */}
            <div className="bg-muted/50 p-2 rounded-md border border-border">
              <div className="text-xs text-muted-foreground">P&L</div>
              <div className={`text-sm font-semibold ${isProfitable ? 'text-green-500' : 'text-red-500'}`}>
                ${profit.toFixed(2)}
              </div>
            </div>

            {/* % Change */}
            <div className="bg-muted/50 p-2 rounded-md border border-border">
              <div className="text-xs text-muted-foreground">% Change</div>
              <div className={`text-sm font-semibold ${percentChange >= 0 ? 'text-green-500' : 'text-red-500'}`}>
                {percentChange.toFixed(2)}%
              </div>
            </div>
          </div>
        </CardContent>

        <CollapsibleContent>
          <CardContent className="pt-0 pb-4">
            {/* VIEW MODE */}
            <>
              {/* Journal Notes */}
              {hasNotes && (
                <div className="mb-6">
                  <h4 className="text-sm font-medium mb-2">Notes</h4>
                  <div className="bg-muted/30 p-3 rounded-md border border-border">
                    <p className="whitespace-pre-wrap text-sm">{currentTrade.notes}</p>
                  </div>
                </div>
              )}

              {/* Tags */}
              {hasTags && (
                <div className="mb-6">
                  <h4 className="text-sm font-medium mb-2">Tags</h4>
                  <div className="flex flex-wrap gap-2">
                    {currentTrade.tags.map((tag: string) => (
                      <Badge key={tag} variant="outline" className="flex items-center gap-1">
                        <Tag className="h-3 w-3" />
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              {/* Screenshots Gallery */}
              {hasScreenshots && (
                <div className="mb-6">
                  <h4 className="text-sm font-medium mb-2">Screenshots</h4>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                    {validScreenshots.map((screenshot: string, index: number) => (
                      <div key={index} className="relative group">
                        <ImageDisplay
                          src={screenshot}
                          alt={`Screenshot ${index + 1} for ${trade.symbol}`}
                          aspectRatio="video"
                          lightboxGroup={validScreenshots}
                          lightboxIndex={index}
                          className="rounded-md border border-border"
                        />
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* No Content Message */}
              {!hasAnyContent && (
                <div className="text-center py-6 text-muted-foreground">
                  <p>No journal entries for this trade yet.</p>
                  <Button
                    variant="outline"
                    size="sm"
                    className="mt-2"
                    onClick={() => setIsEditDialogOpen(true)}
                  >
                    <Edit className="h-4 w-4 mr-1" />
                    Add Journal Entry
                  </Button>
                </div>
              )}
            </>
          </CardContent>
        </CollapsibleContent>
      </Collapsible>

      {/* Edit Dialog */}
      <TradeJournalEditDialog
        trade={currentTrade}
        open={isEditDialogOpen}
        onOpenChange={setIsEditDialogOpen}
        onUpdate={() => {
          // Fetch the latest trade data from the database
          const fetchLatestTradeData = async () => {
            try {
              const supabase = getSupabaseBrowser();
              const { data, error } = await supabase
                .from("trades")
                .select("*")
                .eq("id", currentTrade.id)
                .single();

              if (error) {
                console.error("Error fetching updated trade data:", error);
                // Still update with what we have
                setCurrentTrade({...currentTrade});
              } else if (data) {
                console.log("Fetched latest trade data:", data);
                // Update with the latest data from the database
                setCurrentTrade(data);

                // Call the parent's onEdit callback with the latest data
                if (onEdit) onEdit(data);
                return;
              }
            } catch (error) {
              console.error("Exception fetching updated trade data:", error);
            }

            // Fallback if fetch fails
            setCurrentTrade({...currentTrade});
            if (onEdit) onEdit(currentTrade);
          };

          // Execute the fetch
          fetchLatestTradeData();
        }}
      />
    </Card>
  );
}
