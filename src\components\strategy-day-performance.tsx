"use client"

import { useState, useEffect } from "react"
import { format } from "date-fns"
import { Trade } from "@/types/trade"
import { Strategy } from "@/types/playbook"
import { getTradesByStrategy } from "@/lib/playbook-service"
import { useAccount } from "@/contexts/account-context"

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"

interface StrategyDayPerformanceProps {
  userId: string
  strategyId: string
  date: Date
  strategy: Strategy
}

interface DayPerformanceMetrics {
  totalTrades: number
  winningTrades: number
  losingTrades: number
  winRate: number
  totalProfit: number
  isProfitable: boolean
}

export function StrategyDayPerformance({
  userId,
  strategyId,
  date,
  strategy
}: StrategyDayPerformanceProps) {
  const [trades, setTrades] = useState<Trade[]>([])
  const [metrics, setMetrics] = useState<DayPerformanceMetrics | null>(null)
  const [loading, setLoading] = useState(false)

  // Import useAccount hook
  const { selectedAccountId } = useAccount()

  useEffect(() => {
    const fetchTrades = async () => {
      if (!userId || !strategyId) return

      setLoading(true)
      try {
        // Pass the selectedAccountId to filter trades by account
        const allTrades = await getTradesByStrategy(userId, strategyId, selectedAccountId)

        // Filter trades for the specific date
        const dateStr = format(date, "yyyy-MM-dd")
        const dayTrades = allTrades.filter(trade =>
          format(new Date(trade.time_close), "yyyy-MM-dd") === dateStr
        )

        setTrades(dayTrades)

        // Calculate metrics
        if (dayTrades.length > 0) {
          const winningTrades = dayTrades.filter(trade => trade.profit > 0)
          const losingTrades = dayTrades.filter(trade => trade.profit < 0)
          const totalProfit = dayTrades.reduce((sum, trade) => sum + trade.profit, 0)

          setMetrics({
            totalTrades: dayTrades.length,
            winningTrades: winningTrades.length,
            losingTrades: losingTrades.length,
            winRate: dayTrades.length > 0 ? winningTrades.length / dayTrades.length : 0,
            totalProfit,
            isProfitable: totalProfit > 0
          })
        } else {
          setMetrics(null)
        }
      } catch (error) {
        console.error("Error fetching strategy trades:", error)
      } finally {
        setLoading(false)
      }
    }

    fetchTrades()
  }, [userId, strategyId, date, selectedAccountId])

  if (loading) {
    return <div className="text-center py-4">Loading...</div>
  }

  if (!metrics) {
    return (
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-lg">{strategy.name}</CardTitle>
          <CardDescription>
            No trades for this strategy on {format(date, "MMMM d, yyyy")}
          </CardDescription>
        </CardHeader>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="text-lg">{strategy.name}</CardTitle>
            <CardDescription>
              Performance on {format(date, "MMMM d, yyyy")}
            </CardDescription>
          </div>
          <Badge
            variant={strategy.status === 'active' ? 'default' :
                   strategy.status === 'testing' ? 'secondary' : 'outline'}
          >
            {strategy.status}
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-1">
            <p className="text-sm font-medium text-muted-foreground">Total P&L</p>
            <p className={cn(
              "text-xl font-bold",
              metrics.isProfitable ? "text-green-500" : "text-red-500"
            )}>
              ${metrics.totalProfit.toFixed(2)}
            </p>
          </div>
          <div className="space-y-1">
            <p className="text-sm font-medium text-muted-foreground">Win Rate</p>
            <p className="text-xl font-bold">
              {(metrics.winRate * 100).toFixed(0)}%
            </p>
          </div>
          <div className="space-y-1">
            <p className="text-sm font-medium text-muted-foreground">Trades</p>
            <p className="text-xl font-bold">{metrics.totalTrades}</p>
          </div>
          <div className="space-y-1">
            <p className="text-sm font-medium text-muted-foreground">W/L</p>
            <p className="text-xl font-bold">
              {metrics.winningTrades}/{metrics.losingTrades}
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
