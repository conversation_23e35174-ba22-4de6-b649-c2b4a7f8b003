'use server'

import { getSupabaseServer } from '@/lib/supabase-server'
import type { Strategy } from '@/types/playbook'

/**
 * Fetches all strategies for a user from the server
 * @param userId The user ID to fetch strategies for
 * @returns An array of strategies
 */
export async function getStrategiesServer(userId: string): Promise<Strategy[]> {
  try {
    const supabase = await getSupabaseServer()

    const { data, error } = await supabase
      .from('strategies')
      .select('*')
      .eq('user_id', userId)
      .order('name', { ascending: true })

    if (error) {
      console.error('Error fetching strategies:', error)
      return []
    }

    return data || []
  } catch (error) {
    console.error('Error in getStrategiesServer:', error)
    return []
  }
}

/**
 * Fetches a specific strategy by ID from the server
 * @param strategyId The strategy ID to fetch
 * @returns The strategy or null if not found
 */
export async function getStrategyByIdServer(strategyId: string): Promise<Strategy | null> {
  try {
    const supabase = await getSupabaseServer()

    const { data, error } = await supabase
      .from('strategies')
      .select('*')
      .eq('id', strategyId)
      .single()

    if (error) {
      console.error('Error fetching strategy:', error)
      return null
    }

    return data
  } catch (error) {
    console.error('Error in getStrategyByIdServer:', error)
    return null
  }
}

/**
 * Fetches setups for a specific strategy from the server
 * @param strategyId The strategy ID to fetch setups for
 * @returns An array of setups
 */
export async function getSetupsByStrategyIdServer(strategyId: string) {
  try {
    const supabase = await getSupabaseServer()

    const { data, error } = await supabase
      .from('setups')
      .select('*')
      .eq('strategy_id', strategyId)
      .order('name', { ascending: true })

    if (error) {
      console.error('Error fetching setups:', error)
      return []
    }

    return data || []
  } catch (error) {
    console.error('Error in getSetupsByStrategyIdServer:', error)
    return []
  }
}

/**
 * Fetches rules for a specific strategy from the server
 * @param strategyId The strategy ID to fetch rules for
 * @returns An array of rules
 */
export async function getRulesByStrategyIdServer(strategyId: string) {
  try {
    const supabase = await getSupabaseServer()

    const { data, error } = await supabase
      .from('strategy_rules')
      .select('*')
      .eq('strategy_id', strategyId)
      .order('rule_type', { ascending: true })

    if (error) {
      console.error('Error fetching rules:', error)
      return []
    }

    return data || []
  } catch (error) {
    console.error('Error in getRulesByStrategyIdServer:', error)
    return []
  }
}
