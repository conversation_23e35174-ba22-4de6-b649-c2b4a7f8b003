"use client"

import { useState } from "react"
import { format } from "date-fns"
import { Edit, Trash2, Calendar, Tag, ChevronDown, ChevronUp, Link as LinkIcon } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"
import { MarkdownRenderer } from "@/components/markdown-renderer"
import { JournalEntry } from "@/lib/journal-service"
import Link from "next/link"

interface JournalEntryCardProps {
  entry: JournalEntry
  onEdit?: () => void
  onDelete?: () => void
  className?: string
}

export function JournalEntryCard({
  entry,
  onEdit,
  onDelete,
  className
}: JournalEntryCardProps) {
  const [expanded, setExpanded] = useState(false)

  // Truncate content for preview
  const previewContent = entry.content.length > 300 && !expanded
    ? `${entry.content.substring(0, 300)}...`
    : entry.content

  return (
    <div className={cn("w-full border-b pb-2 last:border-b-0", className)}>
      <div className="flex justify-between items-start mb-1">
        <div className="text-base font-medium">{entry.title}</div>
        <div className="flex space-x-1">
          {onEdit && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onEdit}
              className="h-6 w-6 p-0"
            >
              <Edit className="h-3.5 w-3.5" />
              <span className="sr-only">Edit</span>
            </Button>
          )}
          {onDelete && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onDelete}
              className="h-6 w-6 p-0 text-destructive hover:text-destructive/80"
            >
              <Trash2 className="h-3.5 w-3.5" />
              <span className="sr-only">Delete</span>
            </Button>
          )}
        </div>
      </div>
      <div className="flex items-center text-xs text-muted-foreground mb-1">
        <Calendar className="h-3 w-3 mr-1" />
        <span>{format(new Date(entry.entry_date), "MMM d, yyyy")}</span>

        {entry.trade_id && (
          <Link href={`/trades?id=${entry.trade_id}`} className="ml-3 flex items-center hover:text-foreground">
            <LinkIcon className="h-3 w-3 mr-1" />
            <span>Linked Trade</span>
          </Link>
        )}
      </div>
      <div className="text-sm">
        <MarkdownRenderer content={previewContent} />
      </div>

      {entry.content.length > 300 && (
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setExpanded(!expanded)}
          className="mt-1 h-6 px-2 text-xs text-muted-foreground hover:text-foreground"
        >
          {expanded ? (
            <>
              <ChevronUp className="h-3 w-3 mr-1" />
              Show Less
            </>
          ) : (
            <>
              <ChevronDown className="h-3 w-3 mr-1" />
              Read More
            </>
          )}
        </Button>
      )}
      {entry.tags && entry.tags.length > 0 && (
        <div className="flex flex-wrap gap-1 mt-1">
          <Tag className="h-3 w-3 text-muted-foreground mr-0.5" />
          {entry.tags.map((tag) => (
            <Badge key={tag} variant="secondary" className="text-xs px-1.5 py-0 h-5">
              {tag}
            </Badge>
          ))}
        </div>
      )}
    </div>
  )
}

export default JournalEntryCard
