/**
 * Custom error classes for different error types
 */

// Base error class for application errors
export class AppError extends Error {
  public code: string;
  public status: number;
  public originalError?: any;

  constructor(message: string, code = 'UNKNOWN_ERROR', status = 500, originalError?: any) {
    super(message);
    this.name = this.constructor.name;
    this.code = code;
    this.status = status;
    this.originalError = originalError;
    
    // Maintains proper stack trace for where our error was thrown (only available on V8)
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, this.constructor);
    }
  }
}

// Authentication errors
export class AuthError extends AppError {
  constructor(message = 'Authentication error', originalError?: any) {
    super(message, 'AUTH_ERROR', 401, originalError);
  }
}

// Permission errors
export class PermissionError extends AppError {
  constructor(message = 'Permission denied', originalError?: any) {
    super(message, 'PERMISSION_ERROR', 403, originalError);
  }
}

// Not found errors
export class NotFoundError extends AppError {
  constructor(message = 'Resource not found', originalError?: any) {
    super(message, 'NOT_FOUND', 404, originalError);
  }
}

// Validation errors
export class ValidationError extends AppError {
  public validationErrors: Record<string, string>;

  constructor(message = 'Validation error', validationErrors: Record<string, string> = {}, originalError?: any) {
    super(message, 'VALIDATION_ERROR', 400, originalError);
    this.validationErrors = validationErrors;
  }
}

// Database errors
export class DatabaseError extends AppError {
  constructor(message = 'Database error', originalError?: any) {
    super(message, 'DATABASE_ERROR', 500, originalError);
  }
}

// Network errors
export class NetworkError extends AppError {
  constructor(message = 'Network error', originalError?: any) {
    super(message, 'NETWORK_ERROR', 503, originalError);
  }
}

/**
 * Error handling utilities
 */

// Parse Supabase error and convert to appropriate AppError
export function parseSupabaseError(error: any): AppError {
  if (!error) {
    return new AppError('Unknown error');
  }

  // Check if it's already an AppError
  if (error instanceof AppError) {
    return error;
  }

  // Handle Supabase errors
  if (error.code) {
    switch (error.code) {
      case 'PGRST116':
        return new NotFoundError('Resource not found', error);
      case '23505':
        return new ValidationError('Duplicate entry', { error: 'This record already exists' }, error);
      case '42P01':
        return new DatabaseError('Table does not exist', error);
      case '42501':
        return new PermissionError('Insufficient privileges', error);
      case 'PGRST301':
        return new PermissionError('Row-level security violation', error);
      case 'P0001':
        return new ValidationError(error.message || 'Database check violation', {}, error);
      case 'auth/invalid-email':
      case 'auth/user-not-found':
      case 'auth/wrong-password':
        return new AuthError(error.message || 'Authentication failed', error);
    }
  }

  // Handle HTTP status codes
  if (error.status) {
    switch (error.status) {
      case 401:
        return new AuthError(error.message || 'Unauthorized', error);
      case 403:
        return new PermissionError(error.message || 'Forbidden', error);
      case 404:
        return new NotFoundError(error.message || 'Not found', error);
      case 400:
        return new ValidationError(error.message || 'Bad request', {}, error);
      case 500:
        return new DatabaseError(error.message || 'Server error', error);
    }
  }

  // Default to generic AppError
  return new AppError(
    error.message || 'An unexpected error occurred',
    'UNKNOWN_ERROR',
    500,
    error
  );
}

// Format error for logging
export function formatErrorForLogging(error: any): string {
  if (error instanceof AppError) {
    return `[${error.code}] ${error.message}${error.originalError ? ` - Original: ${formatErrorForLogging(error.originalError)}` : ''}`;
  }
  
  if (error instanceof Error) {
    return `[${error.name}] ${error.message}`;
  }
  
  if (typeof error === 'object' && error !== null) {
    return JSON.stringify(error);
  }
  
  return String(error);
}

// Handle error with consistent logging and return appropriate fallback
export function handleError<T>(error: any, fallback: T, context: string): T {
  const parsedError = error instanceof AppError ? error : parseSupabaseError(error);
  console.error(`Error in ${context}:`, formatErrorForLogging(parsedError));
  return fallback;
}
