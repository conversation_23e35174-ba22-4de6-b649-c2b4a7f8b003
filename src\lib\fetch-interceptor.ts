/**
 * Fetch Interceptor for Supabase
 * 
 * This module provides a way to intercept and modify fetch requests
 * to ensure they have the proper Accept header set to 'application/json'
 * which prevents 406 (Not Acceptable) errors from the Supabase API.
 */

/**
 * Initialize the fetch interceptor
 * This patches the global fetch function to ensure all requests have the proper headers
 */
export function initFetchInterceptor() {
  if (typeof window === 'undefined') {
    // Server-side: do nothing
    return () => {};
  }

  // Store the original fetch function
  const originalFetch = window.fetch;

  // Replace the global fetch function with our interceptor
  window.fetch = async function interceptedFetch(url, options) {
    // Call our interceptRequest function to modify the request
    const [interceptedUrl, interceptedOptions] = interceptRequest(url, options);
    
    // Call the original fetch with our modified request
    const response = await originalFetch(interceptedUrl, interceptedOptions);
    
    // Return the response (we could also intercept the response here if needed)
    return response;
  };

  // Log that we've initialized the interceptor
  console.log('[FetchInterceptor] Initialized');

  // Return a function to restore the original fetch
  return function restoreOriginalFetch() {
    window.fetch = originalFetch;
    console.log('[FetchInterceptor] Restored original fetch');
  };
}

/**
 * Intercept and modify a request before it's sent
 * @param url The URL to fetch
 * @param options The fetch options
 * @returns A tuple of [url, options] with the modified request
 */
function interceptRequest(
  url: RequestInfo | URL, 
  options?: RequestInit
): [RequestInfo | URL, RequestInit] {
  // Create a new options object if none was provided
  options = options || {};
  
  // Create a new headers object if none was provided
  options.headers = options.headers || {};
  
  // Convert headers to a regular object if it's a Headers instance
  if (options.headers instanceof Headers) {
    const headersObj: Record<string, string> = {};
    options.headers.forEach((value, key) => {
      headersObj[key] = value;
    });
    options.headers = headersObj;
  }
  
  // Check if this is a Supabase request (based on URL)
  const urlString = url.toString();
  if (urlString.includes('supabase.co')) {
    // This is a Supabase request, ensure it has the proper Accept header
    const headers = options.headers as Record<string, string>;
    
    // Add Accept header if not already present
    if (!Object.keys(headers).some(h => h.toLowerCase() === 'accept')) {
      headers['Accept'] = 'application/json';
    }
    
    // Add Content-Type header if not already present
    if (!Object.keys(headers).some(h => h.toLowerCase() === 'content-type')) {
      headers['Content-Type'] = 'application/json';
    }
    
    // Log the modified request for debugging
    console.log(`[FetchInterceptor] Modified Supabase request: ${urlString}`);
  }
  
  // Return the modified request
  return [url, options];
}
