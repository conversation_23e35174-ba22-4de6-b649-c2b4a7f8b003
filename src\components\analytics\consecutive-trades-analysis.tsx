"use client"

import { useState, useMemo } from "react"
import { Trade } from "@/types/trade"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Bar<PERSON>hart3, <PERSON><PERSON><PERSON> as Pie<PERSON>hart<PERSON><PERSON>, TrendingUp } from "lucide-react"
import {
  BarChart, Bar, XAxis, YAxis, CartesianGrid,
  Tooltip, Legend, ResponsiveContainer, Cell, PieChart, Pie
} from "recharts"
import { format } from "date-fns"
import { cn } from "@/lib/utils"

interface ConsecutiveTradesAnalysisProps {
  trades: Trade[]
}

type ChartType = "streaks" | "distribution" | "impact"

export function ConsecutiveTradesAnalysis({ trades }: ConsecutiveTradesAnalysisProps) {
  const [chartType, setChartType] = useState<ChartType>("streaks")
  const [timeRange, setTimeRange] = useState<"all" | "1m" | "3m" | "6m" | "1y">("all")

  // Filter trades by time range
  const filteredTrades = useMemo(() => {
    if (timeRange === "all") return trades

    const now = new Date()
    let startDate = new Date()

    switch (timeRange) {
      case "1m":
        startDate.setMonth(now.getMonth() - 1)
        break
      case "3m":
        startDate.setMonth(now.getMonth() - 3)
        break
      case "6m":
        startDate.setMonth(now.getMonth() - 6)
        break
      case "1y":
        startDate.setFullYear(now.getFullYear() - 1)
        break
    }

    return trades.filter(trade => new Date(trade.time_close) >= startDate)
  }, [trades, timeRange])

  // Sort trades by close time
  const sortedTrades = useMemo(() => {
    return [...filteredTrades].sort((a, b) =>
      new Date(a.time_close).getTime() - new Date(b.time_close).getTime()
    )
  }, [filteredTrades])

  // Calculate consecutive win/loss streaks
  const streakData = useMemo(() => {
    if (sortedTrades.length === 0) return { streaks: [], currentStreak: null, longestWinStreak: 0, longestLossStreak: 0 }

    let currentStreakType: 'win' | 'loss' | null = null
    let currentStreakCount = 0
    let currentStreakProfit = 0
    let streakStartDate = null

    let longestWinStreak = 0
    let longestLossStreak = 0
    let mostProfitableStreak = 0
    let mostCostlyStreak = 0

    const streaks: {
      type: 'win' | 'loss'
      count: number
      totalProfit: number
      startDate: string
      endDate: string
      trades: Trade[]
    }[] = []

    sortedTrades.forEach((trade, index) => {
      const isWin = trade.profit >= 0
      const tradeType = isWin ? 'win' : 'loss'
      const tradeDate = new Date(trade.time_close)

      // If this is the first trade or the streak type has changed
      if (currentStreakType === null || currentStreakType !== tradeType) {
        // If this isn't the first trade, record the previous streak
        if (currentStreakType !== null) {
          streaks.push({
            type: currentStreakType,
            count: currentStreakCount,
            totalProfit: currentStreakProfit,
            startDate: streakStartDate!,
            endDate: sortedTrades[index - 1].time_close,
            trades: sortedTrades.slice(index - currentStreakCount, index)
          })

          // Update longest streaks
          if (currentStreakType === 'win' && currentStreakCount > longestWinStreak) {
            longestWinStreak = currentStreakCount
          } else if (currentStreakType === 'loss' && currentStreakCount > longestLossStreak) {
            longestLossStreak = currentStreakCount
          }

          // Update most profitable/costly streaks
          if (currentStreakType === 'win' && currentStreakProfit > mostProfitableStreak) {
            mostProfitableStreak = currentStreakProfit
          } else if (currentStreakType === 'loss' && currentStreakProfit < mostCostlyStreak) {
            mostCostlyStreak = currentStreakProfit
          }
        }

        // Start a new streak
        currentStreakType = tradeType
        currentStreakCount = 1
        currentStreakProfit = trade.profit
        streakStartDate = trade.time_close
      } else {
        // Continue the current streak
        currentStreakCount++
        currentStreakProfit += trade.profit
      }

      // If this is the last trade, record the final streak
      if (index === sortedTrades.length - 1) {
        streaks.push({
          type: currentStreakType,
          count: currentStreakCount,
          totalProfit: currentStreakProfit,
          startDate: streakStartDate!,
          endDate: trade.time_close,
          trades: sortedTrades.slice(sortedTrades.length - currentStreakCount)
        })

        // Update longest streaks
        if (currentStreakType === 'win' && currentStreakCount > longestWinStreak) {
          longestWinStreak = currentStreakCount
        } else if (currentStreakType === 'loss' && currentStreakCount > longestLossStreak) {
          longestLossStreak = currentStreakCount
        }

        // Update most profitable/costly streaks
        if (currentStreakType === 'win' && currentStreakProfit > mostProfitableStreak) {
          mostProfitableStreak = currentStreakProfit
        } else if (currentStreakType === 'loss' && currentStreakProfit < mostCostlyStreak) {
          mostCostlyStreak = currentStreakProfit
        }
      }
    })

    // Calculate streak distribution
    const streakDistribution = new Map<number, { wins: number, losses: number }>()

    // Initialize distribution with zeros
    for (let i = 1; i <= Math.max(longestWinStreak, longestLossStreak); i++) {
      streakDistribution.set(i, { wins: 0, losses: 0 })
    }

    // Count streaks by length
    streaks.forEach(streak => {
      const current = streakDistribution.get(streak.count) || { wins: 0, losses: 0 }
      if (streak.type === 'win') {
        current.wins++
      } else {
        current.losses++
      }
      streakDistribution.set(streak.count, current)
    })

    // Convert to array for charting
    const distributionData = Array.from(streakDistribution.entries())
      .map(([length, counts]) => ({
        length,
        wins: counts.wins,
        losses: counts.losses
      }))
      .sort((a, b) => a.length - b.length)

    // Calculate streak impact
    const impactData = streaks
      .filter(streak => streak.count >= 3) // Only consider streaks of 3 or more
      .map(streak => ({
        type: streak.type,
        count: streak.count,
        totalProfit: streak.totalProfit,
        startDate: format(new Date(streak.startDate), 'MMM d'),
        endDate: format(new Date(streak.endDate), 'MMM d'),
        averageProfit: streak.totalProfit / streak.count
      }))
      .sort((a, b) => b.totalProfit - a.totalProfit)

    return {
      streaks,
      currentStreak: streaks.length > 0 ? streaks[streaks.length - 1] : null,
      longestWinStreak,
      longestLossStreak,
      mostProfitableStreak,
      mostCostlyStreak,
      distributionData,
      impactData
    }
  }, [sortedTrades])

  // Custom tooltip for the streak chart
  const StreakTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-background border rounded-md p-3 shadow-md">
          <p className="font-medium">Streak Length: {label}</p>
          <p className="text-sm text-muted-foreground">
            Win Streaks: {payload[0].value}
          </p>
          <p className="text-sm text-muted-foreground">
            Loss Streaks: {payload[1].value}
          </p>
        </div>
      )
    }
    return null
  }

  // Custom tooltip for the impact chart
  const ImpactTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload
      return (
        <div className="bg-background border rounded-md p-3 shadow-md">
          <p className="font-medium">
            {data.count} {data.type === 'win' ? 'Win' : 'Loss'} Streak
          </p>
          <p className="text-sm text-muted-foreground">
            Total P/L: ${data.totalProfit.toFixed(2)}
          </p>
          <p className="text-sm text-muted-foreground">
            Avg P/L: ${data.averageProfit.toFixed(2)}
          </p>
          <p className="text-sm text-muted-foreground">
            {data.startDate} - {data.endDate}
          </p>
        </div>
      )
    }
    return null
  }

  // Get color for the bar based on streak type
  const getBarColor = (type: 'win' | 'loss') => {
    return type === 'win' ? "#10b981" : "#ef4444"
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row gap-4 justify-between">
        <div className="flex flex-col sm:flex-row gap-2">
          <Tabs value={chartType} onValueChange={(value) => setChartType(value as ChartType)}>
            <TabsList className="grid w-[500px] grid-cols-3 gap-4 rounded-none border-b bg-transparent">
              <TabsTrigger
                value="streaks"
                className="rounded-none data-[state=active]:bg-background data-[state=active]:border-b data-[state=active]:border-primary data-[state=active]:font-medium"
              >
                <BarChart3 className="mr-2 h-4 w-4" />
                Streak Distribution
              </TabsTrigger>
              <TabsTrigger
                value="distribution"
                className="rounded-none data-[state=active]:bg-background data-[state=active]:border-b data-[state=active]:border-primary data-[state=active]:font-medium"
              >
                <PieChartIcon className="mr-2 h-4 w-4" />
                Win/Loss Distribution
              </TabsTrigger>
              <TabsTrigger
                value="impact"
                className="rounded-none data-[state=active]:bg-background data-[state=active]:border-b data-[state=active]:border-primary data-[state=active]:font-medium"
              >
                <TrendingUp className="mr-2 h-4 w-4" />
                Streak Impact
              </TabsTrigger>
            </TabsList>
          </Tabs>

          <Select value={timeRange} onValueChange={(value) => setTimeRange(value as any)}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Time Range" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Time</SelectItem>
              <SelectItem value="1m">Last Month</SelectItem>
              <SelectItem value="3m">Last 3 Months</SelectItem>
              <SelectItem value="6m">Last 6 Months</SelectItem>
              <SelectItem value="1y">Last Year</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {sortedTrades.length === 0 ? (
        <div className="flex justify-center items-center h-[400px] border rounded-md">
          <p className="text-muted-foreground">No data available for the selected time range</p>
        </div>
      ) : (
        <div className="h-[500px]">
          {chartType === "streaks" && (
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={streakData.distributionData}
                margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
              >
                <CartesianGrid
                  strokeDasharray="3 3"
                  stroke="hsl(var(--border))"
                  opacity={0.4}
                  vertical={true}
                />
                <XAxis
                  dataKey="length"
                  label={{
                    value: "Streak Length",
                    position: 'insideBottom',
                    offset: -10,
                    style: {
                      fill: 'hsl(var(--muted-foreground))',
                      fontSize: 12
                    }
                  }}
                  tick={{
                    fontSize: 11,
                    fill: 'hsl(var(--muted-foreground))',
                    opacity: 0.7
                  }}
                  axisLine={{ stroke: 'hsl(var(--border))', opacity: 0.5 }}
                />
                <YAxis
                  label={{
                    value: "Number of Streaks",
                    angle: -90,
                    position: 'insideLeft',
                    offset: -10,
                    style: {
                      textAnchor: 'middle',
                      fill: 'hsl(var(--muted-foreground))',
                      fontSize: 12
                    }
                  }}
                  tick={{
                    fontSize: 11,
                    fill: 'hsl(var(--muted-foreground))',
                    opacity: 0.7
                  }}
                  axisLine={{ stroke: 'hsl(var(--border))', opacity: 0.5 }}
                  width={60}
                />
                <Tooltip content={<StreakTooltip />} />
                <Legend
                  verticalAlign="top"
                  height={36}
                  wrapperStyle={{
                    paddingTop: '10px',
                    fontSize: '12px'
                  }}
                />
                <Bar dataKey="wins" name="Win Streaks" fill="#10b981" />
                <Bar dataKey="losses" name="Loss Streaks" fill="#ef4444" />
              </BarChart>
            </ResponsiveContainer>
          )}

          {chartType === "distribution" && (
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={[
                    { name: 'Wins', value: sortedTrades.filter(t => t.profit >= 0).length },
                    { name: 'Losses', value: sortedTrades.filter(t => t.profit < 0).length }
                  ]}
                  cx="50%"
                  cy="50%"
                  innerRadius={100}
                  outerRadius={140}
                  paddingAngle={5}
                  dataKey="value"
                  label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(1)}%`}
                >
                  <Cell fill="#10b981" />
                  <Cell fill="#ef4444" />
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          )}

          {chartType === "impact" && (
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={streakData.impactData}
                margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
              >
                <CartesianGrid
                  strokeDasharray="3 3"
                  stroke="hsl(var(--border))"
                  opacity={0.4}
                  vertical={true}
                />
                <XAxis
                  dataKey="startDate"
                  tickFormatter={(value, index) => streakData.impactData ? `${value}-${streakData.impactData[index].endDate}` : value}
                  label={{
                    value: "Streak Period",
                    position: 'insideBottom',
                    offset: -10,
                    style: {
                      fill: 'hsl(var(--muted-foreground))',
                      fontSize: 12
                    }
                  }}
                  tick={{
                    fontSize: 11,
                    fill: 'hsl(var(--muted-foreground))',
                    opacity: 0.7
                  }}
                  axisLine={{ stroke: 'hsl(var(--border))', opacity: 0.5 }}
                />
                <YAxis
                  tickFormatter={(value) => `$${value.toFixed(0)}`}
                  label={{
                    value: "Total Profit/Loss",
                    angle: -90,
                    position: 'insideLeft',
                    offset: -10,
                    style: {
                      textAnchor: 'middle',
                      fill: 'hsl(var(--muted-foreground))',
                      fontSize: 12
                    }
                  }}
                  tick={{
                    fontSize: 11,
                    fill: 'hsl(var(--muted-foreground))',
                    opacity: 0.7
                  }}
                  axisLine={{ stroke: 'hsl(var(--border))', opacity: 0.5 }}
                  width={60}
                />
                <Tooltip content={<ImpactTooltip />} />
                <Legend
                  verticalAlign="top"
                  height={36}
                  wrapperStyle={{
                    paddingTop: '10px',
                    fontSize: '12px'
                  }}
                />
                <Bar
                  dataKey="totalProfit"
                  name="Streak P/L"
                >
                  {streakData.impactData?.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={getBarColor(entry.type)} />
                  ))}
                </Bar>
              </BarChart>
            </ResponsiveContainer>
          )}
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Current Streak Card */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-base">Current Streak</CardTitle>
            <CardDescription>
              Your ongoing streak
            </CardDescription>
          </CardHeader>
          <CardContent>
            {streakData.currentStreak ? (
              <div className="space-y-1">
                <div className={cn(
                  "text-2xl font-bold",
                  streakData.currentStreak.type === 'win' ? "text-emerald-500" : "text-rose-500"
                )}>
                  {streakData.currentStreak.count} {streakData.currentStreak.type === 'win' ? 'Wins' : 'Losses'}
                </div>
                <div className="text-sm text-muted-foreground">
                  P/L: ${streakData.currentStreak.totalProfit.toFixed(2)}
                </div>
                <div className="text-sm text-muted-foreground">
                  Since {format(new Date(streakData.currentStreak.startDate), 'MMM d, yyyy')}
                </div>
              </div>
            ) : (
              <div className="text-muted-foreground">No streak data available</div>
            )}
          </CardContent>
        </Card>

        {/* Longest Win Streak Card */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-base">Longest Win Streak</CardTitle>
            <CardDescription>
              Most consecutive winning trades
            </CardDescription>
          </CardHeader>
          <CardContent>
            {streakData.longestWinStreak > 0 ? (
              <div className="space-y-1">
                <div className="text-2xl font-bold text-emerald-500">
                  {streakData.longestWinStreak} Trades
                </div>
                {streakData.streaks
                  .filter(s => s.type === 'win' && s.count === streakData.longestWinStreak)
                  .slice(0, 1)
                  .map((streak, i) => (
                    <div key={i} className="text-sm text-muted-foreground">
                      P/L: ${streak.totalProfit.toFixed(2)}
                    </div>
                  ))
                }
              </div>
            ) : (
              <div className="text-muted-foreground">No win streak data</div>
            )}
          </CardContent>
        </Card>

        {/* Longest Loss Streak Card */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-base">Longest Loss Streak</CardTitle>
            <CardDescription>
              Most consecutive losing trades
            </CardDescription>
          </CardHeader>
          <CardContent>
            {streakData.longestLossStreak > 0 ? (
              <div className="space-y-1">
                <div className="text-2xl font-bold text-rose-500">
                  {streakData.longestLossStreak} Trades
                </div>
                {streakData.streaks
                  .filter(s => s.type === 'loss' && s.count === streakData.longestLossStreak)
                  .slice(0, 1)
                  .map((streak, i) => (
                    <div key={i} className="text-sm text-muted-foreground">
                      P/L: ${streak.totalProfit.toFixed(2)}
                    </div>
                  ))
                }
              </div>
            ) : (
              <div className="text-muted-foreground">No loss streak data</div>
            )}
          </CardContent>
        </Card>

        {/* Win/Loss Ratio Card */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-base">Win/Loss Ratio</CardTitle>
            <CardDescription>
              Proportion of winning trades
            </CardDescription>
          </CardHeader>
          <CardContent>
            {sortedTrades.length > 0 ? (
              (() => {
                const wins = sortedTrades.filter(t => t.profit >= 0).length
                const losses = sortedTrades.filter(t => t.profit < 0).length
                const winRate = (wins / sortedTrades.length) * 100

                return (
                  <div className="space-y-1">
                    <div className="text-2xl font-bold">
                      {winRate.toFixed(1)}%
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {wins} wins, {losses} losses
                    </div>
                    <div className="text-sm text-muted-foreground">
                      Ratio: {losses > 0 ? (wins / losses).toFixed(2) : '∞'}:1
                    </div>
                  </div>
                )
              })()
            ) : (
              <div className="text-muted-foreground">No trade data</div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Top Streaks Table */}
      {streakData.streaks?.length > 0 && (
        <div className="border rounded-md overflow-hidden">
          <div className="font-medium p-4 border-b">
            Top Streaks by P/L Impact
          </div>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="bg-muted/50">
                  <th className="text-left p-3 font-medium">Type</th>
                  <th className="text-left p-3 font-medium">Length</th>
                  <th className="text-left p-3 font-medium">Period</th>
                  <th className="text-left p-3 font-medium">Total P/L</th>
                  <th className="text-left p-3 font-medium">Avg P/L</th>
                </tr>
              </thead>
              <tbody>
                {streakData.streaks
                  .filter(streak => streak.count >= 3) // Only show streaks of 3 or more
                  .sort((a, b) => Math.abs(b.totalProfit) - Math.abs(a.totalProfit)) // Sort by absolute P/L impact
                  .slice(0, 5) // Top 5 streaks
                  .map((streak, index) => (
                    <tr key={index} className="border-t">
                      <td className="p-3">
                        <span className={cn(
                          "px-2 py-1 rounded-full text-xs",
                          streak.type === 'win' ?
                            "bg-emerald-500/10 text-emerald-500" :
                            "bg-rose-500/10 text-rose-500"
                        )}>
                          {streak.type === 'win' ? 'Win Streak' : 'Loss Streak'}
                        </span>
                      </td>
                      <td className="p-3">{streak.count} trades</td>
                      <td className="p-3">
                        {format(new Date(streak.startDate), 'MMM d')} - {format(new Date(streak.endDate), 'MMM d, yyyy')}
                      </td>
                      <td className={cn(
                        "p-3",
                        streak.totalProfit >= 0 ? "text-emerald-500" : "text-rose-500"
                      )}>
                        ${streak.totalProfit.toFixed(2)}
                      </td>
                      <td className={cn(
                        "p-3",
                        streak.totalProfit / streak.count >= 0 ? "text-emerald-500" : "text-rose-500"
                      )}>
                        ${(streak.totalProfit / streak.count).toFixed(2)}
                      </td>
                    </tr>
                  ))
                }
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  )
}
