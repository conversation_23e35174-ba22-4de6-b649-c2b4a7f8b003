import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { createServerClient } from '@supabase/ssr';
import type { Database } from '@/types/supabase';

// GET handler to fetch user accounts
export async function GET(request: Request) {
  try {
    // Get cookies for server-side Supabase client
    const cookieStore = await cookies();
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
          set(_name: string, _value: string, _options: any) {
            // API routes can't set cookies directly
          },
          remove(_name: string, _options: any) {
            // API routes can't remove cookies directly
          }
        },
      }
    );

    // Get authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    const userId = user.id;

    // Fetch user accounts
    const { data: accounts, error } = await supabase
      .from("trading_accounts")
      .select("*")
      .eq("user_id", userId)
      .order("updated_at", { ascending: false });

    if (error) {
      console.error('Error fetching trading accounts:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    console.log(`Retrieved ${accounts?.length || 0} accounts for user`);
    return NextResponse.json(accounts || []);
  } catch (error) {
    console.error('Error in dashboard-accounts API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
