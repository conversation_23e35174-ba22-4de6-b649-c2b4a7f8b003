import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { createServerClient } from '@supabase/ssr';
import type { Database } from '@/types/supabase';

// GET handler for fetching a specific notebook folder
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    // Get cookies for server-side Supabase client
    const cookieStore = await cookies();
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
          set(_name: string, _value: string, _options: any) {
            // API routes can't set cookies directly
          },
          remove(_name: string, _options: any) {
            // API routes can't remove cookies directly
          }
        },
      }
    );

    // Get authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Fetch the notebook folder
    const { data, error } = await supabase
      .from('notebook_folders')
      .select('*')
      .eq('id', id)
      .eq('user_id', user.id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json({ error: 'Notebook folder not found' }, { status: 404 });
      }
      console.error('Error fetching notebook folder:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    // Get account filter from query parameters
    const url = new URL(request.url);
    const accountIdParam = url.searchParams.get('accountId');

    // Convert empty string back to null, handle undefined
    const accountId = accountIdParam === '' ? null : accountIdParam;

    // Count entries in the folder with account filtering
    let countQuery = supabase
      .from('notebook_entries')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', user.id)
      .eq('folder_id', id);

    // Apply account filtering if specified
    if (accountIdParam !== null) { // If accountId parameter was provided (even if empty)
      if (accountId === null) {
        countQuery = countQuery.is('account_id', null);
      } else {
        countQuery = countQuery.eq('account_id', accountId);
      }
    }

    const { count, error: countError } = await countQuery;

    if (countError) {
      console.error('Error counting entries in folder:', countError);
    }

    return NextResponse.json({
      ...data,
      count: count || 0
    });
  } catch (error) {
    console.error('Error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// PATCH handler for updating a notebook folder
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const body = await request.json();
    const { name, description, color, icon, parent_id } = body;

    // Get cookies for server-side Supabase client
    const cookieStore = await cookies();
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
          set(_name: string, _value: string, _options: any) {
            // API routes can't set cookies directly
          },
          remove(_name: string, _options: any) {
            // API routes can't remove cookies directly
          }
        },
      }
    );

    // Get authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if folder is a system folder
    const { data: folderData, error: folderError } = await supabase
      .from('notebook_folders')
      .select('is_system')
      .eq('id', id)
      .eq('user_id', user.id)
      .single();

    if (folderError) {
      console.error('Error fetching folder:', folderError);
      return NextResponse.json({ error: folderError.message }, { status: 500 });
    }

    if (folderData.is_system) {
      return NextResponse.json({ error: 'System folders cannot be modified' }, { status: 400 });
    }

    // Update the folder
    const { data, error } = await supabase
      .from('notebook_folders')
      .update({
        name,
        description,
        color,
        icon,
        parent_id,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .eq('user_id', user.id)
      .select()
      .single();

    if (error) {
      console.error('Error updating notebook folder:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error('Error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// DELETE handler for deleting a notebook folder
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    // Get cookies for server-side Supabase client
    const cookieStore = await cookies();
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
          set(_name: string, _value: string, _options: any) {
            // API routes can't set cookies directly
          },
          remove(_name: string, _options: any) {
            // API routes can't remove cookies directly
          }
        },
      }
    );

    // Get authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if folder is a system folder
    const { data: folderData, error: folderError } = await supabase
      .from('notebook_folders')
      .select('is_system')
      .eq('id', id)
      .eq('user_id', user.id)
      .single();

    if (folderError) {
      console.error('Error fetching folder:', folderError);
      return NextResponse.json({ error: folderError.message }, { status: 500 });
    }

    if (folderData.is_system) {
      return NextResponse.json({ error: 'System folders cannot be deleted' }, { status: 400 });
    }

    // Update entries in this folder to have no folder
    await supabase
      .from('notebook_entries')
      .update({ folder_id: null })
      .eq('folder_id', id)
      .eq('user_id', user.id);

    // Delete the folder
    const { error } = await supabase
      .from('notebook_folders')
      .delete()
      .eq('id', id)
      .eq('user_id', user.id);

    if (error) {
      console.error('Error deleting notebook folder:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
