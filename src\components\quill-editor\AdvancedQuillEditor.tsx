"use client"

import React, { useState, useEffect, useRef } from 'react';
import dynamic from 'next/dynamic';
import 'react-quill/dist/quill.snow.css';
import './QuillEditor.css';

// Import ReactQuill dynamically to avoid SSR issues
const ReactQuill = dynamic(() => import('react-quill'), {
  ssr: false,
  loading: () => <div className="h-full w-full bg-gray-100 animate-pulse rounded-md" />
});

// Add custom formats and modules if needed
// For example, you could add a custom blot for special formatting

interface AdvancedQuillEditorProps {
  initialContent?: string;
  onChange?: (content: { html: string; text: string }) => void;
  editable?: boolean;
  autofocus?: boolean;
  placeholder?: string;
  className?: string;
}

const AdvancedQuillEditor: React.FC<AdvancedQuillEditorProps> = ({
  initialContent = '',
  onChange,
  editable = true,
  autofocus = false,
  placeholder = 'Start typing...',
  className = '',
}) => {
  const [content, setContent] = useState(initialContent);
  const containerRef = useRef<HTMLDivElement>(null);

  // Define custom toolbar options
  const modules = {
    toolbar: {
      container: [
        [{ 'header': [1, 2, 3, 4, false] }],
        ['bold', 'italic', 'underline', 'strike'],
        [{ 'list': 'ordered' }, { 'list': 'bullet' }, { 'list': 'check' }],
        [{ 'align': [] }],
        ['link', 'image'],
        ['clean'],
        ['code-block', 'blockquote'],
        [{ 'color': [] }, { 'background': [] }],
        ['superscript', 'subscript'],
      ],
      // You can add custom handlers here
      handlers: {
        // For example, a custom image handler
        // 'image': imageHandler
      }
    },
    clipboard: {
      matchVisual: false,
    },
    history: {
      delay: 1000,
      maxStack: 100,
      userOnly: true
    }
  };

  const formats = [
    'header',
    'bold', 'italic', 'underline', 'strike',
    'list', 'bullet', 'check',
    'align',
    'link', 'image',
    'code-block', 'blockquote',
    'color', 'background',
    'superscript', 'subscript',
  ];

  // Handle content changes
  const handleChange = (value: string) => {
    setContent(value);
    if (onChange) {
      // Since we can't reliably access the editor instance through ref in React 18,
      // we'll just extract the text from the HTML
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = value;
      const text = tempDiv.textContent || tempDiv.innerText || '';

      onChange({
        html: value,
        text: text
      });
    }
  };

  // Focus the editor on mount if autofocus is true
  useEffect(() => {
    if (autofocus) {
      // We can't reliably use the ref in React 18, so we'll use a different approach
      setTimeout(() => {
        const editorElement = document.querySelector('.advanced-quill-editor-wrapper .ql-editor');
        if (editorElement) {
          (editorElement as HTMLElement).focus();
        }
      }, 100);
    }
  }, [autofocus]);

  // Make toolbar sticky on scroll
  useEffect(() => {
    const handleScroll = () => {
      if (!containerRef.current) return;

      const toolbar = containerRef.current.querySelector('.ql-toolbar');
      if (!toolbar) return;

      const containerRect = containerRef.current.getBoundingClientRect();

      if (containerRect.top < 0) {
        (toolbar as HTMLElement).style.position = 'fixed';
        (toolbar as HTMLElement).style.top = '0';
        (toolbar as HTMLElement).style.width = `${containerRect.width}px`;
      } else {
        (toolbar as HTMLElement).style.position = 'sticky';
        (toolbar as HTMLElement).style.top = '0';
        (toolbar as HTMLElement).style.width = '100%';
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  return (
    <div
      ref={containerRef}
      className={`advanced-quill-editor-wrapper ${className}`}
    >
      <ReactQuill
        theme="snow"
        value={content}
        onChange={handleChange}
        modules={modules}
        formats={formats}
        readOnly={!editable}
        placeholder={placeholder}
      />
    </div>
  );
};

export default AdvancedQuillEditor;
