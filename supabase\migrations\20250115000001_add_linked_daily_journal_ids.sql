-- Add linked_daily_journal_ids column to notebook_entries table
-- This will properly link notebook entries to daily journal entries for bidirectional sync

DO $$
BEGIN
    -- Check if linked_daily_journal_ids column exists, if not add it
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_schema = 'public'
        AND table_name = 'notebook_entries'
        AND column_name = 'linked_daily_journal_ids'
    ) THEN
        ALTER TABLE public.notebook_entries 
        ADD COLUMN linked_daily_journal_ids UUID[] DEFAULT '{}';
        
        RAISE NOTICE 'Added linked_daily_journal_ids column to notebook_entries table';
    ELSE
        RAISE NOTICE 'linked_daily_journal_ids column already exists in notebook_entries table';
    END IF;
END
$$;

-- Create index for faster queries on linked_daily_journal_ids
CREATE INDEX IF NOT EXISTS idx_notebook_entries_linked_daily_journal_ids 
ON public.notebook_entries USING GIN (linked_daily_journal_ids);

-- Add comment to document the purpose of this column
COMMENT ON COLUMN public.notebook_entries.linked_daily_journal_ids IS 
'Array of daily_journal_entries.id values for bidirectional synchronization between notebook entries and daily journal entries';
