              <div className="divide-y">
                {/* Calculate pagination */}
                {selectedTrades
                  .slice((tradeDetailsPage - 1) * tradesPerPage, tradeDetailsPage * tradesPerPage)
                  .map((trade) => (
                  <div key={trade.position_id} className="grid grid-cols-5 gap-4 p-4 text-sm">
                    <div className="font-medium">{trade.symbol}</div>
                    <div className={cn(
                      "capitalize",
                      trade.type === "buy" ? "text-emerald-500" : "text-rose-500"
                    )}>
                      {trade.type}
                    </div>
                    <div className="text-muted-foreground">
                      {format(new Date(trade.time_close), "HH:mm:ss")}
                    </div>
                    <div className="text-right text-muted-foreground">
                      {trade.volume.toFixed(2)}
                    </div>
                    <div className={cn(
                      "text-right font-medium",
                      trade.profit >= 0 ? "text-emerald-500" : "text-rose-500"
                    )}>
                      ${trade.profit.toFixed(2)}
                    </div>
                  </div>
                ))}
                {selectedTrades.length === 0 && (
                  <div className="p-4 text-center text-muted-foreground">
                    No trades for this date
                  </div>
                )}
              </div>
              
              {/* Pagination controls */}
              {selectedTrades.length > tradesPerPage && (
                <div className="flex items-center justify-center space-x-2 mt-4">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setTradeDetailsPage(p => Math.max(1, p - 1))}
                    disabled={tradeDetailsPage === 1}
                  >
                    Previous
                  </Button>
                  <div className="text-sm">
                    Page {tradeDetailsPage} of {Math.ceil(selectedTrades.length / tradesPerPage)}
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setTradeDetailsPage(p => Math.min(Math.ceil(selectedTrades.length / tradesPerPage), p + 1))}
                    disabled={tradeDetailsPage === Math.ceil(selectedTrades.length / tradesPerPage)}
                  >
                    Next
                  </Button>
                </div>
              )}
