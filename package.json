{"name": "tradepivot", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "cross-env NODE_OPTIONS=\"--no-deprecation\" next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@floating-ui/react": "^0.27.8", "@floating-ui/react-dom": "^2.1.2", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-accordion": "^1.2.7", "@radix-ui/react-alert-dialog": "^1.1.7", "@radix-ui/react-avatar": "^1.1.6", "@radix-ui/react-collapsible": "^1.1.8", "@radix-ui/react-dialog": "^1.1.10", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-scroll-area": "^1.2.4", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toggle": "^1.1.2", "@radix-ui/react-toggle-group": "^1.1.2", "@radix-ui/react-tooltip": "^1.2.0", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.4", "@tanstack/react-query": "^5.76.1", "@tremor/react": "^3.18.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "critters": "^0.0.23", "date-fns": "^4.1.0", "focus-trap-react": "^11.0.3", "framer-motion": "^12.7.4", "lucide-react": "^0.330.0", "next": "^15.2.4", "next-themes": "^0.2.1", "quill": "^2.0.3", "react": "^18.2.0", "react-day-picker": "^9.5.1", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-hook-form": "^7.55.0", "react-markdown": "^10.1.0", "react-quill": "^2.0.0", "recharts": "^2.15.1", "remark-gfm": "^4.0.1", "sonner": "^1.4.0", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "xlsx": "^0.18.5", "zod": "^3.24.2"}, "devDependencies": {"@types/node": "^20.11.17", "@types/react": "^18.2.55", "@types/react-dom": "^18.2.19", "autoprefixer": "^10.4.17", "cross-env": "^7.0.3", "eslint": "^8.56.0", "eslint-config-next": "^15.3.2", "postcss": "^8.4.35", "sass": "^1.89.0", "tailwindcss": "^3.4.1", "typescript": "^5.3.3"}, "overrides": {"picomatch": "^4.0.2"}}