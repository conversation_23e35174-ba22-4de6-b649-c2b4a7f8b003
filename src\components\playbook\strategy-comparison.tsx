"use client"

import { useState, use<PERSON>ffect } from "react"
import { toast } from "sonner"
import { Strategy, StrategyPerformance } from "@/types/playbook"
import {
  getStrategiesForComparison,
  getLatestPerformanceForStrategies,
  prepareComparisonData
} from "@/lib/strategy-comparison-service"
import { format } from "date-fns"
import { useAccount } from "@/contexts/account-context"

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs"
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  <PERSON>ltip,
  Responsive<PERSON>ontainer,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>hart,
  <PERSON><PERSON>rid,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  PolarRadiusAxis,
  Radar,
} from "recharts"
import { Badge } from "@/components/ui/badge"
import { ArrowLeft, BarChart3, LineChart as LineChartIcon, PieChart as PieChartIcon, Radar as RadarIcon, TrendingUp } from "lucide-react"

interface StrategyComparisonProps {
  userId: string
  allStrategies: Strategy[]
  onBack?: () => void
}

export function StrategyComparison({
  userId,
  allStrategies,
  onBack,
}: StrategyComparisonProps) {
  const { selectedAccountId } = useAccount()
  const [selectedStrategies, setSelectedStrategies] = useState<string[]>([])
  const [strategies, setStrategies] = useState<Strategy[]>([])
  const [performanceData, setPerformanceData] = useState<Record<string, StrategyPerformance | null>>({})
  const [isLoading, setIsLoading] = useState(false)
  const [chartType, setChartType] = useState<"bar" | "line" | "radar" | "pie">("bar")
  const [activeTab, setActiveTab] = useState("overview")
  const [activeMetric, setActiveMetric] = useState("winRate")

  // Define colors for the selected strategies with better contrast
  const STRATEGY_COLORS = ['#3B82F6', '#10B981', '#F97316', '#EC4899', '#8B5CF6', '#06B6D4']

  // Load performance data when selected strategies change
  useEffect(() => {
    const loadPerformanceData = async () => {
      if (selectedStrategies.length === 0) {
        setStrategies([])
        setPerformanceData({})
        return
      }

      setIsLoading(true)
      try {
        // Fetch strategies
        const strategiesData = await getStrategiesForComparison(userId, selectedStrategies)
        setStrategies(strategiesData)

        // Fetch latest performance data
        const performanceData = await getLatestPerformanceForStrategies(userId, selectedStrategies, selectedAccountId)
        setPerformanceData(performanceData)
      } catch (error) {
        console.error("Error loading performance data:", error)
        toast.error("Failed to load performance data")
      } finally {
        setIsLoading(false)
      }
    }

    loadPerformanceData()
  }, [userId, selectedStrategies, selectedAccountId])

  // Toggle strategy selection
  const toggleStrategy = (strategyId: string) => {
    if (selectedStrategies.includes(strategyId)) {
      setSelectedStrategies(selectedStrategies.filter(id => id !== strategyId))
    } else {
      // Only allow up to 6 strategies
      if (selectedStrategies.length < 6) {
        setSelectedStrategies([...selectedStrategies, strategyId])
      } else {
        toast.warning("You can only compare up to 6 strategies at a time")
      }
    }
  }

  // Get color for a strategy
  const getStrategyColor = (strategyId: string) => {
    const index = selectedStrategies.indexOf(strategyId)
    return STRATEGY_COLORS[index % STRATEGY_COLORS.length]
  }

  // Prepare data for charts
  const comparisonData = prepareComparisonData(strategies, performanceData)

  // Get the appropriate label for the Y-axis
  const getYAxisLabel = () => {
    switch (activeMetric) {
      case "winRate": return "Win Rate (%)"
      case "profitLoss": return "Profit/Loss ($)"
      case "profitFactor": return "Profit Factor"
      case "expectancy": return "Expectancy"
      case "riskReward": return "Risk/Reward Ratio"
      case "totalTrades": return "Total Trades"
      default: return ""
    }
  }

  // Format the tooltip value based on the active metric
  const formatTooltipValue = (value: number) => {
    switch (activeMetric) {
      case "winRate": return `${value.toFixed(2)}%`
      case "profitLoss": return `$${value.toFixed(2)}`
      case "profitFactor": return value === 999 ? "∞" : value.toFixed(2)
      case "expectancy": return value.toFixed(2)
      case "riskReward": return value.toFixed(2)
      case "totalTrades": return value.toString()
      default: return value.toString()
    }
  }

  // Prepare data for radar chart - restructured for better tooltips
  const metrics = [
    { key: 'winRate', name: 'Win Rate', formatter: (val: number) => `${val.toFixed(2)}%` },
    { key: 'profitLoss', name: 'Profit/Loss', formatter: (val: number) => `$${val.toFixed(2)}` },
    { key: 'profitFactor', name: 'Profit Factor', formatter: (val: number) => val === 999 ? "∞" : val.toFixed(2) },
    { key: 'expectancy', name: 'Expectancy', formatter: (val: number) => val.toFixed(2) },
    { key: 'riskReward', name: 'Risk/Reward', formatter: (val: number) => val.toFixed(2) },
    { key: 'totalTrades', name: 'Total Trades', formatter: (val: number) => val.toString() }
  ];

  // Create radar data points with normalized values for visualization
  const radarData = comparisonData.map(strategy => {
    // Ensure all values have defaults to prevent null/undefined errors
    const winRate = strategy.winRate || 0;
    const profitLoss = strategy.profitLoss || 0;
    const profitFactor = strategy.profitFactor || 0;
    const expectancy = strategy.expectancy || 0;
    const riskReward = strategy.riskReward || 0;
    const totalTrades = strategy.totalTrades || 0;

    const dataPoint: any = {
      name: strategy.name || 'Unknown',
      strategyId: strategy.id,
      // Store original values for tooltips
      originalWinRate: winRate,
      originalProfitLoss: profitLoss,
      originalProfitFactor: profitFactor,
      originalExpectancy: expectancy,
      originalRiskReward: riskReward,
      originalTotalTrades: totalTrades,
      // Normalized values for visualization
      winRate: winRate,
      profitLoss: profitLoss > 0 ? Math.min(profitLoss, 1000) : 0, // Cap at 1000 for visualization
      profitFactor: profitFactor > 0 ? Math.min(profitFactor * 20, 100) : 0, // Scale up and cap
      expectancy: expectancy > 0 ? Math.min(expectancy * 10, 100) : 0, // Scale up and cap
      riskReward: riskReward > 0 ? Math.min(riskReward * 20, 100) : 0, // Scale up and cap
      totalTrades: Math.min(totalTrades, 100) // Cap at 100 for visualization
    };
    return dataPoint;
  })

  // Prepare data for the selected metric
  const metricData = comparisonData.map(strategy => ({
    name: strategy.name,
    value: activeMetric === "winRate" ? strategy.winRate :
           activeMetric === "profitLoss" ? strategy.profitLoss :
           activeMetric === "profitFactor" ? strategy.profitFactor :
           activeMetric === "expectancy" ? strategy.expectancy :
           activeMetric === "riskReward" ? strategy.riskReward :
           strategy.totalTrades,
    color: getStrategyColor(strategy.id)
  }))

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          {onBack && (
            <Button variant="ghost" size="sm" onClick={onBack}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back
            </Button>
          )}
          <h2 className="text-2xl font-bold tracking-tight">
            Strategy Comparison
          </h2>
        </div>
        {/* Chart type selection moved to metrics section */}
      </div>

      <Card className="mb-4 overflow-hidden border-0 shadow-md hover:shadow-lg transition-all duration-300">
        <CardHeader className="pb-2 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/30 dark:to-indigo-950/30 border-b">
          <CardTitle className="text-lg flex items-center">
            <BarChart3 className="mr-2 h-5 w-5 text-blue-500" />
            Strategy Selection
          </CardTitle>
          <CardDescription>
            Select up to 6 strategies to compare their performance metrics
          </CardDescription>
        </CardHeader>
        <CardContent className="pt-4">

          <div className="flex flex-wrap gap-2">
            {allStrategies.map((strategy) => {
              const isSelected = selectedStrategies.includes(strategy.id);
              const strategyColor = getStrategyColor(strategy.id);
              return (
                <Badge
                  key={strategy.id}
                  variant={isSelected ? "default" : "outline"}
                  className={`cursor-pointer transition-all duration-200 ${isSelected ?
                    'font-medium border-2 shadow-sm hover:shadow' : 'hover:bg-muted/50'}`}
                  style={isSelected ? {
                    backgroundColor: strategyColor,
                    borderColor: strategyColor,
                    color: '#FFFFFF',
                    textShadow: '0px 0px 2px rgba(0, 0, 0, 0.5)'
                  } : {}}
                  onClick={() => toggleStrategy(strategy.id)}
                >
                  {isSelected && (
                    <span className="mr-1 animate-pulse">●</span>
                  )}
                  {strategy.name}
                </Badge>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {isLoading ? (
        <Card className="border-0 shadow-md overflow-hidden">
          <CardContent className="pt-6 pb-6 text-center flex flex-col items-center justify-center min-h-[300px]">
            <div className="animate-pulse mb-4">
              <BarChart3 className="h-12 w-12 text-muted-foreground/50" />
            </div>
            <p className="text-muted-foreground font-medium">Loading strategy data...</p>
            <div className="mt-4 w-24 h-1 bg-blue-200 dark:bg-blue-800 rounded-full overflow-hidden">
              <div className="h-full bg-blue-500 animate-progress-indeterminate"></div>
            </div>
          </CardContent>
        </Card>
      ) : selectedStrategies.length === 0 ? (
        <Card className="border-0 shadow-md overflow-hidden">
          <CardContent className="pt-8 pb-8 text-center flex flex-col items-center justify-center min-h-[300px]">
            <div className="mb-4 p-4 rounded-full bg-muted/50">
              <BarChart3 className="h-10 w-10 text-muted-foreground/70" />
            </div>
            <h3 className="text-lg font-medium mb-2">No Strategies Selected</h3>
            <p className="text-muted-foreground max-w-md">Select strategies from above to compare their performance metrics and visualize the differences.</p>
          </CardContent>
        </Card>
      ) : (
        <Tabs defaultValue="overview" value={activeTab} onValueChange={setActiveTab} className="flex flex-col">
          <TabsList className="grid w-full grid-cols-2 rounded-none border-b bg-transparent mb-6">
            <TabsTrigger
              value="overview"
              className="rounded-none data-[state=active]:bg-background data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:font-medium"
            >
              <BarChart3 className="mr-2 h-4 w-4" />
              Overview
            </TabsTrigger>
            <TabsTrigger
              value="metrics"
              className="rounded-none data-[state=active]:bg-background data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:font-medium"
            >
              <TrendingUp className="mr-2 h-4 w-4" />
              Metrics
            </TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="mt-0">
            <Card className="border-0 shadow-md overflow-hidden strategy-card-hover">
              <CardHeader className="pb-2 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/30 dark:to-indigo-950/30 border-b">
                <CardTitle className="flex items-center">
                  <BarChart3 className="mr-2 h-5 w-5 text-blue-500" />
                  Strategy Overview
                </CardTitle>
                <CardDescription>
                  Compare key metrics across selected strategies
                </CardDescription>
              </CardHeader>
              <CardContent className="p-0">
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow className="bg-muted/30 hover:bg-muted/40">
                        <TableHead className="font-semibold">Strategy</TableHead>
                        <TableHead className="font-semibold">Status</TableHead>
                        <TableHead className="font-semibold">Win Rate</TableHead>
                        <TableHead className="font-semibold">Profit/Loss</TableHead>
                        <TableHead className="font-semibold">Expectancy</TableHead>
                        <TableHead className="font-semibold">Risk/Reward</TableHead>
                        <TableHead className="font-semibold">Trades</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {comparisonData.map((strategy) => (
                        <TableRow
                          key={strategy.id}
                          className="hover:bg-muted/30 transition-colors"
                        >
                          <TableCell className="font-medium">
                            <div className="flex items-center">
                              <div
                                className="w-3 h-3 rounded-full mr-2"
                                style={{ backgroundColor: getStrategyColor(strategy.id) }}
                              />
                              {strategy.name}
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge variant={
                              strategy.status === 'active' ? 'default' :
                              strategy.status === 'testing' ? 'secondary' :
                              'outline'
                            }>
                              {strategy.status}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center">
                              <span className={`font-medium ${strategy.winRate >= 50 ? "text-green-500" : "text-red-500"}`}>
                                {strategy.winRate.toFixed(2)}%
                              </span>
                              <div className="ml-2 w-16 h-2 bg-muted rounded-full overflow-hidden">
                                <div
                                  className={`h-full ${strategy.winRate >= 50 ? "bg-green-500" : "bg-red-500"}`}
                                  style={{ width: `${Math.min(100, strategy.winRate)}%` }}
                                />
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <span className={`font-medium ${strategy.profitLoss >= 0 ? "text-green-500" : "text-red-500"}`}>
                              ${strategy.profitLoss.toFixed(2)}
                            </span>
                          </TableCell>
                          <TableCell>
                            <span className={`font-medium ${strategy.expectancy >= 0 ? "text-green-500" : "text-red-500"}`}>
                              {strategy.expectancy.toFixed(2)}
                            </span>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center">
                              <span className="font-medium">{strategy.riskReward.toFixed(2)}</span>
                              {strategy.riskReward >= 1 && (
                                <span className="ml-2 text-xs px-1.5 py-0.5 rounded bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400">
                                  Good
                                </span>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>
                            <span className="font-medium">{strategy.totalTrades}</span>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="metrics" className="mt-0">
            <div className="grid grid-cols-1 gap-4">
              <Card className="border-0 shadow-md overflow-hidden strategy-card-hover">
                <CardHeader className="pb-2 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/30 dark:to-indigo-950/30 border-b">
                  <div className="flex justify-between items-center">
                    <CardTitle className="flex items-center">
                      <BarChart3 className="mr-2 h-5 w-5 text-blue-500" />
                      Metric Comparison
                    </CardTitle>
                    <div className="flex items-center gap-4">
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-muted-foreground">Select metric:</span>
                        <Select value={activeMetric} onValueChange={setActiveMetric}>
                          <SelectTrigger className="w-[180px] border-blue-200 dark:border-blue-900/50 bg-white/80 dark:bg-slate-900/50">
                            <SelectValue placeholder="Select metric" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="winRate">Win Rate</SelectItem>
                            <SelectItem value="profitLoss">Profit/Loss</SelectItem>
                            <SelectItem value="profitFactor">Profit Factor</SelectItem>
                            <SelectItem value="expectancy">Expectancy</SelectItem>
                            <SelectItem value="riskReward">Risk/Reward</SelectItem>
                            <SelectItem value="totalTrades">Total Trades</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="flex items-center gap-1">
                        <Button
                          variant={chartType === "bar" ? "default" : "outline"}
                          size="sm"
                          onClick={() => setChartType("bar")}
                          className="h-8 w-8 p-0 relative"
                          title="Bar Chart"
                        >
                          <BarChart3 className="h-4 w-4" />
                          <span className="sr-only">Bar Chart</span>
                        </Button>
                        <Button
                          variant={chartType === "line" ? "default" : "outline"}
                          size="sm"
                          onClick={() => setChartType("line")}
                          className="h-8 w-8 p-0 relative"
                          title="Line Chart"
                        >
                          <LineChartIcon className="h-4 w-4" />
                          <span className="sr-only">Line Chart</span>
                        </Button>
                        <Button
                          variant={chartType === "radar" ? "default" : "outline"}
                          size="sm"
                          onClick={() => setChartType("radar")}
                          className="h-8 w-8 p-0 relative"
                          title="Radar Chart"
                        >
                          <RadarIcon className="h-4 w-4" />
                          <span className="sr-only">Radar Chart</span>
                        </Button>
                        <Button
                          variant={chartType === "pie" ? "default" : "outline"}
                          size="sm"
                          onClick={() => setChartType("pie")}
                          className="h-8 w-8 p-0 relative"
                          title="Pie Chart"
                        >
                          <PieChartIcon className="h-4 w-4" />
                          <span className="sr-only">Pie Chart</span>
                        </Button>
                      </div>
                    </div>
                  </div>
                  <CardDescription>
                    Compare {activeMetric === "winRate" ? "win rates" :
                             activeMetric === "profitLoss" ? "profit and loss" :
                             activeMetric === "profitFactor" ? "profit factors" :
                             activeMetric === "expectancy" ? "expectancy values" :
                             activeMetric === "riskReward" ? "risk/reward ratios" :
                             "trade counts"} across strategies
                  </CardDescription>
                </CardHeader>
                <CardContent className="pt-4">
                  <div className="h-[400px]">
                    {chartType === "bar" && (
                      <ResponsiveContainer width="100%" height="100%">
                        <BarChart
                          data={metricData}
                          margin={{ top: 20, right: 30, left: 40, bottom: 60 }}
                        >
                          <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" opacity={0.3} />
                          <XAxis
                            dataKey="name"
                            angle={-45}
                            textAnchor="end"
                            height={60}
                            tick={{ fill: 'hsl(var(--muted-foreground))', fontSize: 11, opacity: 0.7 }}
                            stroke="hsl(var(--border))"
                            strokeOpacity={0.5}
                          />
                          <YAxis
                            label={{
                              value: getYAxisLabel(),
                              angle: -90,
                              position: 'insideLeft',
                              style: { fill: 'hsl(var(--muted-foreground))', fontSize: 12 }
                            }}
                            tick={{ fill: 'hsl(var(--muted-foreground))', fontSize: 11, opacity: 0.7 }}
                            stroke="hsl(var(--border))"
                            strokeOpacity={0.5}
                          />
                          <Tooltip
                            formatter={(value: number) => [formatTooltipValue(value), getYAxisLabel()]}
                            contentStyle={{
                              backgroundColor: 'hsl(var(--card))',
                              borderColor: 'hsl(var(--border))',
                              color: 'hsl(var(--foreground))'
                            }}
                          />
                          <Bar dataKey="value">
                            {metricData.map((entry, index) => (
                              <Cell key={`cell-${index}`} fill={entry.color} />
                            ))}
                          </Bar>
                        </BarChart>
                      </ResponsiveContainer>
                    )}

                    {chartType === "line" && (
                      <ResponsiveContainer width="100%" height="100%">
                        <LineChart
                          data={metricData}
                          margin={{ top: 20, right: 30, left: 40, bottom: 60 }}
                        >
                          <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" opacity={0.3} />
                          <XAxis
                            dataKey="name"
                            angle={-45}
                            textAnchor="end"
                            height={60}
                            tick={{ fill: 'hsl(var(--muted-foreground))', fontSize: 11, opacity: 0.7 }}
                            stroke="hsl(var(--border))"
                            strokeOpacity={0.5}
                          />
                          <YAxis
                            label={{
                              value: getYAxisLabel(),
                              angle: -90,
                              position: 'insideLeft',
                              style: { fill: 'hsl(var(--muted-foreground))', fontSize: 12 }
                            }}
                            tick={{ fill: 'hsl(var(--muted-foreground))', fontSize: 11, opacity: 0.7 }}
                            stroke="hsl(var(--border))"
                            strokeOpacity={0.5}
                          />
                          <Tooltip
                            formatter={(value: number) => [formatTooltipValue(value), getYAxisLabel()]}
                            contentStyle={{
                              backgroundColor: 'hsl(var(--card))',
                              borderColor: 'hsl(var(--border))',
                              color: 'hsl(var(--foreground))'
                            }}
                          />
                          <Line
                            type="monotone"
                            dataKey="value"
                            stroke="#8884d8"
                            strokeWidth={2}
                            dot={{ r: 6 }}
                          >
                            {metricData.map((entry, index) => (
                              <Cell key={`cell-${index}`} fill={entry.color} />
                            ))}
                          </Line>
                        </LineChart>
                      </ResponsiveContainer>
                    )}

                    {chartType === "pie" && (
                      <ResponsiveContainer width="100%" height="100%">
                        <PieChart>
                          <Pie
                            data={metricData}
                            cx="50%"
                            cy="50%"
                            labelLine={false}
                            outerRadius={150}
                            fill="#8884d8"
                            dataKey="value"
                            nameKey="name"
                            label={({ name, percent }) =>
                              `${name}: ${formatTooltipValue(
                                metricData.find(item => item.name === name)?.value || 0
                              )}`
                            }
                          >
                            {metricData.map((entry, index) => (
                              <Cell key={`cell-${index}`} fill={entry.color} />
                            ))}
                          </Pie>
                          <Tooltip
                            formatter={(value: number) => [formatTooltipValue(value), getYAxisLabel()]}
                            contentStyle={{
                              backgroundColor: 'hsl(var(--card))',
                              borderColor: 'hsl(var(--border))',
                              color: 'hsl(var(--foreground))'
                            }}
                          />
                          <Legend />
                        </PieChart>
                      </ResponsiveContainer>
                    )}

                    {chartType === "radar" && (
                      <ResponsiveContainer width="100%" height="100%">
                        <RadarChart cx="50%" cy="50%" outerRadius="80%" data={radarData}>
                          <PolarGrid stroke="hsl(var(--border))" />
                          <PolarAngleAxis
                            dataKey="name"
                            tick={{ fill: 'hsl(var(--muted-foreground))', fontSize: 11 }}
                          />
                          <PolarRadiusAxis
                            angle={90}
                            domain={[0, 100]}
                            tick={{ fill: 'hsl(var(--muted-foreground))', fontSize: 10 }}
                          />
                          {comparisonData.map((strategy, index) => (
                            <Radar
                              key={strategy.id}
                              name={strategy.name}
                              dataKey={activeMetric}
                              stroke={getStrategyColor(strategy.id)}
                              fill={getStrategyColor(strategy.id)}
                              fillOpacity={0.3}
                            />
                          ))}
                          <Legend />
                          <Tooltip
                            content={({ active, payload }) => {
                              if (active && payload && payload.length) {
                                try {
                                  const dataPoint = payload[0].payload;
                                  const metricKey = payload[0].dataKey as string;
                                  const strategyName = dataPoint?.name || 'Unknown';

                                  // Get the original value based on the metric
                                  const originalKey = `original${metricKey.charAt(0).toUpperCase() + metricKey.slice(1)}`;
                                  const originalValue = dataPoint?.[originalKey] || 0;

                                  // Find the metric formatter
                                  const metric = metrics.find(m => m.key === metricKey);
                                  const formattedValue = metric ? metric.formatter(originalValue) : originalValue.toString();

                                  return (
                                    <div className="bg-card border border-border p-2 rounded-md shadow-md">
                                      <p className="font-medium text-sm">{strategyName}</p>
                                      <p className="text-sm">
                                        <span className="text-muted-foreground">{metric?.name || metricKey}: </span>
                                        <span className="font-medium">{formattedValue}</span>
                                      </p>
                                    </div>
                                  );
                                } catch (error) {
                                  console.error('Error rendering tooltip:', error);
                                  return (
                                    <div className="bg-card border border-border p-2 rounded-md shadow-md">
                                      <p className="text-sm">Error displaying tooltip</p>
                                    </div>
                                  );
                                }
                              }
                              return null;
                            }}
                          />
                        </RadarChart>
                      </ResponsiveContainer>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Detailed Strategy Summary Card */}
              <Card className="border-0 shadow-md overflow-hidden strategy-card-hover mt-4">
                <CardHeader className="pb-2 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/30 dark:to-indigo-950/30 border-b">
                  <CardTitle className="flex items-center">
                    <PieChartIcon className="mr-2 h-5 w-5 text-blue-500" />
                    Strategy Details
                  </CardTitle>
                  <CardDescription>
                    Detailed performance metrics for each selected strategy
                  </CardDescription>
                </CardHeader>
                <CardContent className="pt-4">
                  <div className="overflow-x-auto">
                    <table className="w-full border-collapse text-sm">
                      <thead>
                        <tr className="border-b">
                          <th className="text-left py-1.5 px-2 font-medium text-muted-foreground">Strategy</th>
                          <th className="text-center py-1.5 px-2 font-medium text-muted-foreground">W/L</th>
                          <th className="text-right py-1.5 px-2 font-medium text-muted-foreground">Win Rate</th>
                          <th className="text-right py-1.5 px-2 font-medium text-muted-foreground">Trades</th>
                          <th className="text-right py-1.5 px-2 font-medium text-muted-foreground">Avg Win</th>
                          <th className="text-right py-1.5 px-2 font-medium text-muted-foreground">Avg Loss</th>
                          <th className="text-right py-1.5 px-2 font-medium text-muted-foreground">P/L</th>
                          <th className="text-right py-1.5 px-2 font-medium text-muted-foreground">PF</th>
                          <th className="text-right py-1.5 px-2 font-medium text-muted-foreground">R/R</th>
                        </tr>
                      </thead>
                      <tbody className="text-sm">
                        {comparisonData.map((strategy) => {
                          const strategyColor = getStrategyColor(strategy.id);
                          return (
                            <tr key={strategy.id} className="border-b hover:bg-muted/30">
                              <td className="py-1.5 px-2">
                                <div className="flex items-center">
                                  <div
                                    className="w-2.5 h-2.5 rounded-full mr-1.5 flex-shrink-0"
                                    style={{ backgroundColor: strategyColor }}
                                  />
                                  <span className="font-medium">{strategy.name}</span>
                                  <Badge
                                    variant={strategy.status === 'active' ? 'default' : 'secondary'}
                                    className="ml-1.5 text-xs"
                                  >
                                    {strategy.status}
                                  </Badge>
                                </div>
                              </td>
                              <td className="py-1.5 px-2">
                                <div className="flex items-center justify-center gap-1">
                                  <span className="text-xs text-green-500 font-medium">{strategy.winningTrades || 0}</span>
                                  <span className="text-xs text-muted-foreground">/</span>
                                  <span className="text-xs text-red-500 font-medium">{strategy.losingTrades || 0}</span>
                                  <div className="ml-1 w-12 bg-muted h-1.5 rounded-full overflow-hidden">
                                    <div
                                      className="bg-green-500 h-full"
                                      style={{ width: `${strategy.winRate || 0}%` }}
                                    ></div>
                                  </div>
                                </div>
                              </td>
                              <td className={`py-1.5 px-2 text-right font-medium ${(strategy.winRate || 0) >= 50 ? "text-green-500" : "text-red-500"}`}>
                                {(strategy.winRate || 0).toFixed(2)}%
                              </td>
                              <td className="py-1.5 px-2 text-right font-medium">
                                {strategy.totalTrades || 0}
                              </td>
                              <td className="py-1.5 px-2 text-right font-medium text-green-500">
                                ${(strategy.averageWin || 0).toFixed(2)}
                              </td>
                              <td className="py-1.5 px-2 text-right font-medium text-red-500">
                                ${(strategy.averageLoss || 0).toFixed(2)}
                              </td>
                              <td className={`py-1.5 px-2 text-right font-medium ${(strategy.profitLoss || 0) >= 0 ? "text-green-500" : "text-red-500"}`}>
                                ${(strategy.profitLoss || 0).toFixed(2)}
                              </td>
                              <td className={`py-1.5 px-2 text-right font-medium ${(strategy.profitFactor || 0) >= 1 ? "text-green-500" : "text-red-500"}`}>
                                {strategy.profitFactor === 999 ? "∞" : (strategy.profitFactor || 0).toFixed(2)}
                              </td>
                              <td className="py-1.5 px-2 text-right font-medium">
                                {(strategy.riskReward || 0).toFixed(2)}
                              </td>
                            </tr>
                          );
                        })}
                      </tbody>
                    </table>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Comparison tab removed */}
        </Tabs>
      )}
    </div>
  )
}
