2025-Jun-03 16:47:47.896064
Starting deployment of shawa0507/trade_journal_production:main to localhost.
2025-Jun-03 16:47:48.206396
Preparing container with helper image: ghcr.io/coollabsio/coolify-helper:1.0.8.
2025-Jun-03 16:47:48.360560
[CMD]: docker stop --time=30 lgsgg0sggg04c80wcc8wcos4
2025-Jun-03 16:47:48.360560
Error response from daemon: No such container: lgsgg0sggg04c80wcc8wcos4
2025-Jun-03 16:47:48.521404
[CMD]: docker rm -f lgsgg0sggg04c80wcc8wcos4
2025-Jun-03 16:47:48.521404
Error response from daemon: No such container: lgsgg0sggg04c80wcc8wcos4
2025-Jun-03 16:47:48.839883
[CMD]: docker run -d --network coolify --name lgsgg0sggg04c80wcc8wcos4 --rm -v /var/run/docker.sock:/var/run/docker.sock ghcr.io/coollabsio/coolify-helper:1.0.8
2025-Jun-03 16:47:48.839883
14e004393d49ba2eb07aa94d46e16d4b519b31c10b43d3c25ffe92e290cc4edf
2025-Jun-03 16:47:51.868609
[CMD]: docker exec lgsgg0sggg04c80wcc8wcos4 bash -c 'GIT_SSH_COMMAND="ssh -o ConnectTimeout=30 -p 22 -o Port=22 -o LogLevel=ERROR -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null" git ls-remote https://x-access-token:<REDACTED>@github.com/shawa0507/trade_journal_production.git main'
2025-Jun-03 16:47:51.868609
c023052e4943fff5e05a02e243d2f8ccb9fcc3fe	refs/heads/main
2025-Jun-03 16:47:52.190982
----------------------------------------
2025-Jun-03 16:47:52.213558
Importing shawa0507/trade_journal_production:main (commit sha HEAD) to /artifacts/lgsgg0sggg04c80wcc8wcos4.
2025-Jun-03 16:47:52.811195
[CMD]: docker exec lgsgg0sggg04c80wcc8wcos4 bash -c 'git clone -b "main" https://x-access-token:<REDACTED>@github.com/shawa0507/trade_journal_production.git /artifacts/lgsgg0sggg04c80wcc8wcos4 && cd /artifacts/lgsgg0sggg04c80wcc8wcos4 && GIT_SSH_COMMAND="ssh -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null" git submodule update --init --recursive && cd /artifacts/lgsgg0sggg04c80wcc8wcos4 && GIT_SSH_COMMAND="ssh -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null" git lfs pull'
2025-Jun-03 16:47:52.811195
Cloning into '/artifacts/lgsgg0sggg04c80wcc8wcos4'...
2025-Jun-03 16:47:55.820653
[CMD]: docker exec lgsgg0sggg04c80wcc8wcos4 bash -c 'cd /artifacts/lgsgg0sggg04c80wcc8wcos4 && git log -1 c023052e4943fff5e05a02e243d2f8ccb9fcc3fe --pretty=%B'
2025-Jun-03 16:47:55.820653
TIPTAP REMOVAL: Clean build without problematic dependencies
2025-Jun-03 16:47:55.820653
2025-Jun-03 16:47:55.820653
Deployment Type: production-release
2025-Jun-03 16:47:55.820653
Deployment Target: coolify-production
2025-Jun-03 16:47:55.820653
Source: shawa0507/trademaster@48014a7008ecffc52d5b186408895a102243b4ef
2025-Jun-03 16:47:55.820653
Workflow: Deploy to Production Repository (Coolify)
2025-Jun-03 16:47:55.820653
Run ID: 15423064050
2025-Jun-03 16:47:55.820653
Triggered by: shawa0507
2025-Jun-03 16:47:55.820653
2025-Jun-03 16:47:55.820653
This is a manual Coolify deployment containing only production-necessary files.
2025-Jun-03 16:47:55.820653
Development files, tests, and documentation have been excluded.
2025-Jun-03 16:47:55.820653
Includes simplified Nixpacks configuration with npm enforcement.
2025-Jun-03 16:47:55.820653
2025-Jun-03 16:47:55.820653
Note: Dependencies and building are handled by Coolify using Nixpacks with npm, not GitHub Actions.
2025-Jun-03 16:47:56.321539
Generating nixpacks configuration with: nixpacks plan -f toml  /artifacts/lgsgg0sggg04c80wcc8wcos4
2025-Jun-03 16:47:56.813062
[CMD]: docker exec lgsgg0sggg04c80wcc8wcos4 bash -c 'nixpacks plan -f toml  /artifacts/lgsgg0sggg04c80wcc8wcos4'
2025-Jun-03 16:47:56.813062
providers = []
2025-Jun-03 16:47:56.813062
buildImage = 'ghcr.io/railwayapp/nixpacks:ubuntu-**********'
2025-Jun-03 16:47:56.813062
2025-Jun-03 16:47:56.813062
[variables]
2025-Jun-03 16:47:56.813062
CI = 'true'
2025-Jun-03 16:47:56.813062
COREPACK_ENABLE_STRICT = '0'
2025-Jun-03 16:47:56.813062
NIXPACKS_METADATA = 'node'
2025-Jun-03 16:47:56.813062
NIXPACKS_NO_CACHE = '1'
2025-Jun-03 16:47:56.813062
NODE_ENV = 'production'
2025-Jun-03 16:47:56.813062
NPM_CONFIG_PACKAGE_MANAGER = 'npm'
2025-Jun-03 16:47:56.813062
NPM_CONFIG_PRODUCTION = 'false'
2025-Jun-03 16:47:56.813062
[phases.build]
2025-Jun-03 16:47:56.813062
dependsOn = ['install']
2025-Jun-03 16:47:56.813062
cmds = ['npm run build']
2025-Jun-03 16:47:56.813062
cacheDirectories = [
2025-Jun-03 16:47:56.813062
'.next/cache',
2025-Jun-03 16:47:56.813062
'node_modules/.cache',
2025-Jun-03 16:47:56.813062
]
2025-Jun-03 16:47:56.813062
2025-Jun-03 16:47:56.813062
[phases.install]
2025-Jun-03 16:47:56.813062
dependsOn = ['setup']
2025-Jun-03 16:47:56.813062
cmds = ['npm ci']
2025-Jun-03 16:47:56.813062
cacheDirectories = ['/root/.npm']
2025-Jun-03 16:47:56.813062
paths = ['/app/node_modules/.bin']
2025-Jun-03 16:47:56.813062
2025-Jun-03 16:47:56.813062
[phases.setup]
2025-Jun-03 16:47:56.813062
nixPkgs = ['nodejs_18']
2025-Jun-03 16:47:56.813062
nixLibs = ['gcc-unwrapped']
2025-Jun-03 16:47:56.813062
nixOverlays = ['https://github.com/railwayapp/nix-npm-overlay/archive/main.tar.gz']
2025-Jun-03 16:47:56.813062
nixpkgsArchive = 'ffeebf0acf3ae8b29f8c7049cd911b9636efd7e7'
2025-Jun-03 16:47:56.813062
cmds = [
2025-Jun-03 16:47:56.813062
"echo 'Setting up npm environment'",
2025-Jun-03 16:47:56.813062
'rm -f pnpm-lock.yaml yarn.lock .pnpmfile.cjs || true',
2025-Jun-03 16:47:56.813062
'npm --version',
2025-Jun-03 16:47:56.813062
]
2025-Jun-03 16:47:56.813062
2025-Jun-03 16:47:56.813062
[start]
2025-Jun-03 16:47:56.813062
cmd = 'npm start'
2025-Jun-03 16:47:57.146799
[CMD]: docker exec lgsgg0sggg04c80wcc8wcos4 bash -c 'nixpacks detect /artifacts/lgsgg0sggg04c80wcc8wcos4'
2025-Jun-03 16:47:57.146799
node
2025-Jun-03 16:47:57.162103
Found application type: node.
2025-Jun-03 16:47:57.179878
If you need further customization, please check the documentation of Nixpacks: https://nixpacks.com/docs/providers/node
2025-Jun-03 16:47:57.211702
Final Nixpacks plan: {
2025-Jun-03 16:47:57.211702
"providers": [],
2025-Jun-03 16:47:57.211702
"buildImage": "ghcr.io\/railwayapp\/nixpacks:ubuntu-**********",
2025-Jun-03 16:47:57.211702
"variables": {
2025-Jun-03 16:47:57.211702
"CI": "true",
2025-Jun-03 16:47:57.211702
"COREPACK_ENABLE_STRICT": "0",
2025-Jun-03 16:47:57.211702
"NIXPACKS_METADATA": "node",
2025-Jun-03 16:47:57.211702
"NIXPACKS_NO_CACHE": "1",
2025-Jun-03 16:47:57.211702
"NODE_ENV": "production",
2025-Jun-03 16:47:57.211702
"NPM_CONFIG_PACKAGE_MANAGER": "npm",
2025-Jun-03 16:47:57.211702
"NPM_CONFIG_PRODUCTION": "false",
2025-Jun-03 16:47:57.211702
"SOURCE_COMMIT": "c023052e4943fff5e05a02e243d2f8ccb9fcc3fe"
2025-Jun-03 16:47:57.211702
},
2025-Jun-03 16:47:57.211702
"phases": {
2025-Jun-03 16:47:57.211702
"build": {
2025-Jun-03 16:47:57.211702
"dependsOn": [
2025-Jun-03 16:47:57.211702
"install"
2025-Jun-03 16:47:57.211702
],
2025-Jun-03 16:47:57.211702
"cmds": [
2025-Jun-03 16:47:57.211702
"npm run build"
2025-Jun-03 16:47:57.211702
],
2025-Jun-03 16:47:57.211702
"cacheDirectories": [
2025-Jun-03 16:47:57.211702
".next\/cache",
2025-Jun-03 16:47:57.211702
"node_modules\/.cache"
2025-Jun-03 16:47:57.211702
]
2025-Jun-03 16:47:57.211702
},
2025-Jun-03 16:47:57.211702
"install": {
2025-Jun-03 16:47:57.211702
"dependsOn": [
2025-Jun-03 16:47:57.211702
"setup"
2025-Jun-03 16:47:57.211702
],
2025-Jun-03 16:47:57.211702
"cmds": [
2025-Jun-03 16:47:57.211702
"npm ci"
2025-Jun-03 16:47:57.211702
],
2025-Jun-03 16:47:57.211702
"cacheDirectories": [
2025-Jun-03 16:47:57.211702
"\/root\/.npm"
2025-Jun-03 16:47:57.211702
],
2025-Jun-03 16:47:57.211702
"paths": [
2025-Jun-03 16:47:57.211702
"\/app\/node_modules\/.bin"
2025-Jun-03 16:47:57.211702
]
2025-Jun-03 16:47:57.211702
},
2025-Jun-03 16:47:57.211702
"setup": {
2025-Jun-03 16:47:57.211702
"nixPkgs": [
2025-Jun-03 16:47:57.211702
"nodejs_18"
2025-Jun-03 16:47:57.211702
],
2025-Jun-03 16:47:57.211702
"nixLibs": [
2025-Jun-03 16:47:57.211702
"gcc-unwrapped"
2025-Jun-03 16:47:57.211702
],
2025-Jun-03 16:47:57.211702
"nixOverlays": [
2025-Jun-03 16:47:57.211702
"https:\/\/github.com\/railwayapp\/nix-npm-overlay\/archive\/main.tar.gz"
2025-Jun-03 16:47:57.211702
],
2025-Jun-03 16:47:57.211702
"nixpkgsArchive": "ffeebf0acf3ae8b29f8c7049cd911b9636efd7e7",
2025-Jun-03 16:47:57.211702
"cmds": [
2025-Jun-03 16:47:57.211702
"echo 'Setting up npm environment'",
2025-Jun-03 16:47:57.211702
"rm -f pnpm-lock.yaml yarn.lock .pnpmfile.cjs || true",
2025-Jun-03 16:47:57.211702
"npm --version"
2025-Jun-03 16:47:57.211702
],
2025-Jun-03 16:47:57.211702
"aptPkgs": [
2025-Jun-03 16:47:57.211702
"curl",
2025-Jun-03 16:47:57.211702
"wget"
2025-Jun-03 16:47:57.211702
]
2025-Jun-03 16:47:57.211702
}
2025-Jun-03 16:47:57.211702
},
2025-Jun-03 16:47:57.211702
"start": {
2025-Jun-03 16:47:57.211702
"cmd": "npm start"
2025-Jun-03 16:47:57.211702
}
2025-Jun-03 16:47:57.211702
}
2025-Jun-03 16:47:58.610094
----------------------------------------
2025-Jun-03 16:47:58.632109
Building docker image started.
2025-Jun-03 16:47:58.638603
To check the current progress, click on Show Debug Logs.
2025-Jun-03 16:47:59.549739
[CMD]: docker exec lgsgg0sggg04c80wcc8wcos4 bash -c 'nixpacks build -c /artifacts/thegameplan.json --no-cache --no-error-without-start -n isk8okc4soc48k0o8sswo4os:c023052e4943fff5e05a02e243d2f8ccb9fcc3fe /artifacts/lgsgg0sggg04c80wcc8wcos4 -o /artifacts/lgsgg0sggg04c80wcc8wcos4'
2025-Jun-03 16:47:59.549739
2025-Jun-03 16:47:59.549739
╔═════════════════════════ Nixpacks v1.34.1 ════════════════════════╗
2025-Jun-03 16:47:59.549739
║ setup      │ pkgs: nodejs_18, curl, wget                          ║
2025-Jun-03 16:47:59.549739
║            │ cmds: echo 'Setting up npm environment'              ║
2025-Jun-03 16:47:59.549739
║            │ rm -f pnpm-lock.yaml yarn.lock .pnpmfile.cjs || true ║
2025-Jun-03 16:47:59.549739
║            │ npm --version                                        ║
2025-Jun-03 16:47:59.549739
║───────────────────────────────────────────────────────────────────║
2025-Jun-03 16:47:59.549739
║ install    │ npm ci                                               ║
2025-Jun-03 16:47:59.549739
║───────────────────────────────────────────────────────────────────║
2025-Jun-03 16:47:59.549739
║ build      │ npm run build                                        ║
2025-Jun-03 16:47:59.549739
║───────────────────────────────────────────────────────────────────║
2025-Jun-03 16:47:59.549739
║ start      │ npm start                                            ║
2025-Jun-03 16:47:59.549739
╚═══════════════════════════════════════════════════════════════════╝
2025-Jun-03 16:47:59.575867
Saved output to:
2025-Jun-03 16:47:59.575867
/artifacts/lgsgg0sggg04c80wcc8wcos4
2025-Jun-03 16:48:00.748257
[CMD]: docker exec lgsgg0sggg04c80wcc8wcos4 bash -c 'cat /artifacts/build.sh'
2025-Jun-03 16:48:00.748257
docker build --no-cache --add-host coolify:******** --add-host coolify-db:******** --add-host coolify-realtime:******** --add-host coolify-redis:******** --network host -f /artifacts/lgsgg0sggg04c80wcc8wcos4/.nixpacks/Dockerfile --build-arg CI='true' --build-arg COREPACK_ENABLE_STRICT='0' --build-arg NIXPACKS_METADATA='node' --build-arg NIXPACKS_NO_CACHE='1' --build-arg NODE_ENV='production' --build-arg NPM_CONFIG_PACKAGE_MANAGER='npm' --build-arg NPM_CONFIG_PRODUCTION='false' --build-arg SOURCE_COMMIT='c023052e4943fff5e05a02e243d2f8ccb9fcc3fe' --build-arg 'COOLIFY_URL=https://trade.flickmail.site' --build-arg 'COOLIFY_FQDN=trade.flickmail.site' --build-arg 'COOLIFY_BRANCH=main' --build-arg 'COOLIFY_RESOURCE_UUID=isk8okc4soc48k0o8sswo4os' --build-arg 'COOLIFY_CONTAINER_NAME=isk8okc4soc48k0o8sswo4os-164745865864' --progress plain -t isk8okc4soc48k0o8sswo4os:c023052e4943fff5e05a02e243d2f8ccb9fcc3fe /artifacts/lgsgg0sggg04c80wcc8wcos4
2025-Jun-03 16:48:02.768194
[CMD]: docker exec lgsgg0sggg04c80wcc8wcos4 bash -c 'bash /artifacts/build.sh'
2025-Jun-03 16:48:02.768194
#0 building with "default" instance using docker driver
2025-Jun-03 16:48:02.768194
2025-Jun-03 16:48:02.768194
#1 [internal] load build definition from Dockerfile
2025-Jun-03 16:48:02.768194
#1 transferring dockerfile: 30B 0.0s
2025-Jun-03 16:48:02.934260
#1 transferring dockerfile: 1.29kB 0.0s done
2025-Jun-03 16:48:02.934260
#1 DONE 0.2s
2025-Jun-03 16:48:02.934260
2025-Jun-03 16:48:02.934260
#2 [internal] load metadata for ghcr.io/railwayapp/nixpacks:ubuntu-**********
2025-Jun-03 16:48:03.446813
#2 DONE 0.5s
2025-Jun-03 16:48:03.549499
#3 [internal] load .dockerignore
2025-Jun-03 16:48:03.549499
#3 transferring context:
2025-Jun-03 16:48:03.723528
#3 transferring context: 2B done
2025-Jun-03 16:48:03.723528
#3 DONE 0.1s
2025-Jun-03 16:48:03.723528
2025-Jun-03 16:48:03.723528
#4 [ 1/15] FROM ghcr.io/railwayapp/nixpacks:ubuntu-**********@sha256:ed406b77fb751927991b8655e76c33a4521c4957c2afeab293be7c63c2a373d2
2025-Jun-03 16:48:03.723528
#4 DONE 0.0s
2025-Jun-03 16:48:03.723528
2025-Jun-03 16:48:03.723528
#5 [ 2/15] WORKDIR /app/
2025-Jun-03 16:48:03.723528
#5 CACHED
2025-Jun-03 16:48:03.723528
2025-Jun-03 16:48:03.723528
#6 [internal] load build context
2025-Jun-03 16:48:05.096646
#6 transferring context: 3.37MB 1.3s done
2025-Jun-03 16:48:05.096646
#6 DONE 1.5s
2025-Jun-03 16:48:05.360956
#7 [ 3/15] COPY .nixpacks/nixpkgs-ffeebf0acf3ae8b29f8c7049cd911b9636efd7e7.nix .nixpacks/nixpkgs-ffeebf0acf3ae8b29f8c7049cd911b9636efd7e7.nix
2025-Jun-03 16:48:05.649712
#7 DONE 0.5s
2025-Jun-03 16:48:05.795098
#8 [ 4/15] RUN nix-env -if .nixpacks/nixpkgs-ffeebf0acf3ae8b29f8c7049cd911b9636efd7e7.nix && nix-collect-garbage -d
2025-Jun-03 16:48:06.633415
#8 0.986 unpacking 'https://github.com/NixOS/nixpkgs/archive/ffeebf0acf3ae8b29f8c7049cd911b9636efd7e7.tar.gz' into the Git cache...
2025-Jun-03 16:49:22.936671
#8 77.28 unpacking 'https://github.com/railwayapp/nix-npm-overlay/archive/main.tar.gz' into the Git cache...
2025-Jun-03 16:49:24.095500
#8 78.45 installing 'ffeebf0acf3ae8b29f8c7049cd911b9636efd7e7-env'
2025-Jun-03 16:49:25.221705
#8 79.57 these 4 derivations will be built:
2025-Jun-03 16:49:25.221705
#8 79.57   /nix/store/1f4a312hz9m6y1ssip52drgkim8az4d6-libraries.drv
2025-Jun-03 16:49:25.221705
#8 79.57   /nix/store/79g4v87v1cgrx5vlwzcagcs6v8ps8fk2-ffeebf0acf3ae8b29f8c7049cd911b9636efd7e7-env.drv
2025-Jun-03 16:49:25.221705
#8 79.57   /nix/store/9smjjb5pkmcbykz8p4786s3a4nq6m030-builder.pl.drv
2025-Jun-03 16:49:25.221705
#8 79.57   /nix/store/frvrfq3qfrqzmlj15jznw81xcifalqb1-ffeebf0acf3ae8b29f8c7049cd911b9636efd7e7-env.drv
2025-Jun-03 16:49:25.221705
#8 79.57 these 43 paths will be fetched (47.87 MiB download, 242.80 MiB unpacked):
2025-Jun-03 16:49:25.221705
#8 79.57   /nix/store/cf7gkacyxmm66lwl5nj6j6yykbrg4q5c-acl-2.3.2
2025-Jun-03 16:49:25.221705
#8 79.57   /nix/store/a9jgnlhkjkxav6qrc3rzg2q84pkl2wvr-attr-2.5.2
2025-Jun-03 16:49:25.221705
#8 79.57   /nix/store/5mh7kaj2fyv8mk4sfq1brwxgc02884wi-bash-5.2p37
2025-Jun-03 16:49:25.221705
#8 79.57   /nix/store/ivl2v8rgg7qh1jkj5pwpqycax3rc2hnl-bzip2-1.0.8
2025-Jun-03 16:49:25.221705
#8 79.57   /nix/store/mglixp03lsp0w986svwdvm7vcy17rdax-bzip2-1.0.8-bin
2025-Jun-03 16:49:25.221705
#8 79.57   /nix/store/4s9rah4cwaxflicsk5cndnknqlk9n4p3-coreutils-9.5
2025-Jun-03 16:49:25.221705
#8 79.57   /nix/store/00g69vw7c9lycy63h45ximy0wmzqx5y6-diffutils-3.10
2025-Jun-03 16:49:25.221705
#8 79.57   /nix/store/74h4z8k82pmp24xryflv4lxkz8jlpqqd-ed-1.20.2
2025-Jun-03 16:49:25.221705
#8 79.57   /nix/store/c4rj90r2m89rxs64hmm857mipwjhig5d-file-5.46
2025-Jun-03 16:49:25.221705
#8 79.57   /nix/store/jqrz1vq5nz4lnv9pqzydj0ir58wbjfy1-findutils-4.10.0
2025-Jun-03 16:49:25.221705
#8 79.57   /nix/store/a3c47r5z1q2c4rz0kvq8hlilkhx2s718-gawk-5.3.1
2025-Jun-03 16:49:25.221705
#8 79.57   /nix/store/bpq1s72cw9qb2fs8mnmlw6hn2c7iy0ss-gcc-14-20241116-lib
2025-Jun-03 16:49:25.221705
#8 79.57   /nix/store/17v0ywnr3akp85pvdi56gwl99ljv95kx-gcc-14-20241116-libgcc
2025-Jun-03 16:49:25.221705
#8 79.57   /nix/store/65h17wjrrlsj2rj540igylrx7fqcd6vq-glibc-2.40-36
2025-Jun-03 16:49:25.221705
#8 79.57   /nix/store/a2byxfv4lc8f2g5xfzw8cz5q8k05wi29-gmp-with-cxx-6.3.0
2025-Jun-03 16:49:25.221705
#8 79.57   /nix/store/1m67ipsk39xvhyqrxnzv2m2p48pil8kl-gnu-config-2024-01-01
2025-Jun-03 16:49:25.338932
#8 79.57   /nix/store/aap6cq56amx4mzbyxp2wpgsf1kqjcr1f-gnugrep-3.11
2025-Jun-03 16:49:25.338932
#8 79.57   /nix/store/fp6cjl1zcmm6mawsnrb5yak1wkz2ma8l-gnumake-4.4.1
2025-Jun-03 16:49:25.338932
#8 79.57   /nix/store/abm77lnrkrkb58z6xp1qwjcr1xgkcfwm-gnused-4.9
2025-Jun-03 16:49:25.338932
#8 79.57   /nix/store/9cwwj1c9csmc85l2cqzs3h9hbf1vwl6c-gnutar-1.35
2025-Jun-03 16:49:25.338932
#8 79.57   /nix/store/nvvj6sk0k6px48436drlblf4gafgbvzr-gzip-1.13
2025-Jun-03 16:49:25.338932
#8 79.57   /nix/store/wwipgdqb4p2fr46kmw9c5wlk799kbl68-icu4c-74.2
2025-Jun-03 16:49:25.338932
#8 79.57   /nix/store/m8w3mf0i4862q22bxad0wspkgdy4jnkk-icu4c-74.2-dev
2025-Jun-03 16:49:25.338932
#8 79.57   /nix/store/34z2792zyd4ayl5186vx0s98ckdaccz9-libidn2-2.3.7
2025-Jun-03 16:49:25.338932
#8 79.57   /nix/store/xcqcgqazykf6s7fsn08k0blnh0wisdcl-libunistring-1.3
2025-Jun-03 16:49:25.338932
#8 79.57   /nix/store/r9ac2hwnmb0nxwsrvr6gi9wsqf2whfqj-libuv-1.49.2
2025-Jun-03 16:49:25.338932
#8 79.57   /nix/store/ll14czvpxglf6nnwmmrmygplm830fvlv-libuv-1.49.2-dev
2025-Jun-03 16:49:25.338932
#8 79.57   /nix/store/6cr0spsvymmrp1hj5n0kbaxw55w1lqyp-libxcrypt-4.4.36
2025-Jun-03 16:49:25.338932
#8 79.57   /nix/store/wlpq101dsifq98z2bk300x4dk80wcybr-nodejs-18.20.5
2025-Jun-03 16:49:25.338932
#8 79.57   /nix/store/h1ydpxkw9qhjdxjpic1pdc2nirggyy6f-openssl-3.3.2
2025-Jun-03 16:49:25.338932
#8 79.57   /nix/store/lygl27c44xv73kx1spskcgvzwq7z337c-openssl-3.3.2-bin
2025-Jun-03 16:49:25.338932
#8 79.57   /nix/store/pp2zf8bdgyz60ds8vcshk2603gcjgp72-openssl-3.3.2-dev
2025-Jun-03 16:49:25.338932
#8 79.57   /nix/store/5yja5dpk2qw1v5mbfbl2d7klcdfrh90w-patch-2.7.6
2025-Jun-03 16:49:25.338932
#8 79.57   /nix/store/srfxqk119fijwnprgsqvn68ys9kiw0bn-patchelf-0.15.0
2025-Jun-03 16:49:25.338932
#8 79.57   /nix/store/3j1p598fivxs69wx3a657ysv3rw8k06l-pcre2-10.44
2025-Jun-03 16:49:25.338932
#8 79.57   /nix/store/1i003ijlh9i0mzp6alqby5hg3090pjdx-perl-5.40.0
2025-Jun-03 16:49:25.338932
#8 79.57   /nix/store/4ig84cyqi6qy4n0sanrbzsw1ixa497jx-stdenv-linux
2025-Jun-03 16:49:25.338932
#8 79.57   /nix/store/d29r1bdmlvwmj52apgcdxfl1mm9c5782-update-autotools-gnu-config-scripts-hook
2025-Jun-03 16:49:25.338932
#8 79.57   /nix/store/acfkqzj5qrqs88a4a6ixnybbjxja663d-xgcc-14-20241116-libgcc
2025-Jun-03 16:49:25.338932
#8 79.57   /nix/store/c2njy6bv84kw1i4bjf5k5gn7gz8hn57n-xz-5.6.3
2025-Jun-03 16:49:25.338932
#8 79.57   /nix/store/h18s640fnhhj2qdh5vivcfbxvz377srg-xz-5.6.3-bin
2025-Jun-03 16:49:25.338932
#8 79.57   /nix/store/cqlaa2xf6lslnizyj9xqa8j0ii1yqw0x-zlib-1.3.1
2025-Jun-03 16:49:25.338932
#8 79.57   /nix/store/1lggwqzapn5mn49l9zy4h566ysv9kzdb-zlib-1.3.1-dev
2025-Jun-03 16:49:25.338932
#8 79.61 copying path '/nix/store/17v0ywnr3akp85pvdi56gwl99ljv95kx-gcc-14-20241116-libgcc' from 'https://cache.nixos.org'...
2025-Jun-03 16:49:25.338932
#8 79.61 copying path '/nix/store/1m67ipsk39xvhyqrxnzv2m2p48pil8kl-gnu-config-2024-01-01' from 'https://cache.nixos.org'...
2025-Jun-03 16:49:25.338932
#8 79.61 copying path '/nix/store/acfkqzj5qrqs88a4a6ixnybbjxja663d-xgcc-14-20241116-libgcc' from 'https://cache.nixos.org'...
2025-Jun-03 16:49:25.338932
#8 79.61 copying path '/nix/store/xcqcgqazykf6s7fsn08k0blnh0wisdcl-libunistring-1.3' from 'https://cache.nixos.org'...
2025-Jun-03 16:49:25.338932
#8 79.63 copying path '/nix/store/d29r1bdmlvwmj52apgcdxfl1mm9c5782-update-autotools-gnu-config-scripts-hook' from 'https://cache.nixos.org'...
2025-Jun-03 16:49:25.338932
#8 79.69 copying path '/nix/store/34z2792zyd4ayl5186vx0s98ckdaccz9-libidn2-2.3.7' from 'https://cache.nixos.org'...
2025-Jun-03 16:49:25.522894
#8 79.73 copying path '/nix/store/65h17wjrrlsj2rj540igylrx7fqcd6vq-glibc-2.40-36' from 'https://cache.nixos.org'...
2025-Jun-03 16:49:26.136739
#8 80.49 copying path '/nix/store/fp6cjl1zcmm6mawsnrb5yak1wkz2ma8l-gnumake-4.4.1' from 'https://cache.nixos.org'...
2025-Jun-03 16:49:26.263037
#8 80.49 copying path '/nix/store/a9jgnlhkjkxav6qrc3rzg2q84pkl2wvr-attr-2.5.2' from 'https://cache.nixos.org'...
2025-Jun-03 16:49:26.263037
#8 80.49 copying path '/nix/store/ivl2v8rgg7qh1jkj5pwpqycax3rc2hnl-bzip2-1.0.8' from 'https://cache.nixos.org'...
2025-Jun-03 16:49:26.263037
#8 80.49 copying path '/nix/store/r9ac2hwnmb0nxwsrvr6gi9wsqf2whfqj-libuv-1.49.2' from 'https://cache.nixos.org'...
2025-Jun-03 16:49:26.263037
#8 80.49 copying path '/nix/store/74h4z8k82pmp24xryflv4lxkz8jlpqqd-ed-1.20.2' from 'https://cache.nixos.org'...
2025-Jun-03 16:49:26.263037
#8 80.49 copying path '/nix/store/a3c47r5z1q2c4rz0kvq8hlilkhx2s718-gawk-5.3.1' from 'https://cache.nixos.org'...
2025-Jun-03 16:49:26.263037
#8 80.49 copying path '/nix/store/6cr0spsvymmrp1hj5n0kbaxw55w1lqyp-libxcrypt-4.4.36' from 'https://cache.nixos.org'...
2025-Jun-03 16:49:26.263037
#8 80.49 copying path '/nix/store/h1ydpxkw9qhjdxjpic1pdc2nirggyy6f-openssl-3.3.2' from 'https://cache.nixos.org'...
2025-Jun-03 16:49:26.263037
#8 80.49 copying path '/nix/store/abm77lnrkrkb58z6xp1qwjcr1xgkcfwm-gnused-4.9' from 'https://cache.nixos.org'...
2025-Jun-03 16:49:26.263037
#8 80.49 copying path '/nix/store/5mh7kaj2fyv8mk4sfq1brwxgc02884wi-bash-5.2p37' from 'https://cache.nixos.org'...
2025-Jun-03 16:49:26.263037
#8 80.49 copying path '/nix/store/3j1p598fivxs69wx3a657ysv3rw8k06l-pcre2-10.44' from 'https://cache.nixos.org'...
2025-Jun-03 16:49:26.263037
#8 80.49 copying path '/nix/store/bpq1s72cw9qb2fs8mnmlw6hn2c7iy0ss-gcc-14-20241116-lib' from 'https://cache.nixos.org'...
2025-Jun-03 16:49:26.263037
#8 80.50 copying path '/nix/store/c2njy6bv84kw1i4bjf5k5gn7gz8hn57n-xz-5.6.3' from 'https://cache.nixos.org'...
2025-Jun-03 16:49:26.263037
#8 80.50 copying path '/nix/store/cqlaa2xf6lslnizyj9xqa8j0ii1yqw0x-zlib-1.3.1' from 'https://cache.nixos.org'...
2025-Jun-03 16:49:26.263037
#8 80.53 copying path '/nix/store/mglixp03lsp0w986svwdvm7vcy17rdax-bzip2-1.0.8-bin' from 'https://cache.nixos.org'...
2025-Jun-03 16:49:26.263037
#8 80.55 copying path '/nix/store/cf7gkacyxmm66lwl5nj6j6yykbrg4q5c-acl-2.3.2' from 'https://cache.nixos.org'...
2025-Jun-03 16:49:26.263037
#8 80.58 copying path '/nix/store/c4rj90r2m89rxs64hmm857mipwjhig5d-file-5.46' from 'https://cache.nixos.org'...
2025-Jun-03 16:49:26.263037
#8 80.58 copying path '/nix/store/1lggwqzapn5mn49l9zy4h566ysv9kzdb-zlib-1.3.1-dev' from 'https://cache.nixos.org'...
2025-Jun-03 16:49:26.263037
#8 80.58 copying path '/nix/store/5yja5dpk2qw1v5mbfbl2d7klcdfrh90w-patch-2.7.6' from 'https://cache.nixos.org'...
2025-Jun-03 16:49:26.263037
#8 80.62 copying path '/nix/store/ll14czvpxglf6nnwmmrmygplm830fvlv-libuv-1.49.2-dev' from 'https://cache.nixos.org'...
2025-Jun-03 16:49:26.401378
#8 80.67 copying path '/nix/store/9cwwj1c9csmc85l2cqzs3h9hbf1vwl6c-gnutar-1.35' from 'https://cache.nixos.org'...
2025-Jun-03 16:49:26.401378
#8 80.68 copying path '/nix/store/h18s640fnhhj2qdh5vivcfbxvz377srg-xz-5.6.3-bin' from 'https://cache.nixos.org'...
2025-Jun-03 16:49:26.401378
#8 80.76 copying path '/nix/store/nvvj6sk0k6px48436drlblf4gafgbvzr-gzip-1.13' from 'https://cache.nixos.org'...
2025-Jun-03 16:49:26.629330
#8 80.84 copying path '/nix/store/aap6cq56amx4mzbyxp2wpgsf1kqjcr1f-gnugrep-3.11' from 'https://cache.nixos.org'...
2025-Jun-03 16:49:26.629330
#8 80.99 copying path '/nix/store/lygl27c44xv73kx1spskcgvzwq7z337c-openssl-3.3.2-bin' from 'https://cache.nixos.org'...
2025-Jun-03 16:49:26.820155
#8 81.03 copying path '/nix/store/pp2zf8bdgyz60ds8vcshk2603gcjgp72-openssl-3.3.2-dev' from 'https://cache.nixos.org'...
2025-Jun-03 16:49:26.820155
#8 81.03 copying path '/nix/store/wwipgdqb4p2fr46kmw9c5wlk799kbl68-icu4c-74.2' from 'https://cache.nixos.org'...
2025-Jun-03 16:49:26.820155
#8 81.04 copying path '/nix/store/a2byxfv4lc8f2g5xfzw8cz5q8k05wi29-gmp-with-cxx-6.3.0' from 'https://cache.nixos.org'...
2025-Jun-03 16:49:26.820155
#8 81.04 copying path '/nix/store/srfxqk119fijwnprgsqvn68ys9kiw0bn-patchelf-0.15.0' from 'https://cache.nixos.org'...
2025-Jun-03 16:49:26.820155
#8 81.17 copying path '/nix/store/4s9rah4cwaxflicsk5cndnknqlk9n4p3-coreutils-9.5' from 'https://cache.nixos.org'...
2025-Jun-03 16:49:27.107676
#8 81.46 copying path '/nix/store/00g69vw7c9lycy63h45ximy0wmzqx5y6-diffutils-3.10' from 'https://cache.nixos.org'...
2025-Jun-03 16:49:27.265874
#8 81.47 copying path '/nix/store/1i003ijlh9i0mzp6alqby5hg3090pjdx-perl-5.40.0' from 'https://cache.nixos.org'...
2025-Jun-03 16:49:27.265874
#8 81.47 copying path '/nix/store/jqrz1vq5nz4lnv9pqzydj0ir58wbjfy1-findutils-4.10.0' from 'https://cache.nixos.org'...
2025-Jun-03 16:49:27.523454
#8 81.87 copying path '/nix/store/4ig84cyqi6qy4n0sanrbzsw1ixa497jx-stdenv-linux' from 'https://cache.nixos.org'...
2025-Jun-03 16:49:27.913054
#8 82.27 building '/nix/store/9smjjb5pkmcbykz8p4786s3a4nq6m030-builder.pl.drv'...
2025-Jun-03 16:49:28.306153
#8 82.66 building '/nix/store/1f4a312hz9m6y1ssip52drgkim8az4d6-libraries.drv'...
2025-Jun-03 16:49:28.459976
#8 82.66 copying path '/nix/store/m8w3mf0i4862q22bxad0wspkgdy4jnkk-icu4c-74.2-dev' from 'https://cache.nixos.org'...
2025-Jun-03 16:49:28.646231
#8 83.00 building '/nix/store/79g4v87v1cgrx5vlwzcagcs6v8ps8fk2-ffeebf0acf3ae8b29f8c7049cd911b9636efd7e7-env.drv'...
2025-Jun-03 16:49:28.802918
#8 83.00 copying path '/nix/store/wlpq101dsifq98z2bk300x4dk80wcybr-nodejs-18.20.5' from 'https://cache.nixos.org'...
2025-Jun-03 16:49:30.451939
#8 84.80 building '/nix/store/frvrfq3qfrqzmlj15jznw81xcifalqb1-ffeebf0acf3ae8b29f8c7049cd911b9636efd7e7-env.drv'...
2025-Jun-03 16:49:30.796920
#8 85.15 created 5 symlinks in user environment
2025-Jun-03 16:49:31.148377
#8 85.50 building '/nix/store/n6w2salpq0in9g5adsgvi31vwg7l79m9-user-environment.drv'...
2025-Jun-03 16:49:31.714248
#8 86.07 removing old generations of profile /nix/var/nix/profiles/per-user/root/channels
2025-Jun-03 16:49:31.822788
#8 86.07 removing old generations of profile /nix/var/nix/profiles/per-user/root/profile
2025-Jun-03 16:49:31.822788
#8 86.07 removing profile version 1
2025-Jun-03 16:49:31.822788
#8 86.07 removing old generations of profile /nix/var/nix/profiles/per-user/root/channels
2025-Jun-03 16:49:31.822788
#8 86.07 removing old generations of profile /nix/var/nix/profiles/per-user/root/profile
2025-Jun-03 16:49:31.822788
#8 86.08 finding garbage collector roots...
2025-Jun-03 16:49:31.822788
#8 86.08 removing stale link from '/nix/var/nix/gcroots/auto/lzjbmb2ry0z7lma2fvpqprb12921pnb5' to '/nix/var/nix/profiles/per-user/root/profile-1-link'
2025-Jun-03 16:49:31.822788
#8 86.10 deleting garbage...
2025-Jun-03 16:49:31.822788
#8 86.11 deleting '/nix/store/b9rj4wk1cxh7g2ib89aqbcapzzar8p2s-user-environment'
2025-Jun-03 16:49:31.822788
#8 86.11 deleting '/nix/store/ir9fki7838bmk4hlj0zmwbw45q101j66-user-environment.drv'
2025-Jun-03 16:49:31.822788
#8 86.11 deleting '/nix/store/xxyn8jfxcpr5ac9dvismfzx39ijh9kiv-env-manifest.nix'
2025-Jun-03 16:49:31.822788
#8 86.15 deleting '/nix/store/4ig84cyqi6qy4n0sanrbzsw1ixa497jx-stdenv-linux'
2025-Jun-03 16:49:31.822788
#8 86.15 deleting '/nix/store/jqrz1vq5nz4lnv9pqzydj0ir58wbjfy1-findutils-4.10.0'
2025-Jun-03 16:49:31.822788
#8 86.16 deleting '/nix/store/5yja5dpk2qw1v5mbfbl2d7klcdfrh90w-patch-2.7.6'
2025-Jun-03 16:49:31.822788
#8 86.16 deleting '/nix/store/aap6cq56amx4mzbyxp2wpgsf1kqjcr1f-gnugrep-3.11'
2025-Jun-03 16:49:31.822788
#8 86.18 deleting '/nix/store/fp6cjl1zcmm6mawsnrb5yak1wkz2ma8l-gnumake-4.4.1'
2025-Jun-03 16:49:32.015804
#8 86.19 deleting '/nix/store/9cwwj1c9csmc85l2cqzs3h9hbf1vwl6c-gnutar-1.35'
2025-Jun-03 16:49:32.015804
#8 86.20 deleting '/nix/store/3j1p598fivxs69wx3a657ysv3rw8k06l-pcre2-10.44'
2025-Jun-03 16:49:32.015804
#8 86.20 deleting '/nix/store/abm77lnrkrkb58z6xp1qwjcr1xgkcfwm-gnused-4.9'
2025-Jun-03 16:49:32.015804
#8 86.22 deleting '/nix/store/na4c03201p0gmhn3bqr089x0xqia157w-source'
2025-Jun-03 16:49:32.015804
#8 86.22 deleting '/nix/store/9fxr7753z31rn59i64dqaajgsx0ap91p-libraries'
2025-Jun-03 16:49:32.015804
#8 86.22 deleting '/nix/store/lwi59jcfwk2lnrakmm1y5vw85hj3n1bi-source'
2025-Jun-03 16:49:38.617146
#8 92.97 deleting '/nix/store/74h4z8k82pmp24xryflv4lxkz8jlpqqd-ed-1.20.2'
2025-Jun-03 16:49:38.823924
#8 92.97 deleting '/nix/store/a3c47r5z1q2c4rz0kvq8hlilkhx2s718-gawk-5.3.1'
2025-Jun-03 16:49:38.823924
#8 92.99 deleting '/nix/store/mglixp03lsp0w986svwdvm7vcy17rdax-bzip2-1.0.8-bin'
2025-Jun-03 16:49:38.823924
#8 92.99 deleting '/nix/store/ivl2v8rgg7qh1jkj5pwpqycax3rc2hnl-bzip2-1.0.8'
2025-Jun-03 16:49:38.823924
#8 92.99 deleting '/nix/store/00g69vw7c9lycy63h45ximy0wmzqx5y6-diffutils-3.10'
2025-Jun-03 16:49:38.823924
#8 93.01 deleting '/nix/store/c4rj90r2m89rxs64hmm857mipwjhig5d-file-5.46'
2025-Jun-03 16:49:38.823924
#8 93.02 deleting '/nix/store/srfxqk119fijwnprgsqvn68ys9kiw0bn-patchelf-0.15.0'
2025-Jun-03 16:49:38.823924
#8 93.02 deleting '/nix/store/h18s640fnhhj2qdh5vivcfbxvz377srg-xz-5.6.3-bin'
2025-Jun-03 16:49:38.823924
#8 93.02 deleting '/nix/store/c2njy6bv84kw1i4bjf5k5gn7gz8hn57n-xz-5.6.3'
2025-Jun-03 16:49:38.823924
#8 93.03 deleting '/nix/store/1i003ijlh9i0mzp6alqby5hg3090pjdx-perl-5.40.0'
2025-Jun-03 16:49:38.841377
#8 93.20 deleting '/nix/store/6cr0spsvymmrp1hj5n0kbaxw55w1lqyp-libxcrypt-4.4.36'
2025-Jun-03 16:49:39.048712
#8 93.20 deleting '/nix/store/d29r1bdmlvwmj52apgcdxfl1mm9c5782-update-autotools-gnu-config-scripts-hook'
2025-Jun-03 16:49:39.048712
#8 93.20 deleting '/nix/store/1m67ipsk39xvhyqrxnzv2m2p48pil8kl-gnu-config-2024-01-01'
2025-Jun-03 16:49:39.048712
#8 93.20 deleting '/nix/store/wf5zj2gbib3gjqllkabxaw4dh0gzcla3-builder.pl'
2025-Jun-03 16:49:39.048712
#8 93.20 deleting '/nix/store/nvvj6sk0k6px48436drlblf4gafgbvzr-gzip-1.13'
2025-Jun-03 16:49:39.048712
#8 93.20 deleting unused links...
2025-Jun-03 16:49:39.048712
#8 93.20 note: currently hard linking saves -0.00 MiB
2025-Jun-03 16:49:39.048712
#8 93.24 29 store paths deleted, 249.11 MiB freed
2025-Jun-03 16:49:39.370127
#8 DONE 93.7s
2025-Jun-03 16:49:39.537898
#9 [ 5/15] RUN sudo apt-get update && sudo apt-get install -y --no-install-recommends curl wget
2025-Jun-03 16:49:40.173346
#9 0.786 Get:1 http://archive.ubuntu.com/ubuntu jammy InRelease [270 kB]
2025-Jun-03 16:49:40.173346
#9 0.786 Get:2 http://security.ubuntu.com/ubuntu jammy-security InRelease [129 kB]
2025-Jun-03 16:49:40.352024
#9 0.964 Get:3 http://archive.ubuntu.com/ubuntu jammy-updates InRelease [128 kB]
2025-Jun-03 16:49:40.533623
#9 0.996 Get:4 http://archive.ubuntu.com/ubuntu jammy-backports InRelease [127 kB]
2025-Jun-03 16:49:40.633814
#9 1.248 Get:5 http://security.ubuntu.com/ubuntu jammy-security/multiverse amd64 Packages [47.7 kB]
2025-Jun-03 16:49:40.803685
#9 1.308 Get:6 http://security.ubuntu.com/ubuntu jammy-security/universe amd64 Packages [1245 kB]
2025-Jun-03 16:49:40.803685
#9 1.417 Get:7 http://security.ubuntu.com/ubuntu jammy-security/restricted amd64 Packages [4468 kB]
2025-Jun-03 16:49:40.983507
#9 1.480 Get:8 http://archive.ubuntu.com/ubuntu jammy/restricted amd64 Packages [164 kB]
2025-Jun-03 16:49:40.983507
#9 1.597 Get:9 http://security.ubuntu.com/ubuntu jammy-security/main amd64 Packages [2979 kB]
2025-Jun-03 16:49:41.149100
#9 1.612 Get:10 http://archive.ubuntu.com/ubuntu jammy/main amd64 Packages [1792 kB]
2025-Jun-03 16:49:41.166296
2025-Jun-03 16:49:41.212645
#9 1.824 Get:11 http://archive.ubuntu.com/ubuntu jammy/multiverse amd64 Packages [266 kB]
2025-Jun-03 16:49:41.375636
#9 1.838 Get:12 http://archive.ubuntu.com/ubuntu jammy/universe amd64 Packages [17.5 MB]
2025-Jun-03 16:49:41.959985
#9 2.572 Get:13 http://archive.ubuntu.com/ubuntu jammy-updates/main amd64 Packages [3290 kB]
2025-Jun-03 16:49:42.074510
#9 2.688 Get:14 http://archive.ubuntu.com/ubuntu jammy-updates/multiverse amd64 Packages [55.7 kB]
2025-Jun-03 16:49:42.299540
#9 2.693 Get:15 http://archive.ubuntu.com/ubuntu jammy-updates/universe amd64 Packages [1553 kB]
2025-Jun-03 16:49:42.299540
#9 2.758 Get:16 http://archive.ubuntu.com/ubuntu jammy-updates/restricted amd64 Packages [4622 kB]
2025-Jun-03 16:49:42.352949
#9 2.964 Get:17 http://archive.ubuntu.com/ubuntu jammy-backports/universe amd64 Packages [35.2 kB]
2025-Jun-03 16:49:42.503629
#9 2.967 Get:18 http://archive.ubuntu.com/ubuntu jammy-backports/main amd64 Packages [83.2 kB]
2025-Jun-03 16:49:43.671491
#9 4.284 Fetched 38.7 MB in 4s (10.9 MB/s)
2025-Jun-03 16:49:43.671491
#9 4.284 Reading package lists...
2025-Jun-03 16:49:45.776793
2025-Jun-03 16:49:45.938171
#9 6.550 Reading package lists...
2025-Jun-03 16:49:48.073741
2025-Jun-03 16:49:48.270036
#9 8.729 Building dependency tree...
2025-Jun-03 16:49:48.731217
#9 9.345 Reading state information...
2025-Jun-03 16:49:48.882550
2025-Jun-03 16:49:49.766115
#9 10.38 curl is already the newest version (7.81.0-1ubuntu1.20).
2025-Jun-03 16:49:49.774068
#9 10.38 The following NEW packages will be installed:
2025-Jun-03 16:49:49.888581
#9 10.38   wget
2025-Jun-03 16:49:49.888581
#9 10.50 0 upgraded, 1 newly installed, 0 to remove and 20 not upgraded.
2025-Jun-03 16:49:49.888581
#9 10.50 Need to get 339 kB of archives.
2025-Jun-03 16:49:49.888581
#9 10.50 After this operation, 950 kB of additional disk space will be used.
2025-Jun-03 16:49:49.888581
#9 10.50 Get:1 http://archive.ubuntu.com/ubuntu jammy-updates/main amd64 wget amd64 1.21.2-2ubuntu1.1 [339 kB]
2025-Jun-03 16:49:50.521723
#9 11.13 debconf: delaying package configuration, since apt-utils is not installed
2025-Jun-03 16:49:50.633243
#9 11.25 Fetched 339 kB in 0s (1474 kB/s)
2025-Jun-03 16:49:50.735469
#9 11.31 Selecting previously unselected package wget.
2025-Jun-03 16:49:50.742255
#9 11.31 (Reading database ... 
(Reading database ... 5%
(Reading database ... 10%
(Reading database ... 15%
(Reading database ... 20%
(Reading database ... 25%
(Reading database ... 30%
(Reading database ... 35%
(Reading database ... 40%
(Reading database ... 45%
(Reading database ... 50%
(Reading database ... 55%
(Reading database ... 60%
(Reading database ... 65%
(Reading database ... 70%
(Reading database ... 75%
(Reading database ... 80%
2025-Jun-03 16:49:50.943788
(Reading database ... 85%
(Reading database ... 90%
(Reading database ... 95%
(Reading database ... 100%
(Reading database ... 10745 files and directories currently installed.)
2025-Jun-03 16:49:50.943788
#9 11.43 Preparing to unpack .../wget_1.21.2-2ubuntu1.1_amd64.deb ...
2025-Jun-03 16:49:50.943788
#9 11.44 Unpacking wget (1.21.2-2ubuntu1.1) ...
2025-Jun-03 16:49:50.943788
#9 11.56 Setting up wget (1.21.2-2ubuntu1.1) ...
2025-Jun-03 16:49:51.088933
#9 DONE 11.7s
2025-Jun-03 16:49:51.250900
#10 [ 6/15] COPY . /app/.
2025-Jun-03 16:49:51.372895
#10 DONE 0.3s
2025-Jun-03 16:49:51.534793
#11 [ 7/15] RUN  echo 'Setting up npm environment'
2025-Jun-03 16:49:51.545406
#11 0.163 Setting up npm environment
2025-Jun-03 16:49:51.728981
#11 DONE 0.2s
2025-Jun-03 16:49:51.728981
2025-Jun-03 16:49:51.728981
#12 [ 8/15] RUN  rm -f pnpm-lock.yaml yarn.lock .pnpmfile.cjs || true
2025-Jun-03 16:49:51.770291
#12 DONE 0.2s
2025-Jun-03 16:49:51.932879
#13 [ 9/15] RUN  npm --version
2025-Jun-03 16:49:52.401745
#13 0.623 npm warn config production Use `--omit=dev` instead.
2025-Jun-03 16:49:52.617238
#13 0.625 10.8.2
2025-Jun-03 16:49:52.625575
#13 DONE 0.7s
2025-Jun-03 16:49:52.625575
2025-Jun-03 16:49:52.625575
#14 [10/15] COPY . /app/.
2025-Jun-03 16:49:53.372209
#14 DONE 0.9s
2025-Jun-03 16:49:53.529051
#15 [11/15] RUN  npm ci
2025-Jun-03 16:49:53.735888
#15 0.359 npm warn config production Use `--omit=dev` instead.
2025-Jun-03 16:49:55.927487
#15 2.549 npm error code EUSAGE
2025-Jun-03 16:49:55.927487
#15 2.549 npm error
2025-Jun-03 16:49:55.927487
#15 2.549 npm error `npm ci` can only install packages when your package.json and package-lock.json or npm-shrinkwrap.json are in sync. Please update your lock file with `npm install` before continuing.
2025-Jun-03 16:49:55.927487
#15 2.549 npm error
2025-Jun-03 16:49:55.927487
#15 2.549 npm error Missing: yjs@13.6.27 from lock file
2025-Jun-03 16:49:55.927487
#15 2.549 npm error Invalid: lock file's picomatch@2.3.1 does not satisfy picomatch@4.0.2
2025-Jun-03 16:49:55.927487
#15 2.549 npm error Missing: picomatch@2.3.1 from lock file
2025-Jun-03 16:49:55.927487
#15 2.549 npm error Missing: picomatch@2.3.1 from lock file
2025-Jun-03 16:49:55.927487
#15 2.549 npm error Missing: lib0@0.2.108 from lock file
2025-Jun-03 16:49:55.927487
#15 2.549 npm error Missing: isomorphic.js@0.2.5 from lock file
2025-Jun-03 16:49:55.927487
#15 2.549 npm error Missing: picomatch@2.3.1 from lock file
2025-Jun-03 16:49:55.927487
#15 2.549 npm error
2025-Jun-03 16:49:55.927487
#15 2.549 npm error Clean install a project
2025-Jun-03 16:49:55.927487
#15 2.549 npm error
2025-Jun-03 16:49:55.927487
#15 2.549 npm error Usage:
2025-Jun-03 16:49:55.927487
#15 2.549 npm error npm ci
2025-Jun-03 16:49:55.927487
#15 2.549 npm error
2025-Jun-03 16:49:55.927487
#15 2.549 npm error Options:
2025-Jun-03 16:49:55.927487
#15 2.549 npm error [--install-strategy <hoisted|nested|shallow|linked>] [--legacy-bundling]
2025-Jun-03 16:49:55.927487
#15 2.549 npm error [--global-style] [--omit <dev|optional|peer> [--omit <dev|optional|peer> ...]]
2025-Jun-03 16:49:55.927487
#15 2.549 npm error [--include <prod|dev|optional|peer> [--include <prod|dev|optional|peer> ...]]
2025-Jun-03 16:49:55.927487
#15 2.549 npm error [--strict-peer-deps] [--foreground-scripts] [--ignore-scripts] [--no-audit]
2025-Jun-03 16:49:55.927487
#15 2.549 npm error [--no-bin-links] [--no-fund] [--dry-run]
2025-Jun-03 16:49:55.927487
#15 2.549 npm error [-w|--workspace <workspace-name> [-w|--workspace <workspace-name> ...]]
2025-Jun-03 16:49:55.927487
#15 2.549 npm error [-ws|--workspaces] [--include-workspace-root] [--install-links]
2025-Jun-03 16:49:55.927487
#15 2.549 npm error
2025-Jun-03 16:49:55.927487
#15 2.549 npm error aliases: clean-install, ic, install-clean, isntall-clean
2025-Jun-03 16:49:55.927487
#15 2.549 npm error
2025-Jun-03 16:49:55.927487
#15 2.549 npm error Run "npm help ci" for more info
2025-Jun-03 16:49:56.070329
#15 2.554 npm error A complete log of this run can be found in: /root/.npm/_logs/2025-06-03T16_49_53_666Z-debug-0.log
2025-Jun-03 16:49:56.070329
#15 ERROR: process "/bin/bash -ol pipefail -c npm ci" did not complete successfully: exit code: 1
2025-Jun-03 16:49:56.070329
------
2025-Jun-03 16:49:56.070329
> [11/15] RUN  npm ci:
2025-Jun-03 16:49:56.070329
2.549 npm error [--include <prod|dev|optional|peer> [--include <prod|dev|optional|peer> ...]]
2025-Jun-03 16:49:56.070329
2.549 npm error [--strict-peer-deps] [--foreground-scripts] [--ignore-scripts] [--no-audit]
2025-Jun-03 16:49:56.070329
2.549 npm error [--no-bin-links] [--no-fund] [--dry-run]
2025-Jun-03 16:49:56.070329
2.549 npm error [-w|--workspace <workspace-name> [-w|--workspace <workspace-name> ...]]
2025-Jun-03 16:49:56.070329
2.549 npm error [-ws|--workspaces] [--include-workspace-root] [--install-links]
2025-Jun-03 16:49:56.070329
2.549 npm error
2025-Jun-03 16:49:56.070329
2.549 npm error aliases: clean-install, ic, install-clean, isntall-clean
2025-Jun-03 16:49:56.070329
2.549 npm error
2025-Jun-03 16:49:56.070329
2.549 npm error Run "npm help ci" for more info
2025-Jun-03 16:49:56.070329
2.554 npm error A complete log of this run can be found in: /root/.npm/_logs/2025-06-03T16_49_53_666Z-debug-0.log
2025-Jun-03 16:49:56.070329
------
2025-Jun-03 16:49:56.079724
1 warning found (use docker --debug to expand):
2025-Jun-03 16:49:56.079724
- UndefinedVar: Usage of undefined variable '$NIXPACKS_PATH' (line 21)
2025-Jun-03 16:49:56.079724
Dockerfile:23
2025-Jun-03 16:49:56.079724
--------------------
2025-Jun-03 16:49:56.079724
21 |     ENV NIXPACKS_PATH=/app/node_modules/.bin:$NIXPACKS_PATH
2025-Jun-03 16:49:56.079724
22 |     COPY . /app/.
2025-Jun-03 16:49:56.079724
23 | >>> RUN  npm ci
2025-Jun-03 16:49:56.079724
24 |
2025-Jun-03 16:49:56.079724
25 |     # build phase
2025-Jun-03 16:49:56.079724
--------------------
2025-Jun-03 16:49:56.079724
ERROR: failed to solve: process "/bin/bash -ol pipefail -c npm ci" did not complete successfully: exit code: 1
2025-Jun-03 16:49:56.088411
exit status 1
2025-Jun-03 16:49:56.155002
Oops something is not okay, are you okay? 😢
2025-Jun-03 16:49:56.165199
#0 building with "default" instance using docker driver
2025-Jun-03 16:49:56.165199
2025-Jun-03 16:49:56.165199
#1 [internal] load build definition from Dockerfile
2025-Jun-03 16:49:56.165199
#1 transferring dockerfile: 30B 0.0s
2025-Jun-03 16:49:56.165199
#1 transferring dockerfile: 1.29kB 0.0s done
2025-Jun-03 16:49:56.165199
#1 DONE 0.2s
2025-Jun-03 16:49:56.165199
2025-Jun-03 16:49:56.165199
#2 [internal] load metadata for ghcr.io/railwayapp/nixpacks:ubuntu-**********
2025-Jun-03 16:49:56.165199
#2 DONE 0.5s
2025-Jun-03 16:49:56.165199
2025-Jun-03 16:49:56.165199
#3 [internal] load .dockerignore
2025-Jun-03 16:49:56.165199
#3 transferring context:
2025-Jun-03 16:49:56.165199
#3 transferring context: 2B done
2025-Jun-03 16:49:56.165199
#3 DONE 0.1s
2025-Jun-03 16:49:56.165199
2025-Jun-03 16:49:56.165199
#4 [ 1/15] FROM ghcr.io/railwayapp/nixpacks:ubuntu-**********@sha256:ed406b77fb751927991b8655e76c33a4521c4957c2afeab293be7c63c2a373d2
2025-Jun-03 16:49:56.165199
#4 DONE 0.0s
2025-Jun-03 16:49:56.165199
2025-Jun-03 16:49:56.165199
#5 [ 2/15] WORKDIR /app/
2025-Jun-03 16:49:56.165199
#5 CACHED
2025-Jun-03 16:49:56.165199
2025-Jun-03 16:49:56.165199
#6 [internal] load build context
2025-Jun-03 16:49:56.165199
#6 transferring context: 3.37MB 1.3s done
2025-Jun-03 16:49:56.165199
#6 DONE 1.5s
2025-Jun-03 16:49:56.165199
2025-Jun-03 16:49:56.165199
#7 [ 3/15] COPY .nixpacks/nixpkgs-ffeebf0acf3ae8b29f8c7049cd911b9636efd7e7.nix .nixpacks/nixpkgs-ffeebf0acf3ae8b29f8c7049cd911b9636efd7e7.nix
2025-Jun-03 16:49:56.165199
#7 DONE 0.5s
2025-Jun-03 16:49:56.165199
2025-Jun-03 16:49:56.165199
#8 [ 4/15] RUN nix-env -if .nixpacks/nixpkgs-ffeebf0acf3ae8b29f8c7049cd911b9636efd7e7.nix && nix-collect-garbage -d
2025-Jun-03 16:49:56.165199
#8 0.986 unpacking 'https://github.com/NixOS/nixpkgs/archive/ffeebf0acf3ae8b29f8c7049cd911b9636efd7e7.tar.gz' into the Git cache...
2025-Jun-03 16:49:56.165199
#8 77.28 unpacking 'https://github.com/railwayapp/nix-npm-overlay/archive/main.tar.gz' into the Git cache...
2025-Jun-03 16:49:56.165199
#8 78.45 installing 'ffeebf0acf3ae8b29f8c7049cd911b9636efd7e7-env'
2025-Jun-03 16:49:56.165199
#8 79.57 these 4 derivations will be built:
2025-Jun-03 16:49:56.165199
#8 79.57   /nix/store/1f4a312hz9m6y1ssip52drgkim8az4d6-libraries.drv
2025-Jun-03 16:49:56.165199
#8 79.57   /nix/store/79g4v87v1cgrx5vlwzcagcs6v8ps8fk2-ffeebf0acf3ae8b29f8c7049cd911b9636efd7e7-env.drv
2025-Jun-03 16:49:56.165199
#8 79.57   /nix/store/9smjjb5pkmcbykz8p4786s3a4nq6m030-builder.pl.drv
2025-Jun-03 16:49:56.165199
#8 79.57   /nix/store/frvrfq3qfrqzmlj15jznw81xcifalqb1-ffeebf0acf3ae8b29f8c7049cd911b9636efd7e7-env.drv
2025-Jun-03 16:49:56.165199
#8 79.57 these 43 paths will be fetched (47.87 MiB download, 242.80 MiB unpacked):
2025-Jun-03 16:49:56.165199
#8 79.57   /nix/store/cf7gkacyxmm66lwl5nj6j6yykbrg4q5c-acl-2.3.2
2025-Jun-03 16:49:56.165199
#8 79.57   /nix/store/a9jgnlhkjkxav6qrc3rzg2q84pkl2wvr-attr-2.5.2
2025-Jun-03 16:49:56.165199
#8 79.57   /nix/store/5mh7kaj2fyv8mk4sfq1brwxgc02884wi-bash-5.2p37
2025-Jun-03 16:49:56.165199
#8 79.57   /nix/store/ivl2v8rgg7qh1jkj5pwpqycax3rc2hnl-bzip2-1.0.8
2025-Jun-03 16:49:56.165199
#8 79.57   /nix/store/mglixp03lsp0w986svwdvm7vcy17rdax-bzip2-1.0.8-bin
2025-Jun-03 16:49:56.165199
#8 79.57   /nix/store/4s9rah4cwaxflicsk5cndnknqlk9n4p3-coreutils-9.5
2025-Jun-03 16:49:56.165199
#8 79.57   /nix/store/00g69vw7c9lycy63h45ximy0wmzqx5y6-diffutils-3.10
2025-Jun-03 16:49:56.165199
#8 79.57   /nix/store/74h4z8k82pmp24xryflv4lxkz8jlpqqd-ed-1.20.2
2025-Jun-03 16:49:56.165199
#8 79.57   /nix/store/c4rj90r2m89rxs64hmm857mipwjhig5d-file-5.46
2025-Jun-03 16:49:56.165199
#8 79.57   /nix/store/jqrz1vq5nz4lnv9pqzydj0ir58wbjfy1-findutils-4.10.0
2025-Jun-03 16:49:56.165199
#8 79.57   /nix/store/a3c47r5z1q2c4rz0kvq8hlilkhx2s718-gawk-5.3.1
2025-Jun-03 16:49:56.165199
#8 79.57   /nix/store/bpq1s72cw9qb2fs8mnmlw6hn2c7iy0ss-gcc-14-20241116-lib
2025-Jun-03 16:49:56.165199
#8 79.57   /nix/store/17v0ywnr3akp85pvdi56gwl99ljv95kx-gcc-14-20241116-libgcc
2025-Jun-03 16:49:56.165199
#8 79.57   /nix/store/65h17wjrrlsj2rj540igylrx7fqcd6vq-glibc-2.40-36
2025-Jun-03 16:49:56.165199
#8 79.57   /nix/store/a2byxfv4lc8f2g5xfzw8cz5q8k05wi29-gmp-with-cxx-6.3.0
2025-Jun-03 16:49:56.165199
#8 79.57   /nix/store/1m67ipsk39xvhyqrxnzv2m2p48pil8kl-gnu-config-2024-01-01
2025-Jun-03 16:49:56.165199
#8 79.57   /nix/store/aap6cq56amx4mzbyxp2wpgsf1kqjcr1f-gnugrep-3.11
2025-Jun-03 16:49:56.165199
#8 79.57   /nix/store/fp6cjl1zcmm6mawsnrb5yak1wkz2ma8l-gnumake-4.4.1
2025-Jun-03 16:49:56.165199
#8 79.57   /nix/store/abm77lnrkrkb58z6xp1qwjcr1xgkcfwm-gnused-4.9
2025-Jun-03 16:49:56.165199
#8 79.57   /nix/store/9cwwj1c9csmc85l2cqzs3h9hbf1vwl6c-gnutar-1.35
2025-Jun-03 16:49:56.165199
#8 79.57   /nix/store/nvvj6sk0k6px48436drlblf4gafgbvzr-gzip-1.13
2025-Jun-03 16:49:56.165199
#8 79.57   /nix/store/wwipgdqb4p2fr46kmw9c5wlk799kbl68-icu4c-74.2
2025-Jun-03 16:49:56.165199
#8 79.57   /nix/store/m8w3mf0i4862q22bxad0wspkgdy4jnkk-icu4c-74.2-dev
2025-Jun-03 16:49:56.165199
#8 79.57   /nix/store/34z2792zyd4ayl5186vx0s98ckdaccz9-libidn2-2.3.7
2025-Jun-03 16:49:56.165199
#8 79.57   /nix/store/xcqcgqazykf6s7fsn08k0blnh0wisdcl-libunistring-1.3
2025-Jun-03 16:49:56.165199
#8 79.57   /nix/store/r9ac2hwnmb0nxwsrvr6gi9wsqf2whfqj-libuv-1.49.2
2025-Jun-03 16:49:56.165199
#8 79.57   /nix/store/ll14czvpxglf6nnwmmrmygplm830fvlv-libuv-1.49.2-dev
2025-Jun-03 16:49:56.165199
#8 79.57   /nix/store/6cr0spsvymmrp1hj5n0kbaxw55w1lqyp-libxcrypt-4.4.36
2025-Jun-03 16:49:56.165199
#8 79.57   /nix/store/wlpq101dsifq98z2bk300x4dk80wcybr-nodejs-18.20.5
2025-Jun-03 16:49:56.165199
#8 79.57   /nix/store/h1ydpxkw9qhjdxjpic1pdc2nirggyy6f-openssl-3.3.2
2025-Jun-03 16:49:56.165199
#8 79.57   /nix/store/lygl27c44xv73kx1spskcgvzwq7z337c-openssl-3.3.2-bin
2025-Jun-03 16:49:56.165199
#8 79.57   /nix/store/pp2zf8bdgyz60ds8vcshk2603gcjgp72-openssl-3.3.2-dev
2025-Jun-03 16:49:56.165199
#8 79.57   /nix/store/5yja5dpk2qw1v5mbfbl2d7klcdfrh90w-patch-2.7.6
2025-Jun-03 16:49:56.165199
#8 79.57   /nix/store/srfxqk119fijwnprgsqvn68ys9kiw0bn-patchelf-0.15.0
2025-Jun-03 16:49:56.165199
#8 79.57   /nix/store/3j1p598fivxs69wx3a657ysv3rw8k06l-pcre2-10.44
2025-Jun-03 16:49:56.165199
#8 79.57   /nix/store/1i003ijlh9i0mzp6alqby5hg3090pjdx-perl-5.40.0
2025-Jun-03 16:49:56.165199
#8 79.57   /nix/store/4ig84cyqi6qy4n0sanrbzsw1ixa497jx-stdenv-linux
2025-Jun-03 16:49:56.165199
#8 79.57   /nix/store/d29r1bdmlvwmj52apgcdxfl1mm9c5782-update-autotools-gnu-config-scripts-hook
2025-Jun-03 16:49:56.165199
#8 79.57   /nix/store/acfkqzj5qrqs88a4a6ixnybbjxja663d-xgcc-14-20241116-libgcc
2025-Jun-03 16:49:56.165199
#8 79.57   /nix/store/c2njy6bv84kw1i4bjf5k5gn7gz8hn57n-xz-5.6.3
2025-Jun-03 16:49:56.165199
#8 79.57   /nix/store/h18s640fnhhj2qdh5vivcfbxvz377srg-xz-5.6.3-bin
2025-Jun-03 16:49:56.165199
#8 79.57   /nix/store/cqlaa2xf6lslnizyj9xqa8j0ii1yqw0x-zlib-1.3.1
2025-Jun-03 16:49:56.165199
#8 79.57   /nix/store/1lggwqzapn5mn49l9zy4h566ysv9kzdb-zlib-1.3.1-dev
2025-Jun-03 16:49:56.165199
#8 79.61 copying path '/nix/store/17v0ywnr3akp85pvdi56gwl99ljv95kx-gcc-14-20241116-libgcc' from 'https://cache.nixos.org'...
2025-Jun-03 16:49:56.165199
#8 79.61 copying path '/nix/store/1m67ipsk39xvhyqrxnzv2m2p48pil8kl-gnu-config-2024-01-01' from 'https://cache.nixos.org'...
2025-Jun-03 16:49:56.165199
#8 79.61 copying path '/nix/store/acfkqzj5qrqs88a4a6ixnybbjxja663d-xgcc-14-20241116-libgcc' from 'https://cache.nixos.org'...
2025-Jun-03 16:49:56.165199
#8 79.61 copying path '/nix/store/xcqcgqazykf6s7fsn08k0blnh0wisdcl-libunistring-1.3' from 'https://cache.nixos.org'...
2025-Jun-03 16:49:56.165199
#8 79.63 copying path '/nix/store/d29r1bdmlvwmj52apgcdxfl1mm9c5782-update-autotools-gnu-config-scripts-hook' from 'https://cache.nixos.org'...
2025-Jun-03 16:49:56.165199
#8 79.69 copying path '/nix/store/34z2792zyd4ayl5186vx0s98ckdaccz9-libidn2-2.3.7' from 'https://cache.nixos.org'...
2025-Jun-03 16:49:56.165199
#8 79.73 copying path '/nix/store/65h17wjrrlsj2rj540igylrx7fqcd6vq-glibc-2.40-36' from 'https://cache.nixos.org'...
2025-Jun-03 16:49:56.165199
#8 80.49 copying path '/nix/store/fp6cjl1zcmm6mawsnrb5yak1wkz2ma8l-gnumake-4.4.1' from 'https://cache.nixos.org'...
2025-Jun-03 16:49:56.165199
#8 80.49 copying path '/nix/store/a9jgnlhkjkxav6qrc3rzg2q84pkl2wvr-attr-2.5.2' from 'https://cache.nixos.org'...
2025-Jun-03 16:49:56.165199
#8 80.49 copying path '/nix/store/ivl2v8rgg7qh1jkj5pwpqycax3rc2hnl-bzip2-1.0.8' from 'https://cache.nixos.org'...
2025-Jun-03 16:49:56.165199
#8 80.49 copying path '/nix/store/r9ac2hwnmb0nxwsrvr6gi9wsqf2whfqj-libuv-1.49.2' from 'https://cache.nixos.org'...
2025-Jun-03 16:49:56.165199
#8 80.49 copying path '/nix/store/74h4z8k82pmp24xryflv4lxkz8jlpqqd-ed-1.20.2' from 'https://cache.nixos.org'...
2025-Jun-03 16:49:56.165199
#8 80.49 copying path '/nix/store/a3c47r5z1q2c4rz0kvq8hlilkhx2s718-gawk-5.3.1' from 'https://cache.nixos.org'...
2025-Jun-03 16:49:56.165199
#8 80.49 copying path '/nix/store/6cr0spsvymmrp1hj5n0kbaxw55w1lqyp-libxcrypt-4.4.36' from 'https://cache.nixos.org'...
2025-Jun-03 16:49:56.165199
#8 80.49 copying path '/nix/store/h1ydpxkw9qhjdxjpic1pdc2nirggyy6f-openssl-3.3.2' from 'https://cache.nixos.org'...
2025-Jun-03 16:49:56.165199
#8 80.49 copying path '/nix/store/abm77lnrkrkb58z6xp1qwjcr1xgkcfwm-gnused-4.9' from 'https://cache.nixos.org'...
2025-Jun-03 16:49:56.165199
#8 80.49 copying path '/nix/store/5mh7kaj2fyv8mk4sfq1brwxgc02884wi-bash-5.2p37' from 'https://cache.nixos.org'...
2025-Jun-03 16:49:56.165199
#8 80.49 copying path '/nix/store/3j1p598fivxs69wx3a657ysv3rw8k06l-pcre2-10.44' from 'https://cache.nixos.org'...
2025-Jun-03 16:49:56.165199
#8 80.49 copying path '/nix/store/bpq1s72cw9qb2fs8mnmlw6hn2c7iy0ss-gcc-14-20241116-lib' from 'https://cache.nixos.org'...
2025-Jun-03 16:49:56.165199
#8 80.50 copying path '/nix/store/c2njy6bv84kw1i4bjf5k5gn7gz8hn57n-xz-5.6.3' from 'https://cache.nixos.org'...
2025-Jun-03 16:49:56.165199
#8 80.50 copying path '/nix/store/cqlaa2xf6lslnizyj9xqa8j0ii1yqw0x-zlib-1.3.1' from 'https://cache.nixos.org'...
2025-Jun-03 16:49:56.165199
#8 80.53 copying path '/nix/store/mglixp03lsp0w986svwdvm7vcy17rdax-bzip2-1.0.8-bin' from 'https://cache.nixos.org'...
2025-Jun-03 16:49:56.165199
#8 80.55 copying path '/nix/store/cf7gkacyxmm66lwl5nj6j6yykbrg4q5c-acl-2.3.2' from 'https://cache.nixos.org'...
2025-Jun-03 16:49:56.165199
#8 80.58 copying path '/nix/store/c4rj90r2m89rxs64hmm857mipwjhig5d-file-5.46' from 'https://cache.nixos.org'...
2025-Jun-03 16:49:56.165199
#8 80.58 copying path '/nix/store/1lggwqzapn5mn49l9zy4h566ysv9kzdb-zlib-1.3.1-dev' from 'https://cache.nixos.org'...
2025-Jun-03 16:49:56.165199
#8 80.58 copying path '/nix/store/5yja5dpk2qw1v5mbfbl2d7klcdfrh90w-patch-2.7.6' from 'https://cache.nixos.org'...
2025-Jun-03 16:49:56.165199
#8 80.62 copying path '/nix/store/ll14czvpxglf6nnwmmrmygplm830fvlv-libuv-1.49.2-dev' from 'https://cache.nixos.org'...
2025-Jun-03 16:49:56.165199
#8 80.67 copying path '/nix/store/9cwwj1c9csmc85l2cqzs3h9hbf1vwl6c-gnutar-1.35' from 'https://cache.nixos.org'...
2025-Jun-03 16:49:56.165199
#8 80.68 copying path '/nix/store/h18s640fnhhj2qdh5vivcfbxvz377srg-xz-5.6.3-bin' from 'https://cache.nixos.org'...
2025-Jun-03 16:49:56.165199
#8 80.76 copying path '/nix/store/nvvj6sk0k6px48436drlblf4gafgbvzr-gzip-1.13' from 'https://cache.nixos.org'...
2025-Jun-03 16:49:56.165199
#8 80.84 copying path '/nix/store/aap6cq56amx4mzbyxp2wpgsf1kqjcr1f-gnugrep-3.11' from 'https://cache.nixos.org'...
2025-Jun-03 16:49:56.165199
#8 80.99 copying path '/nix/store/lygl27c44xv73kx1spskcgvzwq7z337c-openssl-3.3.2-bin' from 'https://cache.nixos.org'...
2025-Jun-03 16:49:56.165199
#8 81.03 copying path '/nix/store/pp2zf8bdgyz60ds8vcshk2603gcjgp72-openssl-3.3.2-dev' from 'https://cache.nixos.org'...
2025-Jun-03 16:49:56.165199
#8 81.03 copying path '/nix/store/wwipgdqb4p2fr46kmw9c5wlk799kbl68-icu4c-74.2' from 'https://cache.nixos.org'...
2025-Jun-03 16:49:56.165199
#8 81.04 copying path '/nix/store/a2byxfv4lc8f2g5xfzw8cz5q8k05wi29-gmp-with-cxx-6.3.0' from 'https://cache.nixos.org'...
2025-Jun-03 16:49:56.165199
#8 81.04 copying path '/nix/store/srfxqk119fijwnprgsqvn68ys9kiw0bn-patchelf-0.15.0' from 'https://cache.nixos.org'...
2025-Jun-03 16:49:56.165199
#8 81.17 copying path '/nix/store/4s9rah4cwaxflicsk5cndnknqlk9n4p3-coreutils-9.5' from 'https://cache.nixos.org'...
2025-Jun-03 16:49:56.165199
#8 81.46 copying path '/nix/store/00g69vw7c9lycy63h45ximy0wmzqx5y6-diffutils-3.10' from 'https://cache.nixos.org'...
2025-Jun-03 16:49:56.165199
#8 81.47 copying path '/nix/store/1i003ijlh9i0mzp6alqby5hg3090pjdx-perl-5.40.0' from 'https://cache.nixos.org'...
2025-Jun-03 16:49:56.165199
#8 81.47 copying path '/nix/store/jqrz1vq5nz4lnv9pqzydj0ir58wbjfy1-findutils-4.10.0' from 'https://cache.nixos.org'...
2025-Jun-03 16:49:56.165199
#8 81.87 copying path '/nix/store/4ig84cyqi6qy4n0sanrbzsw1ixa497jx-stdenv-linux' from 'https://cache.nixos.org'...
2025-Jun-03 16:49:56.165199
#8 82.27 building '/nix/store/9smjjb5pkmcbykz8p4786s3a4nq6m030-builder.pl.drv'...
2025-Jun-03 16:49:56.165199
#8 82.66 building '/nix/store/1f4a312hz9m6y1ssip52drgkim8az4d6-libraries.drv'...
2025-Jun-03 16:49:56.165199
#8 82.66 copying path '/nix/store/m8w3mf0i4862q22bxad0wspkgdy4jnkk-icu4c-74.2-dev' from 'https://cache.nixos.org'...
2025-Jun-03 16:49:56.165199
#8 83.00 building '/nix/store/79g4v87v1cgrx5vlwzcagcs6v8ps8fk2-ffeebf0acf3ae8b29f8c7049cd911b9636efd7e7-env.drv'...
2025-Jun-03 16:49:56.165199
#8 83.00 copying path '/nix/store/wlpq101dsifq98z2bk300x4dk80wcybr-nodejs-18.20.5' from 'https://cache.nixos.org'...
2025-Jun-03 16:49:56.165199
#8 84.80 building '/nix/store/frvrfq3qfrqzmlj15jznw81xcifalqb1-ffeebf0acf3ae8b29f8c7049cd911b9636efd7e7-env.drv'...
2025-Jun-03 16:49:56.165199
#8 85.15 created 5 symlinks in user environment
2025-Jun-03 16:49:56.165199
#8 85.50 building '/nix/store/n6w2salpq0in9g5adsgvi31vwg7l79m9-user-environment.drv'...
2025-Jun-03 16:49:56.165199
#8 86.07 removing old generations of profile /nix/var/nix/profiles/per-user/root/channels
2025-Jun-03 16:49:56.165199
#8 86.07 removing old generations of profile /nix/var/nix/profiles/per-user/root/profile
2025-Jun-03 16:49:56.165199
#8 86.07 removing profile version 1
2025-Jun-03 16:49:56.165199
#8 86.07 removing old generations of profile /nix/var/nix/profiles/per-user/root/channels
2025-Jun-03 16:49:56.165199
#8 86.07 removing old generations of profile /nix/var/nix/profiles/per-user/root/profile
2025-Jun-03 16:49:56.165199
#8 86.08 finding garbage collector roots...
2025-Jun-03 16:49:56.165199
#8 86.08 removing stale link from '/nix/var/nix/gcroots/auto/lzjbmb2ry0z7lma2fvpqprb12921pnb5' to '/nix/var/nix/profiles/per-user/root/profile-1-link'
2025-Jun-03 16:49:56.165199
#8 86.10 deleting garbage...
2025-Jun-03 16:49:56.165199
#8 86.11 deleting '/nix/store/b9rj4wk1cxh7g2ib89aqbcapzzar8p2s-user-environment'
2025-Jun-03 16:49:56.165199
#8 86.11 deleting '/nix/store/ir9fki7838bmk4hlj0zmwbw45q101j66-user-environment.drv'
2025-Jun-03 16:49:56.165199
#8 86.11 deleting '/nix/store/xxyn8jfxcpr5ac9dvismfzx39ijh9kiv-env-manifest.nix'
2025-Jun-03 16:49:56.165199
#8 86.15 deleting '/nix/store/4ig84cyqi6qy4n0sanrbzsw1ixa497jx-stdenv-linux'
2025-Jun-03 16:49:56.165199
#8 86.15 deleting '/nix/store/jqrz1vq5nz4lnv9pqzydj0ir58wbjfy1-findutils-4.10.0'
2025-Jun-03 16:49:56.165199
#8 86.16 deleting '/nix/store/5yja5dpk2qw1v5mbfbl2d7klcdfrh90w-patch-2.7.6'
2025-Jun-03 16:49:56.165199
#8 86.16 deleting '/nix/store/aap6cq56amx4mzbyxp2wpgsf1kqjcr1f-gnugrep-3.11'
2025-Jun-03 16:49:56.165199
#8 86.18 deleting '/nix/store/fp6cjl1zcmm6mawsnrb5yak1wkz2ma8l-gnumake-4.4.1'
2025-Jun-03 16:49:56.165199
#8 86.19 deleting '/nix/store/9cwwj1c9csmc85l2cqzs3h9hbf1vwl6c-gnutar-1.35'
2025-Jun-03 16:49:56.165199
#8 86.20 deleting '/nix/store/3j1p598fivxs69wx3a657ysv3rw8k06l-pcre2-10.44'
2025-Jun-03 16:49:56.165199
#8 86.20 deleting '/nix/store/abm77lnrkrkb58z6xp1qwjcr1xgkcfwm-gnused-4.9'
2025-Jun-03 16:49:56.165199
#8 86.22 deleting '/nix/store/na4c03201p0gmhn3bqr089x0xqia157w-source'
2025-Jun-03 16:49:56.165199
#8 86.22 deleting '/nix/store/9fxr7753z31rn59i64dqaajgsx0ap91p-libraries'
2025-Jun-03 16:49:56.165199
#8 86.22 deleting '/nix/store/lwi59jcfwk2lnrakmm1y5vw85hj3n1bi-source'
2025-Jun-03 16:49:56.165199
#8 92.97 deleting '/nix/store/74h4z8k82pmp24xryflv4lxkz8jlpqqd-ed-1.20.2'
2025-Jun-03 16:49:56.165199
#8 92.97 deleting '/nix/store/a3c47r5z1q2c4rz0kvq8hlilkhx2s718-gawk-5.3.1'
2025-Jun-03 16:49:56.165199
#8 92.99 deleting '/nix/store/mglixp03lsp0w986svwdvm7vcy17rdax-bzip2-1.0.8-bin'
2025-Jun-03 16:49:56.165199
#8 92.99 deleting '/nix/store/ivl2v8rgg7qh1jkj5pwpqycax3rc2hnl-bzip2-1.0.8'
2025-Jun-03 16:49:56.165199
#8 92.99 deleting '/nix/store/00g69vw7c9lycy63h45ximy0wmzqx5y6-diffutils-3.10'
2025-Jun-03 16:49:56.165199
#8 93.01 deleting '/nix/store/c4rj90r2m89rxs64hmm857mipwjhig5d-file-5.46'
2025-Jun-03 16:49:56.165199
#8 93.02 deleting '/nix/store/srfxqk119fijwnprgsqvn68ys9kiw0bn-patchelf-0.15.0'
2025-Jun-03 16:49:56.165199
#8 93.02 deleting '/nix/store/h18s640fnhhj2qdh5vivcfbxvz377srg-xz-5.6.3-bin'
2025-Jun-03 16:49:56.165199
#8 93.02 deleting '/nix/store/c2njy6bv84kw1i4bjf5k5gn7gz8hn57n-xz-5.6.3'
2025-Jun-03 16:49:56.165199
#8 93.03 deleting '/nix/store/1i003ijlh9i0mzp6alqby5hg3090pjdx-perl-5.40.0'
2025-Jun-03 16:49:56.165199
#8 93.20 deleting '/nix/store/6cr0spsvymmrp1hj5n0kbaxw55w1lqyp-libxcrypt-4.4.36'
2025-Jun-03 16:49:56.165199
#8 93.20 deleting '/nix/store/d29r1bdmlvwmj52apgcdxfl1mm9c5782-update-autotools-gnu-config-scripts-hook'
2025-Jun-03 16:49:56.165199
#8 93.20 deleting '/nix/store/1m67ipsk39xvhyqrxnzv2m2p48pil8kl-gnu-config-2024-01-01'
2025-Jun-03 16:49:56.165199
#8 93.20 deleting '/nix/store/wf5zj2gbib3gjqllkabxaw4dh0gzcla3-builder.pl'
2025-Jun-03 16:49:56.165199
#8 93.20 deleting '/nix/store/nvvj6sk0k6px48436drlblf4gafgbvzr-gzip-1.13'
2025-Jun-03 16:49:56.165199
#8 93.20 deleting unused links...
2025-Jun-03 16:49:56.165199
#8 93.20 note: currently hard linking saves -0.00 MiB
2025-Jun-03 16:49:56.165199
#8 93.24 29 store paths deleted, 249.11 MiB freed
2025-Jun-03 16:49:56.165199
#8 DONE 93.7s
2025-Jun-03 16:49:56.165199
2025-Jun-03 16:49:56.165199
#9 [ 5/15] RUN sudo apt-get update && sudo apt-get install -y --no-install-recommends curl wget
2025-Jun-03 16:49:56.165199
#9 0.786 Get:1 http://archive.ubuntu.com/ubuntu jammy InRelease [270 kB]
2025-Jun-03 16:49:56.165199
#9 0.786 Get:2 http://security.ubuntu.com/ubuntu jammy-security InRelease [129 kB]
2025-Jun-03 16:49:56.165199
#9 0.964 Get:3 http://archive.ubuntu.com/ubuntu jammy-updates InRelease [128 kB]
2025-Jun-03 16:49:56.165199
#9 0.996 Get:4 http://archive.ubuntu.com/ubuntu jammy-backports InRelease [127 kB]
2025-Jun-03 16:49:56.165199
#9 1.248 Get:5 http://security.ubuntu.com/ubuntu jammy-security/multiverse amd64 Packages [47.7 kB]
2025-Jun-03 16:49:56.165199
#9 1.308 Get:6 http://security.ubuntu.com/ubuntu jammy-security/universe amd64 Packages [1245 kB]
2025-Jun-03 16:49:56.165199
#9 1.417 Get:7 http://security.ubuntu.com/ubuntu jammy-security/restricted amd64 Packages [4468 kB]
2025-Jun-03 16:49:56.165199
#9 1.480 Get:8 http://archive.ubuntu.com/ubuntu jammy/restricted amd64 Packages [164 kB]
2025-Jun-03 16:49:56.165199
#9 1.597 Get:9 http://security.ubuntu.com/ubuntu jammy-security/main amd64 Packages [2979 kB]
2025-Jun-03 16:49:56.165199
#9 1.612 Get:10 http://archive.ubuntu.com/ubuntu jammy/main amd64 Packages [1792 kB]
2025-Jun-03 16:49:56.165199
#9 1.824 Get:11 http://archive.ubuntu.com/ubuntu jammy/multiverse amd64 Packages [266 kB]
2025-Jun-03 16:49:56.165199
#9 1.838 Get:12 http://archive.ubuntu.com/ubuntu jammy/universe amd64 Packages [17.5 MB]
2025-Jun-03 16:49:56.165199
#9 2.572 Get:13 http://archive.ubuntu.com/ubuntu jammy-updates/main amd64 Packages [3290 kB]
2025-Jun-03 16:49:56.165199
#9 2.688 Get:14 http://archive.ubuntu.com/ubuntu jammy-updates/multiverse amd64 Packages [55.7 kB]
2025-Jun-03 16:49:56.165199
#9 2.693 Get:15 http://archive.ubuntu.com/ubuntu jammy-updates/universe amd64 Packages [1553 kB]
2025-Jun-03 16:49:56.165199
#9 2.758 Get:16 http://archive.ubuntu.com/ubuntu jammy-updates/restricted amd64 Packages [4622 kB]
2025-Jun-03 16:49:56.165199
#9 2.964 Get:17 http://archive.ubuntu.com/ubuntu jammy-backports/universe amd64 Packages [35.2 kB]
2025-Jun-03 16:49:56.165199
#9 2.967 Get:18 http://archive.ubuntu.com/ubuntu jammy-backports/main amd64 Packages [83.2 kB]
2025-Jun-03 16:49:56.165199
#9 4.284 Fetched 38.7 MB in 4s (10.9 MB/s)
2025-Jun-03 16:49:56.165199
#9 4.284 Reading package lists...
2025-Jun-03 16:49:56.165199
#9 6.550 Reading package lists...
2025-Jun-03 16:49:56.165199
#9 8.729 Building dependency tree...
2025-Jun-03 16:49:56.165199
#9 9.345 Reading state information...
2025-Jun-03 16:49:56.165199
#9 10.38 curl is already the newest version (7.81.0-1ubuntu1.20).
2025-Jun-03 16:49:56.165199
#9 10.38 The following NEW packages will be installed:
2025-Jun-03 16:49:56.165199
#9 10.38   wget
2025-Jun-03 16:49:56.165199
#9 10.50 0 upgraded, 1 newly installed, 0 to remove and 20 not upgraded.
2025-Jun-03 16:49:56.165199
#9 10.50 Need to get 339 kB of archives.
2025-Jun-03 16:49:56.165199
#9 10.50 After this operation, 950 kB of additional disk space will be used.
2025-Jun-03 16:49:56.165199
#9 10.50 Get:1 http://archive.ubuntu.com/ubuntu jammy-updates/main amd64 wget amd64 1.21.2-2ubuntu1.1 [339 kB]
2025-Jun-03 16:49:56.165199
#9 11.13 debconf: delaying package configuration, since apt-utils is not installed
2025-Jun-03 16:49:56.165199
#9 11.25 Fetched 339 kB in 0s (1474 kB/s)
2025-Jun-03 16:49:56.165199
#9 11.31 Selecting previously unselected package wget.
2025-Jun-03 16:49:56.165199
#9 11.31 (Reading database ... 
(Reading database ... 5%
(Reading database ... 10%
(Reading database ... 15%
(Reading database ... 20%
(Reading database ... 25%
(Reading database ... 30%
(Reading database ... 35%
(Reading database ... 40%
(Reading database ... 45%
(Reading database ... 50%
(Reading database ... 55%
(Reading database ... 60%
(Reading database ... 65%
(Reading database ... 70%
(Reading database ... 75%
(Reading database ... 80%
(Reading database ... 85%
(Reading database ... 90%
(Reading database ... 95%
(Reading database ... 100%
(Reading database ... 10745 files and directories currently installed.)
2025-Jun-03 16:49:56.165199
#9 11.43 Preparing to unpack .../wget_1.21.2-2ubuntu1.1_amd64.deb ...
2025-Jun-03 16:49:56.165199
#9 11.44 Unpacking wget (1.21.2-2ubuntu1.1) ...
2025-Jun-03 16:49:56.165199
#9 11.56 Setting up wget (1.21.2-2ubuntu1.1) ...
2025-Jun-03 16:49:56.165199
#9 DONE 11.7s
2025-Jun-03 16:49:56.165199
2025-Jun-03 16:49:56.165199
#10 [ 6/15] COPY . /app/.
2025-Jun-03 16:49:56.165199
#10 DONE 0.3s
2025-Jun-03 16:49:56.165199
2025-Jun-03 16:49:56.165199
#11 [ 7/15] RUN  echo 'Setting up npm environment'
2025-Jun-03 16:49:56.165199
#11 0.163 Setting up npm environment
2025-Jun-03 16:49:56.165199
#11 DONE 0.2s
2025-Jun-03 16:49:56.165199
2025-Jun-03 16:49:56.165199
#12 [ 8/15] RUN  rm -f pnpm-lock.yaml yarn.lock .pnpmfile.cjs || true
2025-Jun-03 16:49:56.165199
#12 DONE 0.2s
2025-Jun-03 16:49:56.165199
2025-Jun-03 16:49:56.165199
#13 [ 9/15] RUN  npm --version
2025-Jun-03 16:49:56.165199
#13 0.623 npm warn config production Use `--omit=dev` instead.
2025-Jun-03 16:49:56.165199
#13 0.625 10.8.2
2025-Jun-03 16:49:56.165199
#13 DONE 0.7s
2025-Jun-03 16:49:56.165199
2025-Jun-03 16:49:56.165199
#14 [10/15] COPY . /app/.
2025-Jun-03 16:49:56.165199
#14 DONE 0.9s
2025-Jun-03 16:49:56.165199
2025-Jun-03 16:49:56.165199
#15 [11/15] RUN  npm ci
2025-Jun-03 16:49:56.165199
#15 0.359 npm warn config production Use `--omit=dev` instead.
2025-Jun-03 16:49:56.165199
#15 2.549 npm error code EUSAGE
2025-Jun-03 16:49:56.165199
#15 2.549 npm error
2025-Jun-03 16:49:56.165199
#15 2.549 npm error `npm ci` can only install packages when your package.json and package-lock.json or npm-shrinkwrap.json are in sync. Please update your lock file with `npm install` before continuing.
2025-Jun-03 16:49:56.165199
#15 2.549 npm error
2025-Jun-03 16:49:56.165199
#15 2.549 npm error Missing: yjs@13.6.27 from lock file
2025-Jun-03 16:49:56.165199
#15 2.549 npm error Invalid: lock file's picomatch@2.3.1 does not satisfy picomatch@4.0.2
2025-Jun-03 16:49:56.165199
#15 2.549 npm error Missing: picomatch@2.3.1 from lock file
2025-Jun-03 16:49:56.165199
#15 2.549 npm error Missing: picomatch@2.3.1 from lock file
2025-Jun-03 16:49:56.165199
#15 2.549 npm error Missing: lib0@0.2.108 from lock file
2025-Jun-03 16:49:56.165199
#15 2.549 npm error Missing: isomorphic.js@0.2.5 from lock file
2025-Jun-03 16:49:56.165199
#15 2.549 npm error Missing: picomatch@2.3.1 from lock file
2025-Jun-03 16:49:56.165199
#15 2.549 npm error
2025-Jun-03 16:49:56.165199
#15 2.549 npm error Clean install a project
2025-Jun-03 16:49:56.165199
#15 2.549 npm error
2025-Jun-03 16:49:56.165199
#15 2.549 npm error Usage:
2025-Jun-03 16:49:56.165199
#15 2.549 npm error npm ci
2025-Jun-03 16:49:56.165199
#15 2.549 npm error
2025-Jun-03 16:49:56.165199
#15 2.549 npm error Options:
2025-Jun-03 16:49:56.165199
#15 2.549 npm error [--install-strategy <hoisted|nested|shallow|linked>] [--legacy-bundling]
2025-Jun-03 16:49:56.165199
#15 2.549 npm error [--global-style] [--omit <dev|optional|peer> [--omit <dev|optional|peer> ...]]
2025-Jun-03 16:49:56.165199
#15 2.549 npm error [--include <prod|dev|optional|peer> [--include <prod|dev|optional|peer> ...]]
2025-Jun-03 16:49:56.165199
#15 2.549 npm error [--strict-peer-deps] [--foreground-scripts] [--ignore-scripts] [--no-audit]
2025-Jun-03 16:49:56.165199
#15 2.549 npm error [--no-bin-links] [--no-fund] [--dry-run]
2025-Jun-03 16:49:56.165199
#15 2.549 npm error [-w|--workspace <workspace-name> [-w|--workspace <workspace-name> ...]]
2025-Jun-03 16:49:56.165199
#15 2.549 npm error [-ws|--workspaces] [--include-workspace-root] [--install-links]
2025-Jun-03 16:49:56.165199
#15 2.549 npm error
2025-Jun-03 16:49:56.165199
#15 2.549 npm error aliases: clean-install, ic, install-clean, isntall-clean
2025-Jun-03 16:49:56.165199
#15 2.549 npm error
2025-Jun-03 16:49:56.165199
#15 2.549 npm error Run "npm help ci" for more info
2025-Jun-03 16:49:56.165199
#15 2.554 npm error A complete log of this run can be found in: /root/.npm/_logs/2025-06-03T16_49_53_666Z-debug-0.log
2025-Jun-03 16:49:56.165199
#15 ERROR: process "/bin/bash -ol pipefail -c npm ci" did not complete successfully: exit code: 1
2025-Jun-03 16:49:56.165199
------
2025-Jun-03 16:49:56.165199
> [11/15] RUN  npm ci:
2025-Jun-03 16:49:56.165199
2.549 npm error [--include <prod|dev|optional|peer> [--include <prod|dev|optional|peer> ...]]
2025-Jun-03 16:49:56.165199
2.549 npm error [--strict-peer-deps] [--foreground-scripts] [--ignore-scripts] [--no-audit]
2025-Jun-03 16:49:56.165199
2.549 npm error [--no-bin-links] [--no-fund] [--dry-run]
2025-Jun-03 16:49:56.165199
2.549 npm error [-w|--workspace <workspace-name> [-w|--workspace <workspace-name> ...]]
2025-Jun-03 16:49:56.165199
2.549 npm error [-ws|--workspaces] [--include-workspace-root] [--install-links]
2025-Jun-03 16:49:56.165199
2.549 npm error
2025-Jun-03 16:49:56.165199
2.549 npm error aliases: clean-install, ic, install-clean, isntall-clean
2025-Jun-03 16:49:56.165199
2.549 npm error
2025-Jun-03 16:49:56.165199
2.549 npm error Run "npm help ci" for more info
2025-Jun-03 16:49:56.165199
2.554 npm error A complete log of this run can be found in: /root/.npm/_logs/2025-06-03T16_49_53_666Z-debug-0.log
2025-Jun-03 16:49:56.165199
------
2025-Jun-03 16:49:56.165199
2025-Jun-03 16:49:56.165199
1 warning found (use docker --debug to expand):
2025-Jun-03 16:49:56.165199
- UndefinedVar: Usage of undefined variable '$NIXPACKS_PATH' (line 21)
2025-Jun-03 16:49:56.165199
Dockerfile:23
2025-Jun-03 16:49:56.165199
--------------------
2025-Jun-03 16:49:56.165199
21 |     ENV NIXPACKS_PATH=/app/node_modules/.bin:$NIXPACKS_PATH
2025-Jun-03 16:49:56.165199
22 |     COPY . /app/.
2025-Jun-03 16:49:56.165199
23 | >>> RUN  npm ci
2025-Jun-03 16:49:56.165199
24 |
2025-Jun-03 16:49:56.165199
25 |     # build phase
2025-Jun-03 16:49:56.165199
--------------------
2025-Jun-03 16:49:56.165199
ERROR: failed to solve: process "/bin/bash -ol pipefail -c npm ci" did not complete successfully: exit code: 1
2025-Jun-03 16:49:56.165199
exit status 1
2025-Jun-03 16:49:56.176443
Deployment failed. Removing the new version of your application.
2025-Jun-03 16:49:57.055837
Gracefully shutting down build container: lgsgg0sggg04c80wcc8wcos4
2025-Jun-03 16:49:57.479381
[CMD]: docker stop --time=30 lgsgg0sggg04c80wcc8wcos4
2025-Jun-03 16:49:57.479381
lgsgg0sggg04c80wcc8wcos4
2025-Jun-03 16:49:57.672938
[CMD]: docker rm -f lgsgg0sggg04c80wcc8wcos4
2025-Jun-03 16:49:57.672938
Error response from daemon: No such container: lgsgg0sggg04c80wcc8wcos4