import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { createServerClient } from '@supabase/ssr';
import type { Database } from '@/types/supabase';

export async function GET(request: NextRequest) {
  try {
    // Get the filename from the query parameters
    const { searchParams } = new URL(request.url);
    let filename = searchParams.get('filename');

    if (!filename) {
      return NextResponse.json({ error: 'Filename is required' }, { status: 400 });
    }

    // Clean up the filename - remove any path information and query parameters
    // This ensures we're only using the actual filename
    if (filename.includes('/')) {
      filename = filename.split('/').pop() || filename;
    }
    if (filename.includes('?')) {
      filename = filename.split('?')[0];
    }

    console.log('Proxying image with cleaned filename:', filename);

    // Get cookies for server-side Supabase client
    const cookieStore = await cookies();
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
          set(_name: string, _value: string, _options: any) {
            // Server components can't set cookies directly
          },
          remove(_name: string, _options: any) {
            // Server components can't remove cookies directly
          }
        },
      }
    );

    // Try to download the file from Supabase storage
    const { data, error } = await supabase.storage
      .from('strategyimages')
      .download(filename);

    if (error) {
      console.error('Error downloading image from Supabase:', error);

      // Try a different approach - get the public URL and fetch it directly
      try {
        const { data: publicUrlData } = supabase.storage
          .from('strategyimages')
          .getPublicUrl(filename);

        if (publicUrlData && publicUrlData.publicUrl) {
          console.log('Trying to fetch image directly from public URL:', publicUrlData.publicUrl);

          const response = await fetch(publicUrlData.publicUrl, {
            headers: {
              'Cache-Control': 'no-cache, no-store, must-revalidate',
              'Pragma': 'no-cache',
              'Expires': '0'
            }
          });

          if (!response.ok) {
            throw new Error(`Failed to fetch from public URL: ${response.status} ${response.statusText}`);
          }

          const imageData = await response.arrayBuffer();
          const contentType = response.headers.get('Content-Type') || 'image/jpeg';

          return new NextResponse(imageData, {
            headers: {
              'Content-Type': contentType,
              // Use a more balanced caching strategy - cache for 1 hour but allow revalidation
              'Cache-Control': 'public, max-age=3600, stale-while-revalidate=86400',
              'ETag': `"${filename}-${Date.now().toString(36)}"`,
            },
          });
        }
      } catch (fetchError) {
        console.error('Error fetching from public URL:', fetchError);
      }

      return NextResponse.json({ error: 'Failed to download image' }, { status: 500 });
    }

    if (!data) {
      return NextResponse.json({ error: 'Image not found' }, { status: 404 });
    }

    // Determine the content type based on the filename extension
    let contentType = 'image/jpeg'; // Default
    if (filename.endsWith('.png')) {
      contentType = 'image/png';
    } else if (filename.endsWith('.gif')) {
      contentType = 'image/gif';
    } else if (filename.endsWith('.webp')) {
      contentType = 'image/webp';
    }

    // Convert the blob to an array buffer
    const arrayBuffer = await data.arrayBuffer();

    // Return the image with the appropriate content type
    return new NextResponse(arrayBuffer, {
      headers: {
        'Content-Type': contentType,
        // Use a more balanced caching strategy - cache for 1 hour but allow revalidation
        'Cache-Control': 'public, max-age=3600, stale-while-revalidate=86400',
        'ETag': `"${filename}-${Date.now().toString(36)}"`,
      },
    });
  } catch (error) {
    console.error('Error in image proxy API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
