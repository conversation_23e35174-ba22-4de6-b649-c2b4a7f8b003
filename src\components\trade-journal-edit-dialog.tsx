"use client"

import { useState, useRef, useEffect } from "react"
import { toast } from "sonner"
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON>alogHeader,
  <PERSON><PERSON>T<PERSON>le,
  Di<PERSON>Footer
} from "@/components/ui/dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { ImageDisplay } from "@/components/ui/image-display"
import { getSupabaseBrowser } from "@/lib/supabase"
import { cn } from "@/lib/utils"
import { Loader2, Save, X, Plus, Trash2, Tag, Upload, Image as ImageIcon } from "lucide-react"

interface TradeJournalEditDialogProps {
  trade: any
  open: boolean
  onOpenChange: (open: boolean) => void
  onUpdate?: () => void
}

// Helper function to convert HTML to plain text for display in textarea
// Preserves paragraph spacing and line breaks
const htmlToPlainText = (html: string): string => {
  if (!html) return '';

  // Convert HTML to plain text while preserving formatting
  return html
    .replace(/<\/p>\s*<p>/gi, '\n\n') // Convert paragraph breaks to double line breaks
    .replace(/<p[^>]*>/gi, '') // Remove opening paragraph tags
    .replace(/<\/p>/gi, '') // Remove closing paragraph tags
    .replace(/<br\s*\/?>/gi, '\n') // Convert <br> tags to line breaks
    .replace(/<\/div>\s*<div>/gi, '\n\n') // Convert div breaks to double line breaks
    .replace(/<div[^>]*>/gi, '') // Remove opening div tags
    .replace(/<\/div>/gi, '') // Remove closing div tags
    .replace(/<[^>]*>/g, '') // Remove any remaining HTML tags
    .replace(/&nbsp;/g, ' ') // Replace non-breaking spaces
    .replace(/&amp;/g, '&') // Replace HTML entities
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&quot;/g, '"')
    .replace(/&#39;/g, "'")
    .replace(/\n\s*\n\s*\n/g, '\n\n') // Normalize multiple line breaks to double
    .replace(/^\s+|\s+$/g, '') // Trim leading/trailing whitespace
    .replace(/[ \t]+/g, ' '); // Normalize spaces but preserve line breaks
};

export function TradeJournalEditDialog({
  trade,
  open,
  onOpenChange,
  onUpdate
}: TradeJournalEditDialogProps) {
  // Convert HTML notes to plain text for editing
  const [tradeNotes, setTradeNotes] = useState(() => {
    const notes = trade?.notes || "";
    // If notes contain HTML tags, convert to plain text
    return notes.includes('<') ? htmlToPlainText(notes) : notes;
  })
  const [screenshots, setScreenshots] = useState<string[]>(Array.isArray(trade?.screenshots) ? [...trade.screenshots] : [])
  const [tradeTags, setTradeTags] = useState<string[]>(Array.isArray(trade?.tags) ? [...trade.tags] : [])
  const [newTag, setNewTag] = useState("")
  const [isSaving, setIsSaving] = useState(false)
  const [isUploading, setIsUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // Store original values for cancel functionality
  const [originalTradeNotes, setOriginalTradeNotes] = useState("")
  const [originalScreenshots, setOriginalScreenshots] = useState<string[]>([])
  const [originalTradeTags, setOriginalTradeTags] = useState<string[]>([])

  // Update state when trade prop changes (e.g., from sync updates)
  useEffect(() => {
    if (trade) {
      const notes = trade.notes || "";
      const cleanNotes = notes.includes('<') ? htmlToPlainText(notes) : notes;
      const tradeScreenshots = Array.isArray(trade.screenshots) ? [...trade.screenshots] : [];
      const tradeTags = Array.isArray(trade.tags) ? [...trade.tags] : [];

      // Set current values
      setTradeNotes(cleanNotes);
      setScreenshots(tradeScreenshots);
      setTradeTags(tradeTags);

      // Store original values for cancel functionality
      setOriginalTradeNotes(cleanNotes);
      setOriginalScreenshots(tradeScreenshots);
      setOriginalTradeTags(tradeTags);
    }
  }, [trade]);

  // Function to validate image URLs
  const validateImageUrl = (url: string): boolean => {
    if (!url) return false;
    try {
      new URL(url);
      return true;
    } catch (e) {
      console.error('Invalid image URL:', url);
      return false;
    }
  };

  // Function to handle dialog cancellation
  const handleCancel = () => {
    // Revert all changes to original values
    setTradeNotes(originalTradeNotes);
    setScreenshots(originalScreenshots);
    setTradeTags(originalTradeTags);
    setNewTag("");

    // Close dialog
    onOpenChange(false);
  };

  // Handle file upload for screenshots
  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Check file type
    if (!file.type.startsWith('image/')) {
      toast.error("Please upload an image file");
      return;
    }

    // Check file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      toast.error('File size exceeds 5MB limit');
      return;
    }

    // Set uploading state
    setIsUploading(true);
    setUploadProgress(0);

    try {
      // Create form data
      const formData = new FormData();
      formData.append('file', file);

      // Simulate progress for better UX
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          const newProgress = prev + Math.random() * 15;
          return newProgress > 90 ? 90 : newProgress;
        });
      }, 150);

      // Upload using the API route
      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      });

      // Clear progress interval
      clearInterval(progressInterval);

      if (!response.ok) {
        throw new Error('Failed to upload image');
      }

      // Set progress to 100% when upload is complete
      setUploadProgress(100);

      const { url } = await response.json();

      if (url && validateImageUrl(url)) {
        // Update local state only (no auto-save)
        const updatedScreenshots = [...screenshots, url];
        setScreenshots(updatedScreenshots);

        // Show success message
        toast.success('Screenshot uploaded (will be saved when you click "Save Changes")');
      } else {
        console.error('Invalid image URL returned from upload:', url);
        toast.error('Failed to upload image: Invalid URL returned');
      }
    } catch (error) {
      console.error('Error uploading image:', error);
      toast.error('Failed to upload image');
    } finally {
      // Reset uploading state
      setIsUploading(false);
      setUploadProgress(0);

      // Clear the file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  // Remove a screenshot (local only, no auto-save)
  const removeScreenshot = (index: number) => {
    try {
      // Update the local state only (no auto-save)
      const updatedScreenshots = [...screenshots];
      updatedScreenshots.splice(index, 1);
      setScreenshots(updatedScreenshots);

      // Show success message
      toast.success("Screenshot removed (will be saved when you click \"Save Changes\")");
    } catch (error) {
      console.error("Error removing screenshot:", error);
      toast.error("Failed to remove screenshot");
    }
  };

  // Add a tag
  const addTag = () => {
    if (newTag.trim() && !tradeTags.includes(newTag.trim())) {
      setTradeTags([...tradeTags, newTag.trim()]);
      setNewTag('');
    }
  };

  // Remove a tag
  const removeTag = (tagToRemove: string) => {
    setTradeTags(tradeTags.filter(tag => tag !== tagToRemove));
  };

  // Handle saving trade details with manual sync
  const handleSave = async () => {
    if (!trade) return;

    setIsSaving(true);

    try {
      const supabase = getSupabaseBrowser();

      // Create the update object
      const updateData = {
        notes: tradeNotes || null,
        screenshots: screenshots || [],
        tags: tradeTags || [],
        // Add a flag to indicate if this trade has journal content
        has_journal_content: !!(
          (tradeNotes && tradeNotes.trim().length > 0) ||
          screenshots.length > 0 ||
          tradeTags.length > 0
        ),
        updated_at: new Date().toISOString()
      };

      console.log("Saving trade journal:", updateData);
      console.log("Trade ID for sync:", trade.id);

      // Step 1: Save the trade data
      // Try to use the RPC function first
      try {
        console.log("Attempting to update trade with RPC function");
        const { data: rpcResult, error: rpcError } = await supabase.rpc('update_trade_details', {
          p_trade_id: trade.id,
          p_strategy_id: trade.strategy_id || null,
          p_setup_id: trade.setup_id || null,
          p_notes: updateData.notes,
          p_screenshots: updateData.screenshots,
          p_followed_rules: Array.isArray(trade.followed_rules) ? trade.followed_rules : [],
          p_followed_setup_criteria: Array.isArray(trade.followed_setup_criteria) ? trade.followed_setup_criteria : [],
          p_tags: updateData.tags,
          p_has_journal_content: updateData.has_journal_content
        });

        if (!rpcError) {
          console.log("Trade updated successfully via RPC function");

          // Step 2: Manually trigger sync to linked notebooks
          try {
            const { syncTradeToNotebooks } = await import('@/lib/manual-sync')
            const syncResult = await syncTradeToNotebooks(trade.id)

            if (syncResult.success) {
              toast.success("Trade journal updated and synced successfully");
            } else {
              toast.success("Trade journal updated (sync warning: " + syncResult.error + ")");
            }
          } catch (syncError) {
            console.warn("Sync failed but trade was saved:", syncError)
            toast.success("Trade journal updated (sync failed)")
          }

          onOpenChange(false);
          if (onUpdate) onUpdate();
          return;
        }

        console.warn("RPC function failed, falling back to standard update:", rpcError);
      } catch (rpcError) {
        console.warn("RPC function threw an exception, falling back to standard update:", rpcError);
      }

      // Perform standard update as fallback
      console.log("Performing standard database update");
      const { error } = await supabase
        .from("trades")
        .update({
          notes: updateData.notes,
          screenshots: updateData.screenshots,
          tags: updateData.tags,
          has_journal_content: updateData.has_journal_content,
          updated_at: new Date().toISOString()
        })
        .eq("id", trade.id);

      if (error) {
        console.error("Error updating trade:", error);
        toast.error(`Failed to save trade journal: ${error.message || "Unknown error"}`);
        return;
      }

      // Step 2: Manually trigger sync to linked notebooks (fallback path)
      try {
        const { syncTradeToNotebooks } = await import('@/lib/manual-sync')
        const syncResult = await syncTradeToNotebooks(trade.id)

        if (syncResult.success) {
          toast.success("Trade journal updated and synced successfully");
        } else {
          toast.success("Trade journal updated (sync warning: " + syncResult.error + ")");
        }
      } catch (syncError) {
        console.warn("Sync failed but trade was saved:", syncError)
        toast.success("Trade journal updated (sync failed)")
      }

      onOpenChange(false);
      if (onUpdate) onUpdate();
    } catch (error) {
      console.error("Error saving trade journal:", error);
      toast.error("Failed to save trade journal");
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Edit Trade Journal</DialogTitle>
        </DialogHeader>

        <div className="space-y-6 py-4">
          {/* Trade Notes */}
          <div className="space-y-2">
            <h3 className="text-sm font-medium">Notes</h3>
            <Textarea
              placeholder="Add your trade notes here..."
              value={tradeNotes}
              onChange={(e) => setTradeNotes(e.target.value)}
              className="min-h-[120px]"
            />
          </div>

          {/* Tags */}
          <div className="space-y-2">
            <h3 className="text-sm font-medium">Tags</h3>
            <div className="flex flex-wrap gap-2 mb-2">
              {tradeTags.map((tag) => (
                <Badge key={tag} variant="outline" className="flex items-center gap-1 group">
                  <Tag className="h-3 w-3" />
                  {tag}
                  <X
                    className="h-3 w-3 ml-1 opacity-0 group-hover:opacity-100 cursor-pointer"
                    onClick={() => removeTag(tag)}
                  />
                </Badge>
              ))}
            </div>
            <div className="flex gap-2">
              <Input
                value={newTag}
                onChange={(e) => setNewTag(e.target.value)}
                placeholder="Add a tag..."
                className="flex-1"
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    addTag();
                  }
                }}
              />
              <Button
                variant="outline"
                size="sm"
                onClick={addTag}
                disabled={!newTag.trim()}
              >
                <Plus className="h-4 w-4 mr-1" />
                Add
              </Button>
            </div>
          </div>

          {/* Screenshots */}
          <div className="space-y-2">
            <h3 className="text-sm font-medium">Screenshots</h3>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-2 mb-3">
              {screenshots.map((screenshot, index) => (
                <div
                  key={index}
                  className="relative group"
                >
                  <ImageDisplay
                    src={screenshot}
                    alt={`Screenshot ${index + 1}`}
                    aspectRatio="video"
                    lightboxGroup={screenshots}
                    lightboxIndex={index}
                    className="rounded-md border border-border"
                  />
                  <Button
                    variant="destructive"
                    size="sm"
                    className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity h-8 w-8 p-0 rounded-full"
                    onClick={() => removeScreenshot(index)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              ))}

              {/* Upload button */}
              <label
                className={cn(
                  "relative aspect-video rounded-md border border-dashed flex flex-col items-center justify-center cursor-pointer bg-muted/30 hover:bg-muted/50 transition-colors",
                  isUploading && "pointer-events-none"
                )}
              >
                {isUploading ? (
                  <>
                    <div className="absolute inset-0 bg-background/80 flex flex-col items-center justify-center z-10">
                      <div className="w-full max-w-[80%] h-2 bg-muted rounded-full overflow-hidden mb-2">
                        <div
                          className="h-full bg-primary transition-all duration-300 ease-out"
                          style={{ width: `${uploadProgress}%` }}
                        />
                      </div>
                      <span className="text-xs text-muted-foreground">Uploading... {Math.round(uploadProgress)}%</span>
                    </div>
                    <ImageIcon className="h-6 w-6 mb-1 text-muted-foreground opacity-50" />
                    <span className="text-xs text-muted-foreground opacity-50">Uploading...</span>
                  </>
                ) : (
                  <>
                    <Plus className="h-6 w-6 mb-1 text-muted-foreground" />
                    <span className="text-xs text-muted-foreground">Add Screenshot</span>
                  </>
                )}
                <input
                  type="file"
                  className="sr-only"
                  accept="image/*"
                  onChange={handleFileUpload}
                  ref={fileInputRef}
                  disabled={isUploading}
                />
              </label>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleCancel}>
            <X className="mr-2 h-4 w-4" />
            Cancel
          </Button>
          <Button onClick={handleSave} disabled={isSaving}>
            {isSaving ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                Save Changes
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
