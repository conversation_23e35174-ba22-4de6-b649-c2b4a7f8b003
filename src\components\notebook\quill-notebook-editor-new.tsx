"use client"

import React, { useEffect, useRef, useState } from 'react';
import { EditorContent } from '@/types/notebook';
import 'quill/dist/quill.snow.css';
import './notebook-quill.css';

interface QuillNotebookEditorProps {
  content?: EditorContent | null;
  htmlContent?: string | null;
  onChange?: (content: EditorContent, htmlContent: string) => void;
  editable?: boolean;
  autofocus?: boolean;
  className?: string;
}

const QuillNotebookEditorComponent = React.forwardRef<any, QuillNotebookEditorProps>(({
  content,
  htmlContent,
  onChange,
  editable = true,
  autofocus = false,
  className = '',
}, ref) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [quillInstance, setQuillInstance] = useState<any>(null);
  const changeHandlerRef = useRef<((content: EditorContent, html: string) => void) | null>(null);
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isUserChangeRef = useRef<boolean>(false);

  // Store onChange in ref to avoid dependency changes
  useEffect(() => {
    changeHandlerRef.current = onChange || null;
  }, [onChange]);

  // Convert notebook content to HTML
  const convertContentToHTML = (content: EditorContent | null | undefined): string => {
    if (!content) return '';

    try {
      // If content is already HTML, return it
      if (typeof content === 'string') return content;

      // If we have htmlContent, use it
      if (htmlContent) return htmlContent;

      // Simple conversion from notebook JSON to HTML
      let html = '';

      const processNode = (node: any): string => {
        if (!node) return '';

        if (node.type === 'doc') {
          return node.content ? node.content.map(processNode).join('') : '';
        } else if (node.type === 'paragraph') {
          const content = node.content ? node.content.map(processNode).join('') : '';
          return `<p>${content}</p>`;
        } else if (node.type === 'heading') {
          const level = node.attrs?.level || 1;
          const content = node.content ? node.content.map(processNode).join('') : '';
          return `<h${level}>${content}</h${level}>`;
        } else if (node.type === 'bulletList') {
          const items = node.content ? node.content.map(processNode).join('') : '';
          return `<ul>${items}</ul>`;
        } else if (node.type === 'orderedList') {
          const items = node.content ? node.content.map(processNode).join('') : '';
          return `<ol>${items}</ol>`;
        } else if (node.type === 'listItem') {
          const content = node.content ? node.content.map(processNode).join('') : '';
          return `<li>${content}</li>`;
        } else if (node.type === 'text') {
          let text = node.text || '';

          // Apply marks
          if (node.marks && node.marks.length > 0) {
            node.marks.forEach((mark: any) => {
              if (mark.type === 'bold') text = `<strong>${text}</strong>`;
              if (mark.type === 'italic') text = `<em>${text}</em>`;
              if (mark.type === 'underline') text = `<u>${text}</u>`;
              if (mark.type === 'strike') text = `<s>${text}</s>`;
              // Add more mark conversions as needed
            });
          }

          return text;
        }

        return '';
      };

      html = processNode(content);
      return html || '<p></p>';
    } catch (error) {
      console.error('Error converting content to HTML:', error);
      return htmlContent || '<p></p>';
    }
  };

  // Convert Quill Delta to notebook JSON format
  const convertDeltaToContent = (delta: any): EditorContent => {
    try {
      // Simple conversion from Quill Delta to notebook JSON
      const content: any[] = [];

      // Process the delta operations
      let currentParagraph: any = null;
      let currentList: any = null;
      let currentListItem: any = null;

      if (delta && delta.ops) {
        delta.ops.forEach((op: any) => {
          if (typeof op.insert === 'string') {
            const text = op.insert;
            const attrs = op.attributes || {};

            // Handle newlines with attributes
            if (text === '\n') {
              if (attrs.header) {
                // Finalize current paragraph if exists
                if (currentParagraph && currentParagraph.content.length > 0) {
                  content.push(currentParagraph);
                  currentParagraph = null;
                }

                // Create empty heading if none exists
                if (!currentParagraph) {
                  content.push({
                    type: 'heading',
                    attrs: { level: attrs.header },
                    content: []
                  });
                }
              } else if (attrs.list) {
                // Handle list items
                if (!currentList) {
                  currentList = {
                    type: attrs.list === 'bullet' ? 'bulletList' : 'orderedList',
                    content: []
                  };
                  content.push(currentList);
                }

                if (currentParagraph && currentParagraph.content.length > 0) {
                  if (!currentListItem) {
                    currentListItem = {
                      type: 'listItem',
                      content: [currentParagraph]
                    };
                    currentList.content.push(currentListItem);
                  } else {
                    currentListItem.content.push(currentParagraph);
                  }
                  currentParagraph = null;
                  currentListItem = null;
                }
              } else {
                // Regular paragraph
                if (currentParagraph && currentParagraph.content.length > 0) {
                  content.push(currentParagraph);
                  currentParagraph = null;
                }
              }
            } else {
              // Handle regular text
              if (!currentParagraph) {
                currentParagraph = {
                  type: 'paragraph',
                  content: []
                };
              }

              const textNode: any = {
                type: 'text',
                text
              };

              // Add marks if attributes exist
              if (Object.keys(attrs).length > 0) {
                textNode.marks = [];
                if (attrs.bold) textNode.marks.push({ type: 'bold' });
                if (attrs.italic) textNode.marks.push({ type: 'italic' });
                if (attrs.underline) textNode.marks.push({ type: 'underline' });
                if (attrs.strike) textNode.marks.push({ type: 'strike' });
                // Add more attribute conversions as needed
              }

              currentParagraph.content.push(textNode);
            }
          }
        });
      }

      // Add the last paragraph if it exists
      if (currentParagraph && currentParagraph.content.length > 0) {
        content.push(currentParagraph);
      }

      return {
        type: 'doc',
        content
      };
    } catch (error) {
      console.error('Error converting Delta to content:', error);
      return {
        type: 'doc',
        content: [
          {
            type: 'paragraph',
            content: [
              {
                type: 'text',
                text: ''
              }
            ]
          }
        ]
      };
    }
  };

  // Initialize Quill
  useEffect(() => {
    // Only initialize once and ensure container is ready
    if (containerRef.current && !quillInstance && containerRef.current.isConnected) {
      const initQuill = async () => {
        try {
          // Double-check container is still available and connected
          if (!containerRef.current || !containerRef.current.isConnected) {
            return;
          }

          // Wait for next tick to ensure DOM is fully ready
          await new Promise(resolve => setTimeout(resolve, 0));

          // Final check before proceeding
          if (!containerRef.current || !containerRef.current.isConnected) {
            return;
          }

          // Dynamically import Quill
          const Quill = (await import('quill')).default;

          // Create toolbar options following Quill's recommended format
          const toolbarOptions = [
            ['bold', 'italic', 'underline', 'strike'],        // toggled buttons
            ['blockquote', 'code-block'],

            [{ 'header': 1 }, { 'header': 2 }],               // custom button values
            [{ 'list': 'ordered'}, { 'list': 'bullet' }],
            [{ 'script': 'sub'}, { 'script': 'super' }],      // superscript/subscript
            [{ 'indent': '-1'}, { 'indent': '+1' }],          // outdent/indent

            [{ 'size': ['small', false, 'large', 'huge'] }],  // custom dropdown
            [{ 'header': [1, 2, 3, 4, 5, 6, false] }],

            [{ 'color': [] }, { 'background': [] }],          // dropdown with defaults from theme
            [{ 'font': [] }],
            [{ 'align': [] }],

            ['clean']                                          // remove formatting button
          ];

          // We've already checked containerRef.current is not null above
          const container = containerRef.current as HTMLElement;

          // Clear the container first
          container.innerHTML = '';

          // Create a container for the editor
          const editorContainer = document.createElement('div');
          container.appendChild(editorContainer);

          // We'll handle keyboard bindings after Quill is initialized

          // Initialize Quill with enhanced modules
          const quill = new Quill(editorContainer, {
            modules: {
              toolbar: toolbarOptions,
              keyboard: {
                // We'll customize this after initialization
              },
              history: {
                delay: 1000, // Default value as per documentation
                maxStack: 500, // Increased stack size for more history
                userOnly: true // Only track user changes, not programmatic ones
              }
            },
            placeholder: 'Start typing...',
            theme: 'snow',
            readOnly: !editable
          });

          // Fix keyboard handling for lists and spaces
          try {
            // Get the keyboard module
            const keyboard = quill.getModule('keyboard') as any;

            if (keyboard && typeof keyboard.addBinding === 'function') {
              // Add custom bindings for space in lists
              keyboard.addBinding({
                key: 32, // Space key
                format: ['list']
              }, function() {
                // Insert a space character manually
                const range = quill.getSelection();
                if (range) {
                  quill.insertText(range.index, ' ');
                  quill.setSelection(range.index + 1);
                }
                return false; // Prevent default to avoid double spaces
              });

              // Improve list handling on enter key
              keyboard.addBinding({
                key: 'Enter',
                format: ['list']
              }, function() {
                // Let default behavior happen for enter in lists
                return true;
              });

              // Fix undo/redo according to the history module documentation
              keyboard.addBinding({
                key: 'Z',
                shortKey: true
              }, function() {
                // Get the history module
                const history = quill.getModule('history') as any;
                if (history && typeof history.undo === 'function') {
                  // Call the undo method from the history module
                  history.undo();

                  // Force a cutoff to prevent merging with the next change
                  if (typeof history.cutoff === 'function') {
                    history.cutoff();
                  }
                }
                return false; // Prevent default browser behavior
              });

              keyboard.addBinding({
                key: 'Z',
                shortKey: true,
                shiftKey: true
              }, function() {
                // Get the history module
                const history = quill.getModule('history') as any;
                if (history && typeof history.redo === 'function') {
                  // Call the redo method from the history module
                  history.redo();

                  // Force a cutoff to prevent merging with the next change
                  if (typeof history.cutoff === 'function') {
                    history.cutoff();
                  }
                }
                return false; // Prevent default browser behavior
              });

              console.log('Custom keyboard bindings added successfully');
            } else {
              console.warn('Keyboard module not available or missing addBinding method');
            }
          } catch (error) {
            console.error('Error setting up keyboard bindings:', error);
          }

          // Set initial content with safety checks
          const setInitialContent = () => {
            try {
              // Verify Quill and DOM are ready
              if (!quill || !quill.root || !quill.root.isConnected) {
                console.warn('Quill editor not ready for content initialization');
                return;
              }

              if (content) {
                try {
                  if (typeof content === 'string') {
                    quill.clipboard.dangerouslyPasteHTML(content);
                  } else if (htmlContent) {
                    quill.clipboard.dangerouslyPasteHTML(htmlContent);
                  } else if ((content as any).ops) {
                    // It's already a Delta
                    quill.setContents(content as any);
                  } else {
                    // Convert notebook JSON to HTML
                    const html = convertContentToHTML(content);
                    quill.clipboard.dangerouslyPasteHTML(html);
                  }
                } catch (contentError) {
                  console.error('Error setting specific content type:', contentError);
                  // Fallback: try to set as plain text
                  try {
                    const fallbackText = typeof content === 'string'
                      ? content
                      : JSON.stringify(content);
                    quill.setText(fallbackText);
                  } catch (fallbackError) {
                    console.error('Could not set fallback content:', fallbackError);
                  }
                }
              }
            } catch (error) {
              console.error('Error setting initial content:', error);
            }
          };

          // Delay content setting to ensure DOM is fully ready
          requestAnimationFrame(() => {
            setTimeout(() => {
              setInitialContent();
            }, 50); // Small delay to ensure everything is ready
          });

          // Set up debounced change handler
          quill.on('text-change', (_delta: any, _oldDelta: any, source: string) => {
            // Only trigger onChange for user changes
            if (source === 'user') {
              isUserChangeRef.current = true;

              // Clear existing timeout
              if (debounceTimeoutRef.current) {
                clearTimeout(debounceTimeoutRef.current);
              }

              // Set new timeout for debounced change
              debounceTimeoutRef.current = setTimeout(() => {
                if (changeHandlerRef.current && isUserChangeRef.current) {
                  try {
                    // Verify Quill is still available before accessing its properties
                    if (!quill || !quill.root || !quill.root.isConnected) {
                      console.warn('Quill editor unavailable during change handler');
                      return;
                    }

                    const html = quill.root.innerHTML;
                    const delta = quill.getContents();
                    const jsonContent = convertDeltaToContent(delta);
                    changeHandlerRef.current(jsonContent, html);
                  } catch (error) {
                    console.error('Error in debounced change handler:', error);
                  }
                }
                isUserChangeRef.current = false;
              }, 150); // 150ms debounce - fast enough for good UX, slow enough to prevent excessive calls
            }
          });

          // Focus editor if autofocus is true (with proper timing and safety checks)
          if (autofocus) {
            // Use multiple async layers to ensure DOM is fully ready
            requestAnimationFrame(() => {
              requestAnimationFrame(() => {
                setTimeout(() => {
                  try {
                    // Multiple safety checks before focusing
                    if (quill &&
                        quill.root &&
                        quill.root.isConnected &&
                        document.contains(quill.root) &&
                        quill.root.offsetParent !== null) {

                      // Additional check for selection capability
                      const selection = window.getSelection();
                      if (selection && selection.rangeCount >= 0) {
                        quill.focus();
                      }
                    }
                  } catch (error) {
                    console.warn('Could not focus Quill editor:', error);
                    // Fallback: try to focus the container directly
                    try {
                      if (containerRef.current && containerRef.current.isConnected) {
                        containerRef.current.focus();
                      }
                    } catch (fallbackError) {
                      console.warn('Could not focus container either:', fallbackError);
                    }
                  }
                }, 150); // Increased delay for better reliability
              });
            });
          }

          // Store Quill instance
          setQuillInstance(quill);

          // Expose Quill instance via ref
          if (ref) {
            if (typeof ref === 'function') {
              ref(quill);
            } else {
              ref.current = quill;
            }
          }
        } catch (error) {
          console.error('Error initializing Quill:', error);
        }
      };

      // Use requestAnimationFrame to ensure DOM is painted
      requestAnimationFrame(() => {
        initQuill();
      });
    }

    // Cleanup
    return () => {
      // Clear debounce timeout
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
        debounceTimeoutRef.current = null;
      }

      if (quillInstance) {
        if (ref) {
          if (typeof ref === 'function') {
            ref(null);
          } else {
            ref.current = null;
          }
        }
        setQuillInstance(null);
      }
    };
  }, []);

  // Update readOnly state when editable changes
  useEffect(() => {
    if (quillInstance) {
      quillInstance.enable(editable);
    }
  }, [quillInstance, editable]);

  // Update content when it changes (with optimization to prevent unnecessary updates)
  useEffect(() => {
    if (!quillInstance || !content) return;

    try {
      // Prevent updating content if user is currently typing
      if (isUserChangeRef.current) {
        return;
      }

      // Verify Quill instance and DOM are ready
      if (!quillInstance.root || !quillInstance.root.isConnected) {
        console.warn('Quill editor not ready for content update');
        return;
      }

      // Get current content to compare
      const currentHtml = quillInstance.root.innerHTML;
      let newHtml = '';

      // Determine new content based on type
      if (typeof content === 'string') {
        newHtml = content;
      } else if (htmlContent) {
        newHtml = htmlContent;
      } else if ((content as any).ops) {
        // It's already a Delta - convert to HTML for comparison
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = quillInstance.root.innerHTML;
        newHtml = tempDiv.innerHTML;
      } else {
        // Convert notebook JSON to HTML
        newHtml = convertContentToHTML(content);
      }

      // Only update if content has actually changed
      if (currentHtml !== newHtml) {
        // Get current selection safely
        let selection = null;
        try {
          selection = quillInstance.getSelection();
        } catch (error) {
          console.warn('Could not get selection:', error);
        }

        // Update content based on type with additional safety checks
        try {
          // Double-check that Quill is still ready before updating
          if (!quillInstance || !quillInstance.root || !quillInstance.root.isConnected) {
            console.warn('Quill editor became unavailable during content update');
            return;
          }

          if (typeof content === 'string') {
            quillInstance.clipboard.dangerouslyPasteHTML(content);
          } else if (htmlContent) {
            quillInstance.clipboard.dangerouslyPasteHTML(htmlContent);
          } else if ((content as any).ops) {
            // It's already a Delta
            quillInstance.setContents(content as any);
          } else {
            // Convert notebook JSON to HTML
            const html = convertContentToHTML(content);
            quillInstance.clipboard.dangerouslyPasteHTML(html);
          }
        } catch (error) {
          console.warn('Could not update content:', error);
          // Try to recover by clearing the editor
          try {
            if (quillInstance && quillInstance.root && quillInstance.root.isConnected) {
              quillInstance.setText('');
            }
          } catch (recoveryError) {
            console.error('Could not recover from content update error:', recoveryError);
          }
        }

        // Restore selection if it existed and is valid
        if (selection) {
          try {
            // Use requestAnimationFrame to ensure content is rendered before setting selection
            requestAnimationFrame(() => {
              try {
                if (quillInstance && quillInstance.root && quillInstance.root.isConnected) {
                  quillInstance.setSelection(selection);
                }
              } catch (error) {
                console.warn('Could not restore selection:', error);
              }
            });
          } catch (error) {
            console.warn('Could not schedule selection restoration:', error);
          }
        }
      }
    } catch (error) {
      console.error('Error updating Quill content:', error);
    }
  }, [quillInstance, content, htmlContent]);

  return (
    <div
      ref={containerRef}
      className={`quill-notebook-editor ${className}`}
    />
  );
});

QuillNotebookEditorComponent.displayName = 'QuillNotebookEditor';

// Memoize the component to prevent unnecessary re-renders
export const QuillNotebookEditor = React.memo(QuillNotebookEditorComponent, (prevProps, nextProps) => {
  // Custom comparison function to optimize re-renders
  return (
    prevProps.editable === nextProps.editable &&
    prevProps.autofocus === nextProps.autofocus &&
    prevProps.className === nextProps.className &&
    // Only re-render if content actually changed (shallow comparison for performance)
    prevProps.content === nextProps.content &&
    prevProps.htmlContent === nextProps.htmlContent &&
    prevProps.onChange === nextProps.onChange
  );
});
