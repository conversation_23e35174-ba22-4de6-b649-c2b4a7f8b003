"use client"

import { createContext, useContext, useState, ReactNode } from "react"
import { X } from "lucide-react"
import { AppError } from "@/lib/error-handler"

type ToastType = "success" | "error" | "info" | "warning"

interface Toast {
  id: string
  message: string
  type: ToastType
  duration?: number
}

interface ToastContextType {
  toasts: Toast[]
  showToast: (message: string, type?: ToastType, duration?: number) => void
  showErrorToast: (error: Error | AppError | string, fallbackMessage?: string) => void
  dismissToast: (id: string) => void
}

const ToastContext = createContext<ToastContextType | undefined>(undefined)

export function ToastProvider({ children }: { children: ReactNode }) {
  const [toasts, setToasts] = useState<Toast[]>([])

  const showToast = (message: string, type: ToastType = "info", duration = 5000) => {
    const id = Date.now().toString()
    setToasts((prev) => [...prev, { id, message, type, duration }])

    if (duration > 0) {
      setTimeout(() => {
        dismissToast(id)
      }, duration)
    }
  }

  const showErrorToast = (error: Error | AppError | string, fallbackMessage = "An unexpected error occurred") => {
    let message: string

    if (typeof error === 'string') {
      message = error
    } else if (error instanceof AppError) {
      message = error.message
    } else if (error instanceof Error) {
      message = error.message
    } else {
      message = fallbackMessage
    }

    showToast(message, "error")
  }

  const dismissToast = (id: string) => {
    setToasts((prev) => prev.filter((toast) => toast.id !== id))
  }

  return (
    <ToastContext.Provider value={{ toasts, showToast, showErrorToast, dismissToast }}>
      {children}
      <ToastContainer />
    </ToastContext.Provider>
  )
}

export function useToast() {
  const context = useContext(ToastContext)
  if (context === undefined) {
    throw new Error("useToast must be used within a ToastProvider")
  }
  return context
}

function ToastContainer() {
  const { toasts, dismissToast } = useToast()

  if (toasts.length === 0) return null

  return (
    <div className="fixed bottom-4 right-4 z-50 flex flex-col gap-2 max-w-md">
      {toasts.map((toast) => (
        <div
          key={toast.id}
          className={`
            rounded-md shadow-lg p-4 flex items-start justify-between
            animate-in slide-in-from-right-5 duration-300
            ${toast.type === "success" ? "bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100" : ""}
            ${toast.type === "error" ? "bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100" : ""}
            ${toast.type === "warning" ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100" : ""}
            ${toast.type === "info" ? "bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100" : ""}
          `}
        >
          <div className="flex-1 mr-2">{toast.message}</div>
          <button
            onClick={() => dismissToast(toast.id)}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          >
            <X className="h-4 w-4" />
          </button>
        </div>
      ))}
    </div>
  )
}
