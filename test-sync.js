// Simple test script to verify bidirectional synchronization
// Run this with: node test-sync.js

const testTradeId = '56e1d2a6-37cb-48d7-963d-f1669526e105';
const testNotebookId = 'a25340a9-0772-4dfc-aff9-0dfd8efe02cc';

console.log('Testing bidirectional synchronization...');
console.log('Trade ID:', testTradeId);
console.log('Notebook ID:', testNotebookId);

console.log('\nTo test manually:');
console.log('1. Go to the Daily Journal page');
console.log('2. Find the GBPUSD trade and edit its notes/tags/screenshots');
console.log('3. Check the notebook page to see if changes appear');
console.log('4. Edit the corresponding notebook entry');
console.log('5. Check the Daily Journal page to see if changes sync back');

console.log('\nExpected behavior:');
console.log('- Changes in trade journal should appear in linked notebook entries');
console.log('- Changes in notebook entries should appear in linked trades');
console.log('- Synchronization should be immediate (no page refresh needed)');

console.log('\nDebugging:');
console.log('- Check browser console for sync logs');
console.log('- Look for "Syncing trade changes to notebook entries" messages');
console.log('- Look for "Syncing notebook changes to trades" messages');
