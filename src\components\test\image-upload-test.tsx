'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';
import { uploadImageToStorage, deleteImageFromStorage } from '@/lib/image-service';
import { FallbackImage } from '@/components/ui/fallback-image';
import { RefreshCw, Trash, Upload } from 'lucide-react';

export function ImageUploadTest() {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [uploadedImageUrl, setUploadedImageUrl] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [uploadCount, setUploadCount] = useState(0);
  const [deleteCount, setDeleteCount] = useState(0);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setSelectedFile(e.target.files[0]);
    }
  };

  const handleUpload = async () => {
    if (!selectedFile) {
      toast.error('Please select a file first');
      return;
    }

    setIsUploading(true);
    try {
      const imageUrl = await uploadImageToStorage(selectedFile);
      setUploadedImageUrl(imageUrl);
      setUploadCount(prev => prev + 1);
      toast.success('Image uploaded successfully');
    } catch (error) {
      console.error('Error uploading image:', error);
      toast.error('Failed to upload image');
    } finally {
      setIsUploading(false);
    }
  };

  const handleDelete = async () => {
    if (!uploadedImageUrl) {
      toast.error('No image to delete');
      return;
    }

    setIsDeleting(true);
    try {
      const success = await deleteImageFromStorage(uploadedImageUrl);
      if (success) {
        setUploadedImageUrl(null);
        setSelectedFile(null);
        setDeleteCount(prev => prev + 1);
        toast.success('Image deleted successfully');
      } else {
        toast.error('Failed to delete image');
      }
    } catch (error) {
      console.error('Error deleting image:', error);
      toast.error('Failed to delete image');
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle>Image Upload Test</CardTitle>
        <CardDescription>
          Test uploading and deleting images from the storage bucket
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <Label htmlFor="image-upload-test">Select Image</Label>
          <Input
            id="image-upload-test"
            type="file"
            accept="image/*"
            onChange={handleFileChange}
            className="mt-1"
          />
        </div>
        
        {uploadedImageUrl && (
          <div className="mt-4">
            <h3 className="font-medium mb-2">Uploaded Image:</h3>
            <div className="relative aspect-video w-full max-h-[200px] overflow-hidden rounded-md mb-2 bg-muted/30">
              <FallbackImage
                src={uploadedImageUrl}
                alt="Uploaded image"
                className="w-full h-full object-contain"
                key={`image-${uploadCount}-${deleteCount}`}
                fallbackComponent={
                  <div className="flex items-center justify-center w-full h-full min-h-[100px] bg-muted/20">
                    <span className="text-sm text-muted-foreground">Image preview</span>
                  </div>
                }
              />
            </div>
            <p className="text-xs break-all mt-2">{uploadedImageUrl}</p>
          </div>
        )}
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button 
          onClick={handleUpload} 
          disabled={!selectedFile || isUploading}
          className="flex items-center"
        >
          {isUploading ? (
            <>
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
              Uploading...
            </>
          ) : (
            <>
              <Upload className="h-4 w-4 mr-2" />
              Upload Image
            </>
          )}
        </Button>
        <Button 
          onClick={handleDelete} 
          disabled={!uploadedImageUrl || isDeleting} 
          variant="destructive"
          className="flex items-center"
        >
          {isDeleting ? (
            <>
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
              Deleting...
            </>
          ) : (
            <>
              <Trash className="h-4 w-4 mr-2" />
              Delete Image
            </>
          )}
        </Button>
      </CardFooter>
    </Card>
  );
}
