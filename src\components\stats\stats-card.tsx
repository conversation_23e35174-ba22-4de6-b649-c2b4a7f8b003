"use client"

import { Card, CardContent } from "@/components/ui/card"
import { LucideIcon } from "lucide-react"
import { cn } from "@/lib/utils"

export interface StatsCardProps {
  title: string
  value: string | number
  prefix?: string
  suffix?: string
  trend?: "up" | "down"
  icon?: LucideIcon
  className?: string
}

export function StatsCard({
  title,
  value,
  prefix = "",
  suffix = "",
  trend,
  icon: Icon,
  className,
}: StatsCardProps) {
  return (
    <Card className={cn("dashboard-card bg-card hover:bg-card transition-colors", className)}>
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          <p className="text-sm font-medium text-muted-foreground">{title}</p>
          {Icon && <Icon className="h-4 w-4 text-muted-foreground" />}
        </div>
        <div className="mt-3 flex items-center gap-2">
          <h2 className="text-2xl font-semibold tracking-tight">
            {prefix}
            {value}
            {suffix}
          </h2>
          {trend && (
            <span
              className={cn(
                "flex items-center text-xs font-medium",
                trend === "up" ? "text-emerald-500" : "text-rose-500"
              )}
            >
              {trend === "up" ? (
                <svg
                  className="h-3 w-3"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"
                  />
                </svg>
              ) : (
                <svg
                  className="h-3 w-3"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M13 17h8m0 0v-8m0 8l-8-8-4 4-6-6"
                  />
                </svg>
              )}
            </span>
          )}
        </div>
      </CardContent>
    </Card>
  )
}