"use client"

import { format } from "date-fns"
import { ChartContainer } from "@/components/ui/chart-container"
import {
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  ReferenceLine,
} from "recharts"

interface Trade {
  time_close: string
  profit: number
}

interface TradePerformanceChartProps {
  trades: Trade[]
  initialBalance?: number
}

// Calculate optimal Y-axis domain to better reflect trading activity
function calculateYAxisDomain(data: any[], initialBalance: number) {
  if (!data || data.length === 0) return [initialBalance * 0.95, initialBalance * 1.05]

  // Get min and max balance values
  const balances = data.map(item => item.balance)
  const minBalance = Math.min(...balances)
  const maxBalance = Math.max(...balances)

  // Calculate the range
  const range = maxBalance - minBalance

  // If range is very small, add appropriate padding
  if (range < 100) {
    return [Math.max(0, minBalance - 50), maxBalance + 50]
  }

  // For larger ranges, use a percentage of the range as padding
  const padding = range * 0.1 // 10% padding
  return [Math.max(0, minBalance - padding), maxBalance + padding]
}

// Calculate appropriate tick count based on data range
function calculateTickCount(data: any[]) {
  if (!data || data.length === 0) return 5

  const balances = data.map(item => item.balance)
  const minBalance = Math.min(...balances)
  const maxBalance = Math.max(...balances)
  const range = maxBalance - minBalance

  // For small ranges, use more ticks to show detail
  if (range < 500) return 10
  if (range < 1000) return 8
  if (range < 5000) return 6

  // For larger ranges, use fewer ticks
  return 5
}

export function EnhancedTradePerformanceChart({
  trades,
  initialBalance = 0
}: TradePerformanceChartProps) {
  // Sort trades by close time
  const sortedTrades = [...trades].sort(
    (a, b) => new Date(a.time_close).getTime() - new Date(b.time_close).getTime()
  )

  // Calculate cumulative performance
  let runningBalance = initialBalance
  const chartData = sortedTrades.map((trade) => {
    runningBalance += trade.profit
    return {
      date: new Date(trade.time_close).toISOString().split('T')[0],
      balance: parseFloat(runningBalance.toFixed(2)),
      profit: parseFloat(trade.profit.toFixed(2)),
      // Add a flag to determine if this point is above or below initial balance
      isProfit: runningBalance >= initialBalance
    }
  })

  // Add initial balance as first point if we have trades
  if (chartData.length > 0) {
    const firstTradeDate = new Date(sortedTrades[0].time_close)
    // Set the initial point to one day before the first trade
    firstTradeDate.setDate(firstTradeDate.getDate() - 1)

    chartData.unshift({
      date: firstTradeDate.toISOString().split('T')[0],
      balance: initialBalance,
      profit: 0,
      isProfit: true
    })
  }

  return (
    <ChartContainer>
      <ResponsiveContainer width="100%" height="100%">
        <AreaChart
          data={chartData}
          margin={{ top: 20, right: 20, bottom: 40, left: 60 }}
        >
          {/* Define gradients for profit/loss areas */}
          <defs>
            <linearGradient id="profitGradient" x1="0" y1="0" x2="0" y2="1">
              <stop offset="0%" stopColor="#10b981" stopOpacity={0.45} />
              <stop offset="100%" stopColor="#ffffff" stopOpacity={0.2} />
            </linearGradient>
            <linearGradient id="lossGradient" x1="0" y1="1" x2="0" y2="0">
              <stop offset="0%" stopColor="#ef4444" stopOpacity={0.45} />
              <stop offset="100%" stopColor="#ffffff" stopOpacity={0.2} />
            </linearGradient>
          </defs>

          <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" opacity={0.4} />
          <XAxis
            dataKey="date"
            tickFormatter={(value) => format(new Date(value), "MMM d")}
            stroke="hsl(var(--muted-foreground))"
            tick={(props) => {
              const { x, y, payload } = props;
              return (
                <g transform={`translate(${x},${y})`}>
                  <text
                    x={0}
                    y={0}
                    dy={16}
                    textAnchor="end"
                    fill="hsl(var(--muted-foreground))"
                    opacity={0.7}
                    fontSize={11}
                    transform="rotate(-45)"
                  >
                    {format(new Date(payload.value), "MMM d")}
                  </text>
                </g>
              );
            }}
            height={60}
            axisLine={{ stroke: 'hsl(var(--border))', opacity: 0.5 }}
          />
          <YAxis
            tickFormatter={(value) => `$${value.toLocaleString()}`}
            stroke="hsl(var(--muted-foreground))"
            tick={{ fontSize: 11, fill: 'hsl(var(--muted-foreground))', opacity: 0.7 }}
            axisLine={{ stroke: 'hsl(var(--border))', opacity: 0.5 }}
            domain={calculateYAxisDomain(chartData, initialBalance)}
            allowDecimals={false} // Avoid decimal ticks
            interval={0} // Show all ticks
            tickCount={calculateTickCount(chartData)} // Dynamic tick count
          />
          <Tooltip
            content={({ active, payload }) => {
              if (!active || !payload || !payload.length) return null;

              const date = format(new Date(payload[0].payload.date), "MMM d, yyyy");
              const currentBalance = parseFloat(payload[0].payload.balance).toLocaleString();

              return (
                <div className="bg-popover/75 border border-border/70 rounded-md shadow-sm p-2 text-xs backdrop-blur-sm">
                  <p className="font-medium mb-0.5">{date}</p>
                  <div className="grid grid-cols-2 gap-x-3 gap-y-0.5">
                    <span className="text-muted-foreground">Initial:</span>
                    <span className="font-medium text-right">${initialBalance.toLocaleString()}</span>

                    <span className="text-muted-foreground">Equity:</span>
                    <span className={`font-medium text-right ${
                      payload[0].payload.balance > initialBalance
                        ? 'text-emerald-500'
                        : payload[0].payload.balance < initialBalance
                          ? 'text-rose-500'
                          : ''
                    }`}>
                      ${currentBalance}
                    </span>

                    <span className="text-muted-foreground">P&L:</span>
                    <span className={`font-medium text-right ${
                      payload[0].payload.balance > initialBalance
                        ? 'text-emerald-500'
                        : payload[0].payload.balance < initialBalance
                          ? 'text-rose-500'
                          : ''
                    }`}>
                      ${(payload[0].payload.balance - initialBalance).toLocaleString()}
                      {' '}
                      <span className="text-muted-foreground/80">
                        ({((payload[0].payload.balance - initialBalance) / initialBalance * 100).toFixed(2)}%)
                      </span>
                    </span>
                  </div>
                </div>
              );
            }}
          />
          {/* Reference line at initial balance */}
          <ReferenceLine
            y={initialBalance}
            stroke="rgba(100, 100, 100, 0.5)"
            strokeDasharray="3 3"
            label={{
              value: "Initial Balance",
              position: "insideBottomRight",
              fill: "hsl(var(--muted-foreground))",
              fontSize: 10,
            }}
          />
          {/* Area for profit (above initial balance) */}
          <Area
            type="monotone"
            dataKey={(data) => data.balance > initialBalance ? data.balance : initialBalance}
            name="Balance"
            stroke="none" // No stroke - we'll add a separate line
            strokeWidth={0}
            fillOpacity={1}
            fill="url(#profitGradient)"
            activeDot={false} // No active dots - handled by the main line
            animationDuration={1500}
            animationEasing="ease-out"
            // Only show this area for values above initial balance
            baseValue={initialBalance}
          />

          {/* Area for loss (below initial balance) */}
          <Area
            type="monotone"
            dataKey={(data) => data.balance < initialBalance ? data.balance : initialBalance}
            name="Balance"
            stroke="none" // No stroke - we'll add a separate line
            strokeWidth={0}
            fillOpacity={1}
            fill="url(#lossGradient)"
            activeDot={false} // No active dots - handled by the main line
            animationDuration={1500}
            animationEasing="ease-out"
            // Only show this area for values below initial balance
            baseValue={initialBalance}
          />

          {/* Main line to show the actual equity curve */}
          <Area
            type="monotone"
            dataKey="balance"
            name="Balance"
            stroke="#374151" // Even darker gray stroke for better visibility
            strokeWidth={1.5} // Thin line
            fillOpacity={0} // No fill - the fill is handled by the areas above
            dot={false} // No dots on the line
            activeDot={{ r: 4, strokeWidth: 0 }} // Only show dot on hover
            animationDuration={1500}
            animationEasing="ease-out"
            connectNulls
          />
        </AreaChart>
      </ResponsiveContainer>
    </ChartContainer>
  )
}
