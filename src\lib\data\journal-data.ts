import { getSupabaseBrowser } from '@/lib/supabase-browser'
import { getSupabaseServer } from '@/lib/supabase-server'
import { handleError, NotFoundError } from '@/lib/error-handler'
import { Database } from '@/types/supabase'
import { format } from 'date-fns'

export type JournalEntry = Database["public"]["Tables"]["journal_entries"]["Row"]
export type JournalEntryInsert = Database["public"]["Tables"]["journal_entries"]["Insert"]
export type JournalEntryUpdate = Database["public"]["Tables"]["journal_entries"]["Update"]

export type DailyJournalEntry = {
  id?: string;
  user_id?: string;
  account_id?: string;
  date: string;
  note: string;
  screenshots: string[];
  tags: string[];
  created_at?: string;
  updated_at?: string;
}

/**
 * Get journal entries with optional filtering
 */
export async function getJournalEntries(
  userId: string,
  options?: {
    tradeId?: string;
    startDate?: string;
    endDate?: string;
    tags?: string[];
    searchTerm?: string;
    accountId?: string | null;
    useServer?: boolean;
  }
): Promise<JournalEntry[]> {
  try {
    // If accountId is explicitly null (user has unselected all accounts), return empty array
    if (options?.accountId === null) {
      console.log('No account selected, returning empty journal entries array')
      return []
    }

    const supabase = options?.useServer
      ? await getSupabaseServer()
      : getSupabaseBrowser()

    let query = supabase
      .from("journal_entries")
      .select("*")
      .eq("user_id", userId)
      .order("entry_date", { ascending: false })

    // Apply filters if provided
    if (options?.tradeId) {
      query = query.eq("trade_id", options.tradeId)
    }

    // If an account ID is specified, we need to filter trades by that account
    // and then filter journal entries by those trade IDs
    if (options?.accountId) {
      // First, get all trades for the specified account
      const { data: accountTrades, error: tradesError } = await supabase
        .from("trades")
        .select("id")
        .eq("account_id", options.accountId)

      if (tradesError) {
        throw tradesError
      } else if (accountTrades && accountTrades.length > 0) {
        // Get all trade IDs for this account
        const tradeIds = accountTrades.map(trade => trade.id)
        // Filter journal entries to only include those linked to these trades
        // or entries with no trade_id (general entries)
        query = query.or(`trade_id.in.(${tradeIds.join(',')}),trade_id.is.null`)
      }
    }

    if (options?.startDate) {
      query = query.gte("entry_date", options.startDate)
    }

    if (options?.endDate) {
      query = query.lte("entry_date", options.endDate)
    }

    if (options?.tags && options.tags.length > 0) {
      // Filter entries that contain any of the specified tags
      query = query.overlaps("tags", options.tags)
    }

    if (options?.searchTerm) {
      // Search in title and content
      query = query.or(`title.ilike.%${options.searchTerm}%,content.ilike.%${options.searchTerm}%`)
    }

    const { data: entries, error } = await query

    if (error) {
      throw error
    }

    return entries || []
  } catch (error) {
    return handleError(error, [], 'getJournalEntries')
  }
}

/**
 * Get a journal entry by ID
 */
export async function getJournalEntryById(
  userId: string,
  entryId: string,
  options?: { useServer?: boolean }
): Promise<JournalEntry | null> {
  try {
    const supabase = options?.useServer
      ? await getSupabaseServer()
      : getSupabaseBrowser()

    const { data: entry, error } = await supabase
      .from("journal_entries")
      .select("*")
      .eq("id", entryId)
      .eq("user_id", userId)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        throw new NotFoundError(`Journal entry with ID ${entryId} not found`)
      }
      throw error
    }

    return entry
  } catch (error) {
    return handleError(error, null, 'getJournalEntryById')
  }
}

/**
 * Create a new journal entry
 */
export async function createJournalEntry(
  userId: string,
  entry: Omit<JournalEntryInsert, "user_id">
): Promise<JournalEntry | null> {
  try {
    const supabase = getSupabaseBrowser()

    const { data, error } = await supabase
      .from("journal_entries")
      .insert({
        ...entry,
        user_id: userId,
        updated_at: new Date().toISOString()
      })
      .select()
      .single()

    if (error) {
      throw error
    }

    return data
  } catch (error) {
    return handleError(error, null, 'createJournalEntry')
  }
}

/**
 * Get daily journal entries
 */
export async function getDailyJournalEntries(
  userId: string,
  options?: {
    accountId?: string;
    startDate?: string;
    endDate?: string;
    tags?: string[];
    useServer?: boolean;
  }
): Promise<DailyJournalEntry[]> {
  try {
    const supabase = options?.useServer
      ? await getSupabaseServer()
      : getSupabaseBrowser()

    const { data, error } = await supabase.rpc(
      'get_daily_journal_entries',
      {
        p_user_id: userId,
        p_account_id: options?.accountId || null,
        p_start_date: options?.startDate || null,
        p_end_date: options?.endDate || null,
        p_tags: options?.tags || null
      }
    )

    if (error) {
      throw error
    }

    return data || []
  } catch (error) {
    return handleError(error, [], 'getDailyJournalEntries')
  }
}

/**
 * Get a daily journal entry for a specific date
 */
export async function getDailyJournalEntry(
  userId: string,
  date: Date,
  accountId?: string,
  options?: { useServer?: boolean }
): Promise<DailyJournalEntry | null> {
  try {
    const supabase = options?.useServer
      ? await getSupabaseServer()
      : getSupabaseBrowser()
    const dateStr = format(date, 'yyyy-MM-dd')

    // Build the query to properly handle the account_id filter
    let query = supabase
      .from('daily_journal_entries')
      .select('*')
      .eq('user_id', userId)
      .eq('date', dateStr)

    // Add account filter if provided
    if (accountId) {
      query = query.eq('account_id', accountId)
    } else {
      query = query.is('account_id', null)
    }

    const { data, error } = await query.maybeSingle()

    if (error) {
      // Only throw if it's not a "not found" error
      if (!error.message.includes('not found')) {
        throw error
      }
      return null
    }

    return data
  } catch (error) {
    return handleError(error, null, 'getDailyJournalEntry')
  }
}
