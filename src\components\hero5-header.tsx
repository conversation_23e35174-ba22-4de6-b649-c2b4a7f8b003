"use client"

import React from "react"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import { Logo } from "@/components/logo"
import { motion } from "framer-motion"

export function HeroHeader() {
  const navVariants = {
    hidden: { opacity: 0, y: -20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 30,
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: -10 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 30
      }
    }
  }

  return (
    <motion.header
      initial="hidden"
      animate="visible"
      variants={navVariants}
      className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60"
    >
      <div className="container flex h-14 items-center">
        <motion.div variants={itemVariants} className="mr-4 flex">
          <Logo />
        </motion.div>
        <div className="flex flex-1 items-center justify-between space-x-2 md:justify-end">
          <nav className="flex items-center">
            <motion.div variants={itemVariants}>
              <Link href="/auth">
                <Button variant="ghost" className="mr-2">
                  Log in
                </Button>
              </Link>
            </motion.div>
            <motion.div variants={itemVariants}>
              <Link href="/auth?tab=register">
                <Button>Get Started</Button>
              </Link>
            </motion.div>
          </nav>
        </div>
      </div>
    </motion.header>
  )
}
