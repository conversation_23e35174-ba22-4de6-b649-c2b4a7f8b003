'use client';

import { useState, useEffect, useCallback } from 'react';
import { format, parse } from 'date-fns';
import { toast } from 'sonner';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { useAccount } from '@/contexts/account-context';
import { fetchFilteredJournalData } from './filter-actions';
import { JournalFilter } from './journal-filter';
import { JournalFilterResult } from './types';
import { DateRange } from 'react-day-picker';
import { buildUrlParams, formatDateRange } from './utils';

// Components for journal content
import { TradeJournalList } from './trade-journal-list';
import { DailyJournalList } from './daily-journal-list';

interface JournalClientProps {
  userId: string;
  initialData: JournalFilterResult;
  initialSearchTerm?: string;
  initialTags?: string[];
  initialStartDate?: string;
  initialEndDate?: string;
  initialTab?: string;
}

export default function JournalClient({
  userId,
  initialData,
  initialSearchTerm = '',
  initialTags = [],
  initialStartDate,
  initialEndDate,
  initialTab = 'with-trades'
}: JournalClientProps) {
  // We'll use the useRouter and useSearchParams hooks when needed
  const { selectedAccountId } = useAccount();

  // State for filter parameters
  const [searchTerm, setSearchTerm] = useState(initialSearchTerm);
  const [selectedTags, setSelectedTags] = useState<string[]>(initialTags);
  const [dateRange, setDateRange] = useState<DateRange | undefined>(
    initialStartDate && initialEndDate
      ? {
          from: parse(initialStartDate, 'yyyy-MM-dd', new Date()),
          to: parse(initialEndDate, 'yyyy-MM-dd', new Date())
        }
      : initialStartDate
      ? { from: parse(initialStartDate, 'yyyy-MM-dd', new Date()) }
      : undefined
  );
  const [activeTab, setActiveTab] = useState(initialTab);

  // State for journal data
  const [journalData, setJournalData] = useState<JournalFilterResult>(initialData);
  const [availableTags, setAvailableTags] = useState<string[]>(initialData.availableTags || []);

  // State for pagination
  const [paginationState, setPaginationState] = useState({
    currentPage: 1,
    pageSize: 50,
    hasMore: initialData.pagination?.hasMore || false
  });

  // Loading state
  const [isLoading, setIsLoading] = useState(false);

  // Track if this is the initial load
  const [isInitialLoad, setIsInitialLoad] = useState(true);

  // Fetch filtered journal data
  const fetchJournalData = useCallback(async (
    newSearchTerm: string,
    newTags: string[],
    newDateRange?: DateRange,
    newTab: string = activeTab,
    page: number = 1,
    loadMore: boolean = false
  ) => {
    try {
      setIsLoading(true);

      // Format date range for API call
      const { startDate, endDate } = formatDateRange(newDateRange);

      console.log(`Fetching journal data with filters:`, {
        searchTerm: newSearchTerm,
        tags: newTags,
        startDate,
        endDate,
        tab: newTab,
        page
      });

      // Use server action to fetch filtered data
      const filteredData = await fetchFilteredJournalData(
        userId,
        selectedAccountId,
        newSearchTerm,
        newTags,
        startDate,
        endDate,
        newTab,
        page,
        paginationState.pageSize,
        loadMore
      );

      // Update state with filtered data
      if (loadMore) {
        // Append new data to existing data
        setJournalData(prevData => ({
          ...prevData,
          trades: [...prevData.trades, ...filteredData.trades],
          tradesWithJournalContent: [
            ...prevData.tradesWithJournalContent,
            ...filteredData.tradesWithJournalContent
          ],
          pagination: filteredData.pagination
        }));
      } else {
        // Replace existing data with new data
        setJournalData(filteredData);
      }

      // Update available tags
      setAvailableTags(filteredData.availableTags || []);

      // Update pagination state
      setPaginationState({
        currentPage: page,
        pageSize: paginationState.pageSize,
        hasMore: filteredData.pagination?.hasMore || false
      });

      // Provide feedback on results
      if ((newSearchTerm && newSearchTerm.trim() !== '') || newTags.length > 0) {
        const totalResults = newTab === "with-trades"
          ? filteredData.tradesWithJournalContent.length
          : filteredData.tradingDays.length;

        if (totalResults > 0) {
          toast.success(`Found ${totalResults} ${newTab === "with-trades" ? "trade" : "daily"} journal ${totalResults === 1 ? 'entry' : 'entries'}`);
        } else {
          toast.info(`No ${newTab === "with-trades" ? "trade" : "daily"} journal entries found with current filters`);
        }
      }
    } catch (error) {
      console.error("Error fetching journal data:", error);
      toast.error(`Failed to fetch journal data. Please try again.`);
    } finally {
      setIsLoading(false);
    }
  }, [userId, selectedAccountId, activeTab, paginationState.pageSize]);

  // Reset filters
  const resetFilters = useCallback(() => {
    setSearchTerm('');
    setSelectedTags([]);
    setDateRange(undefined);

    // Update URL
    window.history.replaceState({}, '', '/journal');

    // Fetch unfiltered data
    fetchJournalData('', [], undefined, activeTab);
  }, [activeTab, fetchJournalData]);

  // Handle tab change
  const handleTabChange = useCallback((value: string) => {
    setActiveTab(value);

    // Reset pagination when changing tabs
    setPaginationState({
      ...paginationState,
      currentPage: 1,
      hasMore: false
    });

    // Build URL parameters
    const params = buildUrlParams({
      searchTerm,
      tags: selectedTags,
      startDate: dateRange?.from ? format(dateRange.from, "yyyy-MM-dd") : undefined,
      endDate: dateRange?.to ? format(dateRange.to, "yyyy-MM-dd") : undefined,
      activeTab: value
    });

    // Update URL without full navigation
    window.history.replaceState({}, '', `/journal?${params.toString()}`);

    // Fetch filtered data for the new tab
    fetchJournalData(searchTerm, selectedTags, dateRange, value);
  }, [searchTerm, selectedTags, dateRange, paginationState, fetchJournalData]);

  // Load more data
  const loadMoreData = useCallback(() => {
    if (!paginationState.hasMore || isLoading) return;

    const nextPage = paginationState.currentPage + 1;
    fetchJournalData(searchTerm, selectedTags, dateRange, activeTab, nextPage, true);
  }, [
    searchTerm,
    selectedTags,
    dateRange,
    activeTab,
    paginationState.currentPage,
    paginationState.hasMore,
    isLoading,
    fetchJournalData
  ]);

  // Update when account changes
  useEffect(() => {
    // On initial load, use the server-provided data instead of re-fetching
    if (isInitialLoad) {
      console.log('Using initial server data for journal cards');
      setIsInitialLoad(false);
      return;
    }

    // For subsequent account changes, fetch new data
    if (selectedAccountId) {
      console.log('Account changed, fetching new journal data');
      fetchJournalData(searchTerm, selectedTags, dateRange, activeTab);
    }
  }, [selectedAccountId, isInitialLoad, searchTerm, selectedTags, dateRange, activeTab, fetchJournalData]);

  return (
    <div className="space-y-6">
      <JournalFilter
        availableTags={availableTags}
        selectedTags={selectedTags}
        setSelectedTags={setSelectedTags}
        searchTerm={searchTerm}
        setSearchTerm={setSearchTerm}
        dateRange={dateRange}
        setDateRange={setDateRange}
        activeTab={activeTab}
        onSearch={fetchJournalData}
        onReset={resetFilters}
        isLoading={isLoading}
      />

      <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="with-trades">Trade-Specific</TabsTrigger>
          <TabsTrigger value="without-trades">Daily Journal</TabsTrigger>
        </TabsList>

        <TabsContent value="with-trades" className="space-y-4">
          {isLoading && paginationState.currentPage === 1 ? (
            <div className="space-y-4">
              {Array.from({ length: 3 }).map((_, i) => (
                <div key={i} className="border rounded-lg p-4 space-y-2">
                  <Skeleton className="h-6 w-1/3" />
                  <Skeleton className="h-4 w-1/2" />
                  <Skeleton className="h-20 w-full" />
                </div>
              ))}
            </div>
          ) : journalData.tradesWithJournalContent.length > 0 ? (
            <div className="space-y-4">
              {/* Display trade journal entries */}
              <TradeJournalList
                trades={journalData.tradesWithJournalContent}
                strategyMap={journalData.strategyMap}
              />
              <div className="text-center">
                {paginationState.hasMore && (
                  <Button
                    variant="outline"
                    onClick={loadMoreData}
                    disabled={isLoading}
                  >
                    {isLoading ? 'Loading...' : 'Load More'}
                  </Button>
                )}
              </div>
            </div>
          ) : (
            <div className="text-center py-10 border rounded-lg">
              <h3 className="text-lg font-medium">No Trade Journals Found</h3>
              <p className="text-muted-foreground mt-2">
                You haven't created any trade-specific journal entries yet. Add
                notes or screenshots to your trades to see them here.
              </p>
            </div>
          )}
        </TabsContent>

        <TabsContent value="without-trades" className="space-y-4">
          {isLoading && paginationState.currentPage === 1 ? (
            <div className="space-y-4">
              {Array.from({ length: 3 }).map((_, i) => (
                <div key={i} className="border rounded-lg p-4 space-y-2">
                  <Skeleton className="h-6 w-1/3" />
                  <Skeleton className="h-4 w-1/2" />
                  <Skeleton className="h-20 w-full" />
                </div>
              ))}
            </div>
          ) : journalData.tradingDays.length > 0 ? (
            <div className="space-y-4">
              {/* Display daily journal entries */}
              <DailyJournalList
                tradingDays={journalData.tradingDays}
                userId={userId}
                accountId={selectedAccountId}
              />
            </div>
          ) : (
            <div className="text-center py-10 border rounded-lg">
              <h3 className="text-lg font-medium">No Daily Journals Found</h3>
              <p className="text-muted-foreground mt-2">
                You haven't created any daily journal entries yet. Create a new
                entry to start journaling your trading days.
              </p>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
