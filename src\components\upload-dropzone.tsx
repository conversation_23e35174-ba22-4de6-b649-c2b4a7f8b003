"use client"

import { useState, useCallback } from "react"
import { useDropzone } from "react-dropzone"
import { <PERSON>ertCircle, FileSpreadsheet, Loader2, UploadIcon } from "lucide-react"
import { toast } from "sonner"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { cn } from "@/lib/utils"
import { uploadExcelFile, validateProcessedData } from "@/lib/excel-service"
import { TradePreviewDialog } from "@/components/trade-preview-dialog"
import { type ProcessedData } from "@/lib/excel-processor"

interface UploadDropzoneProps {
  onFileAccepted: (data: ProcessedData) => void
  disabled?: boolean
}

export function UploadDropzone({ onFileAccepted, disabled = false }: UploadDropzoneProps) {
  const [isProcessing, setIsProcessing] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [error, setError] = useState<string | null>(null)
  const [previewData, setPreviewData] = useState<ProcessedData | null>(null)
  const [showPreview, setShowPreview] = useState(false)

  const handleFileUpload = async (file: File) => {
    try {
      setIsProcessing(true)
      setError(null)
      setUploadProgress(10)

      // Simulate upload progress
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval)
            return prev
          }
          return prev + 10
        })
      }, 300)

      // Upload and process the file
      const data = await uploadExcelFile(file)

      clearInterval(progressInterval)
      setUploadProgress(100)

      // Validate the processed data
      if (!validateProcessedData(data)) {
        throw new Error('Invalid data format. Please check your Excel file.')
      }

      // Show preview dialog
      setPreviewData(data)
      setShowPreview(true)
    } catch (error) {
      console.error('Error processing file:', error)
      setError(error instanceof Error ? error.message : 'Failed to process file')
      toast.error(error instanceof Error ? error.message : 'Failed to process file')
      setUploadProgress(0)
    } finally {
      // Keep processing state true until preview is confirmed or canceled
      if (!showPreview) {
        setIsProcessing(false)
      }
    }
  }

  const onDrop = useCallback((acceptedFiles: File[]) => {
    if (acceptedFiles.length > 0) {
      handleFileUpload(acceptedFiles[0])
    }
  }, []) // No dependencies as handleFileUpload uses state setters

  const handleConfirmImport = () => {
    if (previewData) {
      onFileAccepted(previewData)
      setShowPreview(false)
      setPreviewData(null)
      setIsProcessing(false)
      setUploadProgress(0)
    }
  }

  const handleCancelImport = () => {
    setShowPreview(false)
    setPreviewData(null)
    setIsProcessing(false)
    setUploadProgress(0)
  }

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
      'application/vnd.ms-excel': ['.xls'],
      'text/csv': ['.csv']
    },
    maxFiles: 1,
    maxSize: 9 * 1024 * 1024, // 9MB max size
    disabled: isProcessing || disabled
  })

  return (
    <>
      <div
        {...getRootProps()}
        className={cn(
          "flex flex-col items-center justify-center w-full h-40 border-2 border-dashed rounded-lg cursor-pointer transition-colors",
          isDragActive ? "border-primary bg-primary/5" : "border-muted-foreground/25",
          (isProcessing || disabled) ? "opacity-50 cursor-not-allowed" : "hover:border-primary hover:bg-primary/5"
        )}
      >
        <input {...getInputProps()} />
        {isProcessing ? (
          <div className="flex flex-col items-center space-y-2 p-4 w-full">
            <Loader2 className="w-6 h-6 animate-spin text-primary" />
            <p className="text-sm text-muted-foreground">
              {uploadProgress < 100 ? "Processing your file..." : "Preparing data preview..."}
            </p>
            <Progress value={uploadProgress} className="h-2 w-full max-w-xs" />
          </div>
        ) : (
          <>
            <FileSpreadsheet className="w-8 h-8 mb-2 text-muted-foreground" />
            <p className="text-sm font-medium mb-1">
              {isDragActive ? "Drop the file here" : "Upload your trading history"}
            </p>
            <p className="text-xs text-muted-foreground text-center max-w-xs">
              Drag & drop or click to select an MT5 Excel report (max 9MB)
            </p>
          </>
        )}
      </div>

      {error && (
        <Alert variant="destructive" className="mt-2">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Preview Dialog */}
      <TradePreviewDialog
        open={showPreview}
        onOpenChange={setShowPreview}
        data={previewData}
        onConfirm={handleConfirmImport}
        onCancel={handleCancelImport}
        isLoading={isProcessing}
      />
    </>
  )
}