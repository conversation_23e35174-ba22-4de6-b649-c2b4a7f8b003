"use client"

import { useState } from "react"
import { toast } from "sonner"
import { CustomMetric } from "@/types/metrics"
import { deleteCustomMetric } from "@/lib/metrics-service"
import { formatDistanceToNow } from "date-fns"

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { Badge } from "@/components/ui/badge"
import { MoreVertical, Edit, Trash, Plus, ArrowUpRight, ArrowDownRight } from "lucide-react"

interface CustomMetricsListProps {
  userId: string
  metrics: CustomMetric[]
  onEdit: (metric: CustomMetric) => void
  onDelete: (metricId: string) => void
  onAdd: () => void
}

export function CustomMetricsList({ userId, metrics, onEdit, onDelete, onAdd }: CustomMetricsListProps) {
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [metricToDelete, setMetricToDelete] = useState<CustomMetric | null>(null)

  const handleDeleteClick = (metric: CustomMetric) => {
    setMetricToDelete(metric)
    setDeleteDialogOpen(true)
  }

  const confirmDelete = async () => {
    if (!metricToDelete) return

    try {
      const success = await deleteCustomMetric(userId, metricToDelete.id)
      if (success) {
        toast.success("Metric deleted successfully")
        onDelete(metricToDelete.id)
      } else {
        toast.error("Failed to delete metric")
      }
    } catch (error) {
      console.error("Error deleting metric:", error)
      toast.error("An error occurred while deleting the metric")
    } finally {
      setDeleteDialogOpen(false)
      setMetricToDelete(null)
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold tracking-tight">Custom Metrics</h2>
      </div>

      {metrics.length === 0 ? (
        <Card className="border-dashed">
          <CardContent className="pt-6 text-center">
            <p className="text-muted-foreground mb-4">
              You haven't created any custom metrics yet.
            </p>
            <Button onClick={onAdd} variant="outline">
              <Plus className="mr-2 h-4 w-4" /> Create Your First Metric
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {metrics.map((metric) => (
            <Card key={metric.id} className="flex flex-col">
              <CardHeader className="pb-2">
                <div className="flex justify-between items-start">
                  <div className="space-y-1">
                    <CardTitle className="flex items-center">
                      {metric.name}
                      {metric.is_higher_better ? (
                        <ArrowUpRight className="ml-1 h-4 w-4 text-green-500" />
                      ) : (
                        <ArrowDownRight className="ml-1 h-4 w-4 text-red-500" />
                      )}
                    </CardTitle>
                    <CardDescription>
                      {metric.description || "No description provided"}
                    </CardDescription>
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon">
                        <MoreVertical className="h-4 w-4" />
                        <span className="sr-only">Open menu</span>
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => onEdit(metric)}>
                        <Edit className="mr-2 h-4 w-4" />
                        Edit
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem
                        onClick={() => handleDeleteClick(metric)}
                        className="text-destructive focus:text-destructive"
                      >
                        <Trash className="mr-2 h-4 w-4" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </CardHeader>
              <CardContent className="pb-2 flex-grow">
                <div className="space-y-2">
                  <div className="text-sm">
                    <span className="font-medium">Formula:</span>
                    <code className="ml-2 p-1 bg-muted rounded text-xs font-mono">
                      {metric.formula}
                    </code>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {metric.is_percentage && (
                      <Badge variant="outline">Percentage</Badge>
                    )}
                    <Badge variant="outline">
                      {metric.display_precision} decimal{metric.display_precision !== 1 && "s"}
                    </Badge>
                    {metric.target_value !== null && (
                      <Badge variant="outline">
                        Target: {metric.target_value}
                        {metric.is_percentage && "%"}
                      </Badge>
                    )}
                  </div>
                </div>
              </CardContent>
              <CardFooter className="pt-2 text-xs text-muted-foreground">
                Updated {formatDistanceToNow(new Date(metric.updated_at), { addSuffix: true })}
              </CardFooter>
            </Card>
          ))}
        </div>
      )}

      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete the metric &quot;{metricToDelete?.name}&quot;.
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDelete} className="bg-destructive text-destructive-foreground">
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
