"use client"

import React, { useState, useEffect, useRef } from "react"
import { Bold, Italic, List, ListOrdered, AlignLeft, AlignCenter, AlignRight, Heading1, Heading2, Heading3, Underline, Quote } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { cn } from "@/lib/utils"

interface RichTextEditorProps {
  value: string
  onChange: (value: string) => void
  placeholder?: string
  className?: string
  minHeight?: string
}

export function RichTextEditor({
  value,
  onChange,
  placeholder = "Write your content here...",
  className,
  minHeight = "200px"
}: RichTextEditorProps) {
  const [text, setText] = useState(value)
  const [selectionStart, setSelectionStart] = useState(0)
  const [selectionEnd, setSelectionEnd] = useState(0)
  const textareaRef = useRef<HTMLTextAreaElement>(null)

  useEffect(() => {
    setText(value)
  }, [value])

  const handleTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newText = e.target.value
    setText(newText)
    onChange(newText)

    // Save selection position
    if (textareaRef.current) {
      setSelectionStart(textareaRef.current.selectionStart)
      setSelectionEnd(textareaRef.current.selectionEnd)
    }
  }

  const applyFormatting = (prefix: string, suffix: string = prefix) => {
    if (!textareaRef.current) return

    const textarea = textareaRef.current
    const start = textarea.selectionStart
    const end = textarea.selectionEnd
    const selectedText = text.substring(start, end)

    const beforeText = text.substring(0, start)
    const afterText = text.substring(end)

    const newText = beforeText + prefix + selectedText + suffix + afterText
    setText(newText)
    onChange(newText)

    // Set focus back to textarea and restore selection with adjustment for added formatting
    setTimeout(() => {
      textarea.focus()
      textarea.setSelectionRange(
        start + prefix.length,
        end + prefix.length
      )
    }, 0)
  }

  const formatBold = () => applyFormatting("**")
  const formatItalic = () => applyFormatting("*")
  const formatUnderline = () => applyFormatting("__")
  const formatH1 = () => applyFormatting("# ", "\n")
  const formatH2 = () => applyFormatting("## ", "\n")
  const formatH3 = () => applyFormatting("### ", "\n")
  const formatQuote = () => applyFormatting("> ", "\n")

  const formatList = () => {
    if (!textareaRef.current) return

    const textarea = textareaRef.current
    const start = textarea.selectionStart
    const end = textarea.selectionEnd
    const selectedText = text.substring(start, end)

    // Split the selected text into lines
    const lines = selectedText.split("\n")
    const formattedLines = lines.map(line => `- ${line}`)
    const formattedText = formattedLines.join("\n")

    const beforeText = text.substring(0, start)
    const afterText = text.substring(end)

    const newText = beforeText + formattedText + afterText
    setText(newText)
    onChange(newText)

    // Set focus back to textarea
    setTimeout(() => {
      textarea.focus()
      textarea.setSelectionRange(
        start + 2, // Position after the first "- "
        start + formattedText.length
      )
    }, 0)
  }

  const formatOrderedList = () => {
    if (!textareaRef.current) return

    const textarea = textareaRef.current
    const start = textarea.selectionStart
    const end = textarea.selectionEnd
    const selectedText = text.substring(start, end)

    // Split the selected text into lines
    const lines = selectedText.split("\n")
    const formattedLines = lines.map((line, index) => `${index + 1}. ${line}`)
    const formattedText = formattedLines.join("\n")

    const beforeText = text.substring(0, start)
    const afterText = text.substring(end)

    const newText = beforeText + formattedText + afterText
    setText(newText)
    onChange(newText)

    // Set focus back to textarea
    setTimeout(() => {
      textarea.focus()
      textarea.setSelectionRange(
        start + 3, // Position after the first "1. "
        start + formattedText.length
      )
    }, 0)
  }

  return (
    <div className={cn("space-y-2", className)}>
      <div className="flex flex-wrap gap-1 p-1 bg-muted rounded-md">
        <Button
          variant="ghost"
          size="sm"
          onClick={formatBold}
          className="h-8 w-8 p-0"
          title="Bold"
        >
          <Bold className="h-4 w-4" />
          <span className="sr-only">Bold</span>
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={formatItalic}
          className="h-8 w-8 p-0"
          title="Italic"
        >
          <Italic className="h-4 w-4" />
          <span className="sr-only">Italic</span>
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={formatUnderline}
          className="h-8 w-8 p-0"
          title="Underline"
        >
          <Underline className="h-4 w-4" />
          <span className="sr-only">Underline</span>
        </Button>
        <div className="w-px h-8 bg-border mx-1" />
        <Button
          variant="ghost"
          size="sm"
          onClick={formatH1}
          className="h-8 w-8 p-0"
          title="Heading 1"
        >
          <Heading1 className="h-4 w-4" />
          <span className="sr-only">Heading 1</span>
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={formatH2}
          className="h-8 w-8 p-0"
          title="Heading 2"
        >
          <Heading2 className="h-4 w-4" />
          <span className="sr-only">Heading 2</span>
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={formatH3}
          className="h-8 w-8 p-0"
          title="Heading 3"
        >
          <Heading3 className="h-4 w-4" />
          <span className="sr-only">Heading 3</span>
        </Button>
        <div className="w-px h-8 bg-border mx-1" />
        <Button
          variant="ghost"
          size="sm"
          onClick={formatList}
          className="h-8 w-8 p-0"
          title="Bullet List"
        >
          <List className="h-4 w-4" />
          <span className="sr-only">Bullet List</span>
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={formatOrderedList}
          className="h-8 w-8 p-0"
          title="Numbered List"
        >
          <ListOrdered className="h-4 w-4" />
          <span className="sr-only">Numbered List</span>
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={formatQuote}
          className="h-8 w-8 p-0"
          title="Quote"
        >
          <Quote className="h-4 w-4" />
          <span className="sr-only">Quote</span>
        </Button>
      </div>
      <Textarea
        ref={textareaRef}
        value={text}
        onChange={handleTextChange}
        placeholder={placeholder}
        className={cn("font-mono", "min-h-[200px]")}
        style={{ minHeight }}
      />
      <div className="text-xs text-muted-foreground">
        Supports Markdown formatting. Use **bold**, *italic*, and other Markdown syntax.
      </div>
    </div>
  )
}

export default RichTextEditor
