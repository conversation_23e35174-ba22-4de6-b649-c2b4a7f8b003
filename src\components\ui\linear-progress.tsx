"use client"

import { cn } from "@/lib/utils"

interface LinearProgressProps {
  value: number
  max: number
  height?: number
  className?: string
  progressColor?: string
  backgroundColor?: string
  showLabels?: boolean
  leftLabel?: string
  rightLabel?: string
  labelClassName?: string
}

export function LinearProgress({
  value,
  max,
  height = 8,
  className,
  progressColor = "bg-emerald-500",
  backgroundColor = "bg-muted",
  showLabels = false,
  leftLabel = "",
  rightLabel = "",
  labelClassName = ""
}: LinearProgressProps) {
  const progress = Math.min(Math.max(value, 0), max) / max * 100
  
  return (
    <div className={cn("flex flex-col w-full", className)}>
      <div className="relative">
        <div 
          className={cn("w-full rounded-full overflow-hidden", backgroundColor)}
          style={{ height: `${height}px` }}
        >
          <div
            className={cn("h-full transition-all duration-500", progressColor)}
            style={{ width: `${progress}%` }}
          />
        </div>
        
        {showLabels && (
          <div className={cn("flex justify-between mt-1 text-xs text-muted-foreground", labelClassName)}>
            <span>{leftLabel}</span>
            <span>{rightLabel}</span>
          </div>
        )}
      </div>
    </div>
  )
}
