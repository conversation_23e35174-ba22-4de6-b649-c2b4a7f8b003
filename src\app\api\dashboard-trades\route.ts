import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { createServerClient } from '@supabase/ssr';
import type { Database } from '@/types/supabase';

// Define cache configuration
export const revalidate = 0; // Disable caching to ensure fresh data on each request

// GET handler to fetch dashboard trades
export async function GET(request: Request) {
  try {
    // Get URL parameters
    const url = new URL(request.url);
    const accountId = url.searchParams.get('accountId');
    const startDate = url.searchParams.get('startDate');
    const endDate = url.searchParams.get('endDate');
    const symbol = url.searchParams.get('symbol');
    const tradeType = url.searchParams.get('tradeType');
    const minProfit = url.searchParams.get('minProfit');
    const maxProfit = url.searchParams.get('maxProfit');
    const tags = url.searchParams.get('tags');

    // Get cookies for server-side Supabase client
    const cookieStore = await cookies();
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
          set(_name: string, _value: string, _options: any) {
            // API routes can't set cookies directly
          },
          remove(_name: string, _options: any) {
            // API routes can't remove cookies directly
          }
        },
      }
    );

    // Get authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const userId = user.id;

    // If accountId is explicitly null (user has unselected all accounts), return empty array
    if (accountId === 'null') {
      console.log('No account selected, returning empty trades array');
      return NextResponse.json([]);
    }

    // Start building the query
    let query = supabase
      .from("trades")
      .select("*")
      .eq("user_id", userId);

    // Add account filter
    if (accountId && accountId !== 'null') {
      query = query.eq("account_id", accountId);
    }

    // Add date filters if provided
    if (startDate) {
      query = query.gte("time_close", startDate);
    }

    if (endDate) {
      query = query.lte("time_close", endDate);
    }

    // Add symbol filter if provided
    if (symbol) {
      query = query.eq("symbol", symbol);
    }

    // Add trade type filter if provided
    if (tradeType && tradeType !== 'all') {
      query = query.eq("type", tradeType);
    }

    // Add profit range filters if provided
    if (minProfit) {
      query = query.gte("profit", parseFloat(minProfit));
    }

    if (maxProfit) {
      query = query.lte("profit", parseFloat(maxProfit));
    }

    // Add tag filter if provided
    if (tags) {
      const tagArray = JSON.parse(tags);
      if (tagArray && tagArray.length > 0) {
        query = query.overlaps("tags", tagArray);
      }
    }

    // Add ordering
    query = query.order("time_close", { ascending: false });

    // Execute the query
    const { data: trades, error } = await query;

    if (error) {
      console.error('Error fetching trades:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    console.log(`Retrieved ${trades?.length || 0} trades with applied filters`);
    return NextResponse.json(trades || []);
  } catch (error) {
    console.error('Error in dashboard-trades API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
