"use client"

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { type ProcessedData } from "@/lib/excel-processor"
import { cn } from "@/lib/utils"
import { BarChart3, ShieldAlert, Zap } from "lucide-react"

interface AdvancedMetricsCardProps {
  trades: ProcessedData["trades"]
  className?: string
}

export function AdvancedMetricsCard({ trades, className }: AdvancedMetricsCardProps) {
  // Calculate advanced metrics
  const totalTrades = trades.length
  const winningTrades = trades.filter(trade => trade.profit > 0).length
  const losingTrades = trades.filter(trade => trade.profit < 0).length
  const breakEvenTrades = trades.filter(trade => trade.profit === 0).length

  const winRate = totalTrades > 0 ? (winningTrades / totalTrades) * 100 : 0
  const lossRate = totalTrades > 0 ? (losingTrades / totalTrades) * 100 : 0

  const totalProfit = trades.reduce((sum, trade) => sum + trade.profit, 0)
  const grossProfit = trades.filter(trade => trade.profit > 0).reduce((sum, trade) => sum + trade.profit, 0)
  const grossLoss = Math.abs(trades.filter(trade => trade.profit < 0).reduce((sum, trade) => sum + trade.profit, 0))

  const profitFactor = grossLoss > 0 ? grossProfit / grossLoss : grossProfit > 0 ? 999 : 0
  const expectancy = totalTrades > 0 ? totalProfit / totalTrades : 0

  const winningTradesArr = trades.filter(trade => trade.profit > 0)
  const losingTradesArr = trades.filter(trade => trade.profit < 0)

  const avgWin = winningTradesArr.length > 0
    ? winningTradesArr.reduce((sum, trade) => sum + trade.profit, 0) / winningTradesArr.length
    : 0

  const avgLoss = losingTradesArr.length > 0
    ? Math.abs(losingTradesArr.reduce((sum, trade) => sum + trade.profit, 0)) / losingTradesArr.length
    : 0

  const riskRewardRatio = avgLoss > 0 ? avgWin / avgLoss : 0

  // Calculate max drawdown
  let balance = 0
  let peak = 0
  let maxDrawdown = 0
  let maxDrawdownAmount = 0
  let currentDrawdownDuration = 0
  let maxDrawdownDuration = 0
  let inDrawdown = false

  const sortedTrades = [...trades].sort((a, b) =>
    new Date(a.time_close).getTime() - new Date(b.time_close).getTime()
  )

  sortedTrades.forEach(trade => {
    balance += trade.profit

    if (balance > peak) {
      peak = balance
      inDrawdown = false
      currentDrawdownDuration = 0
    } else if (balance < peak) {
      inDrawdown = true
      currentDrawdownDuration++

      const drawdown = peak - balance
      const drawdownPercent = (drawdown / peak) * 100

      if (drawdown > maxDrawdownAmount) {
        maxDrawdownAmount = drawdown
        maxDrawdown = drawdownPercent
      }

      if (currentDrawdownDuration > maxDrawdownDuration) {
        maxDrawdownDuration = currentDrawdownDuration
      }
    }
  })

  // Calculate consecutive wins/losses
  let consecutiveWins = 0
  let consecutiveLosses = 0
  let maxConsecutiveWins = 0
  let maxConsecutiveLosses = 0

  sortedTrades.forEach(trade => {
    if (trade.profit > 0) {
      consecutiveWins++
      consecutiveLosses = 0
      maxConsecutiveWins = Math.max(maxConsecutiveWins, consecutiveWins)
    } else if (trade.profit < 0) {
      consecutiveLosses++
      consecutiveWins = 0
      maxConsecutiveLosses = Math.max(maxConsecutiveLosses, consecutiveLosses)
    } else {
      consecutiveWins = 0
      consecutiveLosses = 0
    }
  })

  return (
    <Card className={cn("dashboard-card bg-card hover:bg-card transition-all duration-300 h-[352px] flex flex-col", className)}>
      <CardHeader className="pb-2 flex-shrink-0">
        <CardTitle className="text-base font-medium">Advanced Metrics</CardTitle>
      </CardHeader>
      <CardContent className="p-0 flex-grow flex flex-col overflow-hidden">
        <Tabs defaultValue="performance" className="flex flex-col h-full">
          <TabsList className="grid w-full grid-cols-3 rounded-none border-b bg-muted/30 dark:bg-transparent flex-shrink-0">
            <TabsTrigger
              value="performance"
              className="rounded-none data-[state=active]:bg-background data-[state=active]:border-b data-[state=active]:border-primary data-[state=active]:font-medium data-[state=active]:shadow-sm"
            >
              <BarChart3 className="mr-2 h-4 w-4" />
              Performance
            </TabsTrigger>
            <TabsTrigger
              value="risk"
              className="rounded-none data-[state=active]:bg-background data-[state=active]:border-b data-[state=active]:border-primary data-[state=active]:font-medium data-[state=active]:shadow-sm"
            >
              <ShieldAlert className="mr-2 h-4 w-4" />
              Risk
            </TabsTrigger>
            <TabsTrigger
              value="streaks"
              className="rounded-none data-[state=active]:bg-background data-[state=active]:border-b data-[state=active]:border-primary data-[state=active]:font-medium data-[state=active]:shadow-sm"
            >
              <Zap className="mr-2 h-4 w-4" />
              Streaks
            </TabsTrigger>
          </TabsList>

          {/* Performance Tab */}
          <TabsContent
            value="performance"
            className="flex-grow overflow-hidden flex flex-col"
          >
            <div className="px-4 py-4 flex-grow overflow-y-auto">
              <div className="grid grid-cols-2 gap-4 pb-4">
                <div>
                  <div className="text-xs text-muted-foreground">Win Rate</div>
                  <div className={cn(
                    "text-sm font-medium",
                    winRate >= 50 ? "text-green-500" : "text-red-500"
                  )}>{winRate.toFixed(2)}%</div>
                </div>
                <div>
                  <div className="text-xs text-muted-foreground">Profit Factor</div>
                  <div className={cn(
                    "text-sm font-medium",
                    profitFactor >= 1 ? "text-green-500" : "text-red-500"
                  )}>{profitFactor.toFixed(2)}</div>
                </div>
                <div>
                  <div className="text-xs text-muted-foreground">Expectancy</div>
                  <div className={cn(
                    "text-sm font-medium",
                    expectancy >= 0 ? "text-green-500" : "text-red-500"
                  )}>${expectancy.toFixed(2)}</div>
                </div>
                <div>
                  <div className="text-xs text-muted-foreground">Risk/Reward</div>
                  <div className={cn(
                    "text-sm font-medium",
                    riskRewardRatio >= 1 ? "text-green-500" : riskRewardRatio > 0 ? "text-yellow-500" : "text-red-500"
                  )}>{riskRewardRatio.toFixed(2)}</div>
                </div>
                <div>
                  <div className="text-xs text-muted-foreground">Avg Win</div>
                  <div className="text-sm font-medium text-green-500">${avgWin.toFixed(2)}</div>
                </div>
                <div>
                  <div className="text-xs text-muted-foreground">Avg Loss</div>
                  <div className="text-sm font-medium text-red-500">${avgLoss.toFixed(2)}</div>
                </div>
              </div>
            </div>
          </TabsContent>

          {/* Risk Tab */}
          <TabsContent
            value="risk"
            className="flex-grow overflow-hidden flex flex-col"
          >
            <div className="px-4 py-4 flex-grow overflow-y-auto">
              <div className="grid grid-cols-2 gap-4 pb-4">
                <div>
                  <div className="text-xs text-muted-foreground">Max Drawdown</div>
                  <div className={cn(
                    "text-sm font-medium",
                    maxDrawdown <= 10 ? "text-green-500" : maxDrawdown <= 20 ? "text-yellow-500" : "text-red-500"
                  )}>{maxDrawdown.toFixed(2)}%</div>
                </div>
                <div>
                  <div className="text-xs text-muted-foreground">Drawdown Amount</div>
                  <div className="text-sm font-medium text-red-500">${maxDrawdownAmount.toFixed(2)}</div>
                </div>
                <div>
                  <div className="text-xs text-muted-foreground">Drawdown Duration</div>
                  <div className={cn(
                    "text-sm font-medium",
                    maxDrawdownDuration <= 5 ? "text-green-500" : maxDrawdownDuration <= 10 ? "text-yellow-500" : "text-red-500"
                  )}>{maxDrawdownDuration} trades</div>
                </div>
                <div>
                  <div className="text-xs text-muted-foreground">Gross Profit</div>
                  <div className="text-sm font-medium text-green-500">${grossProfit.toFixed(2)}</div>
                </div>
                <div>
                  <div className="text-xs text-muted-foreground">Gross Loss</div>
                  <div className="text-sm font-medium text-red-500">${grossLoss.toFixed(2)}</div>
                </div>
                <div>
                  <div className="text-xs text-muted-foreground">Net Profit</div>
                  <div className={cn(
                    "text-sm font-medium",
                    totalProfit >= 0 ? "text-emerald-500" : "text-rose-500"
                  )}>
                    ${totalProfit.toFixed(2)}
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>

          {/* Streaks Tab */}
          <TabsContent
            value="streaks"
            className="flex-grow overflow-hidden flex flex-col"
          >
            <div className="px-4 py-4 flex-grow overflow-y-auto">
              <div className="grid grid-cols-2 gap-4 pb-4">
                <div>
                  <div className="text-xs text-muted-foreground">Max Consecutive Wins</div>
                  <div className="text-sm font-medium text-green-500">{maxConsecutiveWins}</div>
                </div>
                <div>
                  <div className="text-xs text-muted-foreground">Max Consecutive Losses</div>
                  <div className="text-sm font-medium text-red-500">{maxConsecutiveLosses}</div>
                </div>
                <div>
                  <div className="text-xs text-muted-foreground">Total Trades</div>
                  <div className="text-sm font-medium text-purple-500">{totalTrades}</div>
                </div>
                <div>
                  <div className="text-xs text-muted-foreground">Winning Trades</div>
                  <div className="text-sm font-medium text-green-500">{winningTrades} ({winRate.toFixed(1)}%)</div>
                </div>
                <div>
                  <div className="text-xs text-muted-foreground">Losing Trades</div>
                  <div className="text-sm font-medium text-red-500">{losingTrades} ({lossRate.toFixed(1)}%)</div>
                </div>
                <div>
                  <div className="text-xs text-muted-foreground">Break Even</div>
                  <div className="text-sm font-medium text-gray-500">{breakEvenTrades}</div>
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
