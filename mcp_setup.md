{
  "mcpServers": {
    "supabase": {
      "command": "npx",
      "args": [
        "-y",
        "@supabase/mcp-server-supabase@latest",
        "--access-token",
        "<personal-access-token>"
      ]
    }
  }
}

You'll need to create a personal access token (PAT) for the <personal-access-token> field. This token authenticates the MCP server with your Supabase account.

// I am on windows:

Some clients expect a slightly modified JSON format, and Windows users will have to prefix this command with cmd /c. For detailed step-by-step instructions for each client and OS, see our MCP documentation.