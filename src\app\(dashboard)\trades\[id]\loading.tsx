import { Loader2 } from "lucide-react"

export default function Loading() {
  return (
    <div className="flex flex-col justify-center items-center h-64 rounded-lg border border-border bg-card/50 shadow-sm">
      <Loader2 className="h-10 w-10 animate-spin text-purple-500 dark:text-purple-400 mb-4" />
      <p className="text-muted-foreground">Loading trade details...</p>
    </div>
  )
}

// Also export as named export for use in other components
export function TradeDetailsLoading() {
  return <Loading />
}
