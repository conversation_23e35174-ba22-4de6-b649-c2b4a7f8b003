import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { createServerClient } from '@supabase/ssr';
import type { Database } from '@/types/supabase';
import { handleNotebookSync } from '@/lib/sync-utils';

// GET handler for fetching notebook entries
export async function GET(request: NextRequest) {
  try {
    // Get URL parameters
    const url = new URL(request.url);
    const searchTerm = url.searchParams.get('searchTerm') || undefined;
    const tags = url.searchParams.getAll('tags') || undefined;
    const category = url.searchParams.get('category') || undefined;
    const folderId = url.searchParams.get('folderId') || undefined;
    const isTemplate = url.searchParams.get('isTemplate') === 'true' ? true :
                      url.searchParams.get('isTemplate') === 'false' ? false : undefined;
    const accountIdParam = url.searchParams.get('accountId');
    const accountId = accountIdParam === '' ? null : accountIdParam;
    const limit = parseInt(url.searchParams.get('limit') || '20');
    const offset = parseInt(url.searchParams.get('offset') || '0');
    const sortField = url.searchParams.get('sortField') || 'updated_at';
    const sortOrder = url.searchParams.get('sortOrder') || 'desc';

    // Get cookies for server-side Supabase client
    const cookieStore = await cookies();
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
          set(_name: string, _value: string, _options: any) {
            // API routes can't set cookies directly
          },
          remove(_name: string, _options: any) {
            // API routes can't remove cookies directly
          }
        },
      }
    );

    // Get authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Start building the query
    let query = supabase
      .from('notebook_entries')
      .select('*', { count: 'exact' })
      .eq('user_id', user.id);

    // Apply all filters in the correct order
    console.log(`Applying filters:`, { searchTerm, tags: tags?.length, category, folderId, isTemplate, accountId });

    // Apply search filter
    if (searchTerm) {
      console.log(`Applying search filter for term: "${searchTerm}"`);
      // Search in text fields only (title and html_content)
      // Note: JSONB content search requires different approach due to Supabase limitations
      query = query.or(`title.ilike.%${searchTerm}%,html_content.ilike.%${searchTerm}%`);
    }

    // Apply tag filter
    if (tags && tags.length > 0) {
      console.log(`Applying tag filter for tags:`, tags);
      query = query.overlaps('tags', tags);
    }

    // Apply category filter
    if (category) {
      console.log(`Applying category filter: ${category}`);
      query = query.eq('category', category);
    }

    // Apply folder filter (works seamlessly with other filters)
    if (folderId) {
      console.log(`Applying folder filter: ${folderId}`);
      query = query.eq('folder_id', folderId);
    }

    if (isTemplate !== undefined) {
      query = query.eq('is_template', isTemplate);
    }

    if (accountIdParam !== null) { // If accountId parameter was provided (even if empty)
      if (accountId === null) {
        query = query.is('account_id', null);
      } else {
        query = query.eq('account_id', accountId);
      }
    }

    // Apply sorting
    if (sortField && sortOrder) {
      query = query.order(sortField, { ascending: sortOrder === 'asc' });
    } else {
      // Default sorting: pinned first, then by updated_at
      query = query.order('is_pinned', { ascending: false }).order('updated_at', { ascending: false });
    }

    // Apply pagination
    if (limit) {
      query = query.limit(limit);
    }

    if (offset) {
      query = query.range(offset, offset + limit - 1);
    }

    // Execute the query
    console.log(`Executing notebook entries query with filters:`, {
      searchTerm,
      tags: tags?.length,
      category,
      folderId,
      isTemplate,
      accountId,
      limit,
      offset
    });

    const { data, error, count } = await query;

    // Log the actual query for debugging
    if (searchTerm) {
      console.log(`Search query executed for term: "${searchTerm}"`);
    }

    if (error) {
      console.error('Error fetching notebook entries:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    console.log(`Query returned ${count} total entries, returning ${data?.length || 0} entries`);

    // Log successful filter application
    if (searchTerm) {
      console.log(`✅ Search filter applied successfully for term: "${searchTerm}"`);
    }
    if (tags && tags.length > 0) {
      console.log(`✅ Tag filter applied successfully for tags:`, tags);
    }
    if (folderId) {
      console.log(`✅ Folder filter applied successfully for folder: ${folderId}`);
    }
    if (category) {
      console.log(`✅ Category filter applied successfully for category: ${category}`);
    }

    return NextResponse.json({ entries: data || [], count: count || 0 });
  } catch (error) {
    console.error('Error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// POST handler for creating a new notebook entry
export async function POST(request: NextRequest) {
  try {
    // Parse the request body
    const body = await request.json();
    const { title, content, html_content, category, tags, is_pinned, is_template, account_id, folder_id, linked_trade_ids, linked_strategy_ids, screenshots } = body;

    // Get cookies for server-side Supabase client
    const cookieStore = await cookies();
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
          set(_name: string, _value: string, _options: any) {
            // API routes can't set cookies directly
          },
          remove(_name: string, _options: any) {
            // API routes can't remove cookies directly
          }
        },
      }
    );

    // Get authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Create the entry
    const { data, error } = await supabase
      .from('notebook_entries')
      .insert({
        user_id: user.id,
        title,
        content,
        html_content,
        category,
        tags,
        is_pinned,
        is_template,
        account_id,
        folder_id,
        linked_trade_ids,
        linked_strategy_ids,
        screenshots: screenshots || [],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating notebook entry:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    // Perform synchronization after successful creation
    if (data) {
      try {
        await handleNotebookSync(supabase, user.id, {
          html_content,
          tags,
          screenshots: screenshots || [],
          linked_trade_ids,
          linked_strategy_ids,
          category,
          title
        });
      } catch (syncError) {
        console.error('Error during notebook sync:', syncError);
        // Don't fail the request if sync fails, just log it
      }
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error('Error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
