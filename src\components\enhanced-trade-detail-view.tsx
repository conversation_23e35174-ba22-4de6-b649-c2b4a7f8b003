"use client"

import React, { useState, useEffect, useRef } from 'react'
import { format, formatDistanceStrict } from 'date-fns'
import { toast } from 'sonner'
import {
  ArrowLeft,
  BarChart,
  BookOpen,
  Calendar,
  Clock,
  DollarSign,
  ImageIcon,
  Loader2,
  Save,
  XCircle,
  TrendingUp,
  TrendingDown,
  FileText,
  HelpCircle,
  Tag,
  Plus
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { SimpleCheckbox } from '@/components/ui/simple-checkbox'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { ScrollArea } from '@/components/ui/scroll-area'
import { ImageDisplay } from '@/components/ui/image-display'
import { TagInput } from '@/components/tag-input'
import { getSupabaseBrowser } from '@/lib/supabase'
import { uploadImage } from '@/lib/image-uploader'
import { cn } from '@/lib/utils'
import { getAllTags } from '@/lib/journal-service'

import type { Strategy } from '@/types/playbook'
import type { Trade } from '@/types/trade'

// Define local types
interface Setup {
  id: string
  name: string
  strategy_id: string
  visual_cues?: string | null
  confirmation_criteria?: string | null
  image_urls?: string[]
}

interface StrategyRule {
  id: string
  strategy_id: string
  name: string
  description?: string | null
  rule_type: string
}

interface EnhancedTradeDetailViewProps {
  userId: string
  trade: Trade
  strategies: Strategy[]
  onUpdate: () => void
}

export function EnhancedTradeDetailView({ userId, trade, strategies, onUpdate }: EnhancedTradeDetailViewProps) {
  // State for active tab
  const [activeTab, setActiveTab] = useState("details")

  // State for strategy and setup
  const [selectedStrategy, setSelectedStrategy] = useState<Strategy | null>(
    strategies.find(s => s.id === trade.strategy_id) || null
  )
  const [setups, setSetups] = useState<Setup[]>([])
  const [selectedSetupId, setSelectedSetupId] = useState<string | null>(trade.setup_id || null)

  // State for rules and criteria
  const [rules, setRules] = useState<StrategyRule[]>([])
  const [followedRules, setFollowedRules] = useState<string[]>(
    Array.isArray(trade.followed_rules) ? trade.followed_rules : []
  )
  const [followedSetupCriteria, setFollowedSetupCriteria] = useState<string[]>(
    Array.isArray(trade.followed_setup_criteria) ? trade.followed_setup_criteria : []
  )

  // State for notes, screenshots, and tags
  const [tradeNotes, setTradeNotes] = useState(trade.notes || "")
  const [screenshots, setScreenshots] = useState<string[]>(
    Array.isArray(trade.screenshots) ? trade.screenshots : []
  )
  const [tradeTags, setTradeTags] = useState<string[]>(
    Array.isArray(trade.tags) ? trade.tags : []
  )
  const [availableTags, setAvailableTags] = useState<string[]>([])

  // State for loading states
  const [isLoading, setIsLoading] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [isUploading, setIsUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)

  // Debounced save function to reduce database calls
  const debouncedSave = useRef<NodeJS.Timeout | null>(null)

  // Cleanup effect to cancel any pending debounced saves when the component unmounts
  useEffect(() => {
    return () => {
      if (debouncedSave.current) {
        clearTimeout(debouncedSave.current);
      }
    };
  }, [])

  // Ref for file input
  const fileInputRef = useRef<HTMLInputElement>(null)

  // Fetch available tags when component mounts
  useEffect(() => {
    const fetchTags = async () => {
      try {
        const tags = await getAllTags(userId)
        setAvailableTags(tags)
      } catch (error) {
        console.error("Error fetching tags:", error)
      }
    }

    fetchTags()
  }, [userId])

  // Fetch the latest trade data when component mounts
  useEffect(() => {
    const fetchTradeData = async () => {
      try {
        const supabase = getSupabaseBrowser()
        const localStorageKey = `trade_details_${trade.id}`

        console.log("Fetching trade data for ID:", trade.id)

        // Fetch the latest trade data from the database first
        const { data: updatedTrade, error: fetchError } = await supabase
          .from("trades")
          .select("*")
          .eq("id", trade.id)
          .single()

        if (fetchError) {
          console.error("Error fetching trade data from database:", fetchError)

          // Try to get data from localStorage as fallback
          const localData = localStorage.getItem(localStorageKey)

          if (localData) {
            try {
              // Define a type for the localStorage data
              interface LocalTradeDetails {
                notes?: string;
                screenshots?: string[];
                followed_rules?: string[];
                followed_setup_criteria?: string[];
                strategy_id?: string;
                setup_id?: string;
                last_updated?: string;
                is_temporary?: boolean;
              }

              const localTradeDetails = JSON.parse(localData) as LocalTradeDetails
              console.log("Found local trade details as fallback:", localTradeDetails)

              // Only use local data if it's not marked as temporary
              if (!localTradeDetails.is_temporary) {
                setSelectedStrategy(strategies.find(s => s.id === localTradeDetails.strategy_id) || null)
                setSelectedSetupId(localTradeDetails.setup_id || null)
                setTradeNotes(localTradeDetails.notes || "")

                // Handle arrays with proper validation
                const localScreenshots = Array.isArray(localTradeDetails.screenshots) ? localTradeDetails.screenshots : []
                setScreenshots(localScreenshots)

                const localRules = Array.isArray(localTradeDetails.followed_rules)
                  ? localTradeDetails.followed_rules.filter((id: any) => id && typeof id === 'string')
                  : []
                console.log("Setting followed rules from localStorage fallback:", localRules)
                setFollowedRules(localRules)

                const localCriteria = Array.isArray(localTradeDetails.followed_setup_criteria)
                  ? localTradeDetails.followed_setup_criteria.filter((id: any) => id && typeof id === 'string')
                  : []
                console.log("Setting followed setup criteria from localStorage fallback:", localCriteria)
                setFollowedSetupCriteria(localCriteria)
              } else {
                console.log("Local data is marked as temporary, not using it")
              }
            } catch (e) {
              console.error("Error parsing local trade details:", e)
            }
          }
          return
        }

        if (updatedTrade) {
          // Always use database data as the source of truth
          console.log("Using database data:", updatedTrade)

          // Update the trade object with the latest data from the database
          Object.assign(trade, updatedTrade)

          setSelectedStrategy(strategies.find(s => s.id === updatedTrade.strategy_id) || null)
          setSelectedSetupId(updatedTrade.setup_id || null)
          setTradeNotes(updatedTrade.notes || "")

          // Handle arrays with proper validation
          const dbScreenshots = Array.isArray(updatedTrade.screenshots) ? updatedTrade.screenshots : []
          setScreenshots(dbScreenshots)

          const dbRules = Array.isArray(updatedTrade.followed_rules)
            ? updatedTrade.followed_rules.filter((id: any) => id && typeof id === 'string')
            : []
          console.log("Setting followed rules from database:", dbRules)
          setFollowedRules(dbRules)

          const dbCriteria = Array.isArray(updatedTrade.followed_setup_criteria)
            ? updatedTrade.followed_setup_criteria.filter((id: any) => id && typeof id === 'string')
            : []
          console.log("Setting followed setup criteria from database:", dbCriteria)
          setFollowedSetupCriteria(dbCriteria)

          // Clear any temporary localStorage data
          const localData = localStorage.getItem(localStorageKey)
          if (localData) {
            try {
              // Define a type for the localStorage data
              interface LocalTradeDetails {
                is_temporary?: boolean;
              }

              const localTradeDetails = JSON.parse(localData) as LocalTradeDetails
              if (localTradeDetails.is_temporary) {
                console.log("Clearing temporary localStorage data")
                localStorage.removeItem(localStorageKey)
              }
            } catch (e) {
              console.error("Error parsing local trade details:", e)
            }
          }
        }
      } catch (error) {
        console.error("Error fetching trade data:", error)
      }
    }

    fetchTradeData()
  }, [trade.id, strategies, trade])

  // Fetch setups and rules when strategy changes
  useEffect(() => {
    const fetchSetupAndRules = async () => {
      if (!selectedStrategy) {
        setSetups([])
        setRules([])
        return
      }

      setIsLoading(true)

      try {
        const supabase = getSupabaseBrowser()

        // Fetch setups
        const { data: setupsData, error: setupsError } = await supabase
          .from('setups')
          .select('*')
          .eq('strategy_id', selectedStrategy.id)

        if (setupsError) {
          console.error('Error fetching setups:', setupsError)
          return
        }

        setSetups(setupsData || [])

        // Fetch rules
        const { data: rulesData, error: rulesError } = await supabase
          .from('strategy_rules')
          .select('*')
          .eq('strategy_id', selectedStrategy.id)

        if (rulesError) {
          console.error('Error fetching rules:', rulesError)
          return
        }

        setRules(rulesData || [])
      } catch (error) {
        console.error('Error fetching setup and rules:', error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchSetupAndRules()
  }, [selectedStrategy])

  // Handle strategy change
  const handleStrategyChange = (strategyId: string) => {
    console.log("Strategy changed to:", strategyId)

    if (strategyId === "none") {
      console.log("Clearing strategy selection")
      setSelectedStrategy(null)
      setSelectedSetupId(null)
      setFollowedRules([])
      setFollowedSetupCriteria([])
      updateLocalStorage()
    } else {
      const strategy = strategies.find(s => s.id === strategyId) || null
      console.log("Selected strategy:", strategy?.name || "Unknown")
      setSelectedStrategy(strategy)
      updateLocalStorage()
    }
  }

  // Handle setup change
  const handleSetupChange = (setupId: string) => {
    if (setupId === "none") {
      setSelectedSetupId(null)
      setFollowedSetupCriteria([])
      updateLocalStorage()
    } else {
      setSelectedSetupId(setupId)
      setFollowedSetupCriteria([])
      updateLocalStorage()
    }
  }

  // Handle rule checkbox change
  const handleRuleChange = (ruleId: string, checked: boolean) => {
    console.log(`Rule checkbox change: ${ruleId} -> ${checked}`)
    console.log("Current followed rules:", followedRules)

    if (checked) {
      setFollowedRules(prev => {
        const updated = [...prev, ruleId]
        console.log("Updated followed rules (added):", updated)
        updateLocalStorage(screenshots, updated, followedSetupCriteria)
        return updated
      })
    } else {
      setFollowedRules(prev => {
        const updated = prev.filter(id => id !== ruleId)
        console.log("Updated followed rules (removed):", updated)
        updateLocalStorage(screenshots, updated, followedSetupCriteria)
        return updated
      })
    }
  }

  // Handle setup criteria checkbox change
  const handleSetupCriteriaChange = (criteriaId: string, checked: boolean) => {
    console.log(`Setup criteria checkbox change: ${criteriaId} -> ${checked}`)
    console.log("Current followed setup criteria:", followedSetupCriteria)

    if (checked) {
      setFollowedSetupCriteria(prev => {
        const updated = [...prev, criteriaId]
        console.log("Updated followed setup criteria (added):", updated)
        updateLocalStorage(screenshots, followedRules, updated)
        return updated
      })
    } else {
      setFollowedSetupCriteria(prev => {
        const updated = prev.filter(id => id !== criteriaId)
        console.log("Updated followed setup criteria (removed):", updated)
        updateLocalStorage(screenshots, followedRules, updated)
        return updated
      })
    }
  }

  // Update localStorage with current trade details
  // This is only used as a temporary backup before saving to the database
  const updateLocalStorage = (
    updatedScreenshots = screenshots,
    updatedFollowedRules = followedRules,
    updatedFollowedSetupCriteria = followedSetupCriteria,
    updatedTags = tradeTags
  ) => {
    const localStorageKey = `trade_details_${trade.id}`
    const dataToStore = {
      notes: tradeNotes,
      screenshots: updatedScreenshots,
      followed_rules: updatedFollowedRules,
      followed_setup_criteria: updatedFollowedSetupCriteria,
      tags: updatedTags,
      strategy_id: selectedStrategy?.id,
      setup_id: selectedSetupId,
      last_updated: new Date().toISOString(),
      is_temporary: true // Flag to indicate this is temporary data
    }

    console.log("Saving temporary data to localStorage:", dataToStore)
    localStorage.setItem(localStorageKey, JSON.stringify(dataToStore))
  }

  // Helper function to save changes to the database
  const saveChangesToDatabase = async (
    updatedScreenshots = screenshots,
    updatedFollowedRules = followedRules,
    updatedFollowedSetupCriteria = followedSetupCriteria,
    updatedTags = tradeTags
  ) => {
    try {
      const supabase = getSupabaseBrowser()

      // Ensure arrays are properly formatted
      const cleanFollowedRules = Array.isArray(updatedFollowedRules)
        ? updatedFollowedRules.filter(id => id && typeof id === 'string')
        : []

      const cleanFollowedSetupCriteria = Array.isArray(updatedFollowedSetupCriteria)
        ? updatedFollowedSetupCriteria.filter(id => id && typeof id === 'string')
        : []

      // Create the update object
      const updateData = {
        strategy_id: selectedStrategy?.id || null,
        setup_id: selectedSetupId || null,
        notes: tradeNotes || null,
        screenshots: updatedScreenshots || [],
        followed_rules: cleanFollowedRules,
        followed_setup_criteria: cleanFollowedSetupCriteria,
        tags: updatedTags || [],
        // Add a flag to indicate if this trade has journal content
        has_journal_content: !!(
          (tradeNotes && tradeNotes.trim().length > 0) ||
          updatedScreenshots.length > 0 ||
          updatedTags.length > 0
        )
      }

      // Try to use the RPC function first
      try {
        const { error: rpcError } = await supabase.rpc('update_trade_details', {
          p_trade_id: trade.id,
          p_strategy_id: updateData.strategy_id,
          p_setup_id: updateData.setup_id,
          p_notes: updateData.notes,
          p_screenshots: updateData.screenshots,
          p_followed_rules: updateData.followed_rules,
          p_followed_setup_criteria: updateData.followed_setup_criteria,
          p_tags: updateData.tags,
          p_has_journal_content: updateData.has_journal_content
        });

        if (!rpcError) {
          console.log("Database updated successfully via RPC function");

          // Trigger auto-migration queue processing (for new notebook entries)
          try {
            const queueResponse = await fetch('/api/migrate/process-queue', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' }
            });

            if (queueResponse.ok) {
              console.log('Auto-migration queue processed successfully after debounced trade update');
            } else {
              console.warn('Auto-migration queue processing failed after debounced trade update');
            }
          } catch (error) {
            console.warn('Auto-migration queue processing error:', error);
          }

          return true;
        }

        console.warn("RPC function failed, falling back to standard update:", rpcError);
      } catch (rpcError) {
        console.warn("RPC function threw an exception, falling back to standard update:", rpcError);
      }

      // Fallback to standard update
      const { error } = await supabase
        .from("trades")
        .update({
          strategy_id: updateData.strategy_id,
          setup_id: updateData.setup_id,
          notes: updateData.notes,
          screenshots: updateData.screenshots,
          followed_rules: updateData.followed_rules,
          followed_setup_criteria: updateData.followed_setup_criteria,
          tags: updateData.tags,
          has_journal_content: updateData.has_journal_content,
          updated_at: new Date().toISOString()
        })
        .eq("id", trade.id);

      if (error) {
        console.error("Error updating database:", error);
        return false;
      }

      // Trigger auto-migration queue processing (for new notebook entries)
      try {
        const queueResponse = await fetch('/api/migrate/process-queue', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' }
        });

        if (queueResponse.ok) {
          console.log('Auto-migration queue processed successfully after debounced trade update (fallback)');
        } else {
          console.warn('Auto-migration queue processing failed after debounced trade update (fallback)');
        }
      } catch (error) {
        console.warn('Auto-migration queue processing error:', error);
      }

      return true;
    } catch (error) {
      console.error("Exception updating database:", error);
      return false;
    }
  };

  // Debounced save function
  const debouncedSaveToDatabase = (
    updatedScreenshots = screenshots,
    updatedFollowedRules = followedRules,
    updatedFollowedSetupCriteria = followedSetupCriteria,
    updatedTags = tradeTags
  ) => {
    // Clear any existing timeout
    if (debouncedSave.current) {
      clearTimeout(debouncedSave.current);
    }

    // Set a new timeout
    debouncedSave.current = setTimeout(async () => {
      await saveChangesToDatabase(
        updatedScreenshots,
        updatedFollowedRules,
        updatedFollowedSetupCriteria,
        updatedTags
      );
    }, 500); // 500ms debounce time
  };

  // Function to validate image URLs
  const validateImageUrl = (url: string): boolean => {
    if (!url) return false;
    try {
      new URL(url);
      return true;
    } catch (e) {
      console.error('Invalid image URL:', url);
      return false;
    }
  };

  // Handle image upload
  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    // Validate file type
    if (!file.type.startsWith('image/')) {
      toast.error("Please upload an image file")
      return
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      toast.error("File size should not exceed 5MB")
      return
    }

    // Set uploading state
    setIsUploading(true)
    setUploadProgress(0)

    try {
      // Create form data
      const formData = new FormData();
      formData.append('file', file);

      // Simulate progress for better UX
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          const newProgress = prev + Math.random() * 15;
          return newProgress > 90 ? 90 : newProgress;
        });
      }, 150);

      // Upload using the API route
      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
        headers: {
          'Accept': 'application/json',
        },
      });

      // Clear progress interval
      clearInterval(progressInterval)

      if (!response.ok) {
        throw new Error('Failed to upload image');
      }

      // Set progress to 100% when upload is complete
      setUploadProgress(100)

      const { url } = await response.json();

      if (url && validateImageUrl(url)) {
        // Add the new image to the existing screenshots array
        const updatedScreenshots = [...screenshots, url];
        setScreenshots(updatedScreenshots);

        // Update localStorage for backup
        updateLocalStorage(updatedScreenshots);

        // Show success message
        toast.success("Screenshot uploaded successfully");

        // Save to database in the background
        debouncedSaveToDatabase(updatedScreenshots);
      } else {
        console.error('Invalid image URL returned from upload:', url);
        toast.error('Failed to upload image: Invalid URL returned');
      }
    } catch (error) {
      console.error("Error uploading screenshot:", error);
      toast.error("Failed to upload screenshot");
    } finally {
      // Reset uploading state after a short delay to ensure the UI transition is smooth
      setTimeout(() => {
        setIsUploading(false);
        setUploadProgress(0);
      }, 300);

      // Clear the file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  }

  // Handle removing a screenshot
  const removeScreenshot = (index: number) => {
    try {
      // Update the local state immediately for a responsive UI
      setScreenshots(prev => {
        const updated = [...prev]
        updated.splice(index, 1)

        // Update localStorage for backup
        updateLocalStorage(updated)

        // Save to database in the background
        debouncedSaveToDatabase(updated)

        return updated
      })

      // Show success message
      toast.success("Screenshot removed")
    } catch (error) {
      console.error("Error removing screenshot:", error)
      toast.error("Failed to remove screenshot")
    }
  }

  // Save trade details
  const saveTradeDetails = async () => {
    setIsSaving(true)

    try {
      const supabase = getSupabaseBrowser()

      // Ensure arrays are properly formatted
      const cleanFollowedRules = Array.isArray(followedRules)
        ? followedRules.filter(id => id && typeof id === 'string')
        : []

      const cleanFollowedSetupCriteria = Array.isArray(followedSetupCriteria)
        ? followedSetupCriteria.filter(id => id && typeof id === 'string')
        : []

      // Create the update object
      const updateData = {
        strategy_id: selectedStrategy?.id || null,
        setup_id: selectedSetupId || null,
        notes: tradeNotes || null,
        screenshots: screenshots || [],
        followed_rules: cleanFollowedRules,
        followed_setup_criteria: cleanFollowedSetupCriteria,
        tags: tradeTags || [],
        // Add a flag to indicate if this trade has journal content
        has_journal_content: (tradeNotes && tradeNotes.trim().length > 0) || screenshots.length > 0
      }

      console.log("Saving trade details:", updateData)

      // Check if strategy assignment has changed
      const strategyChanged = trade.strategy_id !== updateData.strategy_id
      console.log("Strategy changed:", strategyChanged, "from", trade.strategy_id, "to", updateData.strategy_id)

      // Try to use the RPC function first
      try {
        console.log("Attempting to update trade with RPC function")
        const { data: rpcResult, error: rpcError } = await supabase.rpc('update_trade_details', {
          p_trade_id: trade.id,
          p_strategy_id: updateData.strategy_id,
          p_setup_id: updateData.setup_id,
          p_notes: updateData.notes,
          p_screenshots: updateData.screenshots,
          p_followed_rules: updateData.followed_rules,
          p_followed_setup_criteria: updateData.followed_setup_criteria,
          p_tags: updateData.tags,
          p_has_journal_content: updateData.has_journal_content
        })

        if (rpcError) {
          console.error("RPC update failed, falling back to standard update:", rpcError)
          // Fall through to standard update
        } else {
          console.log("RPC update successful:", rpcResult)
          // Skip standard update if RPC was successful
          if (rpcResult === true) {
            console.log("RPC update confirmed successful")
          }
        }
      } catch (rpcError) {
        console.error("Error in RPC update, falling back to standard update:", rpcError)
      }

      // Perform standard update as fallback
      console.log("Performing standard database update")
      const { error } = await supabase
        .from("trades")
        .update({
          strategy_id: updateData.strategy_id,
          setup_id: updateData.setup_id,
          notes: updateData.notes,
          screenshots: updateData.screenshots,
          followed_rules: updateData.followed_rules,
          followed_setup_criteria: updateData.followed_setup_criteria,
          tags: updateData.tags,
          has_journal_content: updateData.has_journal_content,
          updated_at: new Date().toISOString()
        })
        .eq("id", trade.id)

      if (error) {
        console.error("Error updating trade:", error)
        console.error("Error details:", JSON.stringify(error, null, 2))
        toast.error(`Failed to save trade details: ${error.message || "Unknown error"}`)
        return
      }

      console.log("Trade updated successfully in database")

      // Trigger auto-migration queue processing (for new notebook entries)
      try {
        const queueResponse = await fetch('/api/migrate/process-queue', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' }
        });

        if (queueResponse.ok) {
          console.log('Auto-migration queue processed successfully after trade update');
        } else {
          console.warn('Auto-migration queue processing failed after trade update');
        }
      } catch (error) {
        console.warn('Auto-migration queue processing error:', error);
      }

      // Fetch the updated trade to ensure we have the latest data
      console.log("Fetching updated trade from database")
      const { data: updatedTrade, error: fetchError } = await supabase
        .from("trades")
        .select("*")
        .eq("id", trade.id)
        .single()

      if (fetchError) {
        console.error("Error fetching updated trade:", fetchError)
        // Continue anyway since the update was successful
      } else {
        console.log("Successfully fetched updated trade:", updatedTrade)
      }

      // Update our local trade object with the new data
      if (updatedTrade) {
        // Use the data from the database
        console.log("Using updated trade data from database")
        Object.assign(trade, updatedTrade)
      } else {
        // If we couldn't get the updated trade, update our local copy
        console.log("Using local update data")
        const tradeWithDetails = trade as any
        tradeWithDetails.strategy_id = updateData.strategy_id
        tradeWithDetails.setup_id = updateData.setup_id
        tradeWithDetails.notes = updateData.notes
        tradeWithDetails.screenshots = updateData.screenshots
        tradeWithDetails.followed_rules = cleanFollowedRules
        tradeWithDetails.followed_setup_criteria = cleanFollowedSetupCriteria
        tradeWithDetails.tags = updateData.tags
        tradeWithDetails.has_journal_content = updateData.has_journal_content
      }

      console.log("Updated local trade object:", trade)

      // Force a re-render by creating new arrays
      setFollowedRules([...cleanFollowedRules])
      setFollowedSetupCriteria([...cleanFollowedSetupCriteria])

      // Always dispatch events to notify other components
      console.log("Dispatching events to notify other components")

      // Create a custom event with detailed information
      const strategyUpdateEvent = new CustomEvent('strategy-assignment-changed', {
        detail: {
          tradeId: trade.id,
          strategyId: updateData.strategy_id,
          strategyName: selectedStrategy?.name || null,
          timestamp: new Date().toISOString()
        }
      })

      // Dispatch the event to notify other components
      document.dispatchEvent(strategyUpdateEvent)

      // Force a global refresh of trades data with a more specific event
      const refreshEvent = new CustomEvent('refresh-trades-data', {
        detail: {
          tradeId: trade.id,
          strategyId: updateData.strategy_id,
          strategyName: selectedStrategy?.name || null,
          timestamp: new Date().toISOString(),
          source: 'enhanced-trade-detail-view'
        }
      })
      window.dispatchEvent(refreshEvent)

      // Also dispatch a global event for any component that might be listening
      const globalEvent = new CustomEvent('global-data-change', {
        detail: {
          type: 'strategy-assignment',
          tradeId: trade.id,
          strategyId: updateData.strategy_id,
          strategyName: selectedStrategy?.name || null,
          timestamp: new Date().toISOString()
        }
      })
      window.dispatchEvent(globalEvent)

      // Show appropriate success message
      if (strategyChanged) {
        if (updateData.strategy_id) {
          const strategyName = selectedStrategy?.name || "selected strategy"
          toast.success(`Trade assigned to ${strategyName}`)
        } else {
          toast.success("Strategy assignment removed")
        }
      } else {
        toast.success("Trade details saved successfully")
      }

      // Call onUpdate to refresh parent components
      onUpdate()
    } catch (error) {
      console.error("Error saving trade details:", error)
      toast.error("Failed to save trade details")
    } finally {
      setIsSaving(false)
    }
  }

  // Get the selected setup
  const selectedSetup = setups.find(setup => setup.id === selectedSetupId)

  // Parse visual cues and confirmation criteria into arrays for checkboxes
  const visualCues = selectedSetup?.visual_cues
    ? selectedSetup.visual_cues.split('\n').filter(line => line.trim())
    : []

  const confirmationCriteria = selectedSetup?.confirmation_criteria
    ? selectedSetup.confirmation_criteria.split('\n').filter(line => line.trim())
    : []

  return (
    <div className="space-y-8 p-1">
      {/* Trade summary card */}
      <Card className="bg-card border border-border overflow-hidden shadow-sm hover:shadow-md transition-shadow">
        <CardContent className="pt-6">
          <div className="flex justify-between items-center mb-6">
            <div>
              <h3 className="text-2xl font-bold">{trade.symbol}</h3>
              <div className="text-sm text-muted-foreground mt-1">
                {format(new Date(trade.time_open), "MMM d, yyyy 'at' h:mm a")}
              </div>
              {/* Display assigned strategy */}
              {trade.strategy_id && (
                <div className="mt-2">
                  <Badge
                    className="px-3 py-1 bg-gradient-to-r from-purple-500 to-indigo-500 hover:from-purple-600 hover:to-indigo-600 text-white dark:from-purple-600/90 dark:to-indigo-600/90 dark:hover:from-purple-500/90 dark:hover:to-indigo-500/90 shadow-sm hover:shadow transition-all border-0"
                  >
                    <BookOpen className="h-3.5 w-3.5 mr-1.5 text-white dark:text-white" />
                    {strategies.find(s => s.id === trade.strategy_id)?.name || "Unknown Strategy"}
                  </Badge>
                </div>
              )}
            </div>
            <div className="flex flex-col items-end">
              <div className={`text-2xl font-bold ${trade.profit >= 0 ? "text-emerald-500 dark:text-emerald-400" : "text-rose-500 dark:text-rose-400"}`}>
                ${trade.profit.toFixed(2)}
              </div>
              <div className={`text-sm font-medium px-2 py-1 rounded-full ${
                trade.type === 'buy'
                  ? "bg-emerald-100 text-emerald-700 dark:bg-emerald-900/30 dark:text-emerald-400"
                  : "bg-rose-100 text-rose-700 dark:bg-rose-900/30 dark:text-rose-400"
              } mt-1`}>
                {trade.type.toUpperCase()} {trade.volume} Lot
              </div>
            </div>
          </div>

          {/* Progress bar showing profit/loss visually */}
          <div className="h-2 bg-muted mb-6 rounded-full overflow-hidden">
            {trade.profit >= 0 ? (
              <div
                className="h-full bg-emerald-500 dark:bg-emerald-400"
                style={{ width: `${Math.min(100, Math.abs(trade.profit) / 10)}%` }}
              />
            ) : (
              <div
                className="h-full bg-rose-500 dark:bg-rose-400"
                style={{ width: `${Math.min(100, Math.abs(trade.profit) / 10)}%` }}
              />
            )}
          </div>

          {/* Trade details grid */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-x-6 gap-y-4 text-sm">
            <div className="flex items-center gap-3 bg-[#F8FAFC] dark:bg-muted/30 p-3 rounded-lg">
              <Calendar className="h-5 w-5 text-purple-500 dark:text-purple-400" />
              <div>
                <div className="font-medium text-xs mb-1 text-muted-foreground">Entry Date</div>
                <div className="font-medium">{format(new Date(trade.time_open), "yyyy-MM-dd HH:mm")}</div>
              </div>
            </div>
            <div className="flex items-center gap-3 bg-[#F8FAFC] dark:bg-muted/30 p-3 rounded-lg">
              <Calendar className="h-5 w-5 text-purple-500 dark:text-purple-400" />
              <div>
                <div className="font-medium text-xs mb-1 text-muted-foreground">Exit Date</div>
                <div className="font-medium">{format(new Date(trade.time_close), "yyyy-MM-dd HH:mm")}</div>
              </div>
            </div>
            <div className="flex items-center gap-3 bg-[#F8FAFC] dark:bg-muted/30 p-3 rounded-lg">
              <Clock className="h-5 w-5 text-purple-500 dark:text-purple-400" />
              <div>
                <div className="font-medium text-xs mb-1 text-muted-foreground">Duration</div>
                <div className="font-medium">
                  {new Date(trade.time_open).getTime() !== new Date(trade.time_close).getTime() ?
                    formatDistanceStrict(
                      new Date(trade.time_open),
                      new Date(trade.time_close)
                    ) : "0 seconds"}
                </div>
              </div>
            </div>
            <div className="flex items-center gap-3 bg-[#F8FAFC] dark:bg-muted/30 p-3 rounded-lg">
              <BarChart className="h-5 w-5 text-purple-500 dark:text-purple-400" />
              <div>
                <div className="font-medium text-xs mb-1 text-muted-foreground">Volume</div>
                <div className="font-medium">{trade.volume}</div>
              </div>
            </div>
            <div className="flex items-center gap-3 bg-[#F8FAFC] dark:bg-muted/30 p-3 rounded-lg">
              <TrendingUp className="h-5 w-5 text-purple-500 dark:text-purple-400" />
              <div>
                <div className="font-medium text-xs mb-1 text-muted-foreground">Entry Price</div>
                <div className="font-medium">${trade.price_open.toFixed(5)}</div>
              </div>
            </div>
            <div className="flex items-center gap-3 bg-[#F8FAFC] dark:bg-muted/30 p-3 rounded-lg">
              <TrendingDown className="h-5 w-5 text-purple-500 dark:text-purple-400" />
              <div>
                <div className="font-medium text-xs mb-1 text-muted-foreground">Exit Price</div>
                <div className="font-medium">${trade.price_close.toFixed(5)}</div>
              </div>
            </div>
            <div className="flex items-center gap-3 bg-[#F8FAFC] dark:bg-muted/30 p-3 rounded-lg">
              <DollarSign className="h-5 w-5 text-purple-500 dark:text-purple-400" />
              <div>
                <div className="font-medium text-xs mb-1 text-muted-foreground">Commission</div>
                <div className="font-medium">${trade.commission.toFixed(2)}</div>
              </div>
            </div>
            <div className="flex items-center gap-3 bg-[#F8FAFC] dark:bg-muted/30 p-3 rounded-lg">
              <DollarSign className="h-5 w-5 text-purple-500 dark:text-purple-400" />
              <div>
                <div className="font-medium text-xs mb-1 text-muted-foreground">Swap</div>
                <div className="font-medium">${trade.swap.toFixed(2)}</div>
              </div>
            </div>
            <div className="flex items-center gap-3 bg-[#F8FAFC] dark:bg-muted/30 p-3 rounded-lg">
              <DollarSign className="h-5 w-5 text-purple-500 dark:text-purple-400" />
              <div>
                <div className="font-medium text-xs mb-1 text-muted-foreground">NET P/L</div>
                <div className={`font-medium ${trade.profit >= 0 ? "text-emerald-500 dark:text-emerald-400" : "text-rose-500 dark:text-rose-400"}`}>
                  ${trade.profit.toFixed(2)}
                </div>
              </div>
            </div>
            {/* Strategy row removed to avoid duplication */}
          </div>
        </CardContent>
      </Card>

      {/* Tabs for Details and Playbook */}
      <Tabs defaultValue="details" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="flex w-full grid-cols-2 rounded-none border-b bg-transparent">
          <TabsTrigger
            value="details"
            className="flex-1 rounded-none data-[state=active]:bg-background data-[state=active]:border-b data-[state=active]:border-primary data-[state=active]:font-medium"
          >
            <FileText className="mr-2 h-4 w-4" />
            Trade Details
          </TabsTrigger>
          <TabsTrigger
            value="playbook"
            className="flex-1 rounded-none data-[state=active]:bg-background data-[state=active]:border-b data-[state=active]:border-primary data-[state=active]:font-medium"
          >
            <BookOpen className="mr-2 h-4 w-4" />
            Playbook
          </TabsTrigger>
        </TabsList>

        {/* Details Tab Content */}
        <TabsContent value="details" className="space-y-6 pt-6">
          {/* Trade Notes */}
          <Card className="shadow-sm hover:shadow-md transition-shadow border border-border/60">
            <CardHeader className="pb-2 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/30 dark:to-indigo-950/30 border-b">
              <CardTitle className="text-base flex items-center">
                <FileText className="h-4 w-4 mr-2 text-blue-500" />
                <span className="dark:text-[#FAFAFA]">Trade Notes</span>
              </CardTitle>
              <CardDescription>
                Add your observations and thoughts about this trade
              </CardDescription>
            </CardHeader>
            <CardContent className="pt-4">
              <Textarea
                placeholder="Enter your notes about this trade..."
                className="min-h-[150px] border-border/60 focus:border-purple-500 focus:ring-purple-500/20 dark:focus:border-purple-400 dark:focus:ring-purple-400/20"
                value={tradeNotes}
                onChange={(e) => {
                  setTradeNotes(e.target.value)
                  // Debounce localStorage update to avoid excessive writes
                  if (window.tradeNotesTimeout) {
                    clearTimeout(window.tradeNotesTimeout)
                  }
                  window.tradeNotesTimeout = setTimeout(() => {
                    updateLocalStorage()
                  }, 500)
                }}
              />
            </CardContent>
          </Card>

          {/* Trade Tags */}
          <Card className="shadow-sm hover:shadow-md transition-shadow border border-border/60">
            <CardHeader className="pb-2 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/30 dark:to-indigo-950/30 border-b">
              <CardTitle className="text-base flex items-center">
                <Tag className="h-4 w-4 mr-2 text-blue-500" />
                <span className="dark:text-[#FAFAFA]">Trade Tags</span>
              </CardTitle>
              <CardDescription>
                Add tags to categorize and filter your trades
              </CardDescription>
            </CardHeader>
            <CardContent className="pt-4">
              <TagInput
                value={tradeTags}
                onChange={(newTags) => {
                  setTradeTags(newTags)
                  updateLocalStorage(screenshots, followedRules, followedSetupCriteria, newTags)
                }}
                suggestions={availableTags}
                placeholder="Add tags..."
              />
              <p className="text-xs text-muted-foreground mt-2">
                Tags help you filter and categorize your trades. Examples: "Gap Up", "Breakout", "FOMC", etc.
              </p>
            </CardContent>
          </Card>

          {/* Screenshots */}
          <Card className="shadow-sm hover:shadow-md transition-shadow border border-border/60">
            <CardHeader className="pb-2 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/30 dark:to-indigo-950/30 border-b">
              <CardTitle className="text-base flex items-center">
                <ImageIcon className="h-4 w-4 mr-2 text-blue-500" />
                <span className="dark:text-[#FAFAFA]">Screenshots</span>
              </CardTitle>
              <CardDescription>
                Add chart screenshots or other visual references
              </CardDescription>
            </CardHeader>
            <CardContent className="pt-4">
              <div className="space-y-5">
                <div className="flex items-center justify-between mb-4">
                  <label
                    className={cn(
                      "relative aspect-video w-40 rounded-md border border-dashed flex flex-col items-center justify-center cursor-pointer bg-muted/30 hover:bg-muted/50 transition-colors",
                      isUploading && "pointer-events-none"
                    )}
                  >
                    {isUploading ? (
                      <>
                        <div className="absolute inset-0 bg-background/80 flex flex-col items-center justify-center z-10">
                          <div className="w-full max-w-[80%] h-2 bg-muted rounded-full overflow-hidden mb-2">
                            <div
                              className="h-full bg-primary transition-all duration-300 ease-out"
                              style={{ width: `${uploadProgress}%` }}
                            />
                          </div>
                          <span className="text-xs text-muted-foreground">Uploading... {Math.round(uploadProgress)}%</span>
                        </div>
                        <ImageIcon className="h-6 w-6 mb-1 text-muted-foreground opacity-50" />
                        <span className="text-xs text-muted-foreground opacity-50">Uploading...</span>
                      </>
                    ) : (
                      <>
                        <Plus className="h-6 w-6 mb-1 text-muted-foreground" />
                        <span className="text-xs text-muted-foreground">Add Screenshot</span>
                      </>
                    )}
                    <input
                      ref={fileInputRef}
                      type="file"
                      className="sr-only"
                      accept="image/*"
                      onChange={handleImageUpload}
                      disabled={isUploading}
                    />
                  </label>
                </div>

                {screenshots.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {screenshots.map((screenshot, index) => (
                      <div key={index} className="relative group overflow-hidden rounded-lg shadow-sm">
                        <ImageDisplay
                          src={screenshot}
                          alt={`Trade screenshot ${index + 1}`}
                          className="rounded-lg border border-border/60 hover:border-purple-500/50 dark:hover:border-purple-400/50 transition-colors"
                          aspectRatio="video"
                          lightboxGroup={screenshots}
                          lightboxIndex={index}
                        />
                        <Button
                          variant="destructive"
                          size="icon"
                          className="absolute top-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity h-8 w-8 shadow-md z-10"
                          onClick={(e) => {
                            e.stopPropagation();
                            removeScreenshot(index);
                          }}
                        >
                          <XCircle className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center p-10 border border-dashed border-border/60 rounded-lg text-muted-foreground bg-muted/20">
                    <ImageIcon className="h-10 w-10 mx-auto mb-3 text-muted-foreground/50" />
                    <p>No screenshots added yet.</p>
                    <p className="text-sm mt-1">Click "Add Screenshot" to upload a chart image.</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Playbook Tab Content */}
        <TabsContent value="playbook" className="space-y-6 pt-6">
          {/* Strategy Selection */}
          <Card className="shadow-sm hover:shadow-md transition-shadow border border-border/60">
            <CardHeader className="pb-2 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/30 dark:to-indigo-950/30 border-b">
              <CardTitle className="text-base flex items-center">
                <BookOpen className="h-4 w-4 mr-2 text-blue-500" />
                <span className="dark:text-[#FAFAFA]">Strategy</span>
              </CardTitle>
              <CardDescription>
                Assign a trading strategy to this trade
              </CardDescription>
            </CardHeader>
            <CardContent className="pt-4">
              <Select
                value={selectedStrategy?.id || "none"}
                onValueChange={handleStrategyChange}
              >
                <SelectTrigger className="border-border/60 focus:ring-purple-500/20 focus:border-purple-500 dark:focus:ring-purple-400/20 dark:focus:border-purple-400">
                  <SelectValue placeholder="Select a strategy" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">None</SelectItem>
                  {strategies.map((strategy) => (
                    <SelectItem key={strategy.id} value={strategy.id}>
                      {strategy.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              {selectedStrategy && (
                <div className="mt-5 p-4 bg-muted/30 rounded-lg border border-border/40">
                  <div className="font-medium text-lg mb-2 text-purple-600 dark:text-[#FAFAFA]">{selectedStrategy.name}</div>
                  <div className="text-sm text-muted-foreground">
                    {selectedStrategy.description || "No description provided"}
                  </div>
                  <div className="flex flex-wrap gap-2 mt-3">
                    {selectedStrategy.market_conditions?.map((condition) => (
                      <Badge key={condition} variant="outline" className="text-xs bg-background border-purple-200 text-purple-700 dark:border-purple-800 dark:text-[#FAFAFA]">
                        {condition}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Setup Selection */}
          {selectedStrategy && (
            <Card className="shadow-sm hover:shadow-md transition-shadow border border-border/60">
              <CardHeader className="pb-2 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/30 dark:to-indigo-950/30 border-b">
                <CardTitle className="text-base flex items-center">
                  <TrendingUp className="h-4 w-4 mr-2 text-blue-500" />
                  <span className="dark:text-[#FAFAFA]">Setup</span>
                </CardTitle>
                <CardDescription>
                  Select the setup pattern used for this trade
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-4">
                <Select
                  value={selectedSetupId || "none"}
                  onValueChange={handleSetupChange}
                  disabled={isLoading || setups.length === 0}
                >
                  <SelectTrigger className="border-border/60 focus:ring-purple-500/20 focus:border-purple-500 dark:focus:ring-purple-400/20 dark:focus:border-purple-400">
                    <SelectValue placeholder={isLoading ? "Loading setups..." : setups.length === 0 ? "No setups available" : "Select a setup"} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">None</SelectItem>
                    {setups.map((setup) => (
                      <SelectItem key={setup.id} value={setup.id}>
                        {setup.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                {selectedSetup && (
                  <div className="mt-5 space-y-5">
                    {/* Setup Image */}
                    {selectedSetup.image_urls && selectedSetup.image_urls.length > 0 && (
                      <div className="relative aspect-video w-full overflow-hidden rounded-lg bg-muted/30 border border-border/40">
                        <img
                          src={selectedSetup.image_urls[0]}
                          alt={`${selectedSetup.name} chart example`}
                          className="w-full h-auto object-contain"
                        />
                      </div>
                    )}

                    {/* Visual Cues */}
                    {visualCues.length > 0 && (
                      <div>
                        <h4 className="text-sm font-medium mb-3 flex items-center">
                          <span className="h-2 w-2 rounded-full bg-purple-500 dark:bg-purple-400 mr-2"></span>
                          <span className="dark:text-[#FAFAFA]">Visual Cues</span>
                        </h4>
                        <div className="space-y-3 border border-border/60 rounded-lg p-4 bg-muted/20">
                          {visualCues.map((cue, index) => (
                            <div key={`cue-${index}`} className="flex items-start space-x-3 hover:bg-muted/30 p-2 rounded-md transition-colors">
                              <SimpleCheckbox
                                id={`cue-${index}`}
                                checked={followedSetupCriteria.includes(`cue-${index}`)}
                                onChange={(checked) => {
                                  console.log(`Cue checkbox ${index} changed to:`, checked)
                                  handleSetupCriteriaChange(`cue-${index}`, checked)
                                }}
                              />
                              <label
                                htmlFor={`cue-${index}`}
                                className="text-sm leading-tight cursor-pointer"
                              >
                                {cue}
                              </label>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Confirmation Criteria */}
                    {confirmationCriteria.length > 0 && (
                      <div>
                        <h4 className="text-sm font-medium mb-3 flex items-center">
                          <span className="h-2 w-2 rounded-full bg-purple-500 dark:bg-purple-400 mr-2"></span>
                          <span className="dark:text-[#FAFAFA]">Confirmation Criteria</span>
                        </h4>
                        <div className="space-y-3 border border-border/60 rounded-lg p-4 bg-muted/20">
                          {confirmationCriteria.map((criteria, index) => (
                            <div key={`criteria-${index}`} className="flex items-start space-x-3 hover:bg-muted/30 p-2 rounded-md transition-colors">
                              <SimpleCheckbox
                                id={`criteria-${index}`}
                                checked={followedSetupCriteria.includes(`criteria-${index}`)}
                                onChange={(checked) => {
                                  console.log(`Criteria checkbox ${index} changed to:`, checked)
                                  handleSetupCriteriaChange(`criteria-${index}`, checked)
                                }}
                              />
                              <label
                                htmlFor={`criteria-${index}`}
                                className="text-sm leading-tight cursor-pointer"
                              >
                                {criteria}
                              </label>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Rules Checklist */}
          {selectedStrategy && rules.length > 0 && (
            <Card className="shadow-sm hover:shadow-md transition-shadow border border-border/60">
              <CardHeader className="pb-2 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/30 dark:to-indigo-950/30 border-b">
                <CardTitle className="text-base flex items-center">
                  <FileText className="h-4 w-4 mr-2 text-blue-500" />
                  <span className="dark:text-[#FAFAFA]">Strategy Rules</span>
                </CardTitle>
                <CardDescription>
                  Check the rules you followed for this trade
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-4">
                <ScrollArea className="h-[350px] pr-4">
                  <div className="space-y-5">
                    {/* Group rules by type */}
                    {['entry', 'exit', 'stop_loss', 'take_profit', 'position_sizing', 'other'].map(ruleType => {
                      const typeRules = rules.filter(rule => rule.rule_type === ruleType);
                      if (typeRules.length === 0) return null;

                      return (
                        <div key={ruleType} className="space-y-3">
                          <h4 className="text-sm font-medium capitalize flex items-center">
                            <span className="h-2 w-2 rounded-full bg-purple-500 dark:bg-purple-400 mr-2"></span>
                            <span className="dark:text-[#FAFAFA]">{ruleType.replace('_', ' ')} Rules</span>
                          </h4>
                          <div className="space-y-3 border border-border/60 rounded-lg p-4 bg-muted/20">
                            {typeRules.map((rule) => (
                              <div key={rule.id} className="flex items-start space-x-3 hover:bg-muted/30 p-2 rounded-md transition-colors">
                                <SimpleCheckbox
                                  id={rule.id}
                                  checked={followedRules.includes(rule.id)}
                                  onChange={(checked) => {
                                    console.log(`Checkbox ${rule.id} changed to:`, checked)
                                    console.log(`Rule ID: ${rule.id}, Rule name: ${rule.name}`)
                                    handleRuleChange(rule.id, checked)
                                  }}
                                />
                                <label
                                  htmlFor={rule.id}
                                  className="text-sm leading-tight cursor-pointer"
                                >
                                  <span className="font-medium">{rule.name}</span>
                                  {rule.description && (
                                    <span className="block text-muted-foreground text-xs mt-1">
                                      {rule.description}
                                    </span>
                                  )}
                                </label>
                              </div>
                            ))}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>
          )}

          {/* Help Card */}
          <Card className="bg-gradient-to-br from-purple-50 to-blue-50 dark:from-purple-950/30 dark:to-blue-950/30 border-dashed border-purple-200 dark:border-purple-800/50 shadow-sm">
            <CardHeader className="pb-2 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/30 dark:to-indigo-950/30 border-b">
              <CardTitle className="text-base flex items-center">
                <HelpCircle className="h-4 w-4 mr-2 text-blue-500" />
                <span className="dark:text-[#FAFAFA]">How to Use Playbook Tracking</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-sm text-muted-foreground space-y-3">
                <p>Track how well you followed your trading plan by checking off the rules and setup criteria you adhered to.</p>
                <ul className="space-y-2">
                  {[
                    "Select your strategy and setup from the dropdowns",
                    "Check off the visual cues you identified",
                    "Check off the confirmation criteria you verified",
                    "Check off the strategy rules you followed"
                  ].map((item, index) => (
                    <li key={index} className="flex items-start">
                      <span className="h-5 w-5 flex items-center justify-center rounded-full bg-purple-100 dark:bg-purple-900/40 text-purple-600 dark:text-purple-400 mr-2 text-xs font-medium flex-shrink-0 mt-0.5">
                        {index + 1}
                      </span>
                      <span>{item}</span>
                    </li>
                  ))}
                </ul>
                <p className="text-xs italic mt-2 text-purple-600/70 dark:text-purple-400/70">
                  This helps you analyze which aspects of your strategy are most effective and where you might need improvement.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Save Button */}
      <div className="flex justify-end mt-4">
        <Button
          onClick={saveTradeDetails}
          disabled={isSaving}
          className="w-auto md:w-auto"
        >
          {isSaving ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Saving...
            </>
          ) : (
            <>
              <Save className="mr-2 h-4 w-4" />
              Save Trade Details
            </>
          )}
        </Button>
      </div>
    </div>
  )
}
