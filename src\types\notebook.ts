import { Database } from "./supabase"

// Type for notebook entry from the database
export type NotebookEntry = Database["public"]["Tables"]["notebook_entries"]["Row"]

// Type for inserting a new notebook entry
export type NotebookEntryInsert = Database["public"]["Tables"]["notebook_entries"]["Insert"]

// Type for updating a notebook entry
export type NotebookEntryUpdate = Database["public"]["Tables"]["notebook_entries"]["Update"]

// Type for notebook folder from the database
export type NotebookFolder = Database["public"]["Tables"]["notebook_folders"]["Row"]

// Type for inserting a new notebook folder
export type NotebookFolderInsert = Database["public"]["Tables"]["notebook_folders"]["Insert"] & {
  is_category?: boolean;
}

// Type for updating a notebook folder
export type NotebookFolderUpdate = Database["public"]["Tables"]["notebook_folders"]["Update"]

// Type for the editor content in JSON format
export interface EditorContent {
  type: string;
  content: Array<{
    type: string;
    attrs?: Record<string, any>;
    content?: Array<{
      type: string;
      text?: string;
      attrs?: Record<string, any>;
      marks?: Array<{
        type: string;
        attrs?: Record<string, any>;
      }>;
      content?: any[];
    }>;
  }>;
}

// Type for notebook entry with additional client-side properties
export interface NotebookEntryWithMeta extends NotebookEntry {
  isNew?: boolean
  isDirty?: boolean
  isLoading?: boolean
  is_selected?: boolean
  error?: string | null
}

// Type for notebook filter options
export interface NotebookFilterOptions {
  searchTerm?: string
  tags?: string[]
  category?: string
  folderId?: string
  isTemplate?: boolean
  accountId?: string | null
}

// Type for notebook sort options
export type NotebookSortField = 'title' | 'updated_at' | 'created_at'
export type NotebookSortOrder = 'asc' | 'desc'

export interface NotebookSortOptions {
  field: NotebookSortField
  order: NotebookSortOrder
}

// Type for notebook view options
export type NotebookViewMode = 'list' | 'grid'

// Type for notebook template
export interface NotebookTemplate {
  id: string
  title: string
  content: EditorContent
  category?: string
  tags?: string[]
}

// Type for notebook category
export interface NotebookCategory {
  name: string
  count: number
}

// Type for notebook tag
export interface NotebookTag {
  name: string
  count: number
}

// Type for notebook folder with additional client-side properties
export interface NotebookFolderWithMeta extends NotebookFolder {
  count: number | undefined
  isExpanded?: boolean
}

// Type for linked item (trade or strategy)
export interface LinkedItem {
  id: string
  type: 'trade' | 'strategy'
  title: string
  date?: string
  symbol?: string
}
