import { NextRequest, NextResponse } from "next/server"
import { getSupabaseServer } from "@/lib/supabase-server"

export async function GET(request: NextRequest) {
  try {
    console.log('=== TESTING AUTO-MIGRATION SYSTEM ===')

    const supabase = await getSupabaseServer()
    const { data: { user }, error: userError } = await supabase.auth.getUser()

    if (userError || !user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    // Check queue table exists and get stats
    const { data: queueStats, error: queueError } = await supabase
      .from("auto_migrate_queue")
      .select("processed, created_at")
      .order("created_at", { ascending: false })
      .limit(10)

    if (queueError) {
      console.error('Queue table error:', queueError)
      return NextResponse.json({
        error: "Queue table not accessible",
        details: queueError.message
      }, { status: 500 })
    }

    // Get recent trades with journal content
    const { data: recentTrades, error: tradesError } = await supabase
      .from("trades")
      .select("id, symbol, notes, journal_content, has_journal_content, created_at")
      .eq("user_id", user.id)
      .or("notes.not.is.null,journal_content.not.is.null,has_journal_content.eq.true")
      .order("created_at", { ascending: false })
      .limit(5)

    // Get recent daily journal entries
    const { data: recentJournals, error: journalsError } = await supabase
      .from("daily_journal_entries")
      .select("id, date, note, created_at")
      .eq("user_id", user.id)
      .not("note", "is", null)
      .order("created_at", { ascending: false })
      .limit(5)

    // Get recent notebook entries
    const { data: recentNotebooks, error: notebooksError } = await supabase
      .from("notebook_entries")
      .select("id, title, linked_trade_ids, linked_daily_journal_ids, created_at")
      .eq("user_id", user.id)
      .order("created_at", { ascending: false })
      .limit(10)

    // Check Edge Function status
    const edgeFunctionUrl = `${process.env.NEXT_PUBLIC_SUPABASE_URL}/functions/v1/auto-migrate-to-notebook`
    let edgeFunctionStatus = 'unknown'
    
    try {
      const testResponse = await fetch(edgeFunctionUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`,
        },
        body: JSON.stringify({
          type: 'TEST',
          table: 'test',
          record: { id: 'test' }
        })
      })
      
      edgeFunctionStatus = testResponse.ok ? 'accessible' : `error-${testResponse.status}`
    } catch (error) {
      edgeFunctionStatus = 'unreachable'
    }

    const stats = {
      queueItems: {
        total: queueStats?.length || 0,
        processed: queueStats?.filter(item => item.processed).length || 0,
        pending: queueStats?.filter(item => !item.processed).length || 0,
        recent: queueStats?.slice(0, 5) || []
      },
      trades: {
        withJournalContent: recentTrades?.length || 0,
        recent: recentTrades || []
      },
      dailyJournals: {
        withContent: recentJournals?.length || 0,
        recent: recentJournals || []
      },
      notebooks: {
        total: recentNotebooks?.length || 0,
        withTradeLinks: recentNotebooks?.filter(nb => nb.linked_trade_ids?.length > 0).length || 0,
        withJournalLinks: recentNotebooks?.filter(nb => nb.linked_daily_journal_ids?.length > 0).length || 0,
        recent: recentNotebooks || []
      },
      edgeFunction: {
        status: edgeFunctionStatus,
        url: edgeFunctionUrl
      }
    }

    console.log('Auto-migration system stats:', stats)

    return NextResponse.json({
      success: true,
      message: "Auto-migration system test completed",
      stats,
      recommendations: generateRecommendations(stats)
    })

  } catch (error) {
    console.error('Error in auto-migration test:', error)
    return NextResponse.json(
      { error: "Internal server error", details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}

function generateRecommendations(stats: any): string[] {
  const recommendations = []

  if (stats.queueItems.pending > 0) {
    recommendations.push(`${stats.queueItems.pending} items pending in migration queue - run /api/migrate/process-queue`)
  }

  if (stats.trades.withJournalContent > 0 && stats.notebooks.withTradeLinks === 0) {
    recommendations.push('Trades with journal content found but no notebook entries with trade links - auto-migration may not be working')
  }

  if (stats.dailyJournals.withContent > 0 && stats.notebooks.withJournalLinks === 0) {
    recommendations.push('Daily journals with content found but no notebook entries with journal links - auto-migration may not be working')
  }

  if (stats.edgeFunction.status !== 'accessible') {
    recommendations.push(`Edge Function not accessible (${stats.edgeFunction.status}) - check deployment`)
  }

  if (recommendations.length === 0) {
    recommendations.push('Auto-migration system appears to be working correctly')
  }

  return recommendations
}
