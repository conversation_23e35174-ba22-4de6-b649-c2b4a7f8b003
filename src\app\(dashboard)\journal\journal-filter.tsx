'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { format } from 'date-fns';
import { toast } from 'sonner';
import { Search, Filter, X, Calendar, Tag, Check } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { TableDateRangePicker } from '@/components/ui/table-date-range-picker';
import { DateRange } from 'react-day-picker';
import { findBestMatchingTag, buildUrlParams, formatDateRange, normalizeString } from './utils';

interface JournalFilterProps {
  availableTags: string[];
  selectedTags: string[];
  setSelectedTags: (tags: string[]) => void;
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  dateRange: DateRange | undefined;
  setDateRange: (range: DateRange | undefined) => void;
  activeTab: string;
  onSearch: (term: string, tags: string[], dateRange?: DateRange) => Promise<void>;
  onReset: () => void;
  isLoading: boolean;
}

export function JournalFilter({
  availableTags,
  selectedTags,
  setSelectedTags,
  searchTerm,
  setSearchTerm,
  dateRange,
  setDateRange,
  activeTab,
  onSearch,
  onReset,
  isLoading
}: JournalFilterProps) {
  const router = useRouter();
  const [isFilterOpen, setIsFilterOpen] = useState(false);

  // Toggle a tag selection
  const toggleTag = (tag: string) => {
    if (selectedTags.includes(tag)) {
      setSelectedTags(selectedTags.filter(t => t !== tag));
    } else {
      setSelectedTags([...selectedTags, tag]);
    }
  };

  // Apply filters
  const applyFilters = async () => {
    // If there's a search term that might be a tag, check if it matches any available tags
    if (searchTerm && searchTerm.trim() !== '' && selectedTags.length === 0) {
      const term = searchTerm.trim();
      console.log(`Checking if search term "${term}" matches any tags`);

      // Find the best matching tag
      const matchingTag = findBestMatchingTag(availableTags, term);

      if (matchingTag) {
        // If it matches a tag, add it to selected tags
        console.log(`Search term "${term}" matches tag "${matchingTag}", adding to selected tags`);
        setSelectedTags([matchingTag]);

        // Show success toast for better feedback
        toast.success(`Filtering by tag: ${matchingTag}`);
      }
    }

    // Close the filter popover
    setIsFilterOpen(false);

    // Update URL and fetch filtered data
    await onSearch(searchTerm, selectedTags, dateRange);
  };

  // Reset all filters
  const resetFilters = () => {
    setSearchTerm('');
    setSelectedTags([]);
    setDateRange(undefined);
    onReset();
  };

  // Handle search input with debounce
  const handleSearchInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newSearchTerm = e.target.value;
    setSearchTerm(newSearchTerm);

    // Apply search after a short delay to avoid too many requests
    const delayDebounceFn = setTimeout(async () => {
      // Check if the search term matches any available tag
      const term = newSearchTerm.trim();
      let updatedTags = [...selectedTags];

      // IMPROVED TAG SEARCH LOGIC
      // We'll always search for both the search term AND any matching tags
      if (term) {
        console.log(`Searching with term: "${term}" in tab "${activeTab}"`);

        // Check if the term exactly matches any tag
        const exactMatch = availableTags.find(tag =>
          normalizeString(tag) === normalizeString(term)
        );

        if (exactMatch && !selectedTags.includes(exactMatch)) {
          // If it exactly matches a tag, add it to selected tags
          console.log(`Search term "${term}" exactly matches tag "${exactMatch}", adding to selected tags`);
          updatedTags = [...selectedTags, exactMatch];
          setSelectedTags(updatedTags);

          // Show success toast for better feedback
          toast.success(`Added tag filter: ${exactMatch}`);
        }
        // If no exact match and term is at least 3 characters, check for partial matches
        else if (term.length >= 3 && selectedTags.length === 0) {
          // Check for tags that start with the term
          const startsWithMatches = availableTags.filter(tag =>
            normalizeString(tag).startsWith(normalizeString(term))
          );

          // Check for tags that contain the term
          const containsMatches = availableTags.filter(tag =>
            normalizeString(tag).includes(normalizeString(term)) &&
            !startsWithMatches.includes(tag)
          );

          // Combine matches with starts-with matches taking priority
          const allMatches = [...startsWithMatches, ...containsMatches];

          if (allMatches.length === 1) {
            // If only one match, use it
            console.log(`Search term "${term}" matches tag "${allMatches[0]}", adding to selected tags`);
            updatedTags = [allMatches[0]];
            setSelectedTags(updatedTags);

            // Show success toast for better feedback
            toast.success(`Filtering by tag: ${allMatches[0]}`);
          } else if (allMatches.length > 1) {
            // If multiple matches, show a toast with suggestions
            console.log(`Search term "${term}" matches multiple tags:`, allMatches);
            toast.info(`Multiple matching tags found. Did you mean: ${allMatches.slice(0, 3).join(', ')}${allMatches.length > 3 ? '...' : ''}`);

            // IMPORTANT: Even with multiple matches, we'll still search using the term
            // This ensures the search works even if we don't select a specific tag
          }
        }
      }

      // IMPORTANT: Always search with both the search term AND any selected tags
      // This ensures we find results even if the search term doesn't match a tag exactly

      // Build URL parameters
      const params = buildUrlParams({
        searchTerm: newSearchTerm,
        tags: updatedTags,
        startDate: dateRange?.from ? format(dateRange.from, "yyyy-MM-dd") : undefined,
        endDate: dateRange?.to ? format(dateRange.to, "yyyy-MM-dd") : undefined,
        activeTab
      });

      // Update URL without full navigation
      window.history.replaceState({}, '', `/journal?${params.toString()}`);

      // Fetch filtered data - ALWAYS pass both search term and tags
      await onSearch(newSearchTerm, updatedTags, dateRange);
    }, 500); // Delay to give user time to type

    return () => clearTimeout(delayDebounceFn);
  };

  return (
    <div className="flex flex-col space-y-4 border-b pb-4">

      <div className="flex items-center space-x-4">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search journal entries..."
            value={searchTerm}
            onChange={handleSearchInput}
            className="pl-8"
          />
        </div>

        <Popover open={isFilterOpen} onOpenChange={setIsFilterOpen}>
          <PopoverTrigger asChild>
            <Button variant="outline">
              <Filter className="mr-2 h-4 w-4" />
              Filters
              {(selectedTags.length > 0 || dateRange?.from || dateRange?.to) && (
                <Badge variant="secondary" className="ml-2 px-1 py-0">
                  {selectedTags.length + (dateRange?.from ? 1 : 0) + (dateRange?.to ? 1 : 0)}
                </Badge>
              )}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-80">
            <div className="space-y-4">
              <div className="space-y-2">
                <h4 className="font-medium">Date Range</h4>
                <TableDateRangePicker
                  dateRange={dateRange}
                  onDateRangeChange={setDateRange}
                  align="center"
                />
              </div>

              <div className="space-y-2">
                <h4 className="font-medium">Tags</h4>
                <div className="flex flex-wrap gap-2 max-h-[150px] overflow-y-auto p-2 border rounded-md">
                  {availableTags.length > 0 ? (
                    availableTags.map((tag) => (
                      <Badge
                        key={tag}
                        variant={selectedTags.includes(tag) ? "default" : "outline"}
                        className="cursor-pointer"
                        onClick={() => toggleTag(tag)}
                      >
                        {tag}
                      </Badge>
                    ))
                  ) : (
                    <div className="text-sm text-muted-foreground">No tags available</div>
                  )}
                </div>
              </div>

              <div className="flex justify-between">
                <Button variant="outline" size="sm" onClick={resetFilters}>
                  Reset
                </Button>
                <Button size="sm" onClick={applyFilters}>
                  Apply Filters
                </Button>
              </div>
            </div>
          </PopoverContent>
        </Popover>

        <Button
          variant="outline"
          onClick={resetFilters}
          disabled={!searchTerm && selectedTags.length === 0 && !dateRange?.from && !dateRange?.to}
        >
          <X className="mr-2 h-4 w-4" />
          Clear
        </Button>
      </div>

      {/* Active filters display */}
      {(selectedTags.length > 0 || dateRange?.from || dateRange?.to) && (
        <div className="flex flex-wrap items-center gap-2 text-sm">
          <span className="text-muted-foreground">Active filters:</span>

          {dateRange?.from && dateRange?.to && (
            <Badge variant="secondary" className="flex items-center gap-1">
              <Calendar className="h-3 w-3" />
              {format(dateRange.from, "MMM d, yyyy")} - {format(dateRange.to, "MMM d, yyyy")}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setDateRange(undefined)}
                className="h-4 w-4 p-0 ml-1 text-muted-foreground hover:text-foreground"
              >
                <X className="h-3 w-3" />
                <span className="sr-only">Remove date filter</span>
              </Button>
            </Badge>
          )}

          {dateRange?.from && !dateRange?.to && (
            <Badge variant="secondary" className="flex items-center gap-1">
              <Calendar className="h-3 w-3" />
              From: {format(dateRange.from, "MMM d, yyyy")}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setDateRange(undefined)}
                className="h-4 w-4 p-0 ml-1 text-muted-foreground hover:text-foreground"
              >
                <X className="h-3 w-3" />
                <span className="sr-only">Remove date filter</span>
              </Button>
            </Badge>
          )}

          {selectedTags.map(tag => (
            <Badge key={tag} variant="secondary" className="flex items-center gap-1">
              <Tag className="h-3 w-3" />
              {tag}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setSelectedTags(selectedTags.filter(t => t !== tag))}
                className="h-4 w-4 p-0 ml-1 text-muted-foreground hover:text-foreground"
              >
                <X className="h-3 w-3" />
                <span className="sr-only">Remove tag filter</span>
              </Button>
            </Badge>
          ))}
        </div>
      )}

      {/* Loading indicator */}
      {isLoading && (
        <div className="flex items-center space-x-2 text-sm text-muted-foreground bg-muted/30 px-3 py-1.5 rounded-md">
          <div className="h-4 w-4 rounded-full border-2 border-purple-500 border-t-transparent animate-spin"></div>
          <span>Filtering journals...</span>
        </div>
      )}
    </div>
  );
}
