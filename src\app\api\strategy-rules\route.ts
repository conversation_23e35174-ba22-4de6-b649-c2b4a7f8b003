import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { createServerClient } from '@supabase/ssr';
import type { Database } from '@/types/supabase';
import { StrategyRule } from '@/types/playbook';

// GET handler to fetch strategy rules
export async function GET(request: Request) {
  try {
    // Get URL parameters
    const url = new URL(request.url);
    const ruleId = url.searchParams.get('id');
    const strategyId = url.searchParams.get('strategyId');

    if (!strategyId && !ruleId) {
      return NextResponse.json({ error: 'Either strategy ID or rule ID is required' }, { status: 400 });
    }

    // Get cookies for server-side Supabase client
    const cookieStore = await cookies();
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
          set(_name: string, _value: string, _options: any) {
            // API routes can't set cookies directly
          },
          remove(_name: string, _options: any) {
            // API routes can't remove cookies directly
          }
        },
      }
    );

    // Get authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const userId = user.id;

    // If ruleId is provided, fetch a single rule
    if (ruleId) {
      const { data, error } = await supabase
        .from('strategy_rules')
        .select('*')
        .eq('id', ruleId)
        .eq('user_id', userId)
        .single();

      if (error) {
        return NextResponse.json({ error: error.message }, { status: 500 });
      }

      return NextResponse.json(data);
    }

    // Otherwise, fetch rules for a strategy
    const { data, error } = await supabase
      .from('strategy_rules')
      .select('*')
      .eq('strategy_id', strategyId)
      .eq('user_id', userId)
      .order('priority', { ascending: true });

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json(data || []);
  } catch (error) {
    console.error('Error fetching strategy rules:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// POST handler to create a rule
export async function POST(request: Request) {
  try {
    const body = await request.json();

    // Get cookies for server-side Supabase client
    const cookieStore = await cookies();
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
          set(_name: string, _value: string, _options: any) {
            // API routes can't set cookies directly
          },
          remove(_name: string, _options: any) {
            // API routes can't remove cookies directly
          }
        },
      }
    );

    // Get authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const userId = user.id;

    // Validate required fields
    if (!body.strategy_id) {
      return NextResponse.json({ error: 'Strategy ID is required' }, { status: 400 });
    }

    if (!body.rule_type) {
      return NextResponse.json({ error: 'Rule type is required' }, { status: 400 });
    }

    if (!body.name) {
      return NextResponse.json({ error: 'Rule name is required' }, { status: 400 });
    }

    // Create rule
    const { data, error } = await supabase
      .from('strategy_rules')
      .insert([
        {
          user_id: userId,
          strategy_id: body.strategy_id,
          rule_type: body.rule_type,
          name: body.name,
          description: body.description || null,
          priority: body.priority || 0
        }
      ])
      .select()
      .single();

    if (error) {
      // Check for unique constraint violation if there is one
      if (error.code === '23505') {
        return NextResponse.json({
          error: 'A rule with this name already exists for this strategy. Please use a different name.'
        }, { status: 409 });
      }

      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error('Error creating strategy rule:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// PATCH handler to update a rule
export async function PATCH(request: Request) {
  try {
    const body = await request.json();

    // Get cookies for server-side Supabase client
    const cookieStore = await cookies();
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
          set(_name: string, _value: string, _options: any) {
            // API routes can't set cookies directly
          },
          remove(_name: string, _options: any) {
            // API routes can't remove cookies directly
          }
        },
      }
    );

    // Get authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const userId = user.id;

    // Validate required fields
    if (!body.id) {
      return NextResponse.json({ error: 'Rule ID is required' }, { status: 400 });
    }

    // Update rule
    const { data, error } = await supabase
      .from('strategy_rules')
      .update({
        rule_type: body.rule_type,
        name: body.name,
        description: body.description,
        priority: body.priority,
        updated_at: new Date().toISOString()
      })
      .eq('id', body.id)
      .eq('user_id', userId)
      .select()
      .single();

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error('Error updating strategy rule:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// DELETE handler to delete a rule
export async function DELETE(request: Request) {
  try {
    // Get URL parameters
    const url = new URL(request.url);
    const ruleId = url.searchParams.get('id');

    if (!ruleId) {
      return NextResponse.json({ error: 'Rule ID is required' }, { status: 400 });
    }

    // Get cookies for server-side Supabase client
    const cookieStore = await cookies();
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
          set(_name: string, _value: string, _options: any) {
            // API routes can't set cookies directly
          },
          remove(_name: string, _options: any) {
            // API routes can't remove cookies directly
          }
        },
      }
    );

    // Get authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const userId = user.id;

    // Delete rule
    const { error } = await supabase
      .from('strategy_rules')
      .delete()
      .eq('id', ruleId)
      .eq('user_id', userId);

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting strategy rule:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
