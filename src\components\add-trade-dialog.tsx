import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ead<PERSON>, DialogTitle, DialogDescription } from "@/components/ui/dialog"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Card } from "@/components/ui/card"
import { UploadDropzone } from "@/components/upload-dropzone"
import { ManualTradeForm } from "@/components/manual-trade-form"
import { type ProcessedData } from "@/lib/excel-processor"
import { saveTradeData } from "@/lib/trade-service"
import { toast } from "sonner"
import { cn } from "@/lib/utils"

interface AddTradeDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  userId: string | null
  accountId?: string | null
  onTradeAdded: (data: ProcessedData) => void
}

export function AddTradeDialog({ open, onOpenChange, userId, accountId, onTradeAdded }: AddTradeDialogProps) {
  const [activeTab, setActiveTab] = useState("file-upload")

  const handleProcessedData = async (data: ProcessedData) => {
    if (!userId) {
      toast.error("Please sign in to upload trade data")
      return
    }

    try {
      // Save the data to the database
      await saveTradeData(userId, data, accountId || undefined)

      // Notify the parent component
      onTradeAdded(data)
      toast.success("Successfully imported trading history")
      onOpenChange(false)
    } catch (error) {
      console.error("Error saving trade data:", error)
      toast.error("Error saving trading data")
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-3xl">
        <DialogHeader>
          <DialogTitle>Add Trade</DialogTitle>
          <DialogDescription>
            Import your trades using one of the methods below.
          </DialogDescription>
        </DialogHeader>
        <Tabs defaultValue="file-upload" className="w-full" value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="file-upload">File Upload</TabsTrigger>
            <TabsTrigger value="broker-sync">Broker Sync</TabsTrigger>
            <TabsTrigger value="manual">Manual</TabsTrigger>
          </TabsList>
          <TabsContent value="file-upload" className="mt-4">
            <Card className="p-6">
              <div className="space-y-4">
                <div className="text-sm text-muted-foreground">
                  Upload your trading history file from your broker platform
                </div>
                <UploadDropzone onFileAccepted={handleProcessedData} />
                <div className="text-xs text-muted-foreground">
                  Supported formats: MT4/MT5 Excel reports (.xlsx, .xls) and CSV files (max 9MB)
                </div>
              </div>
            </Card>
          </TabsContent>
          <TabsContent value="broker-sync" className="mt-4">
            <Card className="p-6">
              <div className="text-sm text-muted-foreground">
                Coming soon: Direct broker synchronization
              </div>
            </Card>
          </TabsContent>
          <TabsContent value="manual" className="mt-4">
            <Card className="p-6">
              {userId && accountId ? (
                <ManualTradeForm userId={userId} accountId={accountId} onTradeAdded={handleProcessedData} />
              ) : userId ? (
                <div className="text-sm text-muted-foreground">
                  Please select an account to add trades manually
                </div>
              ) : (
                <div className="text-sm text-muted-foreground">
                  Please sign in to add trades manually
                </div>
              )}
            </Card>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  )
}