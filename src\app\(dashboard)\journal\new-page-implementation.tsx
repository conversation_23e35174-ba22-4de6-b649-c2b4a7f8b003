import { cookies } from 'next/headers';
import { createServerClient } from '@supabase/ssr';
import { redirect } from 'next/navigation';
import { format } from 'date-fns';
import JournalClient from './new-client';
import { fetchFilteredJournalData } from './filter-actions';
import { Database } from '@/types/supabase';

export default async function JournalPage({
  searchParams,
}: {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>
}) {
  // In Next.js 15, searchParams is a Promise that needs to be awaited
  const resolvedParams = await searchParams;

  // Initialize Supabase client
  const cookieStore = await cookies();
  const supabase = createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value;
        },
        set(_name, _value, _options) {
          // Server components can't set cookies directly
        },
        remove(_name, _options) {
          // Server components can't remove cookies directly
        }
      }
    }
  );

  // Get user session
  const {
    data: { user },
  } = await supabase.auth.getUser();

  // Redirect if not authenticated
  if (!user) {
    redirect('/login');
  }

  // Parse search parameters
  const searchTerm = typeof resolvedParams.searchTerm === 'string' ? resolvedParams.searchTerm : '';
  
  // Handle tags parameter (can be string or array)
  let tags: string[] = [];
  if (Array.isArray(resolvedParams.tags)) {
    tags = resolvedParams.tags;
  } else if (typeof resolvedParams.tags === 'string') {
    tags = [resolvedParams.tags];
  }
  
  const startDate = typeof resolvedParams.startDate === 'string' ? resolvedParams.startDate : undefined;
  const endDate = typeof resolvedParams.endDate === 'string' ? resolvedParams.endDate : undefined;
  const activeTab = typeof resolvedParams.tab === 'string' ? resolvedParams.tab : 'with-trades';

  // Fetch initial data
  try {
    console.log('Fetching initial journal data with parameters:', {
      userId: user.id,
      searchTerm,
      tags,
      startDate,
      endDate,
      activeTab,
    });

    const initialData = await fetchFilteredJournalData(
      user.id,
      null, // We'll let the client component handle account selection
      searchTerm,
      tags,
      startDate,
      endDate,
      activeTab
    );

    return (
      <div className="container mx-auto py-6">
        <JournalClient
          userId={user.id}
          initialData={initialData}
          initialSearchTerm={searchTerm}
          initialTags={tags}
          initialStartDate={startDate}
          initialEndDate={endDate}
          initialTab={activeTab}
        />
      </div>
    );
  } catch (error) {
    console.error('Error fetching initial journal data:', error);
    
    // Return error state
    return (
      <div className="container mx-auto py-6">
        <div className="bg-destructive/10 border border-destructive rounded-lg p-4 text-center">
          <h2 className="text-lg font-medium text-destructive">Error Loading Journal</h2>
          <p className="mt-2">
            There was an error loading your journal data. Please try refreshing the page.
          </p>
        </div>
      </div>
    );
  }
}
