-- Create daily_journal_entries table
CREATE TABLE IF NOT EXISTS public.daily_journal_entries (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    account_id UUID,
    date DATE NOT NULL,
    note TEXT,
    screenshots TEXT[] DEFAULT '{}',
    tags TEXT[] DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Add a unique constraint that handles NULL account_id values
CREATE UNIQUE INDEX daily_journal_entries_user_account_date_idx
ON public.daily_journal_entries (user_id, COALESCE(account_id, '********-0000-0000-0000-************'::uuid), date);

-- Add RLS policies for daily_journal_entries
ALTER TABLE public.daily_journal_entries ENABLE ROW LEVEL SECURITY;

-- Policy for selecting entries (users can only see their own entries)
CREATE POLICY "Users can view their own daily journal entries"
    ON public.daily_journal_entries
    FOR SELECT
    USING (auth.uid() = user_id);

-- Policy for inserting entries (users can only insert their own entries)
CREATE POLICY "Users can insert their own daily journal entries"
    ON public.daily_journal_entries
    FOR INSERT
    WITH CHECK (auth.uid() = user_id);

-- Policy for updating entries (users can only update their own entries)
CREATE POLICY "Users can update their own daily journal entries"
    ON public.daily_journal_entries
    FOR UPDATE
    USING (auth.uid() = user_id);

-- Policy for deleting entries (users can only delete their own entries)
CREATE POLICY "Users can delete their own daily journal entries"
    ON public.daily_journal_entries
    FOR DELETE
    USING (auth.uid() = user_id);

-- Create function to upsert daily journal entries
CREATE OR REPLACE FUNCTION public.upsert_daily_journal_entry(
    p_user_id UUID,
    p_account_id UUID,
    p_date DATE,
    p_note TEXT,
    p_screenshots TEXT[],
    p_tags TEXT[]
) RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    v_entry_id UUID;
    v_existing_id UUID;
BEGIN
    -- Check if entry already exists
    SELECT id INTO v_existing_id
    FROM public.daily_journal_entries
    WHERE user_id = p_user_id
    AND (
        (account_id IS NULL AND p_account_id IS NULL) OR
        account_id = p_account_id
    )
    AND date = p_date;

    IF v_existing_id IS NOT NULL THEN
        -- Update existing entry
        UPDATE public.daily_journal_entries
        SET
            note = p_note,
            screenshots = p_screenshots,
            tags = p_tags,
            updated_at = NOW()
        WHERE id = v_existing_id
        RETURNING id INTO v_entry_id;
    ELSE
        -- Insert new entry
        INSERT INTO public.daily_journal_entries (
            user_id,
            account_id,
            date,
            note,
            screenshots,
            tags,
            updated_at
        ) VALUES (
            p_user_id,
            p_account_id,
            p_date,
            p_note,
            p_screenshots,
            p_tags,
            NOW()
        )
        RETURNING id INTO v_entry_id;
    END IF;

    -- Return the entry ID
    RETURN v_entry_id;
END;
$$;

-- Create function to get daily journal entries
CREATE OR REPLACE FUNCTION public.get_daily_journal_entries(
    p_user_id UUID,
    p_account_id UUID,
    p_start_date DATE DEFAULT NULL,
    p_end_date DATE DEFAULT NULL,
    p_tags TEXT[] DEFAULT NULL
) RETURNS SETOF daily_journal_entries
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    RETURN QUERY
    SELECT *
    FROM public.daily_journal_entries
    WHERE user_id = p_user_id
    AND (p_account_id IS NULL OR account_id = p_account_id)
    AND (p_start_date IS NULL OR date >= p_start_date)
    AND (p_end_date IS NULL OR date <= p_end_date)
    AND (p_tags IS NULL OR tags && p_tags)
    ORDER BY date DESC;
END;
$$;
