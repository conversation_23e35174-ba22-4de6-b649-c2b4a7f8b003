import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { createServerClient } from '@supabase/ssr';
import type { Database } from '@/types/supabase';

// GET handler for fetching journal entries
export async function GET(request: NextRequest) {
  try {
    // Get the URL search params
    const searchParams = request.nextUrl.searchParams;
    const searchTerm = searchParams.get('searchTerm');
    const tags = searchParams.getAll('tags');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const accountId = searchParams.get('accountId');

    // Get cookies for server-side Supabase client
    const cookieStore = await cookies();
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
          set(_name: string, _value: string, _options: any) {
            // Server components can't set cookies directly
          },
          remove(_name: string, _options: any) {
            // Server components can't remove cookies directly
          }
        },
      }
    );

    // Get authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Start building the query
    let query = supabase
      .from('journal_entries')
      .select('*')
      .eq('user_id', user.id)
      .order('entry_date', { ascending: false });

    // Apply filters
    if (accountId) {
      query = query.eq('account_id', accountId);
    }

    if (startDate) {
      query = query.gte('entry_date', startDate);
    }

    if (endDate) {
      query = query.lte('entry_date', endDate);
    }

    if (tags && tags.length > 0) {
      // Filter entries that have at least one of the selected tags
      query = query.filter('tags', 'cs', `{${tags.join(',')}}`);
    }

    // Execute the query
    const { data, error } = await query;

    if (error) {
      console.error('Error fetching journal entries:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    // If there's a search term, filter the results client-side
    let filteredData = data || [];
    if (searchTerm && searchTerm.trim() !== '') {
      const term = searchTerm.toLowerCase().trim();
      filteredData = filteredData.filter(entry => {
        // Search in title
        if (entry.title && entry.title.toLowerCase().includes(term)) return true;

        // Search in content
        if (entry.content && entry.content.toLowerCase().includes(term)) return true;

        // Search in tags
        if (Array.isArray(entry.tags) && entry.tags.some((tag: string) => tag.toLowerCase().includes(term))) return true;

        return false;
      });
    }

    return NextResponse.json(filteredData);
  } catch (error) {
    console.error('Error in journal entries API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// POST handler for creating a new journal entry
export async function POST(request: NextRequest) {
  try {
    // Parse the request body
    const body = await request.json();

    // Get cookies for server-side Supabase client
    const cookieStore = await cookies();
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
          set(_name: string, _value: string, _options: any) {
            // Server components can't set cookies directly
          },
          remove(_name: string, _options: any) {
            // Server components can't remove cookies directly
          }
        },
      }
    );

    // Get authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Create the journal entry
    const { data, error } = await supabase
      .from('journal_entries')
      .insert({
        ...body,
        user_id: user.id,
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating journal entry:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in journal entries API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
