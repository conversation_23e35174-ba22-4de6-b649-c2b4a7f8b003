import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { createServerClient } from '@supabase/ssr';
import type { Database } from '@/types/supabase';
import { format } from 'date-fns';

// Helper function to compare dates (ignoring time)
const areDatesEqual = (date1: Date, date2: Date): boolean => {
  // Set both dates to UTC midnight to ensure proper comparison
  const date1UTC = new Date(Date.UTC(
    date1.getFullYear(),
    date1.getMonth(),
    date1.getDate()
  ));

  const date2UTC = new Date(Date.UTC(
    date2.getFullYear(),
    date2.getMonth(),
    date2.getDate()
  ));

  // Compare the UTC dates
  return date1UTC.getTime() === date2UTC.getTime();
}

// Function to calculate trading days from trades
const calculateTradingDays = (trades: any[]): string[] => {
  const days = new Set<string>();

  trades.forEach(trade => {
    try {
      const tradeDate = new Date(trade.time_close);
      // Format date as ISO string (YYYY-MM-DD)
      const dateStr = format(tradeDate, 'yyyy-MM-dd');
      days.add(dateStr);
    } catch (error) {
      console.error('Error processing trade date:', error);
    }
  });

  // Convert to array and sort in descending order (newest first)
  return Array.from(days).sort((a, b) => {
    const dateA = new Date(a);
    const dateB = new Date(b);
    return dateB.getTime() - dateA.getTime();
  });
}

// GET handler for fetching trades for journal
export async function GET(request: NextRequest) {
  try {
    // Get the URL search params
    const searchParams = request.nextUrl.searchParams;
    const searchTerm = searchParams.get('searchTerm');
    const tags = searchParams.getAll('tags');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const accountId = searchParams.get('accountId');

    // Get cookies for server-side Supabase client
    const cookieStore = await cookies();
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name) {
            return cookieStore.get(name)?.value;
          },
          set(_name, _value, _options) {
            // API routes can't set cookies directly
          },
          remove(_name, _options) {
            // API routes can't remove cookies directly
          }
        },
      }
    );

    // Get authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Start building the query
    let query = supabase
      .from('trades')
      .select('*')
      .eq('user_id', user.id)
      .order('time_close', { ascending: false });

    // Apply filters
    if (accountId) {
      query = query.eq('account_id', accountId);
    }

    if (startDate) {
      query = query.gte('time_close', `${startDate}T00:00:00`);
    }

    if (endDate) {
      query = query.lte('time_close', `${endDate}T23:59:59.999`);
    }

    // Apply server-side filtering for tags
    if (tags && tags.length > 0) {
      // Use Postgres array contains operator for tag filtering
      query = query.contains('tags', tags);
    }

    // Apply server-side filtering for search term
    if (searchTerm && searchTerm.trim() !== '') {
      const term = searchTerm.toLowerCase().trim();

      // Use Postgres OR conditions and ILIKE for case-insensitive search
      query = query.or(`notes.ilike.%${term}%,symbol.ilike.%${term}%`);

      // We can't directly search in the tags array with ILIKE
      // For tag search, we'll need to do a separate query or post-process
    }

    // Execute the query
    const { data, error } = await query;

    if (error) {
      console.error('Error fetching trades:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    // Post-process for tag search if needed
    let filteredData = data || [];

    // If we have a search term, we need to also check tags since we can't do that in the query
    if (searchTerm && searchTerm.trim() !== '') {
      const term = searchTerm.toLowerCase().trim();

      // Filter for tag matches that weren't caught by the OR condition
      const tagMatches = (data || []).filter(trade =>
        Array.isArray(trade.tags) &&
        trade.tags.some((tag: string) => tag.toLowerCase().includes(term))
      );

      // Combine and deduplicate results
      const allMatches = [...filteredData, ...tagMatches];
      const uniqueIds = new Set();
      filteredData = allMatches.filter(trade => {
        if (uniqueIds.has(trade.id)) return false;
        uniqueIds.add(trade.id);
        return true;
      });
    }

    // Calculate trading days and include them in the response
    const tradingDays = calculateTradingDays(filteredData);

    return NextResponse.json({
      trades: filteredData,
      tradingDays: tradingDays
    });
  } catch (error) {
    console.error('Error in journal trades API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
