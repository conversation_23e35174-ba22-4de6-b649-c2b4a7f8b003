import { NextRequest, NextResponse } from "next/server"
import { getSupabaseServer } from "@/lib/supabase-server"

export async function POST(request: NextRequest) {
  try {
    console.log('=== MANUAL DAILY JOURNAL TO NOTEBOOK MIGRATION API CALLED ===')

    const { dailyJournalId } = await request.json()

    if (!dailyJournalId) {
      return NextResponse.json(
        { error: "Daily journal ID is required" },
        { status: 400 }
      )
    }

    // Get authenticated user
    const supabase = await getSupabaseServer()
    const { data: { user }, error: userError } = await supabase.auth.getUser()

    if (userError || !user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    // Fetch the daily journal entry to get current data
    const { data: dailyJournal, error: fetchError } = await supabase
      .from("daily_journal_entries")
      .select("*")
      .eq("id", dailyJournalId)
      .eq("user_id", user.id)
      .single()

    if (fetchError || !dailyJournal) {
      return NextResponse.json(
        { error: "Daily journal entry not found" },
        { status: 404 }
      )
    }

    // Call the auto-migrate Edge Function manually
    const edgeFunctionUrl = `${process.env.NEXT_PUBLIC_SUPABASE_URL}/functions/v1/auto-migrate-to-notebook`
    const serviceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

    if (!serviceKey) {
      console.error('SUPABASE_SERVICE_ROLE_KEY not found in environment variables')
      return NextResponse.json(
        { error: "Service key not configured - check environment variables" },
        { status: 500 }
      )
    }

    console.log('Calling auto-migrate Edge Function:', edgeFunctionUrl)
    console.log('Daily journal data:', { 
      id: dailyJournal.id, 
      date: dailyJournal.date,
      hasNote: !!dailyJournal.note
    })

    const migrateResponse = await fetch(edgeFunctionUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${serviceKey}`,
      },
      body: JSON.stringify({
        type: 'INSERT',
        table: 'daily_journal_entries',
        record: dailyJournal,
        old_record: null
      })
    })

    if (!migrateResponse.ok) {
      const errorText = await migrateResponse.text()
      console.error('Auto-migrate function failed:', errorText)
      return NextResponse.json(
        { error: "Migration failed", details: errorText },
        { status: 500 }
      )
    }

    const migrateResult = await migrateResponse.json()
    console.log('Manual migration completed successfully:', migrateResult)

    return NextResponse.json({
      success: true,
      message: "Daily journal entry migrated to notebook successfully",
      migrated: migrateResult.migrated,
      notebookEntryId: migrateResult.notebookEntryId
    })

  } catch (error) {
    console.error('Error in manual daily journal migration:', error)
    return NextResponse.json(
      { error: "Internal server error", details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}
