"use client"

import { QueryClient, QueryClientProvider } from "@tanstack/react-query"
import { useState } from "react"

export function QueryProvider({ children }: { children: React.ReactNode }) {
  const [queryClient] = useState(() => new QueryClient({
    defaultOptions: {
      queries: {
        staleTime: 30 * 1000, // 30 seconds - optimized for real-time updates
        gcTime: 10 * 60 * 1000, // 10 minutes - keep data longer for better UX
        refetchOnWindowFocus: false, // Rely on real-time subscriptions
        refetchOnMount: false, // Don't refetch if we have fresh data
        retry: 1, // Reduce retries for faster error feedback
        retryDelay: 1000, // Faster retry delay
      },
      mutations: {
        retry: 1, // Reduce mutation retries
        retryDelay: 1000,
      },
    },
  }))

  return (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  )
}
