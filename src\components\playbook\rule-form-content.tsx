"use client"

import { useState, useEffect } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { RULE_TYPES } from "@/types/playbook"

import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Button } from "@/components/ui/button"
import { BookMarked, FileText, BarChart3, ListFilter, ArrowDownUp } from "lucide-react"

// Define the form schema
const formSchema = z.object({
  rule_type: z.enum(["entry", "exit", "stop_loss", "take_profit", "position_sizing", "other"]),
  name: z.string().min(1, "Name is required").max(100, "Name must be less than 100 characters"),
  description: z.string().optional(),
  priority: z.coerce.number().int().min(0),
})

// Use the inferred type from the schema
type FormValues = z.infer<typeof formSchema>

interface RuleFormContentProps {
  onSubmit: (data: FormValues) => void
  initialData?: FormValues | null
  isSubmitting?: boolean
  strategyId: string
  strategyName: string
}

export function RuleFormContent({
  onSubmit,
  initialData = null,
  isSubmitting = false,
  strategyId,
  strategyName
}: RuleFormContentProps) {
  // Initialize form with default values or existing rule values
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: initialData || {
      rule_type: "entry",
      name: "",
      description: "",
      priority: 0,
    },
  })

  // Update form when initialData changes
  useEffect(() => {
    if (initialData) {
      form.reset(initialData)
    }
  }, [initialData, form])

  // Get rule type label
  const getRuleTypeLabel = (type: string) => {
    const ruleType = RULE_TYPES.find(t => t.value === type)
    return ruleType ? ruleType.label : type.charAt(0).toUpperCase() + type.slice(1)
  }

  // Handle form submission
  const handleSubmit = (values: any) => {
    onSubmit(values as FormValues)
  }

  return (
    <div className="space-y-6">
      <div className="space-y-3 mb-6">
        <div className="flex items-center gap-2">
          <div className="p-2 rounded-full bg-blue-100 dark:bg-blue-950/50">
            <BookMarked className="h-5 w-5 text-blue-600 dark:text-blue-400" />
          </div>
          <div>
            <h3 className="text-lg font-medium">Rule Details for "{strategyName}"</h3>
            <p className="text-sm text-muted-foreground">
              Define trading rules for your strategy
            </p>
          </div>
        </div>
        <div className="h-1 w-full bg-gradient-to-r from-blue-200 to-indigo-200 dark:from-blue-900/40 dark:to-indigo-900/40 rounded-full"></div>
      </div>

      <Form {...form}>
        <form id="rule-form" onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
          <FormField
            control={form.control}
            name="rule_type"
            render={({ field }) => (
              <FormItem className="bg-card/30 p-4 rounded-lg border border-border/50 shadow-sm">
                <div className="flex items-center gap-2 mb-2">
                  <ListFilter className="h-4 w-4 text-blue-500" />
                  <FormLabel className="text-base">Rule Type</FormLabel>
                </div>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger className="border-blue-200 dark:border-blue-900/50 focus-visible:ring-blue-500">
                      <SelectValue placeholder="Select rule type" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {RULE_TYPES.map((type) => (
                      <SelectItem key={type.value} value={type.value}>
                        {type.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormDescription className="mt-2">
                  The type of trading rule
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem className="bg-card/30 p-4 rounded-lg border border-border/50 shadow-sm">
                <div className="flex items-center gap-2 mb-2">
                  <FileText className="h-4 w-4 text-indigo-500" />
                  <FormLabel className="text-base">Rule Name</FormLabel>
                </div>
                <FormControl>
                  <Input
                    placeholder={`${getRuleTypeLabel(form.watch("rule_type"))} Rule`}
                    {...field}
                    className="border-blue-200 dark:border-blue-900/50 focus-visible:ring-blue-500"
                  />
                </FormControl>
                <FormDescription className="mt-2">
                  <span>A clear, descriptive name for this rule. <strong>Keep it concise and specific.</strong></span>
                  <div className="mt-2 p-2 bg-muted/50 rounded-md text-xs">
                    <div className="font-medium mb-1">Examples:</div>
                    <div>• "Enter on Golden Cross"</div>
                    <div>• "Exit when RSI {'>'}  70"</div>
                    <div>• "Risk 2% per trade"</div>
                  </div>
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="description"
            render={({ field }) => (
              <FormItem className="bg-card/30 p-4 rounded-lg border border-border/50 shadow-sm">
                <div className="flex items-center gap-2 mb-2">
                  <BarChart3 className="h-4 w-4 text-purple-500" />
                  <FormLabel className="text-base">Description</FormLabel>
                </div>
                <FormControl>
                  <Textarea
                    placeholder="Enter when price breaks above the resistance level with increased volume..."
                    className="min-h-[120px] border-blue-200 dark:border-blue-900/50 focus-visible:ring-blue-500"
                    {...field}
                    value={field.value || ""}
                  />
                </FormControl>
                <FormDescription className="mt-2">
                  <span>Detailed description of this rule. <strong>Keep each rule focused on a single condition or action.</strong></span>
                  <div className="mt-2 p-2 bg-muted/50 rounded-md text-xs">
                    <div className="font-medium mb-1">Best practices:</div>
                    <div>• Focus on one specific rule per entry</div>
                    <div>• Be clear and specific about when and how to apply this rule</div>
                    <div>• Avoid combining multiple rules in one description</div>
                    <div>• This rule will appear as a checkbox in the trade details view</div>
                  </div>
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="priority"
            render={({ field }) => (
              <FormItem className="bg-card/30 p-4 rounded-lg border border-border/50 shadow-sm">
                <div className="flex items-center gap-2 mb-2">
                  <ArrowDownUp className="h-4 w-4 text-green-500" />
                  <FormLabel className="text-base">Priority</FormLabel>
                </div>
                <FormControl>
                  <Input
                    type="number"
                    min="0"
                    step="1"
                    {...field}
                    className="border-blue-200 dark:border-blue-900/50 focus-visible:ring-blue-500"
                  />
                </FormControl>
                <FormDescription className="mt-2">
                  Priority order (lower numbers have higher priority)
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Submit button moved to wizard footer */}
        </form>
      </Form>
    </div>
  )
}
