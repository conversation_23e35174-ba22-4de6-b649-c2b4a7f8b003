"use client"

import { useState, useEffect } from "react"
import { getSupabaseClient } from "@/lib/supabase-singleton"

interface User {
  id: string
  email?: string
}

export function useUser() {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const supabase = getSupabaseClient()

  useEffect(() => {
    const fetchUser = async () => {
      try {
        setLoading(true)

        // Get the authenticated user using getUser() for better security
        const { data: { user: authUser }, error: userError } = await supabase.auth.getUser()

        if (authUser && !userError) {
          setUser({
            id: authUser.id,
            email: authUser.email
          })
        } else {
          setUser(null)
        }
      } catch (error) {
        console.error("Error fetching user:", error)
        setUser(null)
      } finally {
        setLoading(false)
      }
    }

    fetchUser()

    // Set up auth state change listener
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (event, session) => {
        if (session?.user) {
          setUser({
            id: session.user.id,
            email: session.user.email
          })
        } else {
          setUser(null)
        }
        setLoading(false)
      }
    )

    return () => {
      subscription.unsubscribe()
    }
  }, [supabase])

  return { user, loading }
}

