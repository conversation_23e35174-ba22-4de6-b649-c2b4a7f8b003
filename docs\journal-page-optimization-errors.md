# Journal Page Optimization: Errors and Solutions

This document outlines the errors encountered during the server-side rendering (SSR) implementation and performance optimization of the Journal page, along with their solutions.

## Table of Contents

1. [Next.js 15 Async APIs](#nextjs-15-async-apis)
2. [TypeScript Type Errors](#typescript-type-errors)
3. [Symbol Conversion Error](#symbol-conversion-error)
4. [Server-Side Data Fetching Issues](#server-side-data-fetching-issues)
5. [Pagination Implementation Challenges](#pagination-implementation-challenges)

## Next.js 15 Async APIs

### Error

```
Error: Route "/journal" used `searchParams.tab`. `searchParams` should be awaited before using its properties. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis
    at JournalPage (src\app\(dashboard)\journal\page.tsx:38:33)
  36 |   const startDate = searchParams.startDate as string;
  37 |   const endDate = searchParams.endDate as string;
> 38 |   const activeTab = searchParams.tab as string || "all";
     |                                 ^
  39 |
  40 |   // Create a Supabase client for server-side operations
  41 |   // Use await with cookies() as it's now an async function in Next.js 15
```

### Solution

In Next.js 15, dynamic APIs like `searchParams` are now asynchronous and need to be awaited before accessing their properties. We updated the page component to properly handle this:

```typescript
export default async function JournalPage() {
  // Get the current URL
  const url = new URL(headers().get("x-url") || "http://localhost");
  
  // Extract search parameters from the URL
  const searchTerm = url.searchParams.get('searchTerm') || "";
  const tagsArray = url.searchParams.getAll('tags');
  const tags = tagsArray.length > 0 ? tagsArray : [];
  const startDate = url.searchParams.get('startDate') || undefined;
  const endDate = url.searchParams.get('endDate') || undefined;
  const activeTab = url.searchParams.get('tab') || "all";
}
```

However, this approach also had issues with the `headers()` function, which is also asynchronous in Next.js 15. We then tried a different approach:

```typescript
export default async function JournalPage({
  searchParams
}: {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>
}) {
  // In Next.js 15, searchParams is a Promise that needs to be awaited
  const resolvedParams = await searchParams;
  
  // Extract search parameters with proper type checking
  const searchTerm = typeof resolvedParams.searchTerm === 'string' ? resolvedParams.searchTerm : '';
  
  // Handle tags which could be a string, array, or undefined
  let tags: string[] = [];
  if (Array.isArray(resolvedParams.tags)) {
    tags = resolvedParams.tags;
  } else if (typeof resolvedParams.tags === 'string') {
    tags = [resolvedParams.tags];
  }
  
  // Handle date parameters
  const startDate = typeof resolvedParams.startDate === 'string' ? resolvedParams.startDate : undefined;
  const endDate = typeof resolvedParams.endDate === 'string' ? resolvedParams.endDate : undefined;
  
  // Handle tab parameter with default
  const activeTab = typeof resolvedParams.tab === 'string' ? resolvedParams.tab : 'all';
}
```

## Symbol Conversion Error

### Error

```
TypeError: Cannot convert a Symbol value to a string
    at JournalPage (rsc://React/Server/webpack-internal:///(rsc)/./src/app/(dashboard)/journal/page.tsx?0:30:131)
    at resolveErrorDev (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.browser.development.js:1845:46)
```

### Solution

This error occurred when trying to convert the `searchParams` object to a URL string. We fixed it by properly handling the Promise and using type checking:

```typescript
// Define default values for search parameters
let searchTerm = "";
let tags: string[] = [];
let startDate: string | undefined = undefined;
let endDate: string | undefined = undefined;
let activeTab = "all";

// If searchParams is provided, extract values safely
if (searchParams) {
  // We'll use a try-catch block to handle both synchronous and asynchronous cases
  try {
    if ('searchTerm' in searchParams) {
      searchTerm = (searchParams.searchTerm as string) || "";
    }
    
    if ('tags' in searchParams) {
      const tagsParam = searchParams.tags;
      tags = Array.isArray(tagsParam) ? tagsParam : tagsParam ? [tagsParam as string] : [];
    }
    
    if ('startDate' in searchParams) {
      startDate = searchParams.startDate as string;
    }
    
    if ('endDate' in searchParams) {
      endDate = searchParams.endDate as string;
    }
    
    if ('tab' in searchParams) {
      activeTab = (searchParams.tab as string) || "all";
    }
  } catch (error) {
    console.error('Error processing search parameters:', error);
    // Use default values if there's an error
  }
}
```

## TypeScript Type Errors

### Error

Multiple TypeScript errors related to implicit 'any' types and type compatibility:

```
Parameter 'dateStr' implicitly has an 'any' type.
Argument of type 'unknown' is not assignable to parameter of type 'string'.
```

### Solution

We fixed these errors by adding explicit type annotations and type assertions:

1. For the implicit 'any' type errors:
   ```typescript
   // Before
   setTradingDays(filteredData.tradingDays.map(dateStr => new Date(dateStr)));
   
   // After
   setTradingDays(filteredData.tradingDays.map((dateStr: string) => new Date(dateStr)));
   ```

2. For the 'unknown' type errors:
   ```typescript
   // Before
   Object.entries(filteredData.strategyMap).forEach(([key, value]) => {
     newStrategyMap.set(key, value);
   });
   
   // After
   Object.entries(filteredData.strategyMap).forEach(([key, value]) => {
     newStrategyMap.set(key, value as string);
   });
   ```

## Server-Side Data Fetching Issues

### Error

When implementing parallel data fetching, we encountered issues with the Supabase client and cookies:

```
Error: cookies() is a server function and cannot be used in a client component
```

### Solution

We ensured that all Supabase client creation and data fetching was done in server components:

```typescript
// Create a Supabase client for server-side operations
const cookieStore = await cookies();

// Create a Supabase client for server-side operations using the updated API
const supabase = createServerClient<Database>(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
  {
    cookies: {
      get(name) {
        return cookieStore.get(name)?.value;
      },
      set(_name, _value, _options) {
        // Server components can't set cookies directly
      },
      remove(_name, _options) {
        // Server components can't remove cookies directly
      }
    }
  }
);

// Fetch data in parallel for better performance
const [
  journalEntriesResult,
  tagsDataResult,
  dailyJournalEntriesResult
] = await Promise.all([
  // Fetch journal entries
  supabase
    .from("journal_entries")
    .select("*")
    .eq("user_id", userId)
    .order("entry_date", { ascending: false }),
  
  // Fetch all tags
  supabase
    .from("journal_entries")
    .select("tags")
    .eq("user_id", userId),
  
  // Fetch daily journal entries
  supabase.rpc(
    'get_daily_journal_entries',
    {
      p_user_id: userId,
      p_account_id: selectedAccountId || null,
      p_start_date: startDate || null,
      p_end_date: endDate || null,
      p_tags: tags.length > 0 ? tags : null
    }
  )
]);
```

## Pagination Implementation Challenges

### Error

When implementing pagination, we encountered issues with state management and data merging:

```
TypeError: Cannot read properties of undefined (reading 'hasMore')
```

### Solution

We implemented a proper pagination state and load more functionality:

1. Added pagination state:
   ```typescript
   const [paginationState, setPaginationState] = useState({
     currentPage: pagination?.currentPage || 1,
     totalPages: pagination?.totalPages || 1,
     pageSize: pagination?.pageSize || 50,
     totalCount: pagination?.totalCount || 0,
     isLoading: false,
     hasMore: pagination?.totalPages ? pagination.currentPage < pagination.totalPages : false
   });
   ```

2. Implemented load more functionality:
   ```typescript
   const loadMoreTrades = async () => {
     if (paginationState.isLoading || !paginationState.hasMore) return;
     
     try {
       setPaginationState(prev => ({ ...prev, isLoading: true }));
       
       // Use server action to fetch more trades
       const nextPage = paginationState.currentPage + 1;
       const filteredData = await fetchFilteredJournalData(
         userId,
         selectedAccountId,
         searchTerm,
         selectedTags,
         dateRange?.from ? format(dateRange.from, "yyyy-MM-dd") : undefined,
         dateRange?.to ? format(dateRange.to, "yyyy-MM-dd") : undefined,
         activeTab,
         nextPage,
         paginationState.pageSize,
         true // loadMore flag
       );
       
       // Append new trades to existing trades
       setAllTrades(prev => [...prev, ...filteredData.trades]);
       
       // Update pagination state
       setPaginationState(prev => ({
         ...prev,
         currentPage: nextPage,
         isLoading: false,
         hasMore: filteredData.pagination.hasMore
       }));
       
       // Update other state as needed
       setTradesWithJournalContent(prev => {
         const newTradesWithContent = filteredData.tradesWithJournalContent || [];
         return [...prev, ...newTradesWithContent];
       });
     } catch (error) {
       console.error("Error loading more trades:", error);
       setPaginationState(prev => ({ ...prev, isLoading: false }));
       toast.error("Failed to load more trades");
     }
   };
   ```

3. Added a "Load More" button to the UI:
   ```tsx
   {paginationState.hasMore && (
     <div className="flex justify-center mt-6">
       <Button
         variant="outline"
         onClick={loadMoreTrades}
         disabled={paginationState.isLoading}
         className="w-full max-w-xs"
       >
         {paginationState.isLoading ? (
           <>
             <Loader2 className="mr-2 h-4 w-4 animate-spin" />
             Loading...
           </>
         ) : (
           <>Load More Trades</>
         )}
       </Button>
     </div>
   )}
   ```

These solutions have successfully addressed all the errors we encountered during the server-side rendering and performance optimization process for the Journal page.
