"use client"

import { useState, useEffect } from "react"
import { format } from "date-fns"
import { Calendar as CalendarIcon, Save, Loader2 } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { cn } from "@/lib/utils"
import { RichTextEditor } from "@/components/rich-text-editor"
import { TagInput } from "@/components/tag-input"
import { JournalEntry, JournalEntryInsert, getAllTags } from "@/lib/journal-service"

interface JournalEntryFormProps {
  entry?: JournalEntry
  onSubmit: (entry: Omit<JournalEntryInsert, "user_id">) => Promise<void>
  onCancel?: () => void
  userId: string
  tradeId?: string
  className?: string
}

export function JournalEntryForm({
  entry,
  onSubmit,
  onCancel,
  userId,
  tradeId,
  className
}: JournalEntryFormProps) {
  const [title, setTitle] = useState(entry?.title || "")
  const [content, setContent] = useState(entry?.content || "")
  const [tags, setTags] = useState<string[]>(entry?.tags || [])
  const [date, setDate] = useState<Date>(entry?.entry_date ? new Date(entry.entry_date) : new Date())
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [availableTags, setAvailableTags] = useState<string[]>([])

  useEffect(() => {
    // Fetch all available tags for suggestions
    const fetchTags = async () => {
      const tags = await getAllTags(userId)
      setAvailableTags(tags)
    }

    fetchTags()
  }, [userId])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!title.trim()) {
      alert("Please enter a title for your journal entry")
      return
    }
    
    try {
      setIsSubmitting(true)
      
      await onSubmit({
        title,
        content,
        tags,
        entry_date: format(date, "yyyy-MM-dd"),
        trade_id: tradeId || entry?.trade_id || null
      })
      
    } catch (error) {
      console.error("Error submitting journal entry:", error)
      alert("Failed to save journal entry. Please try again.")
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Card className={cn("w-full", className)}>
      <form onSubmit={handleSubmit}>
        <CardHeader>
          <CardTitle>{entry ? "Edit Journal Entry" : "New Journal Entry"}</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="title">Title</Label>
            <Input
              id="title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="Enter a title for your journal entry"
              required
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="date">Date</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "w-full justify-start text-left font-normal",
                    !date && "text-muted-foreground"
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {date ? format(date, "PPP") : "Select a date"}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <Calendar
                  mode="single"
                  selected={date}
                  onSelect={(date) => date && setDate(date)}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="content">Content</Label>
            <RichTextEditor
              value={content}
              onChange={setContent}
              placeholder="Write your journal entry here..."
              minHeight="300px"
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="tags">Tags</Label>
            <TagInput
              value={tags}
              onChange={setTags}
              placeholder="Add tags..."
              suggestions={availableTags}
            />
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          {onCancel && (
            <Button type="button" variant="outline" onClick={onCancel}>
              Cancel
            </Button>
          )}
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                Save Entry
              </>
            )}
          </Button>
        </CardFooter>
      </form>
    </Card>
  )
}

export default JournalEntryForm
