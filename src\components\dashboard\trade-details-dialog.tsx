"use client"

import { useState, useEffect } from "react"
import { format } from "date-fns"
import { useRouter } from "next/navigation"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription
} from "@/components/ui/dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import { type ProcessedData } from "@/lib/excel-processor"
import { JournalDetailsDialog } from "@/components/journal-details-dialog"
import { hasJournalEntry, getJournalEntryForDate } from "@/lib/journal-utils"
import { BookOpen, ExternalLink } from "lucide-react"
import { getStrategies } from "@/lib/playbook-service"
import { useUser } from "@/hooks/use-user"
import type { Trade as BaseTrade } from "@/types/trade"

// Extend the base Trade type to include strategy_name
interface Trade extends BaseTrade {
  strategy_name?: string | null
}

interface TradeDetailsDialogProps {
  isOpen: boolean
  onOpenChange: (open: boolean) => void
  selectedDate: Date | undefined
  selectedTrades: Trade[]
  tradesPerPage?: number
}

export function TradeDetailsDialog({
  isOpen,
  onOpenChange,
  selectedDate,
  selectedTrades,
  tradesPerPage = 10
}: TradeDetailsDialogProps) {
  const router = useRouter()
  const { user } = useUser()
  const [currentPage, setCurrentPage] = useState(1)
  const [isJournalDialogOpen, setIsJournalDialogOpen] = useState(false)
  const [strategies, setStrategies] = useState<any[]>([])
  const [tradesWithStrategy, setTradesWithStrategy] = useState<Trade[]>([])

  // Function to navigate to trade details page
  const navigateToTradeDetails = (tradeId: string) => {
    // Add source=calendar to the URL to indicate where the user is coming from
    router.push(`/trades/${tradeId}?source=calendar`)
    onOpenChange(false) // Close the dialog
  }

  // Fetch strategies when component mounts
  useEffect(() => {
    const fetchStrategies = async () => {
      if (!user?.id) return

      try {
        const strategiesData = await getStrategies(user.id)
        setStrategies(strategiesData)
      } catch (error) {
        console.error("Error fetching strategies:", error)
      }
    }

    fetchStrategies()
  }, [user?.id])

  // Add strategy names to trades
  useEffect(() => {
    if (!selectedTrades || !strategies.length) {
      setTradesWithStrategy(selectedTrades)
      return
    }

    const tradesWithStrategyNames = selectedTrades.map(trade => {
      const strategy = strategies.find(s => s.id === trade.strategy_id)
      return {
        ...trade,
        strategy_name: strategy?.name || null
      }
    })

    setTradesWithStrategy(tradesWithStrategyNames)
  }, [selectedTrades, strategies])

  // Calculate pagination
  const totalPages = Math.ceil(tradesWithStrategy.length / tradesPerPage)
  const startIndex = (currentPage - 1) * tradesPerPage
  const endIndex = startIndex + tradesPerPage
  const currentTrades = tradesWithStrategy.slice(startIndex, endIndex)

  // Calculate daily totals
  const totalProfit = tradesWithStrategy.reduce((sum, trade) => sum + trade.profit, 0)
  const winningTrades = tradesWithStrategy.filter(trade => trade.profit > 0)
  const losingTrades = tradesWithStrategy.filter(trade => trade.profit < 0)
  const winRate = tradesWithStrategy.length > 0
    ? (winningTrades.length / tradesWithStrategy.length) * 100
    : 0

  // State to track if there's a journal entry for this date
  const [hasJournal, setHasJournal] = useState(false)
  const [isCheckingJournal, setIsCheckingJournal] = useState(false)

  // Check if there's a journal entry for this date
  useEffect(() => {
    if (!selectedDate || !user?.id) {
      setHasJournal(false)
      return
    }

    // Immediately check cache for instant feedback
    const cachedEntry = hasJournalEntry(selectedDate)
    setHasJournal(cachedEntry)

    // Only do async check if cache doesn't show an entry
    if (!cachedEntry) {
      setIsCheckingJournal(true)

      const checkForJournalEntry = async () => {
        try {
          // Get the selected account ID from localStorage
          const selectedAccountId = localStorage.getItem('selectedAccountId')

          // Fetch the journal entry
          const entry = await getJournalEntryForDate(selectedDate, user.id, selectedAccountId || undefined)
          const hasEntry = !!(entry?.note || (entry?.screenshots && entry.screenshots.length > 0))
          setHasJournal(hasEntry)
        } catch (error) {
          console.error("Error checking for journal entry:", error)
          setHasJournal(false)
        } finally {
          setIsCheckingJournal(false)
        }
      }

      checkForJournalEntry()
    }
  }, [selectedDate, user?.id])

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[85vh] overflow-y-auto">
        <DialogHeader className="pr-8">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
            <div>
              <DialogTitle>
                Trades for {selectedDate ? format(selectedDate, "MMMM d, yyyy") : ""}
              </DialogTitle>
              <DialogDescription>
                Summary of trading activity and performance metrics for this day.
              </DialogDescription>
            </div>
          </div>
        </DialogHeader>

        {/* View Journal Button - Moved below header to avoid overlap with close button */}
        {(hasJournal || isCheckingJournal) && (
          <div className="flex justify-end mb-4">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsJournalDialogOpen(true)}
              disabled={isCheckingJournal}
            >
              <BookOpen className="h-4 w-4 mr-2 text-foreground dark:text-foreground" />
              View Journal
            </Button>
          </div>
        )}

        {/* Daily Summary */}
        <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 mb-4">
          <div className="dashboard-card bg-card p-3 rounded-md">
            <div className="text-sm text-muted-foreground">Total P&L</div>
            <div className={cn(
              "text-lg font-bold",
              totalProfit >= 0 ? "text-emerald-500" : "text-rose-500"
            )}>
              ${totalProfit.toFixed(2)}
            </div>
          </div>
          <div className="dashboard-card bg-card p-3 rounded-md">
            <div className="text-sm text-muted-foreground">Trades</div>
            <div className="text-lg font-bold">{selectedTrades.length}</div>
          </div>
          <div className="dashboard-card bg-card p-3 rounded-md">
            <div className="text-sm text-muted-foreground">Win/Loss</div>
            <div className="text-lg font-bold">
              {winningTrades.length}/{losingTrades.length}
            </div>
          </div>
          <div className="dashboard-card bg-card p-3 rounded-md">
            <div className="text-sm text-muted-foreground">Win Rate</div>
            <div className="text-lg font-bold">{winRate.toFixed(1)}%</div>
          </div>
        </div>

        {/* Trades Table */}
        <div className="border rounded-md overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full min-w-full">
              <thead>
                <tr className="bg-muted/50">
                  <th className="px-2 sm:px-4 py-2 text-left text-xs font-medium text-muted-foreground">Symbol</th>
                  <th className="px-2 sm:px-4 py-2 text-left text-xs font-medium text-muted-foreground">Type</th>
                  <th className="px-2 sm:px-4 py-2 text-left text-xs font-medium text-muted-foreground hidden xs:table-cell">Entry</th>
                  <th className="px-2 sm:px-4 py-2 text-left text-xs font-medium text-muted-foreground hidden xs:table-cell">Exit</th>
                  <th className="px-2 sm:px-4 py-2 text-left text-xs font-medium text-muted-foreground hidden sm:table-cell">Duration</th>
                  <th className="px-2 sm:px-4 py-2 text-left text-xs font-medium text-muted-foreground hidden md:table-cell">Strategy</th>
                  <th className="px-2 sm:px-4 py-2 text-right text-xs font-medium text-muted-foreground">P&L</th>
                  <th className="px-2 sm:px-4 py-2 text-center text-xs font-medium text-muted-foreground">Details</th>
                </tr>
              </thead>
            <tbody>
              {currentTrades.map((trade, i) => {
                const entryTime = new Date(trade.time_open)
                const exitTime = new Date(trade.time_close)
                const durationMs = exitTime.getTime() - entryTime.getTime()
                const durationMinutes = Math.floor(durationMs / (1000 * 60))

                return (
                  <tr key={i} className="border-t hover:bg-muted/50 cursor-pointer" onClick={() => navigateToTradeDetails(String(trade.id))}>
                    <td className="px-2 sm:px-4 py-2 text-xs sm:text-sm">{trade.symbol}</td>
                    <td className="px-2 sm:px-4 py-2 text-xs sm:text-sm">
                      <span className={trade.type === "buy" ? "text-emerald-500" : "text-rose-500"}>
                        {trade.type.toUpperCase()}
                      </span>
                    </td>
                    <td className="px-2 sm:px-4 py-2 text-xs sm:text-sm hidden xs:table-cell">{format(entryTime, "HH:mm:ss")}</td>
                    <td className="px-2 sm:px-4 py-2 text-xs sm:text-sm hidden xs:table-cell">{format(exitTime, "HH:mm:ss")}</td>
                    <td className="px-2 sm:px-4 py-2 text-xs sm:text-sm hidden sm:table-cell">{durationMinutes}m</td>
                    <td className="px-2 sm:px-4 py-2 text-xs sm:text-sm hidden md:table-cell">
                      {trade.strategy_name ? (
                        <span className="px-2 py-0.5 bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-400 rounded-full text-xs">
                          {trade.strategy_name}
                        </span>
                      ) : (
                        <span className="text-muted-foreground text-xs">—</span>
                      )}
                    </td>
                    <td className={cn(
                      "px-2 sm:px-4 py-2 text-xs sm:text-sm text-right font-medium",
                      trade.profit >= 0 ? "text-emerald-500" : "text-rose-500"
                    )}>
                      ${trade.profit.toFixed(2)}
                    </td>
                    <td className="px-2 sm:px-4 py-2 text-center">
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-6 w-6"
                        onClick={(e) => {
                          e.stopPropagation(); // Prevent row click
                          navigateToTradeDetails(String(trade.id));
                        }}
                      >
                        <ExternalLink className="h-3.5 w-3.5 text-muted-foreground" />
                      </Button>
                    </td>
                  </tr>
                )
              })}
            </tbody>
            </table>
          </div>
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex justify-between items-center mt-4">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(p => Math.max(1, p - 1))}
              disabled={currentPage === 1}
              className="text-xs sm:text-sm px-2 sm:px-4"
            >
              <span className="hidden xs:inline">Previous</span>
              <span className="xs:hidden">Prev</span>
            </Button>
            <div className="text-xs sm:text-sm text-muted-foreground">
              {currentPage}/{totalPages}
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(p => Math.min(totalPages, p + 1))}
              disabled={currentPage === totalPages}
              className="text-xs sm:text-sm px-2 sm:px-4"
            >
              Next
            </Button>
          </div>
        )}
      </DialogContent>

      {/* Journal Details Dialog */}
      <JournalDetailsDialog
        isOpen={isJournalDialogOpen}
        onOpenChange={setIsJournalDialogOpen}
        selectedDate={selectedDate}
        selectedTrades={selectedTrades}
      />
    </Dialog>
  )
}
