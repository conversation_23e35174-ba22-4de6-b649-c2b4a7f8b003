"use client"

import { useEffect, useState, useRef, useCallback, forwardRef, useImperativeHandle } from "react"
import { QuillNotebookEditor } from "@/components/notebook/quill-notebook-editor-new"
import { ScreenshotDisplay } from "@/components/notebook/screenshot-display"
import { EditorContent } from "@/types/notebook"

interface NewNotebookEditorProps {
  content?: EditorContent | null
  htmlContent?: string | null
  screenshots?: string[]
  onChange?: (content: EditorContent, htmlContent: string) => void
  onScreenshotsChange?: (screenshots: string[]) => void
  onSave?: () => void  // Kept for API compatibility but not used directly in this component
  onCancel?: () => void  // Kept for API compatibility but not used directly in this component
  readOnly?: boolean
  autoFocus?: boolean
  className?: string
  noteId?: string | null
  isEditing?: boolean
}

export const NewNotebookEditor = forwardRef<any, NewNotebookEditorProps>(({
  content,
  htmlContent,
  screenshots = [],
  onChange,
  onScreenshotsChange,
  onSave,
  onCancel,
  readOnly = false,
  autoFocus = false,
  className,
  noteId,
  isEditing = false
}, ref) => {
  const [editorContent, setEditorContent] = useState<EditorContent | null>(null)
  const [isInitialized, setIsInitialized] = useState(false)
  const editorRef = useRef<any>(null)

  // Expose editor methods to parent component
  useImperativeHandle(ref, () => ({
    focus: () => {
      if (editorRef.current && typeof editorRef.current.focus === 'function') {
        editorRef.current.focus()
      }
    },
    getEditor: () => editorRef.current
  }), [])

  // Initialize editor content
  useEffect(() => {
    try {
      // Process content
      let contentToUse;

      if (content) {
        // If content is a string (JSON string), parse it
        contentToUse = typeof content === 'string'
          ? JSON.parse(content)
          : content;
      } else {
        // Initialize with empty document
        contentToUse = {
          type: "doc",
          content: [
            {
              type: "paragraph",
              content: []
            }
          ]
        };
      }

      // For QuillNotebookEditor, we can pass the content object directly
      setEditorContent(contentToUse);

      // Mark as initialized
      setIsInitialized(true);
    } catch (error) {
      console.error("Error parsing editor content:", error);
      // Initialize with empty document
      setEditorContent({
        type: "doc",
        content: [
          {
            type: "paragraph",
            content: []
          }
        ]
      });

      setIsInitialized(true);
    }
  }, [content, noteId, isEditing]);

  // Handle editor content change with debounce to prevent cursor issues
  const handleEditorChange = useCallback((jsonContent: EditorContent, html: string) => {
    if (!isInitialized) return

    try {
      // Call onChange callback if provided
      if (onChange) {
        onChange(jsonContent, html)
      }
    } catch (error) {
      console.error("Error handling editor change:", error)
    }
  }, [isInitialized, onChange])

  return (
    <div className={`flex flex-col h-full pr-4 ${className}`}>
      {/* Editor takes most space and handles its own scrolling */}
      <div className="flex-grow overflow-hidden pr-2">
        {isInitialized && (
          <QuillNotebookEditor
            key={`note-editor-${noteId || 'new'}-${isEditing ? 'edit' : 'view'}`} // Add key to force re-render when note changes
            ref={editorRef}
            content={editorContent}
            htmlContent={htmlContent}
            onChange={handleEditorChange}
            editable={isEditing && !readOnly} // Only editable in edit mode and not readOnly
            autofocus={autoFocus && isEditing}
            className="notebook-editor-instance h-full"
          />
        )}
      </div>

      {/* Screenshots Section - fixed at bottom */}
      <div className="border-t mt-4 pt-4 flex-shrink-0">
        <ScreenshotDisplay
          screenshots={screenshots}
          isEditing={isEditing && !readOnly}
          onChange={onScreenshotsChange}
        />
      </div>
    </div>
  )
})

NewNotebookEditor.displayName = 'NewNotebookEditor'
