# Analytics Page SSR Implementation Plan

This document outlines the detailed implementation plan for migrating the Analytics page from client-side rendering to server-side rendering while maintaining the current UI and functionality.

## Current Implementation Analysis

The current Analytics page (`src/app/(dashboard)/analytics/page.tsx`) is implemented as a client component with the following characteristics:

1. **Authentication**: Uses `getSupabaseClient()` to get the Supabase client and checks for an authenticated user.
2. **Data Fetching**: Fetches trades and summary data using `getTrades()` and `getUserSummary()` functions.
3. **State Management**: Uses several state variables to manage the UI state:
   - `activeTab`: Controls which analytics tab is currently active
   - `trades`: Stores the fetched trade data
   - `isLoading`: Tracks loading state
   - `userId`: Stores the authenticated user ID
   - `initialBalance`: Stores the initial account balance
4. **Account Context**: Uses the `useAccount` hook to get the selected account ID.
5. **UI Components**: Renders various analytics components based on the active tab.

## Implementation Steps

### Step 1: Create Server Component

Create a new server component that will handle authentication and initial data fetching:

```tsx
// src/app/(dashboard)/analytics/page.tsx
import { cookies } from 'next/headers';
import { createServerClient } from '@supabase/ssr';
import { redirect } from 'next/navigation';
import type { Database } from '@/types/supabase';
import ClientWrapper from './client-wrapper';

export default async function AnalyticsPage() {
  // Get cookies for server-side Supabase client
  const cookieStore = await cookies();
  const supabase = createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value;
        },
        set(_name: string, _value: string, _options: any) {
          // Server components can't set cookies directly
        },
        remove(_name: string, _options: any) {
          // Server components can't remove cookies directly
        }
      },
    }
  );

  // Get authenticated user
  const { data: { user }, error: userError } = await supabase.auth.getUser();
  if (userError || !user) {
    redirect('/login');
  }

  const userId = user.id;

  // Fetch user accounts
  const { data: accounts, error: accountsError } = await supabase
    .from("trading_accounts")
    .select("*")
    .eq("user_id", userId)
    .order("updated_at", { ascending: false });

  if (accountsError) {
    console.error('Error fetching trading accounts:', accountsError);
    // Continue with empty accounts array
  }

  // Get the selected account ID from the first account (if any)
  const selectedAccountId = accounts && accounts.length > 0 ? accounts[0].id : null;

  // Fetch trades for the selected account
  let trades = [];
  if (selectedAccountId) {
    const { data: tradesData, error: tradesError } = await supabase
      .from("trades")
      .select("*")
      .eq("account_id", selectedAccountId)
      .order("time_close", { ascending: false });

    if (tradesError) {
      console.error('Error fetching trades:', tradesError);
    } else {
      trades = tradesData || [];
    }
  }

  // Fetch trading summary for the selected account
  let summary = null;
  if (selectedAccountId) {
    const { data: summaryData, error: summaryError } = await supabase
      .from("trading_summaries")
      .select("*")
      .eq("account_id", selectedAccountId)
      .maybeSingle();

    if (summaryError) {
      console.error('Error fetching trading summary:', summaryError);
    } else {
      summary = summaryData;
    }
  }

  // Pass the fetched data to the client component
  return (
    <ClientWrapper
      userId={userId}
      initialTrades={trades || []}
      initialSummary={summary}
      selectedAccountId={selectedAccountId}
    />
  );
}
```

### Step 2: Create Client Wrapper

Create a client wrapper component that will dynamically import the client component:

```tsx
// src/app/(dashboard)/analytics/client-wrapper.tsx
"use client"

import dynamic from 'next/dynamic';
import { Trade } from '@/types/trade';

// Dynamically import the client component with no SSR
const AnalyticsClient = dynamic(() => import('./client'), {
  ssr: false
});

interface ClientWrapperProps {
  userId: string;
  initialTrades: Trade[];
  initialSummary: any | null;
  selectedAccountId: string | null;
}

export default function ClientWrapper({ 
  userId, 
  initialTrades, 
  initialSummary, 
  selectedAccountId 
}: ClientWrapperProps) {
  return (
    <AnalyticsClient
      userId={userId}
      initialTrades={initialTrades}
      initialSummary={initialSummary}
      selectedAccountId={selectedAccountId}
    />
  );
}
```

### Step 3: Create Client Component

Create a client component that will handle the UI logic and state management:

```tsx
// src/app/(dashboard)/analytics/client.tsx
"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { SymbolPerformance } from "@/components/analytics/symbol-performance"
import { TimeOfDayAnalysis } from "@/components/analytics/time-of-day-analysis"
import { TradeDurationAnalysis } from "@/components/analytics/trade-duration-analysis"
import { DrawdownAnalysis } from "@/components/analytics/drawdown-analysis"
import { ConsecutiveTradesAnalysis } from "@/components/analytics/consecutive-trades-analysis"
import { RiskManagementMetrics } from "@/components/analytics/risk-management-metrics"
import { BarChart3, Clock, Loader2, ShieldAlert, Timer, TrendingDown, Zap } from "lucide-react"
import { Trade } from "@/types/trade"
import { useAccount } from "@/contexts/account-context"
import { toast } from "sonner"

interface AnalyticsClientProps {
  userId: string;
  initialTrades: Trade[];
  initialSummary: any | null;
  selectedAccountId: string | null;
}

export default function AnalyticsClient({
  userId,
  initialTrades,
  initialSummary,
  selectedAccountId: initialAccountId
}: AnalyticsClientProps) {
  const router = useRouter()
  const [activeTab, setActiveTab] = useState("symbol-performance")
  const [trades, setTrades] = useState<Trade[]>(initialTrades)
  const [isLoading, setIsLoading] = useState(false)
  const [initialBalance, setInitialBalance] = useState<number>(
    initialSummary?.initial_balance || 10000
  )
  const { selectedAccountId } = useAccount()

  // Fetch trades when selected account changes
  useEffect(() => {
    if (selectedAccountId !== initialAccountId) {
      fetchTrades()
    }
  }, [selectedAccountId, initialAccountId])

  // Function to fetch trades
  const fetchTrades = async () => {
    if (!selectedAccountId) return

    try {
      setIsLoading(true)
      
      // Fetch trades from API
      const response = await fetch(`/api/analytics-data?accountId=${selectedAccountId}`)
      if (!response.ok) throw new Error('Failed to fetch analytics data')
      const data = await response.json()
      
      setTrades(data.trades || [])
      setInitialBalance(data.summary?.initial_balance || 10000)
    } catch (error) {
      console.error("Error fetching analytics data:", error)
      toast.error("Failed to load analytics data")
    } finally {
      setIsLoading(false)
    }
  }

  // Rest of the component remains the same as the original implementation
  // ...

  // Return the UI
  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-[calc(100vh-200px)]">
        <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
      </div>
    )
  }

  if (selectedAccountId === null) {
    return (
      <div className="container py-6 space-y-6">
        <div>
          <h1 className="text-2xl font-semibold">Advanced Analytics</h1>
          <p className="text-muted-foreground">
            Gain deeper insights into your trading performance with advanced analytics
          </p>
        </div>

        <Card className="p-8 text-center">
          {/* No account selected UI */}
        </Card>
      </div>
    )
  }

  return (
    <div className="container py-6 space-y-6">
      {/* Analytics UI */}
    </div>
  )
}
```

### Step 4: Create API Routes

Create API routes for client-side data fetching:

```tsx
// src/app/api/analytics-data/route.ts
import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { createServerClient } from '@supabase/ssr';
import type { Database } from '@/types/supabase';

export async function GET(request: Request) {
  try {
    // Get URL parameters
    const url = new URL(request.url);
    const accountId = url.searchParams.get('accountId');

    if (!accountId) {
      return NextResponse.json({ error: 'Account ID is required' }, { status: 400 });
    }

    // Get cookies for server-side Supabase client
    const cookieStore = await cookies();
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
          set(_name: string, _value: string, _options: any) {
            // API routes can't set cookies directly
          },
          remove(_name: string, _options: any) {
            // API routes can't remove cookies directly
          }
        },
      }
    );

    // Get authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    const userId = user.id;

    // Fetch trades
    const { data: trades, error: tradesError } = await supabase
      .from("trades")
      .select("*")
      .eq("user_id", userId)
      .eq("account_id", accountId)
      .order("time_close", { ascending: false });

    if (tradesError) {
      return NextResponse.json({ error: tradesError.message }, { status: 500 });
    }

    // Fetch summary
    const { data: summary, error: summaryError } = await supabase
      .from("trading_summaries")
      .select("*")
      .eq("account_id", accountId)
      .maybeSingle();

    if (summaryError) {
      return NextResponse.json({ error: summaryError.message }, { status: 500 });
    }

    return NextResponse.json({
      trades: trades || [],
      summary: summary || null
    });
  } catch (error) {
    console.error('Error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
```

## Testing Plan

1. **Authentication Testing**:
   - Verify that unauthenticated users are redirected to the login page.
   - Verify that authenticated users can access the analytics page.

2. **Data Loading Testing**:
   - Verify that initial data is loaded correctly from the server.
   - Verify that data is refreshed when the selected account changes.

3. **UI Testing**:
   - Verify that all tabs and charts render correctly.
   - Verify that the loading state is displayed appropriately.
   - Verify that the "No Account Selected" state is displayed when no account is selected.

4. **Performance Testing**:
   - Verify that the page loads quickly.
   - Verify that tab switching is responsive.

## Implementation Checklist

- [ ] Create server component (`page.tsx`)
- [ ] Create client wrapper (`client-wrapper.tsx`)
- [ ] Create client component (`client.tsx`)
- [ ] Create API route (`/api/analytics-data/route.ts`)
- [ ] Test authentication flow
- [ ] Test data loading
- [ ] Test UI rendering
- [ ] Test performance
