"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { PersonalInfoForm } from "@/components/profile/personal-info-form"
import { PasswordChangeForm } from "@/components/profile/password-change-form"
import { ProfilePictureUpload } from "@/components/profile/profile-picture-upload"
import { AccountPreferences } from "@/components/profile/account-preferences"

interface ProfileClientProps {
  userId: string;
  userEmail: string | undefined;
  userName: string;
  profileData: any | null;
  preferencesData: any | null;
  avatarUrl: string | null;
}

export default function ProfileClient({
  userId,
  userEmail,
  userName,
  profileData,
  preferencesData,
  avatarUrl
}: ProfileClientProps) {
  const [activeTab, setActiveTab] = useState("personal-info")

  return (
    <div className="container py-6 space-y-6">
      <div>
        <h1 className="text-2xl font-semibold">Profile Settings</h1>
        <p className="text-muted-foreground">
          Manage your account settings and preferences
        </p>
      </div>

      <div className="bg-yellow-500/10 border border-yellow-500/50 rounded-md p-4 text-sm">
        <h3 className="font-medium text-yellow-500 mb-1">Database Setup Required</h3>
        <p className="text-muted-foreground">
          Some profile features may be limited until the database setup is complete. Please refer to the <code className="bg-muted px-1 py-0.5 rounded">PROFILE_SETUP.md</code> file for instructions on how to set up the required database tables and storage buckets.
        </p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid grid-cols-4 w-full max-w-3xl">
          <TabsTrigger value="personal-info">Personal Info</TabsTrigger>
          <TabsTrigger value="password">Password</TabsTrigger>
          <TabsTrigger value="profile-picture">Profile Picture</TabsTrigger>
          <TabsTrigger value="preferences">Preferences</TabsTrigger>
        </TabsList>

        <TabsContent value="personal-info">
          <Card>
            <CardHeader>
              <CardTitle>Personal Information</CardTitle>
              <CardDescription>
                Update your personal information and contact details
              </CardDescription>
            </CardHeader>
            <CardContent>
              <PersonalInfoForm 
                userId={userId}
                userEmail={userEmail}
                initialData={{
                  full_name: profileData?.full_name || userName,
                  email: profileData?.email || userEmail || '',
                  phone: profileData?.phone || '',
                  bio: profileData?.bio || '',
                  location: profileData?.location || '',
                  website: profileData?.website || '',
                }}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="password">
          <Card>
            <CardHeader>
              <CardTitle>Change Password</CardTitle>
              <CardDescription>
                Update your password to keep your account secure
              </CardDescription>
            </CardHeader>
            <CardContent>
              <PasswordChangeForm />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="profile-picture">
          <Card>
            <CardHeader>
              <CardTitle>Profile Picture</CardTitle>
              <CardDescription>
                Upload or update your profile picture
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ProfilePictureUpload 
                userId={userId}
                userName={profileData?.full_name || userName}
                initialAvatarUrl={avatarUrl}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="preferences">
          <Card>
            <CardHeader>
              <CardTitle>Account Preferences</CardTitle>
              <CardDescription>
                Customize your account settings and preferences
              </CardDescription>
            </CardHeader>
            <CardContent>
              <AccountPreferences 
                userId={userId}
                initialData={{
                  theme: preferencesData?.theme || 'system',
                  emailNotifications: preferencesData?.email_notifications !== undefined ? preferencesData.email_notifications : true,
                  tradingAlerts: preferencesData?.trading_alerts !== undefined ? preferencesData.trading_alerts : true,
                  weeklyReports: preferencesData?.weekly_reports !== undefined ? preferencesData.weekly_reports : true,
                  defaultCurrency: preferencesData?.default_currency || 'USD',
                  defaultTimeZone: preferencesData?.default_timezone || 'UTC',
                }}
              />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
