"use client"

import { useState, useEffect, useMemo } from "react"
import { useAccount } from "@/contexts/account-context"
import { Wallet } from "lucide-react"
import Link from "next/link"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Button } from "@/components/ui/button"

import { cn } from "@/lib/utils"

interface AccountSwitcherProps {
  className?: string
  variant?: "default" | "outline" | "ghost"
  size?: "default" | "sm" | "lg" | "icon"
}

export function AccountSwitcher({ className, variant = "outline", size = "default" }: AccountSwitcherProps) {
  const { accounts, selectedAccountId, setSelectedAccountId, isLoading, isAccountSwitching, isInitializing } = useAccount()
  const [open, setOpen] = useState(false)

  // Find the selected account (memoized for performance)
  const selectedAccount = useMemo(() =>
    accounts.find(account => account.id === selectedAccountId),
    [accounts, selectedAccountId]
  )

  // Debug logging for account updates
  useEffect(() => {
    if (selectedAccount) {
      console.log('AccountSwitcher: Selected account updated:', selectedAccount.name)
    }
  }, [selectedAccount?.name, selectedAccount?.id])

  // Handle account selection
  const handleSelectAccount = (accountId: string) => {
    // Only update if the account is actually changing
    if (accountId !== selectedAccountId) {
      console.log('Account switcher: Changing account from', selectedAccountId, 'to', accountId)
      setSelectedAccountId(accountId)
    } else {
      console.log('Account switcher: Account already selected, no change needed')
    }
    setOpen(false)
  }

  return (
    <DropdownMenu open={open} onOpenChange={setOpen}>
      <DropdownMenuTrigger asChild>
        <Button
          variant={variant}
          size={size}
          className={cn(
            "flex items-center gap-2 min-w-[180px] justify-between",
            !selectedAccount && "text-muted-foreground",
            className
          )}
        >
          <div className="flex items-center gap-2 truncate">
            <Wallet className="h-4 w-4" />
            <span className="truncate">
              {isInitializing || isAccountSwitching
                ? "Loading accounts..."
                : selectedAccount
                  ? selectedAccount.name
                  : "Select Account"}
            </span>
          </div>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-[220px]">
        <DropdownMenuLabel>Trading Accounts</DropdownMenuLabel>
        <DropdownMenuSeparator />
        {accounts.length === 0 ? (
          <DropdownMenuItem disabled className="text-muted-foreground">
            No accounts found
          </DropdownMenuItem>
        ) : (
          accounts.map((account) => (
            <DropdownMenuItem
              key={account.id}
              className={cn(
                "flex items-center gap-2 cursor-pointer",
                selectedAccountId === account.id && "bg-accent font-medium"
              )}
              onClick={() => handleSelectAccount(account.id)}
            >
              <Wallet className="h-4 w-4" />
              <div className="flex flex-col">
                <span className="text-sm">{account.name}</span>
                <span className="text-xs text-muted-foreground">{account.broker}</span>
              </div>
            </DropdownMenuItem>
          ))
        )}
        <DropdownMenuSeparator />
        <DropdownMenuItem asChild>
          <Link href="/accounts" className="text-sm">
            Manage Accounts
          </Link>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
