"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { ArrowLeft } from "lucide-react"
import { UploadDropzone } from "@/components/upload-dropzone"
import { ManualTradeForm } from "@/components/manual-trade-form"
import { type ProcessedData } from "@/lib/excel-processor"
import { saveTradeData } from "@/lib/trade-service"
import { toast } from "sonner"
import { getSupabaseBrowser } from "@/lib/supabase"
import { useAccount } from "@/contexts/account-context"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

export default function AddTradePage() {
  const [activeTab, setActiveTab] = useState("file-upload")
  const [userId, setUserId] = useState<string | null>(null)
  const [selectedAccount, setSelectedAccount] = useState<string | null>(null)
  const router = useRouter()
  const supabase = getSupabaseBrowser()
  const { accounts, selectedAccountId } = useAccount()

  useEffect(() => {
    const fetchUser = async () => {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user?.id) {
        console.log('No authenticated user found')
        router.push('/auth')
        return
      }
      setUserId(user.id)
    }

    fetchUser()
  }, [supabase.auth, router])

  // Set the selected account when selectedAccountId changes
  useEffect(() => {
    if (selectedAccountId) {
      setSelectedAccount(selectedAccountId)
    }
  }, [selectedAccountId])

  const handleProcessedData = async (data: ProcessedData) => {
    if (!userId) {
      toast.error("Please sign in to upload trade data")
      return
    }

    if (!selectedAccount) {
      toast.error("Please select an account")
      return
    }

    try {
      // Save the data to the database with the selected account
      await saveTradeData(userId, data, selectedAccount)

      // Show success message
      toast.success("Successfully imported trading history")

      // Redirect to dashboard
      router.push('/dashboard')
    } catch (error) {
      // Get a meaningful error message
      const errorMessage = error instanceof Error
        ? error.message
        : typeof error === 'object' && error !== null
          ? JSON.stringify(error)
          : 'Unknown error';

      console.error("Error saving trade data:", error)
      toast.error(`Error saving trading data: ${errorMessage}`)
    }
  }

  return (
    <div className="container py-10 max-w-5xl">
      <div className="flex items-center mb-6">
        <Button
          variant="ghost"
          size="icon"
          onClick={() => router.back()}
          className="mr-2"
        >
          <ArrowLeft className="h-5 w-5" />
        </Button>
        <h1 className="text-3xl font-bold tracking-tight">Add Trade</h1>
      </div>

      <Tabs defaultValue="file-upload" className="w-full" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3 mb-8">
          <TabsTrigger value="file-upload">File Upload</TabsTrigger>
          <TabsTrigger value="broker-sync">Broker Sync</TabsTrigger>
          <TabsTrigger value="manual">Manual Entry</TabsTrigger>
        </TabsList>

        <TabsContent value="file-upload">
          <Card>
            <CardHeader>
              <CardTitle>Upload Trading History</CardTitle>
              <CardDescription>
                Select an account and upload your trading history file
              </CardDescription>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="space-y-6">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Select Account</label>
                  <Select
                    value={selectedAccount || ''}
                    onValueChange={setSelectedAccount}
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select an account" />
                    </SelectTrigger>
                    <SelectContent>
                      {accounts.length === 0 ? (
                        <div className="p-2 text-sm text-muted-foreground">
                          No accounts found. Please create an account first.
                        </div>
                      ) : (
                        accounts.map((account) => (
                          <SelectItem key={account.id} value={account.id}>
                            {account.name} ({account.broker})
                          </SelectItem>
                        ))
                      )}
                    </SelectContent>
                  </Select>
                  {accounts.length === 0 && (
                    <div className="mt-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => router.push('/accounts')}
                      >
                        Create Account
                      </Button>
                    </div>
                  )}
                </div>

                <div className="space-y-4">
                  <div className="text-sm text-muted-foreground">
                    Upload your trading history file from your broker platform
                  </div>
                  <UploadDropzone
                    onFileAccepted={handleProcessedData}
                    disabled={!selectedAccount || accounts.length === 0}
                  />
                  <div className="text-xs text-muted-foreground">
                    Supported formats: MT4/MT5 Excel reports (.xlsx, .xls) and CSV files (max 9MB)
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="broker-sync">
          <Card>
            <CardContent className="pt-6">
              <div className="text-sm space-y-4">
                <h3 className="text-lg font-medium">Coming Soon: Direct Broker Synchronization</h3>
                <p className="text-muted-foreground">
                  This feature will allow you to connect directly to your broker's API and automatically import your trading history.
                </p>
                <div className="bg-muted p-4 rounded-md">
                  <h4 className="font-medium mb-2">Supported Brokers (Coming Soon):</h4>
                  <ul className="list-disc list-inside space-y-1 text-muted-foreground">
                    <li>MetaTrader 4/5</li>
                    <li>cTrader</li>
                    <li>TradingView</li>
                    <li>Interactive Brokers</li>
                    <li>And more...</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="manual">
          <Card>
            <CardHeader>
              <CardTitle>Manual Trade Entry</CardTitle>
              <CardDescription>
                Select an account and enter your trade details manually
              </CardDescription>
            </CardHeader>
            <CardContent className="pt-0">
              {userId ? (
                <div className="space-y-6">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Select Account</label>
                    <Select
                      value={selectedAccount || ''}
                      onValueChange={setSelectedAccount}
                    >
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder="Select an account" />
                      </SelectTrigger>
                      <SelectContent>
                        {accounts.length === 0 ? (
                          <div className="p-2 text-sm text-muted-foreground">
                            No accounts found. Please create an account first.
                          </div>
                        ) : (
                          accounts.map((account) => (
                            <SelectItem key={account.id} value={account.id}>
                              {account.name} ({account.broker})
                            </SelectItem>
                          ))
                        )}
                      </SelectContent>
                    </Select>
                    {accounts.length === 0 && (
                      <div className="mt-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => router.push('/accounts')}
                        >
                          Create Account
                        </Button>
                      </div>
                    )}
                  </div>

                  {selectedAccount ? (
                    <ManualTradeForm
                      userId={userId}
                      accountId={selectedAccount}
                      onTradeAdded={handleProcessedData}
                    />
                  ) : (
                    <div className="text-sm text-muted-foreground p-4 border rounded-md bg-muted/50">
                      Please select an account to add trades manually
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-sm text-muted-foreground">
                  Please sign in to add trades manually
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
