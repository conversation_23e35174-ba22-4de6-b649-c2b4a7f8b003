# Server-Side Rendering Implementation Guide

This guide outlines the approach for implementing Server-Side Rendering (SSR) on the remaining pages of the TradePivot application while maintaining the current UI and functionality.

## Current Implementation Pattern

After examining the dashboard, playbook, and journal pages, we've identified the following pattern for implementing SSR:

### 1. Three-File Structure

Each page is split into three files:

1. **Server Component (`page.tsx`)**
   - No `"use client"` directive
   - Handles authentication and data fetching
   - Passes data to client wrapper component

2. **Client Wrapper (`client-wrapper.tsx`)**
   - Has `"use client"` directive
   - Uses dynamic import with `ssr: false` to import the client component
   - Passes props from server component to client component

3. **Client Component (`client.tsx`)**
   - Has `"use client"` directive
   - Contains all the UI logic and state management
   - Receives initial data from server component via client wrapper

### 2. Data Flow

- Server component fetches initial data using Supabase server client
- Initial data is passed to client component via props
- Client component uses initial data for first render
- Client component may fetch additional data via API routes

### 3. Authentication

- Server component handles authentication using `supabase.auth.getUser()`
- If user is not authenticated, server component redirects to login page
- User ID is passed to client component for subsequent API calls

## Implementation Steps

For each page that needs to be migrated to SSR, follow these steps:

### Step 1: Create Server Component

1. Examine the current client-side implementation to identify:
   - Required initial data
   - Authentication requirements
   - User preferences and settings

2. Create or modify the `page.tsx` file:
   - Remove `"use client"` directive
   - Add server-side Supabase client setup
   - Add authentication check
   - Fetch initial data
   - Pass data to client wrapper component

```tsx
// src/app/(dashboard)/[page-name]/page.tsx
import { cookies } from 'next/headers';
import { createServerClient } from '@supabase/ssr';
import { redirect } from 'next/navigation';
import type { Database } from '@/types/supabase';
import ClientWrapper from './client-wrapper';

export default async function PageName() {
  // Get cookies for server-side Supabase client
  const cookieStore = await cookies();
  const supabase = createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value;
        },
        set(_name: string, _value: string, _options: any) {
          // Server components can't set cookies directly
        },
        remove(_name: string, _options: any) {
          // Server components can't remove cookies directly
        }
      },
    }
  );

  // Get authenticated user
  const { data: { user }, error: userError } = await supabase.auth.getUser();
  if (userError || !user) {
    redirect('/login');
  }

  const userId = user.id;

  // Fetch initial data
  const { data, error } = await supabase
    .from('your_table')
    .select('*')
    .eq('user_id', userId);

  // Fetch any other required data
  // ...

  // Pass the fetched data to the client component
  return (
    <ClientWrapper
      userId={userId}
      initialData={data || []}
      // Pass any other required data
    />
  );
}
```

### Step 2: Create Client Wrapper

Create a `client-wrapper.tsx` file that dynamically imports the client component:

```tsx
// src/app/(dashboard)/[page-name]/client-wrapper.tsx
"use client"

import dynamic from 'next/dynamic';
import { YourDataType } from '@/types/your-types';

// Dynamically import the client component with no SSR
const ClientComponent = dynamic(() => import('./client'), {
  ssr: false
});

interface ClientWrapperProps {
  userId: string;
  initialData: YourDataType[];
  // Add any other required props
}

export default function ClientWrapper({ userId, initialData, ...otherProps }: ClientWrapperProps) {
  return <ClientComponent
    userId={userId}
    initialData={initialData}
    {...otherProps}
  />;
}
```

### Step 3: Create/Modify Client Component

Create or modify the `client.tsx` file to handle UI logic and state management:

```tsx
// src/app/(dashboard)/[page-name]/client.tsx
"use client"

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { YourDataType } from '@/types/your-types';
// Import UI components and hooks

interface ClientComponentProps {
  userId: string;
  initialData: YourDataType[];
  // Add any other required props
}

export default function ClientComponent({ userId, initialData, ...otherProps }: ClientComponentProps) {
  // Initialize state with initial data
  const [data, setData] = useState<YourDataType[]>(initialData);
  const router = useRouter();

  // Add any other state variables
  // ...

  // Add any effects for data fetching
  useEffect(() => {
    // Fetch additional data if needed
    // ...
  }, [userId]);

  // Add any event handlers
  // ...

  // Render UI
  return (
    <div>
      {/* Render UI components */}
    </div>
  );
}
```

### Step 4: Create/Update API Routes

Create or update API routes for client-side data fetching:

```tsx
// src/app/api/[endpoint]/route.ts
import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { createServerClient } from '@supabase/ssr';
import type { Database } from '@/types/supabase';

export async function GET(request: Request) {
  try {
    // Get URL parameters
    const url = new URL(request.url);
    const param = url.searchParams.get('param');

    // Get cookies for server-side Supabase client
    const cookieStore = await cookies();
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
          set(_name: string, _value: string, _options: any) {
            // API routes can't set cookies directly
          },
          remove(_name: string, _options: any) {
            // API routes can't remove cookies directly
          }
        },
      }
    );

    // Get authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    const userId = user.id;

    // Fetch data
    const { data, error } = await supabase
      .from('your_table')
      .select('*')
      .eq('user_id', userId);

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json(data || []);
  } catch (error) {
    console.error('Error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
```

## Pages to Implement

Based on the server-side migration plan, the following pages need to be implemented:

1. **Analytics Page**
   - Server Component: `src/app/(dashboard)/analytics/page.tsx`
   - Client Wrapper: `src/app/(dashboard)/analytics/client-wrapper.tsx`
   - Client Component: `src/app/(dashboard)/analytics/client.tsx`
   - API Routes: 
     - `src/app/api/analytics-data/route.ts`
     - `src/app/api/analytics-metrics/route.ts`

2. **Trades Page**
   - Server Component: `src/app/(dashboard)/trades/page.tsx`
   - Client Wrapper: `src/app/(dashboard)/trades/client-wrapper.tsx`
   - Client Component: `src/app/(dashboard)/trades/client.tsx`
   - API Routes: 
     - `src/app/api/trades/route.ts` (already updated)

3. **Profile Page**
   - Server Component: `src/app/(dashboard)/profile/page.tsx`
   - Client Wrapper: `src/app/(dashboard)/profile/client-wrapper.tsx`
   - Client Component: `src/app/(dashboard)/profile/client.tsx`
   - API Routes: 
     - `src/app/api/user-profile/route.ts`
     - `src/app/api/user-settings/route.ts`

## Implementation Order

1. **Analytics Page** (Medium Priority)
2. **Trades Page** (Medium Priority)
3. **Profile Page** (Low Priority)

## Testing Approach

For each page:

1. Implement the server component and client wrapper
2. Test authentication and initial data loading
3. Implement API routes for client-side data fetching
4. Test the complete page functionality
5. Verify that the UI and user experience remain unchanged

Only move to the next page after the current page is fully functional and tested.
