"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { toast } from "sonner"
import { StrategyRule, Strategy, RULE_TYPES } from "@/types/playbook"
import { createStrategyRule, updateStrategyRule } from "@/lib/playbook-service"

import { Button } from "@/components/ui/button"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"

// Define the form schema
const formSchema = z.object({
  strategy_id: z.string().min(1, "Strategy is required"),
  rule_type: z.enum(["entry", "exit", "stop_loss", "take_profit", "position_sizing", "other"]),
  name: z.string().min(1, "Name is required").max(100, "Name must be less than 100 characters"),
  description: z.string().min(1, "Description is required"),
  priority: z.coerce.number().int().min(0),
})

type FormValues = z.infer<typeof formSchema>

interface RuleFormProps {
  userId: string
  rule?: StrategyRule
  strategies: Strategy[]
  preSelectedStrategyId?: string
  onSuccess?: (rule: StrategyRule) => void
  onCancel?: () => void
}

export function RuleForm({ userId, rule, strategies, preSelectedStrategyId, onSuccess, onCancel }: RuleFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)

  const isEditing = !!rule

  // Initialize form with default values or existing rule values
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      strategy_id: rule?.strategy_id || preSelectedStrategyId || "",
      rule_type: rule?.rule_type || "entry",
      name: rule?.name || "",
      description: rule?.description || "",
      priority: rule?.priority || 0,
    },
  })

  // Handle form submission
  const onSubmit = async (values: FormValues) => {
    setIsSubmitting(true)

    try {
      let result: StrategyRule | null

      if (isEditing && rule) {
        // Update existing rule
        result = await updateStrategyRule(userId, rule.id, values)
        if (result) {
          toast.success("Rule updated successfully")
        }
      } else {
        // Create new rule
        result = await createStrategyRule(userId, values)
        if (result) {
          toast.success("Rule created successfully")
          form.reset() // Reset form after successful creation
        }
      }

      if (result && onSuccess) {
        onSuccess(result)
      }
    } catch (error) {
      console.error("Error saving rule:", error)
      toast.error("Failed to save rule")
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Card className="w-full max-w-3xl mx-auto">
      <CardHeader>
        <CardTitle>{isEditing ? "Edit Rule" : "Create New Rule"}</CardTitle>
        <CardDescription>
          {isEditing
            ? "Update your trading rule details"
            : "Document a specific rule for your trading strategy"}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="strategy_id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Strategy</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    disabled={!!preSelectedStrategyId || isEditing}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a strategy" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {strategies.map((strategy) => (
                        <SelectItem key={strategy.id} value={strategy.id}>
                          {strategy.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    The strategy this rule belongs to
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="rule_type"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Rule Type</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select rule type" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {RULE_TYPES.map((type) => (
                        <SelectItem key={type.value} value={type.value}>
                          {type.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    The category this rule belongs to
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Rule Name</FormLabel>
                  <FormControl>
                    <Input placeholder="Wait for confirmation candle" {...field} />
                  </FormControl>
                  <FormDescription>
                    A clear, descriptive name for this rule
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Only enter a trade after seeing a confirmation candle that closes above the resistance level..."
                      className="min-h-[150px]"
                      {...field}
                      value={field.value || ""}
                    />
                  </FormControl>
                  <FormDescription>
                    Detailed description of this rule and how to apply it
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="priority"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Priority</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      min="0"
                      step="1"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    Order in which this rule should be displayed (lower numbers first)
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <CardFooter className="px-0 pb-0 pt-6 flex justify-between">
              {onCancel && (
                <Button
                  type="button"
                  variant="outline"
                  onClick={onCancel}
                  disabled={isSubmitting}
                >
                  Cancel
                </Button>
              )}
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting
                  ? "Saving..."
                  : isEditing
                  ? "Update Rule"
                  : "Create Rule"}
              </Button>
            </CardFooter>
          </form>
        </Form>
      </CardContent>
    </Card>
  )
}
