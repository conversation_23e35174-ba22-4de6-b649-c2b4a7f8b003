"use client"

import React, { useState, useEffect, useRef } from 'react'
import { cn } from '@/lib/utils'

interface EditorTransitionWrapperProps {
  children: React.ReactNode
  noteId: string | null
  isEditing: boolean
  className?: string
  onTransitionComplete?: () => void
}

export function EditorTransitionWrapper({
  children,
  noteId,
  isEditing,
  className,
  onTransitionComplete
}: EditorTransitionWrapperProps) {
  const [isTransitioning, setIsTransitioning] = useState(false)
  const [currentNoteId, setCurrentNoteId] = useState(noteId)
  const [currentEditingState, setCurrentEditingState] = useState(isEditing)
  const [showContent, setShowContent] = useState(true)
  const timeoutRef = useRef<NodeJS.Timeout | null>(null)
  const transitionRef = useRef<HTMLDivElement>(null)

  // Check for reduced motion preference
  const prefersReducedMotion = typeof window !== 'undefined'
    ? window.matchMedia('(prefers-reduced-motion: reduce)').matches
    : false

  // Animation durations (reduced if user prefers reduced motion)
  const FADE_DURATION = prefersReducedMotion ? 50 : 250
  const LOADING_DELAY = prefersReducedMotion ? 25 : 100

  useEffect(() => {
    // Clear any existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }

    // Check if we need to transition
    const noteChanged = currentNoteId !== noteId
    const editingChanged = currentEditingState !== isEditing

    if (noteChanged || (editingChanged && noteId)) {
      // Start transition
      setIsTransitioning(true)
      setShowContent(false)

      // Wait for fade out, then update content
      timeoutRef.current = setTimeout(() => {
        setCurrentNoteId(noteId)
        setCurrentEditingState(isEditing)

        // Small delay to ensure content is updated, then fade in
        setTimeout(() => {
          setShowContent(true)
          setIsTransitioning(false)

          // Notify parent that transition is complete
          if (onTransitionComplete) {
            setTimeout(onTransitionComplete, FADE_DURATION)
          }
        }, LOADING_DELAY)
      }, FADE_DURATION)
    } else {
      // No transition needed, just update states
      setCurrentNoteId(noteId)
      setCurrentEditingState(isEditing)
      setShowContent(true)
      setIsTransitioning(false)
    }

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [noteId, isEditing, FADE_DURATION, LOADING_DELAY, onTransitionComplete])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [])

  return (
    <>
      {/* Add CSS animation styles */}
      <style jsx>{`
        @keyframes slideIn {
          from {
            transform: translateX(-100%);
          }
          to {
            transform: translateX(0%);
          }
        }

        /* Smooth transitions for reduced motion users */
        @media (prefers-reduced-motion: reduce) {
          * {
            animation-duration: 0.01ms !important;
            animation-iteration-count: 1 !important;
            transition-duration: 0.01ms !important;
          }
        }
      `}</style>

      <div
        ref={transitionRef}
        className={cn(
          "relative h-full w-full transition-opacity duration-250 ease-in-out",
          showContent ? "opacity-100" : "opacity-0",
          prefersReducedMotion && "transition-none",
          className
        )}
        style={{
          transitionDuration: prefersReducedMotion ? '0ms' : `${FADE_DURATION}ms`
        }}
      >
      {/* Modern loading indicator - subtle progress bar */}
      {isTransitioning && (
        <div className={cn(
          "absolute top-0 left-0 right-0 h-0.5 bg-gradient-to-r from-primary/20 via-primary to-primary/20 z-10",
          "transition-opacity duration-200",
          prefersReducedMotion && "transition-none"
        )}>
          <div className={cn(
            "h-full bg-primary rounded-full transition-transform duration-300 ease-out",
            "transform translate-x-[-100%] animate-[slideIn_300ms_ease-out_forwards]",
            prefersReducedMotion && "animate-none transform-none"
          )} />
        </div>
      )}

      {/* Content */}
      <div className={cn(
        "h-full w-full transition-opacity duration-200",
        showContent ? "opacity-100" : "opacity-0",
        prefersReducedMotion && "transition-none"
      )}>
        {children}
      </div>
      </div>
    </>
  )
}
