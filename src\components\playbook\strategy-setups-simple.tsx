"use client"

import { useState, useEffect } from "react"
import { toast } from "sonner"
import { Setup, Strategy } from "@/types/playbook"
import { getSetups, createSetup, updateSetup, deleteSetup } from "@/lib/playbook-service"

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { ImageDisplay } from "@/components/ui/image-display"
import { SetupFormSimple, SetupFormValues } from "./setup-form-simple"
import { Edit, Plus, RefreshCw, Trash2 } from "lucide-react"

interface StrategySetupsSimpleProps {
  userId: string
  strategy: Strategy
}

export function StrategySetupsSimple({ userId, strategy }: StrategySetupsSimpleProps) {
  const [setups, setSetups] = useState<Setup[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isCreating, setIsCreating] = useState(false)
  const [isEditing, setIsEditing] = useState(false)
  const [currentSetup, setCurrentSetup] = useState<Setup | null>(null)

  // Fetch setups on mount and when strategy changes
  useEffect(() => {
    fetchSetups()
  }, [userId, strategy.id])

  // Fetch setups from the API
  const fetchSetups = async () => {
    setIsLoading(true)
    try {
      const data = await getSetups(userId, strategy.id)
      setSetups(data)
    } catch (error) {
      console.error('Error fetching setups:', error)
      toast.error('Failed to load setups')
    } finally {
      setIsLoading(false)
    }
  }

  // Handle creating a new setup
  const handleCreateSetup = async (data: SetupFormValues) => {
    try {
      const newSetup = await createSetup(userId, {
        ...data,
        image_urls: data.image_urls || [],
        strategy_id: strategy.id,
      })

      if (newSetup) {
        toast.success('Setup created successfully')
        setIsCreating(false)
        fetchSetups()
      }
    } catch (error) {
      console.error('Error creating setup:', error)
      toast.error('Failed to create setup')
      throw error
    }
  }

  // Handle updating an existing setup
  const handleUpdateSetup = async (data: SetupFormValues) => {
    if (!currentSetup) return

    try {
      const updatedSetup = await updateSetup(userId, currentSetup.id, {
        ...data,
        image_urls: data.image_urls || [],
      })

      if (updatedSetup) {
        toast.success('Setup updated successfully')
        setIsEditing(false)
        setCurrentSetup(null)
        fetchSetups()
      }
    } catch (error) {
      console.error('Error updating setup:', error)
      toast.error('Failed to update setup')
      throw error
    }
  }

  // Handle deleting a setup
  const handleDeleteSetup = async (setupId: string) => {
    if (!confirm('Are you sure you want to delete this setup?')) return

    try {
      const success = await deleteSetup(userId, setupId)

      if (success) {
        toast.success('Setup deleted successfully')
        fetchSetups()
      }
    } catch (error) {
      console.error('Error deleting setup:', error)
      toast.error('Failed to delete setup')
    }
  }

  return (
    <div className="space-y-6">
      {/* Create Setup Dialog */}
      <Dialog open={isCreating} onOpenChange={setIsCreating}>
        <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Create New Setup</DialogTitle>
            <DialogDescription>
              Add a new setup pattern for this strategy
            </DialogDescription>
          </DialogHeader>
          <SetupFormSimple
            setup={null}
            onSave={handleCreateSetup}
            onCancel={() => setIsCreating(false)}
          />
        </DialogContent>
      </Dialog>

      {/* Edit Setup Dialog */}
      <Dialog open={isEditing} onOpenChange={setIsEditing}>
        <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Setup</DialogTitle>
            <DialogDescription>
              Update this setup's details
            </DialogDescription>
          </DialogHeader>
          <SetupFormSimple
            setup={currentSetup}
            onSave={handleUpdateSetup}
            onCancel={() => {
              setIsEditing(false)
              setCurrentSetup(null)
            }}
          />
        </DialogContent>
      </Dialog>

      {/* Setups List */}
      <Card>
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-base">Strategy Setups</CardTitle>
              <CardDescription>
                Entry conditions and setup patterns for this strategy
              </CardDescription>
            </div>
            <div className="flex space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={fetchSetups}
                disabled={isLoading}
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
              <Button
                size="sm"
                onClick={() => setIsCreating(true)}
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Setup
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex justify-center items-center h-40">
              <div className="animate-pulse">Loading setups...</div>
            </div>
          ) : (
            <div className="space-y-4">
              {setups.length > 0 ? (
                setups.map((setup, index) => (
                  <div key={setup.id || index} className="p-4 border rounded-md">
                    <div className="flex justify-between items-start mb-3">
                      <h3 className="font-medium text-base">{setup.name}</h3>
                      <div className="flex space-x-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            setCurrentSetup(setup)
                            setIsEditing(true)
                          }}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDeleteSetup(setup.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>

                    {/* Display image if available */}
                    {setup.image_urls && setup.image_urls.length > 0 && (
                      <div className="mb-4">
                        <div className="relative w-full rounded-md mb-2 bg-muted/30">
                          <ImageDisplay
                            src={setup.image_urls[0]}
                            alt={`${setup.name} chart example`}
                            className="w-full h-auto min-h-[300px]"
                            aspectRatio="auto"
                          />
                        </div>
                      </div>
                    )}

                    {setup.description && (
                      <div className="mb-4">
                        <h4 className="text-sm font-medium mb-1">Description:</h4>
                        <p className="text-sm text-muted-foreground">{setup.description}</p>
                      </div>
                    )}

                    {setup.visual_cues && (
                      <div className="mb-4">
                        <h4 className="text-sm font-medium mb-1">Visual Cues:</h4>
                        <p className="text-sm text-muted-foreground">{setup.visual_cues}</p>
                      </div>
                    )}

                    {setup.confirmation_criteria && (
                      <div>
                        <h4 className="text-sm font-medium mb-1">Confirmation Criteria:</h4>
                        <p className="text-sm text-muted-foreground">{setup.confirmation_criteria}</p>
                      </div>
                    )}
                  </div>
                ))
              ) : (
                <div className="text-center py-8">
                  <p className="text-muted-foreground">No setups defined for this strategy yet.</p>
                  <Button
                    variant="outline"
                    size="sm"
                    className="mt-4"
                    onClick={() => setIsCreating(true)}
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Create Your First Setup
                  </Button>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
