"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { toast } from "sonner"
import { CustomMetric, METRIC_VARIABLES, METRIC_OPERATORS } from "@/types/metrics"
import { createCustomMetric, updateCustomMetric, validateMetricFormula } from "@/lib/metrics-service"

import { Button } from "@/components/ui/button"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  Card<PERSON>it<PERSON>,
} from "@/components/ui/card"
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>rig<PERSON>,
} from "@/components/ui/tooltip"
import { InfoIcon } from "lucide-react"

// Define the form schema
const formSchema = z.object({
  name: z.string().min(1, "Name is required").max(50, "Name must be less than 50 characters"),
  description: z.string().optional(),
  formula: z.string().min(1, "Formula is required"),
  is_percentage: z.boolean(),
  display_precision: z.coerce.number().int().min(0).max(10),
  is_higher_better: z.boolean(),
  target_value: z.coerce.number().optional(),
})

type FormValues = z.infer<typeof formSchema>

interface CustomMetricFormProps {
  userId: string
  metric?: CustomMetric
  onSuccess?: (metric: CustomMetric) => void
  onCancel?: () => void
}

export function CustomMetricForm({ userId, metric, onSuccess, onCancel }: CustomMetricFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [formulaError, setFormulaError] = useState<string | null>(null)

  const isEditing = !!metric

  // Initialize form with default values or existing metric values
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: metric?.name || "",
      description: metric?.description || "",
      formula: metric?.formula || "",
      is_percentage: metric?.is_percentage || false,
      display_precision: metric?.display_precision || 2,
      is_higher_better: metric?.is_higher_better || true,
      target_value: metric?.target_value || undefined,
    },
  })

  // Handle form submission
  const onSubmit = async (values: FormValues) => {
    setIsSubmitting(true)
    setFormulaError(null)

    try {
      // Validate the formula
      const validation = validateMetricFormula(values.formula)
      if (!validation.isValid) {
        setFormulaError(validation.error || "Invalid formula")
        setIsSubmitting(false)
        return
      }

      let result: CustomMetric | null

      if (isEditing && metric) {
        // Update existing metric
        result = await updateCustomMetric(userId, metric.id, values)
        if (result) {
          toast.success("Metric updated successfully")
        }
      } else {
        // Create new metric
        result = await createCustomMetric(userId, values)
        if (result) {
          toast.success("Metric created successfully")
          form.reset() // Reset form after successful creation
        }
      }

      if (result && onSuccess) {
        onSuccess(result)
      }
    } catch (error) {
      console.error("Error saving metric:", error)
      toast.error("Failed to save metric")
    } finally {
      setIsSubmitting(false)
    }
  }

  // Validate formula on blur
  const validateFormula = () => {
    const formula = form.getValues("formula")
    if (formula) {
      const validation = validateMetricFormula(formula)
      if (!validation.isValid) {
        setFormulaError(validation.error || "Invalid formula")
      } else {
        setFormulaError(null)
      }
    }
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>{isEditing ? "Edit Metric" : "Create New Metric"}</CardTitle>
        <CardDescription>
          {isEditing
            ? "Update your custom trading metric"
            : "Define a new custom metric to track your trading performance"}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Metric Name</FormLabel>
                  <FormControl>
                    <Input placeholder="Win Rate %" {...field} />
                  </FormControl>
                  <FormDescription>
                    A short, descriptive name for your metric
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Percentage of winning trades out of total trades"
                      {...field}
                      value={field.value || ""}
                    />
                  </FormControl>
                  <FormDescription>
                    Optional description explaining what this metric measures
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="space-y-4">
              <div>
                <h3 className="text-sm font-medium mb-2">Available Variables</h3>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-2 text-xs">
                  {METRIC_VARIABLES.map((variable) => (
                    <TooltipProvider key={variable.name}>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant="outline"
                            size="sm"
                            type="button"
                            className="justify-start h-auto py-1 px-2 font-mono"
                            onClick={() => {
                              const currentFormula = form.getValues("formula")
                              form.setValue(
                                "formula",
                                currentFormula ? `${currentFormula} ${variable.name}` : variable.name
                              )
                            }}
                          >
                            {variable.name}
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent side="bottom" className="max-w-xs">
                          <p>{variable.description}</p>
                          <p className="text-xs mt-1">Example: {variable.example}</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  ))}
                </div>
              </div>

              <div>
                <h3 className="text-sm font-medium mb-2">Operators</h3>
                <div className="grid grid-cols-4 md:grid-cols-6 gap-2 text-xs">
                  {METRIC_OPERATORS.map((operator) => (
                    <TooltipProvider key={operator.symbol}>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant="outline"
                            size="sm"
                            type="button"
                            className="justify-center h-auto py-1 px-2 font-mono"
                            onClick={() => {
                              const currentFormula = form.getValues("formula")
                              form.setValue(
                                "formula",
                                currentFormula ? `${currentFormula} ${operator.symbol}` : operator.symbol
                              )
                            }}
                          >
                            {operator.symbol}
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent side="bottom" className="max-w-xs">
                          <p>{operator.description}</p>
                          <p className="text-xs mt-1">Example: {operator.example}</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  ))}
                </div>
              </div>
            </div>

            <FormField
              control={form.control}
              name="formula"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Formula</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Textarea
                        placeholder="(win_count / total_trades) * 100"
                        className="font-mono h-24"
                        {...field}
                        onBlur={() => {
                          field.onBlur()
                          validateFormula()
                        }}
                      />
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              type="button"
                              variant="ghost"
                              size="icon"
                              className="absolute top-2 right-2 h-6 w-6"
                            >
                              <InfoIcon className="h-4 w-4" />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent side="left" className="max-w-xs">
                            <p>
                              Create a formula using the available variables and operators.
                              Example: (win_count / total_trades) * 100
                            </p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                  </FormControl>
                  {formulaError && (
                    <p className="text-sm font-medium text-destructive mt-1">{formulaError}</p>
                  )}
                  <FormDescription>
                    Define how this metric is calculated using the variables above
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="display_precision"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Decimal Precision</FormLabel>
                    <Select
                      onValueChange={(value) => field.onChange(parseInt(value))}
                      defaultValue={field.value.toString()}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select precision" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {[0, 1, 2, 3, 4, 5].map((value) => (
                          <SelectItem key={value} value={value.toString()}>
                            {value} {value === 1 ? "decimal place" : "decimal places"}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      Number of decimal places to display
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="target_value"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Target Value (Optional)</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder="70"
                        {...field}
                        value={field.value === undefined ? "" : field.value}
                        onChange={(e) => {
                          const value = e.target.value === "" ? undefined : parseFloat(e.target.value)
                          field.onChange(value)
                        }}
                      />
                    </FormControl>
                    <FormDescription>
                      A target value for this metric
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="is_percentage"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                    <div className="space-y-0.5">
                      <FormLabel>Display as Percentage</FormLabel>
                      <FormDescription>
                        Show this metric with a % symbol
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="is_higher_better"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                    <div className="space-y-0.5">
                      <FormLabel>Higher is Better</FormLabel>
                      <FormDescription>
                        Is a higher value better for this metric?
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>

            <CardFooter className="px-0 pb-0 pt-6 flex justify-between">
              {onCancel && (
                <Button
                  type="button"
                  variant="outline"
                  onClick={onCancel}
                  disabled={isSubmitting}
                >
                  Cancel
                </Button>
              )}
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting
                  ? "Saving..."
                  : isEditing
                  ? "Update Metric"
                  : "Create Metric"}
              </Button>
            </CardFooter>
          </form>
        </Form>
      </CardContent>
    </Card>
  )
}
