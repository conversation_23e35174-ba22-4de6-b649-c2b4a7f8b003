import { createClient } from '@supabase/supabase-js'
import { createBrowserClient } from '@supabase/ssr'
import type { Database } from '@/types/supabase'

// Get environment variables with non-null assertions
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

// Validate environment variables at runtime
if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables')
}

// Create a single client instance
// Use a global variable to ensure we only create one instance
let clientInstance: ReturnType<typeof createBrowserClient<Database>> | null = null

// This function ensures we only create one Supabase client instance
export function getSupabaseClient() {
  if (typeof window === 'undefined') {
    // Server-side: create a new instance each time
    console.log('Creating server-side Supabase client')
    return createClient<Database>(supabaseUrl, supabaseAnonKey)
  }

  // Client-side: reuse the same instance
  if (!clientInstance) {
    console.log('Creating global Supabase client singleton')
    clientInstance = createBrowserClient<Database>(supabaseUrl, supabaseAnonKey)
  }
  return clientInstance
}
