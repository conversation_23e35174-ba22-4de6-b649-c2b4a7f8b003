"use client"

import { useState, useEffect } from "react"
import { CustomMetric, Goal } from "@/types/metrics"
import { calculateMetricValue, calculateMetricProgress, getMetricTemplateType, getMetricStatusText } from "@/lib/metrics-service"
import { METRIC_TEMPLATES, getTemplateById } from "@/lib/metric-templates"

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import {
  MoreH<PERSON>zon<PERSON>,
  Edit,
  Trash2,
  Plus,
  TrendingUp,
  TrendingDown,
  Target,
  Shield,
  Calculator,
  Sparkles
} from "lucide-react"
import { cn } from "@/lib/utils"

interface EnhancedMetricsListProps {
  userId: string
  accountId: string | null
  metrics: CustomMetric[]
  goals: Goal[]
  trades: any[]
  onEdit: (metric: CustomMetric) => void
  onDelete: (metricId: string) => void
  onAdd: () => void
  onViewGoals?: () => void
  preComputedValues?: Record<string, number>
  isLoading?: boolean
  isAccountSwitching?: boolean
}

export function EnhancedMetricsList({
  userId,
  accountId,
  metrics,
  goals,
  trades,
  onEdit,
  onDelete,
  onAdd,
  onViewGoals,
  preComputedValues,
  isLoading = false,
  isAccountSwitching = false
}: EnhancedMetricsListProps) {
  const [metricValues, setMetricValues] = useState<Record<string, number>>(preComputedValues || {})
  const [deleteMetric, setDeleteMetric] = useState<CustomMetric | null>(null)

  // Update metric values when preComputedValues change (from parent component)
  useEffect(() => {
    console.log('[EnhancedMetricsList] Updating metric values from preComputed:', preComputedValues);
    setMetricValues(preComputedValues || {});
  }, [preComputedValues]);

  // Calculate metric values when trades or metrics change (only if we don't have pre-computed values)
  useEffect(() => {
    // If we're loading or account switching, don't calculate yet
    if (isLoading || isAccountSwitching) {
      console.log('[EnhancedMetricsList] Skipping calculation - loading or account switching');
      return;
    }

    // If we have pre-computed values for all metrics, use those instead
    if (preComputedValues && metrics.length > 0 && metrics.every(metric => metric.id in preComputedValues)) {
      console.log('[EnhancedMetricsList] Using pre-computed values, skipping client-side calculation');
      return;
    }

    // Skip if no metrics to calculate
    if (metrics.length === 0) {
      console.log('[EnhancedMetricsList] No metrics to calculate');
      setMetricValues({});
      return;
    }

    console.log('[EnhancedMetricsList] Calculating metric values client-side for account:', accountId);
    const calculateValues = async () => {
      const values: Record<string, number> = {}

      for (const metric of metrics) {
        values[metric.id] = await calculateMetricValue(metric, trades, accountId || undefined)
      }

      console.log('[EnhancedMetricsList] Calculated values:', values);
      setMetricValues(values)
    }

    calculateValues()
  }, [metrics, trades, accountId, isLoading, isAccountSwitching, preComputedValues])

  const formatMetricValue = (value: number, isPercentage: boolean): string => {
    const formattedValue = value.toFixed(2)
    return isPercentage ? `${formattedValue}%` : formattedValue
  }

  const getMetricProgress = (metric: CustomMetric, value: number): number => {
    return calculateMetricProgress(metric, value)
  }

  const getMetricStatus = (metric: CustomMetric, value: number) => {
    if (!metric.target_value) return null

    const progress = getMetricProgress(metric, value)

    if (progress >= 100) return 'success'
    if (progress >= 80) return 'on_track'
    if (progress >= 50) return 'behind'
    return 'at_risk'
  }

  const getTemplateInfo = (metric: CustomMetric) => {
    // Try to match with a template based on formula
    return METRIC_TEMPLATES.find(template => template.formula === metric.formula)
  }

  // Show loading state during account switching or initial loading
  if (isLoading || isAccountSwitching) {
    return (
      <Card className="text-center py-12">
        <CardContent>
          <div className="flex flex-col items-center space-y-4">
            <div className="rounded-full bg-muted p-4">
              <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
            </div>
            <div className="space-y-2">
              <h3 className="text-lg font-semibold">Loading Metrics</h3>
              <p className="text-muted-foreground max-w-md">
                {isAccountSwitching ? 'Switching accounts...' : 'Loading your custom metrics and calculations...'}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (metrics.length === 0) {
    return (
      <Card className="text-center py-12">
        <CardContent>
          <div className="flex flex-col items-center space-y-4">
            <div className="rounded-full bg-muted p-4">
              <Calculator className="h-8 w-8 text-muted-foreground" />
            </div>
            <div className="space-y-2">
              <h3 className="text-lg font-semibold">No Custom Metrics</h3>
              <p className="text-muted-foreground max-w-md">
                Create custom metrics to track specific aspects of your trading performance.
                Choose from professional templates or build your own formulas.
              </p>
            </div>
            <Button onClick={onAdd} className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Create Your First Metric
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Custom Metrics</h2>
          <p className="text-muted-foreground">
            Track your trading performance with {metrics.length} custom metric{metrics.length !== 1 ? 's' : ''}
          </p>
        </div>
        <Button onClick={onAdd} className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          Add Metric
        </Button>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {metrics.map((metric) => {
          const value = metricValues[metric.id] || 0
          const status = getMetricStatus(metric, value)
          const templateInfo = getTemplateInfo(metric)

          return (
            <Card key={metric.id} className="relative group hover:shadow-md transition-all border-border/50 dark:border-border">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="space-y-1 flex-1">
                    <div className="flex items-center gap-2">
                      <CardTitle className="text-base">{metric.name}</CardTitle>
                      {templateInfo && (
                        <Badge variant="secondary" className="text-xs flex items-center gap-1">
                          <Sparkles className="h-3 w-3" />
                          Template
                        </Badge>
                      )}
                    </div>
                    {metric.description && (
                      <CardDescription className="text-sm">
                        {metric.description}
                      </CardDescription>
                    )}
                  </div>
                  
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button 
                        variant="ghost" 
                        size="icon" 
                        className="h-8 w-8 opacity-0 group-hover:opacity-100 transition-opacity"
                      >
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => onEdit(metric)}>
                        <Edit className="h-4 w-4 mr-2" />
                        Edit
                      </DropdownMenuItem>
                      <DropdownMenuItem 
                        onClick={() => setDeleteMetric(metric)}
                        className="text-destructive"
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </CardHeader>

              <CardContent className="pt-0">
                <div className="space-y-4">
                  {/* Metric Value Display */}
                  <div className="text-center">
                    <div className={cn(
                      "text-3xl font-bold",
                      status === 'success' && "text-emerald-600 dark:text-emerald-400",
                      status === 'on_track' && "text-green-600 dark:text-green-400",
                      status === 'behind' && "text-amber-600 dark:text-amber-400",
                      status === 'at_risk' && "text-red-600 dark:text-red-400"
                    )}>
                      {formatMetricValue(value, metric.is_percentage)}
                    </div>
                    
                    {metric.target_value && (
                      <div className="flex items-center justify-center gap-2 mt-2">
                        {getMetricTemplateType(metric) === 'threshold' ? (
                          <Shield className="h-4 w-4 text-muted-foreground" />
                        ) : (
                          <Target className="h-4 w-4 text-muted-foreground" />
                        )}
                        <span className="text-sm text-muted-foreground">
                          {getMetricTemplateType(metric) === 'threshold' ? 'Threshold' : 'Target'}: {formatMetricValue(metric.target_value, metric.is_percentage)}
                        </span>
                        {status && (
                          <div className="flex items-center">
                            {status === 'success' ? (
                              <TrendingUp className="h-4 w-4 text-emerald-600 dark:text-emerald-400" />
                            ) : (
                              <TrendingDown className="h-4 w-4 text-amber-600 dark:text-amber-400" />
                            )}
                          </div>
                        )}
                      </div>
                    )}
                  </div>

                  {/* Formula Display */}
                  <div className="bg-muted/50 rounded-md p-3">
                    <div className="text-xs text-muted-foreground mb-1">Formula:</div>
                    <code className="text-xs font-mono">{metric.formula}</code>
                  </div>

                  {/* Template Information */}
                  {templateInfo && (
                    <div className="text-xs text-muted-foreground">
                      <strong>Category:</strong> {templateInfo.category.charAt(0).toUpperCase() + templateInfo.category.slice(1)}
                    </div>
                  )}

                  {/* Performance Indicator */}
                  {metric.target_value && (
                    <div className="space-y-2">
                      <div className="flex justify-between text-xs">
                        {(() => {
                          const statusInfo = getMetricStatusText(metric, value)
                          return (
                            <>
                              <span className={statusInfo.isBreached ? 'text-red-600 dark:text-red-400 font-medium' : 'text-muted-foreground'}>
                                {statusInfo.text}
                              </span>
                              <span className={statusInfo.isBreached ? 'text-red-600 dark:text-red-400 font-medium' : 'text-muted-foreground'}>
                                {getMetricProgress(metric, value).toFixed(0)}%
                              </span>
                            </>
                          )
                        })()}
                      </div>
                      <div className="w-full bg-muted rounded-full h-2">
                        <div
                          className={cn(
                            "h-2 rounded-full transition-all",
                            status === 'success' ? "bg-emerald-500" :
                            status === 'on_track' ? "bg-green-500" :
                            status === 'behind' ? "bg-amber-500" : "bg-red-500"
                          )}
                          style={{
                            width: `${getMetricProgress(metric, value)}%`
                          }}
                        />
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {(() => {
                          const templateType = getMetricTemplateType(metric)
                          const label = templateType === 'threshold' ? 'Threshold' : 'Target'
                          const direction = templateType === 'threshold' ? 'or lower' :
                                          metric.is_higher_better ? 'or higher' : 'or lower'
                          return `${label}: ${formatMetricValue(metric.target_value, metric.is_percentage)} ${direction}`
                        })()}
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={!!deleteMetric} onOpenChange={() => setDeleteMetric(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Metric</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete "{deleteMetric?.name}"? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => {
                if (deleteMetric) {
                  onDelete(deleteMetric.id)
                  setDeleteMetric(null)
                }
              }}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
