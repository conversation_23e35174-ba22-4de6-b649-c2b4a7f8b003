"use client"

import { useState, use<PERSON>ffe<PERSON> } from "react"
import { toast } from "sonner"
import { Strategy, StrategyPerformance } from "@/types/playbook"
import { Trade } from "@/types/trade"
import { calculateStrategyPerformance, getTradesByStrategy } from "@/lib/playbook-service"
import { format, subMonths, startOfMonth, endOfMonth } from "date-fns"
import { useAccount } from "@/contexts/account-context"

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs"
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>Axis,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON>,
} from "recharts"
import { ArrowLeft, RefreshCw } from "lucide-react"

interface StrategyPerformanceProps {
  userId: string
  strategy: Strategy
  performances: StrategyPerformance[]
  onBack?: () => void
}

export function StrategyPerformanceView({
  userId,
  strategy,
  performances,
  onBack,
}: StrategyPerformanceProps) {
  const { selectedAccountId } = useAccount()
  const [isCalculating, setIsCalculating] = useState(false)
  const [timeframe, setTimeframe] = useState("last3months")
  const [activeTab, setActiveTab] = useState("overview")

  // Listen for strategy assignment changes and global refresh events
  useEffect(() => {
    const handleStrategyAssignmentChange = (event: Event) => {
      const customEvent = event as CustomEvent;
      if (customEvent.detail?.strategyId === strategy.id) {
        console.log("Strategy assignment changed for current strategy, recalculating performance")
        // Add a small delay to ensure the database has been updated
        setTimeout(() => {
          calculatePerformance()
        }, 300)
      }
    }

    const handleRefreshTradesData = (event: Event) => {
      const customEvent = event as CustomEvent;
      console.log("Global refresh-trades-data event received:", customEvent.detail)

      // Check if this event is relevant to this strategy
      if (customEvent.detail?.strategyId === strategy.id) {
        console.log("Refresh event is for this strategy, recalculating performance")
        // Add a small delay to ensure the database has been updated
        setTimeout(() => {
          calculatePerformance()
        }, 300)
      } else if (!customEvent.detail?.strategyId) {
        console.log("Global refresh event with no specific strategy, recalculating performance for:", strategy.id)
        // Add a small delay to ensure the database has been updated
        setTimeout(() => {
          calculatePerformance()
        }, 300)
      }
    }

    const handleGlobalDataChange = (event: Event) => {
      const customEvent = event as CustomEvent;
      console.log("Global data change event received:", customEvent.detail)

      if (customEvent.detail?.type === 'strategy-assignment' &&
          (customEvent.detail?.strategyId === strategy.id || !customEvent.detail?.strategyId)) {
        console.log("Strategy assignment changed via global event, recalculating performance")

        // Add a small delay to ensure the database has been updated
        setTimeout(() => {
          calculatePerformance()
        }, 300)
      }
    }

    // Add event listeners
    document.addEventListener('strategy-assignment-changed', handleStrategyAssignmentChange)
    window.addEventListener('refresh-trades-data', handleRefreshTradesData)
    window.addEventListener('global-data-change', handleGlobalDataChange)

    // Clean up
    return () => {
      document.removeEventListener('strategy-assignment-changed', handleStrategyAssignmentChange)
      window.removeEventListener('refresh-trades-data', handleRefreshTradesData)
      window.removeEventListener('global-data-change', handleGlobalDataChange)
    }
  }, [strategy.id, selectedAccountId])

  const calculatePerformance = async () => {
    // If already calculating, don't start another calculation
    if (isCalculating) {
      console.log("Already calculating performance, skipping")
      return
    }

    setIsCalculating(true)

    try {
      let startDate: Date
      let endDate = new Date()

      switch (timeframe) {
        case "lastMonth":
          startDate = startOfMonth(subMonths(new Date(), 1))
          endDate = endOfMonth(subMonths(new Date(), 1))
          break
        case "last3months":
          startDate = startOfMonth(subMonths(new Date(), 3))
          break
        case "last6months":
          startDate = startOfMonth(subMonths(new Date(), 6))
          break
        case "lastYear":
          startDate = startOfMonth(subMonths(new Date(), 12))
          break
        default:
          startDate = startOfMonth(subMonths(new Date(), 3))
      }

      const formattedStartDate = format(startDate, "yyyy-MM-dd")
      const formattedEndDate = format(endDate, "yyyy-MM-dd")

      console.log(`Calculating performance for strategy ${strategy.id} from ${formattedStartDate} to ${formattedEndDate}`)

      const result = await calculateStrategyPerformance(
        userId,
        strategy.id,
        formattedStartDate,
        formattedEndDate,
        selectedAccountId
      )

      if (result) {
        console.log("Performance calculated successfully:", result)
        toast.success("Performance calculated successfully")

        // Instead of reloading the page, dispatch a custom event to notify other components
        const performanceUpdatedEvent = new CustomEvent('strategy-performance-updated', {
          detail: {
            strategyId: strategy.id,
            performance: result,
            timestamp: new Date().toISOString()
          }
        })
        window.dispatchEvent(performanceUpdatedEvent)

        // Dispatch a global event for any component that might be listening
        const globalEvent = new CustomEvent('global-data-change', {
          detail: {
            type: 'strategy-performance-updated',
            strategyId: strategy.id,
            timestamp: new Date().toISOString()
          }
        })
        window.dispatchEvent(globalEvent)
      } else {
        console.log("No trades found in the selected period")
        toast.error("No trades found in the selected period")
      }
    } catch (error) {
      console.error("Error calculating performance:", error)
      toast.error("Failed to calculate performance")
    } finally {
      setIsCalculating(false)
    }
  }

  // Sort performances by date (newest first)
  const sortedPerformances = [...performances].sort(
    (a, b) => new Date(b.period_end).getTime() - new Date(a.period_start).getTime()
  )

  // Get the most recent performance for the overview
  const latestPerformance = sortedPerformances[0]

  // Prepare data for win/loss pie chart
  const winLossData = latestPerformance
    ? [
        { name: "Winning Trades", value: latestPerformance.winning_trades },
        { name: "Losing Trades", value: latestPerformance.losing_trades },
      ]
    : []

  const COLORS = ["#10b981", "#ef4444"]

  // Prepare data for performance metrics chart
  const performanceMetricsData = sortedPerformances.map((perf) => ({
    period: `${format(new Date(perf.period_start), "MMM yyyy")} - ${format(
      new Date(perf.period_end),
      "MMM yyyy"
    )}`,
    winRate: (perf.win_rate * 100).toFixed(2),
    profitLoss: perf.profit_loss.toFixed(2),
    expectancy: perf.expectancy.toFixed(2),
  }))

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-2">
          {onBack && (
            <Button variant="ghost" size="icon" onClick={onBack}>
              <ArrowLeft className="h-4 w-4" />
            </Button>
          )}
          <h2 className="text-2xl font-bold tracking-tight">
            Performance: {strategy.name}
          </h2>
        </div>
        <div className="flex items-center gap-2">
          <Select
            value={timeframe}
            onValueChange={setTimeframe}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select timeframe" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="lastMonth">Last Month</SelectItem>
              <SelectItem value="last3months">Last 3 Months</SelectItem>
              <SelectItem value="last6months">Last 6 Months</SelectItem>
              <SelectItem value="lastYear">Last Year</SelectItem>
            </SelectContent>
          </Select>
          <Button onClick={calculatePerformance} disabled={isCalculating}>
            <RefreshCw className={`mr-2 h-4 w-4 ${isCalculating ? "animate-spin" : ""}`} />
            {isCalculating ? "Calculating..." : "Calculate"}
          </Button>
        </div>
      </div>

      {performances.length === 0 ? (
        <Card>
          <CardContent className="pt-6 text-center">
            <p className="text-muted-foreground mb-4">
              No performance data available for this strategy yet.
            </p>
            <div className="space-y-4">
              <p className="text-sm text-muted-foreground">
                Make sure you have trades assigned to this strategy. You can assign trades to strategies when manually entering trades or by editing existing trades.
              </p>
              <Button onClick={calculatePerformance} disabled={isCalculating}>
                <RefreshCw className={`mr-2 h-4 w-4 ${isCalculating ? "animate-spin" : ""}`} />
                {isCalculating ? "Calculating..." : "Calculate Performance"}
              </Button>
            </div>
          </CardContent>
        </Card>
      ) : (
        <Tabs defaultValue="overview" value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="mb-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="history">Performance History</TabsTrigger>
            <TabsTrigger value="charts">Charts</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="mt-0">
            {latestPerformance ? (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Performance Summary</CardTitle>
                    <CardDescription>
                      {format(new Date(latestPerformance.period_start), "MMM d, yyyy")} to{" "}
                      {format(new Date(latestPerformance.period_end), "MMM d, yyyy")}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <p className="text-sm font-medium">Total Trades</p>
                        <p className="text-2xl font-bold">{latestPerformance.total_trades}</p>
                      </div>
                      <div className="space-y-2">
                        <p className="text-sm font-medium">Win Rate</p>
                        <p className="text-2xl font-bold">{(latestPerformance.win_rate * 100).toFixed(2)}%</p>
                      </div>
                      <div className="space-y-2">
                        <p className="text-sm font-medium">Profit/Loss</p>
                        <p className={`text-2xl font-bold ${latestPerformance.profit_loss >= 0 ? "text-green-500" : "text-red-500"}`}>
                          {latestPerformance.profit_loss.toFixed(2)}
                        </p>
                      </div>
                      <div className="space-y-2">
                        <p className="text-sm font-medium">Expectancy</p>
                        <p className={`text-2xl font-bold ${latestPerformance.expectancy >= 0 ? "text-green-500" : "text-red-500"}`}>
                          {latestPerformance.expectancy.toFixed(2)}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Win/Loss Distribution</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="h-[200px]">
                      <ResponsiveContainer width="100%" height="100%">
                        <PieChart>
                          <Pie
                            data={winLossData}
                            cx="50%"
                            cy="50%"
                            labelLine={false}
                            outerRadius={80}
                            fill="#8884d8"
                            dataKey="value"
                            label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                          >
                            {winLossData.map((entry, index) => (
                              <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                            ))}
                          </Pie>
                          <Legend />
                        </PieChart>
                      </ResponsiveContainer>
                    </div>
                  </CardContent>
                </Card>

                <Card className="md:col-span-2">
                  <CardHeader>
                    <CardTitle>Detailed Metrics</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      <div className="space-y-2">
                        <p className="text-sm font-medium">Winning Trades</p>
                        <p className="text-xl font-bold">{latestPerformance.winning_trades}</p>
                      </div>
                      <div className="space-y-2">
                        <p className="text-sm font-medium">Losing Trades</p>
                        <p className="text-xl font-bold">{latestPerformance.losing_trades}</p>
                      </div>
                      <div className="space-y-2">
                        <p className="text-sm font-medium">Average Win</p>
                        <p className="text-xl font-bold text-green-500">{latestPerformance.average_win.toFixed(2)}</p>
                      </div>
                      <div className="space-y-2">
                        <p className="text-sm font-medium">Average Loss</p>
                        <p className="text-xl font-bold text-red-500">{latestPerformance.average_loss.toFixed(2)}</p>
                      </div>
                      <div className="space-y-2">
                        <p className="text-sm font-medium">Largest Win</p>
                        <p className="text-xl font-bold text-green-500">{latestPerformance.largest_win.toFixed(2)}</p>
                      </div>
                      <div className="space-y-2">
                        <p className="text-sm font-medium">Largest Loss</p>
                        <p className="text-xl font-bold text-red-500">{latestPerformance.largest_loss.toFixed(2)}</p>
                      </div>
                      <div className="space-y-2">
                        <p className="text-sm font-medium">Avg Risk/Reward</p>
                        <p className="text-xl font-bold">{latestPerformance.average_risk_reward.toFixed(2)}</p>
                      </div>
                      <div className="space-y-2">
                        <p className="text-sm font-medium">Target R/R</p>
                        <p className="text-xl font-bold">{strategy.risk_reward_ratio || "N/A"}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            ) : (
              <Card>
                <CardContent className="pt-6 text-center">
                  <p className="text-muted-foreground">No performance data available.</p>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="history" className="mt-0">
            <Card>
              <CardHeader>
                <CardTitle>Performance History</CardTitle>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Period</TableHead>
                      <TableHead>Trades</TableHead>
                      <TableHead>Win Rate</TableHead>
                      <TableHead>Profit/Loss</TableHead>
                      <TableHead>Avg Win</TableHead>
                      <TableHead>Avg Loss</TableHead>
                      <TableHead>Expectancy</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {sortedPerformances.map((perf) => (
                      <TableRow key={perf.id}>
                        <TableCell>
                          {format(new Date(perf.period_start), "MMM d, yyyy")} - {format(new Date(perf.period_end), "MMM d, yyyy")}
                        </TableCell>
                        <TableCell>{perf.total_trades}</TableCell>
                        <TableCell>{(perf.win_rate * 100).toFixed(2)}%</TableCell>
                        <TableCell className={perf.profit_loss >= 0 ? "text-green-500" : "text-red-500"}>
                          {perf.profit_loss.toFixed(2)}
                        </TableCell>
                        <TableCell className="text-green-500">{perf.average_win.toFixed(2)}</TableCell>
                        <TableCell className="text-red-500">{perf.average_loss.toFixed(2)}</TableCell>
                        <TableCell className={perf.expectancy >= 0 ? "text-green-500" : "text-red-500"}>
                          {perf.expectancy.toFixed(2)}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="charts" className="mt-0">
            <div className="grid grid-cols-1 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle>Performance Metrics Over Time</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-[400px]">
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart
                        data={performanceMetricsData.reverse()}
                        margin={{
                          top: 20,
                          right: 30,
                          left: 20,
                          bottom: 70,
                        }}
                      >
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis
                          dataKey="period"
                          angle={-45}
                          textAnchor="end"
                          height={70}
                        />
                        <YAxis />
                        <Tooltip />
                        <Legend />
                        <Bar dataKey="winRate" name="Win Rate (%)" fill="#8884d8" />
                        <Bar dataKey="profitLoss" name="Profit/Loss" fill="#82ca9d" />
                        <Bar dataKey="expectancy" name="Expectancy" fill="#ffc658" />
                      </BarChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      )}
    </div>
  )
}
