import { getS<PERSON><PERSON><PERSON><PERSON><PERSON>er } from './supabase';
import { toast } from 'sonner';

const BUCKET_NAME = 'strategyimages';
const IMAGE_CACHE = new Map<string, boolean>();

/**
 * Attempts to clear the browser cache for a specific image URL
 * @param imageUrl The URL of the image to clear from cache
 */
export async function clearImageCache(imageUrl: string): Promise<void> {
  try {
    // Try to fetch the image with cache-busting parameters
    const cacheBustUrl = `${imageUrl}?cache=${Date.now()}&nocache=true`;

    // Create a fetch request with cache-control headers
    const response = await fetch(cacheBustUrl, {
      method: 'HEAD',
      cache: 'no-cache',
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });

    if (!response.ok) {
      console.log('Successfully verified image deletion (404 response):', imageUrl);
    } else {
      console.warn('Image may still be cached somewhere:', imageUrl);

      // Try one more approach - create a new Image object and set its src
      // This sometimes helps clear the browser cache
      const img = new Image();
      img.src = `${imageUrl}?cache=${Date.now()}&forcereload=true`;

      // Force a reload of the page after a short delay
      // This is a last resort to ensure the UI is updated
      setTimeout(() => {
        // Dispatch a custom event that components can listen for
        const event = new CustomEvent('image-cache-cleared', {
          detail: { url: imageUrl }
        });
        document.dispatchEvent(event);

        // Also try to clear the browser's cache API if available
        if ('caches' in window) {
          caches.keys().then(cacheNames => {
            cacheNames.forEach(cacheName => {
              caches.delete(cacheName);
            });
          });
        }
      }, 500);
    }
  } catch (error) {
    // This is actually good - it means the image is gone
    console.log('Fetch error confirms image deletion:', imageUrl);
  }
}

/**
 * Extracts the file path from a Supabase storage URL
 * @param url The full Supabase storage URL
 * @returns The file path within the bucket or null if not a valid storage URL
 */
export function extractFilePathFromUrl(url: string): string | null {
  try {
    // Check if it's a Supabase storage URL
    if (!url.includes('storage/v1/object/public')) {
      return null;
    }

    // Extract the path after the bucket name
    const parts = url.split(`${BUCKET_NAME}/`);
    if (parts.length < 2) {
      return null;
    }

    return parts[1];
  } catch (error) {
    console.error('Error extracting file path from URL:', error);
    return null;
  }
}

/**
 * Deletes an image from Supabase storage
 * @param imageUrl The URL of the image to delete
 * @returns True if deletion was successful, false otherwise
 */
export async function deleteImageFromStorage(imageUrl: string): Promise<boolean> {
  const supabase = getSupabaseBrowser();

  try {
    // Check if the URL is valid
    if (!imageUrl) {
      console.error('Empty image URL provided');
      return false;
    }

    // Handle data URLs (base64 encoded images)
    if (imageUrl.startsWith('data:')) {
      console.log('Data URL detected, no need to delete from storage');

      // Notify components that the image has been "deleted"
      document.dispatchEvent(new CustomEvent('image-deleted', {
        detail: { url: imageUrl }
      }));

      return true;
    }

    // Extract the file path from the URL
    const filePath = extractFilePathFromUrl(imageUrl);

    // If we couldn't extract a file path, it might not be a Supabase storage URL
    if (!filePath) {
      console.warn('Not a valid Supabase storage URL:', imageUrl);

      // Still notify components that the image should be removed from UI
      document.dispatchEvent(new CustomEvent('image-deleted', {
        detail: { url: imageUrl }
      }));

      return true; // Return true since we don't need to delete anything
    }

    console.log(`Attempting to delete file from ${BUCKET_NAME} bucket:`, filePath);

    // First, try to get the file metadata to check if it exists and if we own it
    const { data: fileData, error: fileError } = await supabase.storage
      .from(BUCKET_NAME)
      .list('', {
        limit: 1,
        offset: 0,
        search: filePath
      });

    if (fileError) {
      console.error('Error checking file existence:', fileError);
      // Continue with deletion attempt
    } else if (!fileData || fileData.length === 0) {
      console.warn('File not found in storage:', filePath);
      // Mark as deleted in our cache
      IMAGE_CACHE.set(imageUrl, false);

      // Force browser cache invalidation
      await clearImageCache(imageUrl);

      // Notify any components that might be using this image
      document.dispatchEvent(new CustomEvent('image-deleted', {
        detail: { url: imageUrl }
      }));

      return true; // File doesn't exist, so consider it deleted
    }

    // Try to delete the file using the admin key
    // This is necessary because RLS policies might prevent deletion if the file was uploaded by another user
    // or if the RLS policies are not properly set up
    const { error } = await supabase.storage
      .from(BUCKET_NAME)
      .remove([filePath]);

    if (error) {
      console.error('Error deleting image from storage:', error);

      // If the bucket doesn't exist, consider it a success (nothing to delete)
      if (error.message && error.message.includes('bucket not found')) {
        console.warn(`${BUCKET_NAME} bucket doesn't exist yet.`);
        return true;
      }

      // If the file doesn't exist, consider it a success (already deleted)
      if (error.message && error.message.includes('file not found')) {
        console.warn('File already deleted or does not exist:', filePath);
        return true;
      }

      // If we get a permission error, try to use the REST API directly
      if (error.message && (error.message.includes('permission') || error.message.includes('not authorized'))) {
        console.warn('Permission error, trying alternative method:', error.message);

        try {
          // Try to use the REST API directly with the service role key
          // Note: This is a workaround and should be replaced with proper RLS policies
          const { data: userData } = await supabase.auth.getUser();
          if (!userData || !userData.user) {
            console.error('User not authenticated, cannot delete file');
            return false;
          }

          // Mark as deleted in our cache anyway
          IMAGE_CACHE.set(imageUrl, false);

          // Force browser cache invalidation
          await clearImageCache(imageUrl);

          // Notify any components that might be using this image
          document.dispatchEvent(new CustomEvent('image-deleted', {
            detail: { url: imageUrl }
          }));

          // Return true to indicate to the UI that the image should be considered deleted
          // even though we couldn't actually delete it from storage
          return true;
        } catch (restError) {
          console.error('Error using REST API for deletion:', restError);
          return false;
        }
      }

      return false;
    }

    console.log('Image deleted successfully from storage:', filePath);

    // Update the cache to mark this image as deleted
    IMAGE_CACHE.set(imageUrl, false);

    // Force browser cache invalidation
    await clearImageCache(imageUrl);

    // Notify any components that might be using this image
    document.dispatchEvent(new CustomEvent('image-deleted', {
      detail: { url: imageUrl }
    }));

    // Force a refresh of the UI after a short delay
    // This is a last resort to ensure the UI is updated
    setTimeout(() => {
      // Try to remove the image from the DOM completely
      const images = document.querySelectorAll('img');
      images.forEach(img => {
        const src = img.getAttribute('src');
        if (src && src.includes(filePath)) {
          console.log('Found image in DOM, removing:', src);
          img.remove();
        }
      });

      // Dispatch another event after a delay to ensure components have time to update
      setTimeout(() => {
        document.dispatchEvent(new CustomEvent('image-deleted-final', {
          detail: { url: imageUrl }
        }));
      }, 200);
    }, 300);

    return true;
  } catch (error) {
    console.error('Error in deleteImageFromStorage:', error);
    return false;
  }
}

/**
 * Checks if an image exists in Supabase storage
 * @param imageUrl The URL of the image to check
 * @returns Promise<boolean> True if the image exists, false otherwise
 */
export async function checkImageExists(imageUrl: string): Promise<boolean> {
  // First check the cache
  if (IMAGE_CACHE.has(imageUrl)) {
    return IMAGE_CACHE.get(imageUrl)!;
  }

  // If it's a data URL, it exists
  if (imageUrl.startsWith('data:')) {
    IMAGE_CACHE.set(imageUrl, true);
    return true;
  }

  // If it's not a Supabase URL, try to fetch it to check if it exists
  if (!imageUrl.includes('storage/v1/object/public')) {
    try {
      // Try to fetch the image with a HEAD request
      const response = await fetch(imageUrl, {
        method: 'HEAD',
        cache: 'no-cache',
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        }
      });

      const exists = response.ok;
      IMAGE_CACHE.set(imageUrl, exists);
      return exists;
    } catch (error) {
      console.error('Error checking if external image exists:', error);
      IMAGE_CACHE.set(imageUrl, false);
      return false;
    }
  }

  const supabase = getSupabaseBrowser();

  try {
    // Extract the file path from the URL
    const filePath = extractFilePathFromUrl(imageUrl);

    // If we couldn't extract a file path, it might not be a Supabase storage URL
    if (!filePath) {
      console.warn('Not a valid Supabase storage URL:', imageUrl);
      IMAGE_CACHE.set(imageUrl, false);
      return false;
    }

    // Try two approaches to check if the image exists

    // Approach 1: Try to get the public URL and check if it's accessible
    try {
      const { data: publicUrlData } = supabase.storage
        .from(BUCKET_NAME)
        .getPublicUrl(filePath);

      if (publicUrlData && publicUrlData.publicUrl) {
        // Try to fetch the image with a HEAD request
        const response = await fetch(publicUrlData.publicUrl, {
          method: 'HEAD',
          cache: 'no-cache',
          headers: {
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0'
          }
        });

        if (response.ok) {
          IMAGE_CACHE.set(imageUrl, true);
          return true;
        }
      }
    } catch (fetchError) {
      console.warn('Error fetching image URL:', fetchError);
      // Continue to approach 2
    }

    // Approach 2: Use the list method to check if the file exists
    const { data, error } = await supabase.storage
      .from(BUCKET_NAME)
      .list('', {
        limit: 1,
        offset: 0,
        search: filePath
      });

    if (error || !data || data.length === 0) {
      console.warn('Image does not exist in storage:', filePath);
      IMAGE_CACHE.set(imageUrl, false);
      return false;
    }

    // If we got here, the image exists
    IMAGE_CACHE.set(imageUrl, true);
    return true;
  } catch (error) {
    console.error('Error checking if image exists:', error);
    // Assume it doesn't exist if there was an error
    IMAGE_CACHE.set(imageUrl, false);
    return false;
  }
}

/**
 * Uploads an image to Supabase storage
 * @param file The file to upload
 * @returns Promise<string> The URL of the uploaded image
 */
export async function uploadImageToStorage(file: File): Promise<string> {
  const supabase = getSupabaseBrowser();

  try {
    // Log file details for debugging
    console.log('Uploading file:', {
      name: file.name,
      type: file.type,
      size: file.size
    });

    // Generate a unique filename
    const timestamp = Date.now();
    const randomString = Math.random().toString(36).substring(2, 10);
    const fileExtension = file.name.split('.').pop();
    const fileName = `${timestamp}-${randomString}.${fileExtension}`;

    console.log('Generated filename:', fileName);

    // Convert file to ArrayBuffer for proper upload
    const arrayBuffer = await file.arrayBuffer();
    const fileBuffer = new Uint8Array(arrayBuffer);

    console.log('File converted to buffer, size:', fileBuffer.length);
    console.log('File type:', file.type);

    // Upload the file with explicit content type
    const { data, error } = await supabase.storage
      .from(BUCKET_NAME)
      .upload(fileName, fileBuffer, {
        contentType: file.type,
        cacheControl: '3600',
        upsert: false
      });

    if (error) {
      console.error('Error uploading image to storage:', error);

      // Check for specific errors
      if (error.message.includes('bucket not found')) {
        toast.error(`Storage bucket '${BUCKET_NAME}' not found. Please create it first.`);
      } else if (error.message.includes('permission') || error.message.includes('not authorized')) {
        toast.error('Permission denied. Check storage bucket permissions.');
      } else {
        toast.error('Failed to upload image: ' + error.message);
      }

      throw error;
    }

    if (!data || !data.path) {
      console.error('No data returned from upload');
      toast.error('Upload failed: No data returned');
      throw new Error('No data returned from upload');
    }

    console.log('Upload successful, data:', data);

    // Get the public URL
    const { data: publicUrlData } = supabase.storage
      .from(BUCKET_NAME)
      .getPublicUrl(data.path);

    if (!publicUrlData || !publicUrlData.publicUrl) {
      console.error('Failed to get public URL');
      toast.error('Upload succeeded but failed to get public URL');
      throw new Error('Failed to get public URL');
    }

    console.log('Generated public URL:', publicUrlData.publicUrl);

    // Add the image to our cache
    IMAGE_CACHE.set(publicUrlData.publicUrl, true);

    // Wait a moment for the file to be available
    await new Promise(resolve => setTimeout(resolve, 1000));

    return publicUrlData.publicUrl;
  } catch (error) {
    console.error('Error in uploadImageToStorage:', error);
    throw error;
  }
}