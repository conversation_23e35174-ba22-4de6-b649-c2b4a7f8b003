"use client"

import { useMemo } from "react"
import {
  Area,
  AreaChart,
  ResponsiveContainer,
  XAxis,
  YAxis,
  Tooltip,
  ReferenceLine
} from "recharts"
import { cn } from "@/lib/utils"

interface JournalPnLChartProps {
  trades: any[]
  className?: string
}

export function JournalPnLChart({ trades, className }: JournalPnLChartProps) {
  // Generate chart data from trades
  const chartData = useMemo(() => {
    if (!trades || trades.length === 0) return []

    // If we have a single trade, create a simple before/after chart
    if (trades.length === 1) {
      const trade = trades[0]
      return [
        {
          index: 0,
          time: "Before",
          pnl: 0,
          profit: 0
        },
        {
          index: 1,
          time: "After",
          pnl: trade.profit,
          profit: trade.profit
        }
      ]
    }

    // Sort trades by time for multiple trades
    const sortedTrades = [...trades].sort((a, b) => {
      // Ensure we have valid dates
      const aTime = a.time_close ? new Date(a.time_close).getTime() : 0;
      const bTime = b.time_close ? new Date(b.time_close).getTime() : 0;
      return aTime - bTime;
    })

    // Create cumulative P&L data
    let cumulativePnL = 0
    return sortedTrades.map((trade, index) => {
      // Ensure profit is a valid number
      const profit = typeof trade.profit === 'number' ? trade.profit :
                    (typeof trade.profit === 'string' ? parseFloat(trade.profit) : 0);

      // Add to cumulative P&L only if it's a valid number
      if (!isNaN(profit)) {
        cumulativePnL += profit;
      }

      return {
        index,
        time: new Date(trade.time_close).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
        pnl: cumulativePnL,
        profit: profit
      }
    })
  }, [trades])

  // Calculate min and max values for better visualization
  const minValue = useMemo(() => {
    if (chartData.length === 0) return 0
    const min = Math.min(...chartData.map(d => d.pnl))
    return min < 0 ? min * 1.1 : min * 0.9
  }, [chartData])

  const maxValue = useMemo(() => {
    if (chartData.length === 0) return 0
    const max = Math.max(...chartData.map(d => d.pnl))
    return max > 0 ? max * 1.1 : max * 0.9
  }, [chartData])

  // If no trades, show empty chart
  if (chartData.length === 0) {
    return (
      <div className={cn("flex items-center justify-center h-full w-full", className)}>
        <div className="text-xs text-muted-foreground">No trade data</div>
      </div>
    )
  }

  return (
    <div className={cn("h-full w-full", className)}>
      <ResponsiveContainer width="100%" height="100%">
        <AreaChart
          data={chartData}
          margin={{ top: 0, right: 0, left: 0, bottom: 0 }}
        >
          <defs>
            <linearGradient id="profitGradient" x1="0" y1="0" x2="0" y2="1">
              <stop offset="0%" stopColor="#10b981" stopOpacity={0.45} />
              <stop offset="100%" stopColor="#ffffff" stopOpacity={0.2} />
            </linearGradient>
            <linearGradient id="lossGradient" x1="0" y1="1" x2="0" y2="0">
              <stop offset="0%" stopColor="#ef4444" stopOpacity={0.45} />
              <stop offset="100%" stopColor="#ffffff" stopOpacity={0.2} />
            </linearGradient>
          </defs>
          <XAxis
            dataKey="index"
            hide
          />
          <YAxis
            domain={[minValue, maxValue]}
            hide
          />
          <ReferenceLine
            y={0}
            stroke="rgba(100, 100, 100, 0.5)"
            strokeDasharray="3 3"
          />
          <Tooltip
            content={({ active, payload }) => {
              if (active && payload && payload.length) {
                return (
                  <div className="bg-popover/75 border border-border/70 rounded-md shadow-sm p-2 text-xs backdrop-blur-sm">
                    <p className="font-medium mb-0.5">{payload[0].payload.time}</p>
                    <div className="grid grid-cols-2 gap-x-3 gap-y-0.5">
                      <span className="text-muted-foreground">P&L:</span>
                      <span className={cn(
                        "font-medium text-right",
                        payload[0].payload.pnl >= 0 ? "text-emerald-500" : "text-rose-500"
                      )}>
                        ${payload[0].payload.pnl.toFixed(2)}
                      </span>

                      <span className="text-muted-foreground">Trade:</span>
                      <span className={cn(
                        "font-medium text-right",
                        payload[0].payload.profit >= 0 ? "text-emerald-500" : "text-rose-500"
                      )}>
                        ${payload[0].payload.profit.toFixed(2)}
                      </span>
                    </div>
                  </div>
                )
              }
              return null
            }}
          />
          {/* Area for profit (above 0) */}
          <Area
            type="monotone"
            dataKey={(data) => data.pnl > 0 ? data.pnl : 0}
            name="P&L"
            stroke="none"
            strokeWidth={0}
            fillOpacity={1}
            fill="url(#profitGradient)"
            activeDot={false}
            isAnimationActive={false}
            baseValue={0}
          />

          {/* Area for loss (below 0) */}
          <Area
            type="monotone"
            dataKey={(data) => data.pnl < 0 ? data.pnl : 0}
            name="P&L"
            stroke="none"
            strokeWidth={0}
            fillOpacity={1}
            fill="url(#lossGradient)"
            activeDot={false}
            isAnimationActive={false}
            baseValue={0}
          />

          {/* Main line to show the actual P&L curve */}
          <Area
            type="monotone"
            dataKey="pnl"
            name="P&L"
            stroke="#374151"
            strokeWidth={1.5}
            fillOpacity={0}
            activeDot={{ r: 4, strokeWidth: 0 }}
            isAnimationActive={false}
            connectNulls
          />
        </AreaChart>
      </ResponsiveContainer>
    </div>
  )
}
