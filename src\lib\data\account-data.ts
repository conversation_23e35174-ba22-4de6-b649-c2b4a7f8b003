import { getSupabaseBrowser } from '@/lib/supabase-browser'
import { getSupabaseServer } from '@/lib/supabase-server'
import { handleError, NotFoundError } from '@/lib/error-handler'
import { Database } from '@/types/supabase'

export type TradingAccount = Database["public"]["Tables"]["trading_accounts"]["Row"]
export type TradingSummary = Database["public"]["Tables"]["trading_summaries"]["Row"]
export type UserPreferences = Database["public"]["Tables"]["user_preferences"]["Row"]

/**
 * Get all trading accounts for a user
 */
export async function getUserAccounts(
  userId: string,
  options?: { useServer?: boolean }
): Promise<TradingAccount[]> {
  try {
    const supabase = options?.useServer
      ? await getSupabaseServer()
      : getSupabaseBrowser()

    const { data: accounts, error } = await supabase
      .from("trading_accounts")
      .select("*")
      .eq("user_id", userId)
      .order("updated_at", { ascending: false })

    if (error) {
      throw error
    }

    return accounts || []
  } catch (error) {
    return handleError(error, [], 'getUserAccounts')
  }
}

/**
 * Get a trading account by ID
 */
export async function getAccountById(
  userId: string,
  accountId: string,
  options?: { useServer?: boolean }
): Promise<TradingAccount | null> {
  try {
    const supabase = options?.useServer
      ? await getSupabaseServer()
      : getSupabaseBrowser()

    const { data, error } = await supabase
      .from("trading_accounts")
      .select("*")
      .eq("id", accountId)
      .eq("user_id", userId)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        throw new NotFoundError(`Account with ID ${accountId} not found`)
      }
      throw error
    }

    return data
  } catch (error) {
    return handleError(error, null, 'getAccountById')
  }
}

/**
 * Get the selected account ID from user preferences
 */
export async function getSelectedAccountId(
  userId: string,
  options?: { useServer?: boolean }
): Promise<string | null> {
  try {
    const supabase = options?.useServer
      ? await getSupabaseServer()
      : getSupabaseBrowser()

    // First try to get from user_preferences
    const { data: userPrefs, error: prefsError } = await supabase
      .from('user_preferences')
      .select('selected_account_id')
      .eq('user_id', userId)
      .maybeSingle()

    if (prefsError) {
      throw prefsError
    }

    if (userPrefs?.selected_account_id) {
      return userPrefs.selected_account_id
    }

    // If no preference is set, get the most recently updated account
    const { data: accounts, error: accountsError } = await supabase
      .from("trading_accounts")
      .select("id")
      .eq("user_id", userId)
      .order("updated_at", { ascending: false })
      .limit(1)

    if (accountsError) {
      throw accountsError
    }

    return accounts && accounts.length > 0 ? accounts[0].id : null
  } catch (error) {
    return handleError(error, null, 'getSelectedAccountId')
  }
}

/**
 * Set the selected account ID in user preferences
 */
export async function setSelectedAccountId(
  userId: string,
  accountId: string | null
): Promise<boolean> {
  try {
    const supabase = getSupabaseBrowser()

    // Check if user_preferences record exists
    const { data: existingPrefs, error: checkError } = await supabase
      .from('user_preferences')
      .select('user_id')
      .eq('user_id', userId)
      .maybeSingle()

    if (checkError) {
      throw checkError
    }

    if (existingPrefs) {
      // Update existing preferences
      const { error: updateError } = await supabase
        .from('user_preferences')
        .update({
          selected_account_id: accountId,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId)

      if (updateError) {
        throw updateError
      }
    } else {
      // Create new preferences
      const { error: insertError } = await supabase
        .from('user_preferences')
        .insert({
          user_id: userId,
          selected_account_id: accountId,
          updated_at: new Date().toISOString()
        })

      if (insertError) {
        throw insertError
      }
    }

    return true
  } catch (error) {
    return handleError(error, false, 'setSelectedAccountId')
  }
}

/**
 * Get trading summary for an account
 */
export async function getTradingSummary(
  accountId: string,
  options?: { useServer?: boolean }
): Promise<TradingSummary | null> {
  try {
    const supabase = options?.useServer
      ? await getSupabaseServer()
      : getSupabaseBrowser()

    const { data, error } = await supabase
      .from("trading_summaries")
      .select("*")
      .eq("account_id", accountId)
      .maybeSingle()

    if (error) {
      throw error
    }

    return data
  } catch (error) {
    return handleError(error, null, 'getTradingSummary')
  }
}
