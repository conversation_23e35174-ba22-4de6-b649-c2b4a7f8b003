"use client"

import { <PERSON>actN<PERSON> } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, Card<PERSON>itle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import { motion } from "framer-motion"

interface ChartCardProps {
  title: string
  children: ReactNode
  viewOptions?: {
    currentView: string
    options: { id: string; label: string }[]
    onViewChange: (view: string) => void
  }
  className?: string
}

export function ChartCard({ title, children, viewOptions, className }: ChartCardProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Card className={cn("dashboard-card bg-card hover:bg-card transition-all duration-300", className)}>
        <CardHeader className="flex flex-col sm:flex-row items-start sm:items-center justify-between space-y-2 sm:space-y-0 pb-2">
          <CardTitle className="text-base font-medium">
            {title}
          </CardTitle>
          {viewOptions && (
            <div className="flex items-center space-x-1 sm:space-x-2">
              {viewOptions.options.map((option) => (
                <Button
                  key={option.id}
                  variant="ghost"
                  size="sm"
                  className={cn(
                    "hover:bg-accent text-xs sm:text-sm px-2 sm:px-3 transition-all duration-200",
                    viewOptions.currentView === option.id && "bg-accent"
                  )}
                  onClick={() => viewOptions.onViewChange(option.id)}
                >
                  {option.label}
                </Button>
              ))}
            </div>
          )}
        </CardHeader>
        <CardContent>
          <div className="h-[250px] sm:h-[300px]">
            {children}
          </div>
        </CardContent>
      </Card>
    </motion.div>
  )
}
