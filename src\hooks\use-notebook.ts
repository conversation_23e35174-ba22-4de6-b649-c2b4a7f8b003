"use client"

import { useState, useEffect, useCallback } from "react"
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query"
import { toast } from "sonner"
import { useAccount } from "@/contexts/account-context"
import { getSup<PERSON><PERSON><PERSON><PERSON>er } from "@/lib/supabase"
import {
  NotebookEntry,
  NotebookEntryInsert,
  NotebookEntryUpdate,
  NotebookFilterOptions,
  NotebookSortOptions,
  NotebookTag,
  NotebookFolder,
  NotebookFolderInsert,
  NotebookFolderUpdate,
  NotebookFolderWithMeta
} from "@/types/notebook"

// Fetch notebook entries
async function fetchNotebookEntries(
  filters?: NotebookFilterOptions,
  sort?: NotebookSortOptions,
  limit?: number,
  offset?: number
): Promise<{ entries: NotebookEntry[], count: number }> {
  // Build URL with query parameters
  const params = new URLSearchParams()

  if (filters?.searchTerm) {
    params.append("searchTerm", filters.searchTerm)
  }

  if (filters?.tags && filters.tags.length > 0) {
    filters.tags.forEach(tag => params.append("tags", tag))
  }

  if (filters?.category) {
    params.append("category", filters.category)
  }

  if (filters?.folderId) {
    params.append("folderId", filters.folderId)
  }

  if (filters?.isTemplate !== undefined) {
    params.append("isTemplate", filters.isTemplate.toString())
  }

  if (filters?.accountId !== undefined) {
    if (filters.accountId === null) {
      params.append("accountId", "")  // Use empty string for null
    } else {
      params.append("accountId", filters.accountId)
    }
  }

  if (sort) {
    params.append("sortField", sort.field)
    params.append("sortOrder", sort.order)
  }

  if (limit) {
    params.append("limit", limit.toString())
  }

  if (offset) {
    params.append("offset", offset.toString())
  }

  // Log the request URL for debugging
  const requestUrl = `/api/notebook?${params.toString()}`;
  console.log(`Fetching notebook entries with URL: ${requestUrl}`);

  if (filters?.folderId) {
    console.log(`Folder filter active: ${filters.folderId}`);
  }

  const response = await fetch(requestUrl)

  if (!response.ok) {
    const error = await response.json()
    console.error(`Error fetching notebook entries: ${error.error || 'Unknown error'}`)
    throw new Error(error.error || "Failed to fetch notebook entries")
  }

  const data = await response.json();
  console.log(`Received ${data.entries?.length || 0} entries out of ${data.count || 0} total`);

  // Verify folder filtering is working correctly
  if (filters?.folderId && data.entries?.length > 0) {
    const entriesInFolder = data.entries.filter((entry: any) => entry.folder_id === filters.folderId);
    if (entriesInFolder.length !== data.entries.length) {
      console.warn(`Folder filter inconsistency: ${data.entries.length} entries returned, but only ${entriesInFolder.length} belong to folder ${filters.folderId}`);
    }
  }

  return data;
}

// Fetch a single notebook entry
async function fetchNotebookEntry(id: string): Promise<NotebookEntry> {
  const response = await fetch(`/api/notebook/${id}`)

  if (!response.ok) {
    const error = await response.json()
    throw new Error(error.error || "Failed to fetch notebook entry")
  }

  return await response.json()
}

// Create a new notebook entry
async function createNotebookEntry(entry: Omit<NotebookEntryInsert, "user_id">): Promise<NotebookEntry> {
  const response = await fetch("/api/notebook", {
    method: "POST",
    headers: {
      "Content-Type": "application/json"
    },
    body: JSON.stringify(entry)
  })

  if (!response.ok) {
    const error = await response.json()
    throw new Error(error.error || "Failed to create notebook entry")
  }

  return await response.json()
}

// Update a notebook entry
async function updateNotebookEntry(id: string, updates: NotebookEntryUpdate): Promise<NotebookEntry> {
  const response = await fetch(`/api/notebook/${id}`, {
    method: "PATCH",
    headers: {
      "Content-Type": "application/json"
    },
    body: JSON.stringify(updates)
  })

  if (!response.ok) {
    const error = await response.json()
    throw new Error(error.error || "Failed to update notebook entry")
  }

  return await response.json()
}

// Delete a notebook entry
async function deleteNotebookEntry(id: string): Promise<{ success: boolean }> {
  const response = await fetch(`/api/notebook/${id}`, {
    method: "DELETE"
  })

  if (!response.ok) {
    const error = await response.json()
    throw new Error(error.error || "Failed to delete notebook entry")
  }

  return await response.json()
}

// Fetch notebook categories (currently unused but kept for future use)
// async function fetchNotebookCategories(): Promise<NotebookCategory[]> {
//   const response = await fetch("/api/notebook/categories")

//   if (!response.ok) {
//     const error = await response.json()
//     throw new Error(error.error || "Failed to fetch notebook categories")
//   }

//   return await response.json()
// }



// Hook for notebook entries with real-time updates
export function useNotebookEntries(
  initialEntries: NotebookEntry[] = [],
  initialCount: number = 0,
  options?: {
    filters?: NotebookFilterOptions,
    sort?: NotebookSortOptions,
    limit?: number,
    enabled?: boolean
  }
) {
  const { selectedAccountId } = useAccount()
  const queryClient = useQueryClient()
  const [page, setPage] = useState(1)
  const limit = options?.limit || 20
  const offset = (page - 1) * limit

  // Merge filters with account ID
  const filters = {
    ...options?.filters,
    accountId: selectedAccountId
  }

  // Log the current filters for debugging
  useEffect(() => {
    if (filters?.folderId) {
      console.log(`useNotebookEntries hook - filtering by folder ID: ${filters.folderId}`);
    }
  }, [filters?.folderId]);

  // Determine if we have meaningful initial data
  const hasInitialData = initialEntries.length > 0

  // Use React Query to fetch notebook entries with aggressive caching
  const {
    data,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ["notebookEntries", filters, options?.sort, limit, offset],
    queryFn: () => fetchNotebookEntries(filters, options?.sort, limit, offset),
    initialData: hasInitialData ? { entries: initialEntries, count: initialCount } : undefined,
    enabled: options?.enabled !== false,
    staleTime: 0, // Always consider data stale for immediate updates
    refetchOnMount: true, // Always refetch on mount
    refetchOnWindowFocus: true, // Refetch when window gains focus
    refetchInterval: 30 * 1000 // Poll every 30 seconds for updates
  })

  // Set up real-time subscription for notebook entries (UI updates only)
  useEffect(() => {
    if (!selectedAccountId) return

    const supabase = getSupabaseBrowser()
    console.log('Setting up real-time subscription for notebook entries (UI updates only)')

    const subscription = supabase
      .channel('notebook_entries_changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'notebook_entries',
          filter: `account_id=eq.${selectedAccountId}`
        },
        (payload) => {
          console.log('Real-time notebook entry change (UI update only):', payload)

          // Only invalidate and refetch the notebook entries for UI updates
          // No automatic syncing - that happens manually on save
          queryClient.invalidateQueries({ queryKey: ["notebookEntries"] })

          // Reduced toast notifications to avoid spam
          if (payload.eventType === 'INSERT') {
            toast.success('New notebook entry added', { duration: 2000 })
          } else if (payload.eventType === 'DELETE') {
            toast.success('Notebook entry deleted', { duration: 2000 })
          }
          // Don't show toast for updates as they happen frequently during editing
        }
      )
      .subscribe()

    return () => {
      console.log('Cleaning up notebook entries subscription')
      subscription.unsubscribe()
    }
  }, [selectedAccountId, queryClient])

  // Handle pagination
  const handlePageChange = useCallback((newPage: number) => {
    setPage(newPage)
  }, [])

  return {
    entries: data?.entries || [],
    count: data?.count || 0,
    isLoading,
    error,
    refetch,
    page,
    limit,
    setPage: handlePageChange
  }
}

// Hook for a single notebook entry
export function useNotebookEntry(id?: string, initialData?: NotebookEntry) {
  const queryClient = useQueryClient()

  // Use React Query to fetch a notebook entry
  const {
    data: entry,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ["notebookEntry", id],
    queryFn: () => fetchNotebookEntry(id!),
    initialData,
    enabled: !!id
  })

  // Mutation for updating a notebook entry
  const updateMutation = useMutation({
    mutationFn: ({ id, updates }: { id: string, updates: NotebookEntryUpdate }) =>
      updateNotebookEntry(id, updates),
    onSuccess: (data) => {
      // Update the cache
      queryClient.setQueryData(["notebookEntry", id], data)

      // Invalidate the entries list to trigger a refetch
      queryClient.invalidateQueries({ queryKey: ["notebookEntries"] })

      toast.success("Notebook entry updated")
    },
    onError: (error: Error) => {
      toast.error(`Failed to update notebook entry: ${error.message}`)
    }
  })

  // Mutation for deleting a notebook entry
  const deleteMutation = useMutation({
    mutationFn: (id: string) => deleteNotebookEntry(id),
    onSuccess: () => {
      // Invalidate the entries list to trigger a refetch
      queryClient.invalidateQueries({ queryKey: ["notebookEntries"] })

      toast.success("Notebook entry deleted")
    },
    onError: (error: Error) => {
      toast.error(`Failed to delete notebook entry: ${error.message}`)
    }
  })

  // Function to update a notebook entry
  const updateEntry = useCallback((updates: NotebookEntryUpdate) => {
    if (!id) return
    updateMutation.mutate({ id, updates })
  }, [id, updateMutation])

  // Function to delete a notebook entry
  const deleteEntry = useCallback(() => {
    if (!id) return
    deleteMutation.mutate(id)
  }, [id, deleteMutation])

  return {
    entry,
    isLoading,
    error,
    refetch,
    updateEntry,
    deleteEntry,
    isUpdating: updateMutation.isPending,
    isDeleting: deleteMutation.isPending
  }
}

// Hook for creating a notebook entry
export function useCreateNotebookEntry() {
  const queryClient = useQueryClient()
  const { selectedAccountId } = useAccount()

  // Mutation for creating a notebook entry
  const createMutation = useMutation({
    mutationFn: createNotebookEntry,
    onSuccess: (data) => {
      // Invalidate the entries list to trigger a refetch
      queryClient.invalidateQueries({ queryKey: ["notebookEntries"] })

      toast.success("Notebook entry created")
    },
    onError: (error: Error) => {
      toast.error(`Failed to create notebook entry: ${error.message}`)
    }
  })

  // Function to create a notebook entry
  const createEntry = useCallback((entry: Omit<NotebookEntryInsert, "account_id" | "user_id">) => {
    return createMutation.mutateAsync({
      ...entry,
      account_id: selectedAccountId
    })
  }, [createMutation, selectedAccountId])

  return {
    createEntry,
    isCreating: createMutation.isPending
  }
}

// Fetch notebook folders
async function fetchNotebookFolders(accountId?: string | null): Promise<NotebookFolderWithMeta[]> {
  // Build URL with query parameters for account filtering
  const params = new URLSearchParams()

  if (accountId !== undefined) {
    if (accountId === null) {
      params.append("accountId", "")  // Use empty string for null
    } else {
      params.append("accountId", accountId)
    }
  }

  const url = `/api/notebook/folders${params.toString() ? `?${params.toString()}` : ''}`
  const response = await fetch(url)

  if (!response.ok) {
    const error = await response.json()
    throw new Error(error.error || "Failed to fetch notebook folders")
  }

  return await response.json()
}

// Fetch notebook tags
async function fetchNotebookTags(accountId?: string | null): Promise<NotebookTag[]> {
  // Build URL with query parameters for account filtering
  const params = new URLSearchParams()

  if (accountId !== undefined) {
    if (accountId === null) {
      params.append("accountId", "")  // Use empty string for null
    } else {
      params.append("accountId", accountId)
    }
  }

  const url = `/api/notebook/tags${params.toString() ? `?${params.toString()}` : ''}`
  const response = await fetch(url)

  if (!response.ok) {
    const error = await response.json()
    throw new Error(error.error || "Failed to fetch notebook tags")
  }

  return await response.json()
}

// Fetch a single notebook folder
async function fetchNotebookFolder(id: string): Promise<NotebookFolderWithMeta> {
  const response = await fetch(`/api/notebook/folders/${id}`)

  if (!response.ok) {
    const error = await response.json()
    throw new Error(error.error || "Failed to fetch notebook folder")
  }

  return await response.json()
}

// Create a new notebook folder
async function createNotebookFolder(folder: Omit<NotebookFolderInsert, "user_id">): Promise<NotebookFolder> {
  const response = await fetch("/api/notebook/folders", {
    method: "POST",
    headers: {
      "Content-Type": "application/json"
    },
    body: JSON.stringify(folder)
  })

  if (!response.ok) {
    const error = await response.json()
    throw new Error(error.error || "Failed to create notebook folder")
  }

  return await response.json()
}

// Update a notebook folder
async function updateNotebookFolder(id: string, updates: NotebookFolderUpdate): Promise<NotebookFolder> {
  const response = await fetch(`/api/notebook/folders/${id}`, {
    method: "PATCH",
    headers: {
      "Content-Type": "application/json"
    },
    body: JSON.stringify(updates)
  })

  if (!response.ok) {
    const error = await response.json()
    throw new Error(error.error || "Failed to update notebook folder")
  }

  return await response.json()
}

// Delete a notebook folder
async function deleteNotebookFolder(id: string): Promise<{ success: boolean }> {
  const response = await fetch(`/api/notebook/folders/${id}`, {
    method: "DELETE"
  })

  if (!response.ok) {
    const error = await response.json()
    throw new Error(error.error || "Failed to delete notebook folder")
  }

  return await response.json()
}

// Import journal entries
async function importJournalEntries(): Promise<{ trades: number, dailyJournals: number, total: number }> {
  console.log("Starting journal entries import process");

  const response = await fetch("/api/notebook/import", {
    method: "POST",
    headers: {
      "Content-Type": "application/json"
    }
  })

  if (!response.ok) {
    const error = await response.json()
    console.error("Error importing journal entries:", error);
    throw new Error(error.error || "Failed to import journal entries")
  }

  const data = await response.json()
  console.log(`Import completed: ${data.imported.trades} trade entries and ${data.imported.dailyJournals} daily journal entries imported`);
  return data.imported
}

// Hook for notebook folders
export function useNotebookFolders(initialFolders: NotebookFolderWithMeta[] = [], serverSelectedAccountId?: string | null) {
  const { selectedAccountId, isInitializing } = useAccount()

  // Use server account ID during initialization, then switch to client account ID
  // Handle the case where both might be null/undefined
  const effectiveAccountId = isInitializing
    ? (serverSelectedAccountId !== undefined ? serverSelectedAccountId : null)
    : selectedAccountId

  // Debug logging (can be removed in production)
  // console.log('useNotebookFolders:', {
  //   isInitializing,
  //   serverSelectedAccountId,
  //   selectedAccountId,
  //   effectiveAccountId,
  //   initialFoldersCount: initialFolders.length
  // })

  // Use React Query to fetch notebook folders with account filtering
  const {
    data: folders,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ["notebookFolders", effectiveAccountId],
    queryFn: () => fetchNotebookFolders(effectiveAccountId),
    // Use initial data and give it some time to be considered fresh
    initialData: initialFolders,
    staleTime: isInitializing ? 10000 : 5000, // Longer stale time during initialization
    refetchOnMount: !isInitializing, // Don't refetch during initialization
    refetchOnWindowFocus: true, // Still refetch when window gains focus
    // Always enable the query - React Query will handle the timing
    enabled: true,
    // Only refetch if we don't have initial data or if the account has changed
    refetchOnReconnect: true,
  })

  return {
    folders: folders || [],
    isLoading,
    error,
    refetch
  }
}

// Hook for notebook tags
export function useNotebookTags(initialTags: NotebookTag[] = []) {
  const { selectedAccountId } = useAccount()

  // Use React Query to fetch notebook tags with account filtering
  const {
    data: tags,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ["notebookTags", selectedAccountId],
    queryFn: () => fetchNotebookTags(selectedAccountId),
    // Always use initial data to prevent layout shifts, but mark as stale
    initialData: initialTags,
    staleTime: 0, // Always consider data stale for immediate updates
    refetchOnMount: true, // Always refetch on mount
    refetchOnWindowFocus: true, // Refetch when window gains focus
    // Always enable the query - React Query will handle the timing
    enabled: true,
  })

  return {
    tags: tags || [],
    isLoading,
    error,
    refetch
  }
}

// Hook for deleting notebook folders
export function useDeleteNotebookFolder() {
  const queryClient = useQueryClient()
  const { selectedAccountId } = useAccount()

  // Mutation for deleting a notebook folder
  const deleteMutation = useMutation({
    mutationFn: (id: string) => deleteNotebookFolder(id),
    onMutate: async (deletedId) => {
      // Cancel any outgoing refetches so they don't overwrite our optimistic update
      await queryClient.cancelQueries({ queryKey: ["notebookFolders", selectedAccountId] });

      // Snapshot the previous value
      const previousFolders = queryClient.getQueryData<NotebookFolderWithMeta[]>(["notebookFolders", selectedAccountId]);

      // Optimistically update to the new value
      queryClient.setQueryData<NotebookFolderWithMeta[]>(["notebookFolders", selectedAccountId], (old) => {
        if (!old) return [];
        return old.filter(folder => folder.id !== deletedId);
      });

      // Return a context object with the snapshotted value
      return { previousFolders };
    },
    onSuccess: () => {
      // Invalidate the folders list to ensure consistency
      queryClient.invalidateQueries({ queryKey: ["notebookFolders", selectedAccountId] });

      // Invalidate the entries list to trigger a refetch
      queryClient.invalidateQueries({ queryKey: ["notebookEntries"] });

      toast.success("Folder deleted");
    },
    onError: (error: Error, _variables, context) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      if (context?.previousFolders) {
        queryClient.setQueryData(["notebookFolders", selectedAccountId], context.previousFolders);
      }
      toast.error(`Failed to delete folder: ${error.message}`);
    },
    onSettled: () => {
      // Always refetch after error or success to ensure data consistency
      queryClient.invalidateQueries({ queryKey: ["notebookFolders", selectedAccountId] });
    }
  })

  // Function to delete a notebook folder
  const deleteFolder = useCallback((id: string) => {
    return deleteMutation.mutateAsync(id)
  }, [deleteMutation])

  return {
    deleteFolder,
    isDeleting: deleteMutation.isPending
  }
}

// Hook for updating notebook folders
export function useUpdateNotebookFolder() {
  const queryClient = useQueryClient()
  const { selectedAccountId } = useAccount()

  // Mutation for updating a notebook folder
  const updateMutation = useMutation({
    mutationFn: ({ id, updates }: { id: string, updates: NotebookFolderUpdate }) =>
      updateNotebookFolder(id, updates),
    onMutate: async ({ id, updates }) => {
      // Cancel any outgoing refetches so they don't overwrite our optimistic update
      await queryClient.cancelQueries({ queryKey: ["notebookFolders", selectedAccountId] });

      // Snapshot the previous value
      const previousFolders = queryClient.getQueryData<NotebookFolderWithMeta[]>(["notebookFolders", selectedAccountId]);

      // Optimistically update to the new value
      queryClient.setQueryData<NotebookFolderWithMeta[]>(["notebookFolders", selectedAccountId], (old) => {
        if (!old) return [];
        return old.map(folder => {
          if (folder.id === id) {
            // If we're updating the description (for category assignment), handle it specially
            if (updates.description) {
              // For moving to a category
              if (updates.description.includes('Category:')) {
                return { ...folder, ...updates };
              }
            }
            return { ...folder, ...updates };
          }
          return folder;
        });
      });

      // Return a context object with the snapshotted value
      return { previousFolders };
    },
    onSuccess: () => {
      // Invalidate the folders list to ensure consistency
      queryClient.invalidateQueries({ queryKey: ["notebookFolders", selectedAccountId] });

      toast.success("Folder updated");
    },
    onError: (error: Error, _variables, context) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      if (context?.previousFolders) {
        queryClient.setQueryData(["notebookFolders", selectedAccountId], context.previousFolders);
      }
      toast.error(`Failed to update folder: ${error.message}`);
    },
    onSettled: () => {
      // Always refetch after error or success to ensure data consistency
      queryClient.invalidateQueries({ queryKey: ["notebookFolders", selectedAccountId] });
    }
  })

  // Function to update a notebook folder
  const updateFolder = useCallback((id: string, updates: NotebookFolderUpdate) => {
    return updateMutation.mutateAsync({ id, updates })
  }, [updateMutation])

  return {
    updateFolder,
    isUpdating: updateMutation.isPending
  }
}

// Hook for a single notebook folder
export function useNotebookFolder(id?: string, initialData?: NotebookFolderWithMeta) {
  const queryClient = useQueryClient()
  const { selectedAccountId } = useAccount()

  // Use React Query to fetch a notebook folder
  const {
    data: folder,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ["notebookFolder", id],
    queryFn: () => fetchNotebookFolder(id!),
    initialData,
    enabled: !!id
  })

  // Mutation for updating a notebook folder
  const updateMutation = useMutation({
    mutationFn: ({ id, updates }: { id: string, updates: NotebookFolderUpdate }) =>
      updateNotebookFolder(id, updates),
    onSuccess: (data) => {
      // Update the cache
      queryClient.setQueryData(["notebookFolder", id], data)

      // Invalidate the folders list to trigger a refetch
      queryClient.invalidateQueries({ queryKey: ["notebookFolders", selectedAccountId] })

      toast.success("Folder updated")
    },
    onError: (error: Error) => {
      toast.error(`Failed to update folder: ${error.message}`)
    }
  })

  // Mutation for deleting a notebook folder
  const deleteMutation = useMutation({
    mutationFn: (id: string) => deleteNotebookFolder(id),
    onMutate: async (deletedId) => {
      // Cancel any outgoing refetches so they don't overwrite our optimistic update
      await queryClient.cancelQueries({ queryKey: ["notebookFolders", selectedAccountId] });

      // Snapshot the previous value
      const previousFolders = queryClient.getQueryData<NotebookFolderWithMeta[]>(["notebookFolders", selectedAccountId]);

      // Optimistically update to the new value
      queryClient.setQueryData<NotebookFolderWithMeta[]>(["notebookFolders", selectedAccountId], (old) => {
        if (!old) return [];
        return old.filter(folder => folder.id !== deletedId);
      });

      // Return a context object with the snapshotted value
      return { previousFolders };
    },
    onSuccess: () => {
      // Invalidate the folders list to ensure consistency
      queryClient.invalidateQueries({ queryKey: ["notebookFolders", selectedAccountId] });

      // Invalidate the entries list to trigger a refetch
      queryClient.invalidateQueries({ queryKey: ["notebookEntries"] });

      toast.success("Folder deleted");
    },
    onError: (error: Error, _variables, context) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      if (context?.previousFolders) {
        queryClient.setQueryData(["notebookFolders", selectedAccountId], context.previousFolders);
      }
      toast.error(`Failed to delete folder: ${error.message}`);
    },
    onSettled: () => {
      // Always refetch after error or success to ensure data consistency
      queryClient.invalidateQueries({ queryKey: ["notebookFolders", selectedAccountId] });
    }
  })

  // Function to update a notebook folder
  const updateFolder = useCallback((updates: NotebookFolderUpdate) => {
    if (!id) return
    updateMutation.mutate({ id, updates })
  }, [id, updateMutation])

  // Function to delete a notebook folder
  const deleteFolder = useCallback(() => {
    if (!id) return
    deleteMutation.mutate(id)
  }, [id, deleteMutation])

  return {
    folder,
    isLoading,
    error,
    refetch,
    updateFolder,
    deleteFolder,
    isUpdating: updateMutation.isPending,
    isDeleting: deleteMutation.isPending
  }
}

// Hook for creating a notebook folder
export function useCreateNotebookFolder() {
  const queryClient = useQueryClient()
  const { selectedAccountId } = useAccount()

  // Mutation for creating a notebook folder
  const createMutation = useMutation({
    mutationFn: (folder: Omit<NotebookFolderInsert, "user_id">) =>
      createNotebookFolder({ ...folder, account_id: selectedAccountId }),
    onSuccess: (data) => {
      // Immediately update the cache with the new folder to make it appear instantly
      // This provides a better user experience than waiting for a refetch
      queryClient.setQueryData<NotebookFolderWithMeta[]>(["notebookFolders", selectedAccountId], (oldData) => {
        if (!oldData) return [{ ...data, count: 0, isExpanded: false }];
        return [...oldData, { ...data, count: 0, isExpanded: false }];
      });

      // Also invalidate the query to ensure data consistency
      queryClient.invalidateQueries({ queryKey: ["notebookFolders", selectedAccountId] })

      toast.success("Folder created")
    },
    onError: (error: Error) => {
      // Provide more user-friendly error messages
      if (error.message.includes("already exists")) {
        toast.error("This folder name is already in use. Please choose a different name.", {
          duration: 5000,
        });
      } else {
        toast.error(`Failed to create folder: ${error.message}`, {
          duration: 5000,
        });
      }
    }
  })

  // Function to create a notebook folder
  const createFolder = useCallback((folder: Omit<NotebookFolderInsert, "user_id">) => {
    return createMutation.mutateAsync(folder)
  }, [createMutation])

  return {
    createFolder,
    isCreating: createMutation.isPending
  }
}

// Hook for importing journal entries
export function useImportJournalEntries() {
  const queryClient = useQueryClient()
  const { selectedAccountId } = useAccount()

  // Mutation for importing journal entries
  const importMutation = useMutation({
    mutationFn: importJournalEntries,
    onSuccess: (data) => {
      console.log("Journal entries import successful, invalidating queries");

      // Force immediate invalidation of all related queries
      queryClient.invalidateQueries({ queryKey: ["notebookEntries"] });
      queryClient.invalidateQueries({ queryKey: ["notebookFolders", selectedAccountId] });

      // Add a slight delay and then force another refetch to ensure UI is updated
      setTimeout(() => {
        console.log("Performing delayed refetch after import");
        queryClient.invalidateQueries({ queryKey: ["notebookEntries"] });
        queryClient.invalidateQueries({ queryKey: ["notebookFolders", selectedAccountId] });
      }, 1000);

      if (data.total > 0) {
        toast.success(`Successfully imported ${data.total} journal entries (${data.trades} trades, ${data.dailyJournals} daily journals)`);
      } else {
        toast.info("No new journal entries to import");
      }
    },
    onError: (error: Error) => {
      console.error("Error importing journal entries:", error);
      toast.error(`Failed to import journal entries: ${error.message}`);
    }
  })

  // Function to import journal entries
  const importEntries = useCallback(() => {
    return importMutation.mutateAsync()
  }, [importMutation])

  return {
    importEntries,
    isImporting: importMutation.isPending
  }
}
