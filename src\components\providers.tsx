"use client"

import { ThemeProvider } from "@/components/theme-provider"
import { SessionProvider } from "@/components/session-provider"
import { AccountProvider } from "@/contexts/account-context"
import { SidebarProvider } from "@/contexts/sidebar-context"
import { QueryProvider } from "@/providers/query-provider"
import { ToastProvider } from "@/components/ui/toast-provider"
import { Toaster } from "sonner"
import { useState, useEffect } from "react"
import { RadixFixProvider } from "@/components/radix-fix-provider"

export function Providers({ children }: { children: React.ReactNode }) {
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
      </div>
    )
  }

  return (
    <ThemeProvider
      attribute="class"
      defaultTheme="dark"
      enableSystem
      disableTransitionOnChange
    >
      <QueryProvider>
        <SessionProvider>
          <ToastProvider>
            <RadixFixProvider>
              <SidebarProvider>
                <AccountProvider>
                  {children}
                  <Toaster richColors />
                </AccountProvider>
              </SidebarProvider>
            </RadixFixProvider>
          </ToastProvider>
        </SessionProvider>
      </QueryProvider>
    </ThemeProvider>
  )
}