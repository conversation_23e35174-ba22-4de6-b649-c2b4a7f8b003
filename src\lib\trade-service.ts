import { getSupaba<PERSON><PERSON>rowser } from "@/lib/supabase"
import { type ProcessedData } from "@/lib/excel-processor"

export async function saveTradeData(userId: string, data: ProcessedData, selectedAccountId?: string) {
  const supabase = getSupabaseBrowser()

  try {
    console.log('Saving trading account data...')
    let accountId = selectedAccountId

    // If no account ID is provided, create or get the account from the data
    if (!accountId) {
      // Save account information
      const { data: accountData, error: accountError } = await supabase
        .from("trading_accounts")
        .upsert({
          user_id: userId,
          name: data.account.name,
          account_number: data.account.account,
          broker: data.account.company,
          updated_at: new Date().toISOString(),
        }, { onConflict: 'account_number' })
        .select('id')
        .single()

      if (accountError) {
        console.error('Error saving account data:', accountError)
        throw new Error(`Error saving account data: ${accountError.message || JSON.stringify(accountError)}`)
      }

      if (!accountData) {
        throw new Error('Failed to retrieve account ID after saving')
      }

      accountId = accountData.id
    }

    console.log(`Using account ID: ${accountId}`)

    // Prepare trades data with account_id
    const tradesData = data.trades.map((trade) => {
      // Create the base trade object without comment (which doesn't exist in the database)
      const tradeData = {
        user_id: userId,
        account_id: accountId,
        position_id: trade.position_id,
        symbol: trade.symbol,
        type: trade.type,
        volume: trade.volume,
        price_open: trade.price_open,
        price_close: trade.price_close,
        sl: trade.sl || null,
        tp: trade.tp || null,
        time_open: new Date(trade.time_open).toISOString(),
        time_close: new Date(trade.time_close).toISOString(),
        commission: trade.commission,
        swap: trade.swap,
        profit: trade.profit,
        strategy_id: trade.strategy_id || null,
        setup_id: trade.setup_id || null,
      };

      console.log('Prepared trade data:', tradeData);
      return tradeData;
    })

    console.log(`Saving ${tradesData.length} trades...`)

    // For manually entered trades, check if a trade with similar properties already exists
    if (data.trades.length === 1) { // Only do this check for single trade entries (manual entry)
      const trade = tradesData[0];
      console.log('Checking for duplicate trade:', trade.symbol, trade.time_open);

      try {
        // Check for trades with the same symbol and timestamps (within a small window)
        const { data: existingTrades, error: checkError } = await supabase
          .from("trades")
          .select("id")
          .eq("user_id", userId)
          .eq("account_id", accountId)
          .eq("symbol", trade.symbol)
          .eq("type", trade.type)
          .eq("volume", trade.volume)
          .eq("profit", trade.profit)

        if (checkError) {
          console.error('Error checking for existing trade:', checkError);
          // Continue with insertion even if check fails
        } else if (existingTrades && existingTrades.length > 0) {
          console.log(`Similar trade already exists, skipping insertion to prevent duplicates`);
          return; // Exit early, no need to insert anything
        }
      } catch (error) {
        console.error('Error in duplicate check:', error);
        // Continue with insertion even if check fails
      }
    }

    // Save trades in batches to avoid hitting size limits
    const batchSize = 100
    for (let i = 0; i < tradesData.length; i += batchSize) {
      const batch = tradesData.slice(i, i + batchSize)
      const { error: tradesError } = await supabase.from("trades").upsert(batch)

      if (tradesError) {
        console.error(`Error saving trades batch ${i}-${i + batch.length}:`, tradesError)
        throw new Error(`Error saving trades batch ${i}-${i + batch.length}: ${tradesError.message || JSON.stringify(tradesError)}`)
      }
    }

    console.log('Trades saved successfully')

    // Helper function to limit decimal places to 2
    const limitDecimals = (value: number | undefined | null, decimals = 2): number => {
      if (value === undefined || value === null) return 0;
      // Convert to number, round to specified decimal places, then convert back to number to remove trailing zeros
      return parseFloat(value.toFixed(decimals));
    };

    // Prepare summary data with all required fields
    const summaryData = {
      user_id: userId,
      account_id: accountId,
      initial_balance: limitDecimals(data.summary.initial_balance || 10000), // Add initial balance
      total_net_profit: limitDecimals(data.summary.total_net_profit || 0),
      gross_profit: limitDecimals(data.summary.gross_profit || 0),
      gross_loss: limitDecimals(data.summary.gross_loss || 0),
      profit_factor: limitDecimals(data.summary.profit_factor || 0),
      expected_payoff: limitDecimals(data.summary.expected_payoff || 0),
      recovery_factor: limitDecimals(data.summary.recovery_factor || 0),
      sharpe_ratio: limitDecimals(data.summary.sharpe_ratio || 0),
      balance_drawdown_absolute: limitDecimals(data.summary.balance_drawdown_absolute || 0),
      balance_drawdown_maximal: data.summary.balance_drawdown_maximal || '0.00 (0.00%)',
      balance_drawdown_relative: data.summary.balance_drawdown_relative || '0.00% (0.00)',
      total_trades: data.summary.total_trades || 0,
      short_trades_won: data.summary.short_trades_won || '0 (0.00%)',
      long_trades_won: data.summary.long_trades_won || '0 (0.00%)',
      profit_trades: data.summary.profit_trades || '0.00%',
      loss_trades: data.summary.loss_trades || '0.00%',
      largest_profit_trade: limitDecimals(data.summary.largest_profit_trade || 0),
      largest_loss_trade: limitDecimals(data.summary.largest_loss_trade || 0),
      average_profit_trade: limitDecimals(data.summary.average_profit_trade || 0),
      average_loss_trade: limitDecimals(data.summary.average_loss_trade || 0),
      maximum_consecutive_wins: data.summary.maximum_consecutive_wins || '0 (0.00)',
      maximum_consecutive_losses: data.summary.maximum_consecutive_losses || '0 (0.00)',
      maximal_consecutive_profit: data.summary.maximal_consecutive_profit || '0.00 (0)',
      maximal_consecutive_loss: data.summary.maximal_consecutive_loss || '0.00 (0)',
      average_consecutive_wins: limitDecimals(data.summary.average_consecutive_wins || 0),
      average_consecutive_losses: limitDecimals(data.summary.average_consecutive_losses || 0),
    }

    console.log('Saving trading summary...', summaryData)

    // First, check if a summary already exists for this account
    const { data: existingSummary, error: fetchError } = await supabase
      .from("trading_summaries")
      .select("id")
      .eq("account_id", accountId)
      .maybeSingle()

    if (fetchError) {
      console.error('Error checking for existing summary:', fetchError)
      throw new Error(`Error checking for existing summary: ${fetchError.message || JSON.stringify(fetchError)}`)
    }

    let savedSummary
    let summaryError

    // If summary exists, update it; otherwise, insert a new one
    if (existingSummary) {
      console.log('Updating existing summary with ID:', existingSummary.id)
      const result = await supabase
        .from("trading_summaries")
        .update(summaryData)
        .eq("id", existingSummary.id)
        .select()

      savedSummary = result.data
      summaryError = result.error
    } else {
      console.log('Inserting new summary')
      const result = await supabase
        .from("trading_summaries")
        .insert(summaryData)
        .select()

      savedSummary = result.data
      summaryError = result.error
    }

    if (summaryError) {
      console.error('Error saving summary data:', summaryError)
      throw new Error(`Error saving summary data: ${summaryError.message || JSON.stringify(summaryError)}`)
    }

    console.log('Trading summary saved successfully', savedSummary)
  } catch (error) {
    // Ensure we have a proper error message
    const errorMessage = error instanceof Error
      ? error.message
      : typeof error === 'object' && error !== null
        ? JSON.stringify(error)
        : 'Unknown error';

    console.error('Error in saveTradeData:', error)
    throw new Error(`Error in saveTradeData: ${errorMessage}`)
  }
}

export async function getTrades(userId: string, accountId?: string | null) {
  return await getUserTrades(userId, accountId)
}

export async function getUserTrades(
  userId: string,
  accountId?: string | null,
  startDate?: string | Date | null,
  endDate?: string | Date | null,
  symbol?: string | null,
  tradeType?: string | null,
  minProfit?: number | null,
  maxProfit?: number | null,
  tags?: string[] | null
) {
  const supabase = getSupabaseBrowser()

  try {
    console.log('Fetching user trades with filters:', { startDate, endDate, symbol, tradeType, minProfit, maxProfit, tags })

    // No demo data - only use real data from the database

    // If accountId is explicitly null (user has unselected all accounts), return empty array
    if (accountId === null) {
      console.log('No account selected, returning empty trades array')
      return []
    }

    // Start building the query
    let query = supabase
      .from("trades")
      .select("*")

    // Add account filter
    if (accountId) {
      query = query.eq("account_id", accountId)
    }

    // Add date filters if provided
    if (startDate) {
      const formattedStartDate = typeof startDate === 'string'
        ? startDate
        : startDate instanceof Date
          ? startDate.toISOString()
          : null

      if (formattedStartDate) {
        query = query.gte("time_close", formattedStartDate)
      }
    }

    if (endDate) {
      const formattedEndDate = typeof endDate === 'string'
        ? endDate
        : endDate instanceof Date
          ? endDate.toISOString()
          : null

      if (formattedEndDate) {
        query = query.lte("time_close", formattedEndDate)
      }
    }

    // Add symbol filter if provided
    if (symbol) {
      query = query.eq("symbol", symbol)
    }

    // Add trade type filter if provided
    if (tradeType) {
      query = query.eq("type", tradeType)
    }

    // Add profit range filters if provided
    if (minProfit !== undefined && minProfit !== null) {
      query = query.gte("profit", minProfit)
    }

    if (maxProfit !== undefined && maxProfit !== null) {
      query = query.lte("profit", maxProfit)
    }

    // Add tag filter if provided
    if (tags && tags.length > 0) {
      query = query.overlaps("tags", tags)
    }

    // Add ordering
    query = query.order("time_close", { ascending: false })

    // Execute the query
    const { data: trades, error } = await query

    if (error) {
      console.error('Error fetching trades:', error)
      throw error
    }

    console.log(`Retrieved ${trades?.length || 0} trades with applied filters`)
    return trades || []
  } catch (error) {
    console.error('Error in getUserTrades:', error)
    return [] // Return empty array instead of throwing to prevent UI errors
  }
}

export async function getUserSummary(userId: string, accountId?: string | null) {
  const supabase = getSupabaseBrowser()

  try {
    console.log('Fetching user trading summary for userId:', userId)

    // No demo data - only use real data from the database

    // If accountId is explicitly null (user has unselected all accounts), return null
    if (accountId === null) {
      console.log('No account selected, returning null for summary')
      return null
    }

    // If an account ID is specified, get the summary for that account
    if (accountId) {
      console.log(`Fetching summary for specific account: ${accountId}`)
      const { data: summary, error } = await supabase
        .from("trading_summaries")
        .select("*")
        .eq("account_id", accountId)
        .maybeSingle()

      if (error) {
        console.error('Error fetching trading summary:', error)
        throw error
      }

      console.log('Trading summary retrieved successfully')
      return summary
    }

    // Otherwise, get the summary for the user's most recently updated account
    // First, get the user's accounts
    const { data: accounts, error: accountsError } = await supabase
      .from("trading_accounts")
      .select("id")
      .eq("user_id", userId)
      .order("updated_at", { ascending: false })
      .limit(1)

    if (accountsError) {
      console.error('Error fetching trading accounts:', accountsError)
      throw accountsError
    }

    if (!accounts || accounts.length === 0) {
      console.log('No trading accounts found for user')
      return null
    }

    const defaultAccountId = accounts[0].id
    console.log(`Found default account ID: ${defaultAccountId}`)

    // Now get the summary for this account
    const { data: summary, error } = await supabase
      .from("trading_summaries")
      .select("*")
      .eq("account_id", defaultAccountId)
      .maybeSingle()

    if (error) {
      console.error('Error fetching trading summary:', error)
      throw error
    }

    console.log('Trading summary retrieved successfully')
    return summary
  } catch (error) {
    console.error('Error in getUserSummary:', error)
    return null // Return null instead of throwing to prevent UI errors
  }
}

// Generate a demo summary from trades
function generateDemoSummary(trades: any[]) {
  const totalTrades = trades.length
  const winningTrades = trades.filter((t: any) => t.profit > 0).length
  const losingTrades = trades.filter((t: any) => t.profit < 0).length

  const winRate = totalTrades > 0 ? (winningTrades / totalTrades) * 100 : 0
  const lossRate = totalTrades > 0 ? (losingTrades / totalTrades) * 100 : 0

  const totalProfit = trades.reduce((sum: number, t: any) => sum + t.profit, 0)
  const grossProfit = trades.filter((t: any) => t.profit > 0).reduce((sum: number, t: any) => sum + t.profit, 0)
  const grossLoss = Math.abs(trades.filter((t: any) => t.profit < 0).reduce((sum: number, t: any) => sum + t.profit, 0))

  const profitFactor = grossLoss > 0 ? grossProfit / grossLoss : grossProfit > 0 ? 999 : 0
  const expectedPayoff = totalTrades > 0 ? totalProfit / totalTrades : 0

  // Calculate max drawdown
  let balance = 10000 // Starting balance
  let peak = balance
  let maxDrawdown = 0
  let maxDrawdownAmount = 0

  const sortedTrades = [...trades].sort((a, b) =>
    new Date(a.time_close).getTime() - new Date(b.time_close).getTime()
  )

  sortedTrades.forEach(trade => {
    balance += trade.profit
    peak = Math.max(peak, balance)

    const drawdown = peak - balance
    const drawdownPercent = peak > 0 ? (drawdown / peak) * 100 : 0

    if (drawdown > maxDrawdownAmount) {
      maxDrawdownAmount = drawdown
      maxDrawdown = drawdownPercent
    }
  })

  // Format values as strings to match expected format
  const winRateStr = `${winRate.toFixed(2)}%`
  const lossRateStr = `${lossRate.toFixed(2)}%`
  const maxDrawdownStr = `${maxDrawdownAmount.toFixed(2)} (${maxDrawdown.toFixed(2)}%)`
  const maxDrawdownRelStr = `${maxDrawdown.toFixed(2)}% (${maxDrawdownAmount.toFixed(2)})`

  console.log('Generated demo summary with total_trades:', totalTrades)
  console.log('Win rate string:', winRateStr)

  return {
    id: 'demo-summary',
    user_id: 'demo-user',
    account_id: 'demo-account',
    initial_balance: 10000, // Add initial balance
    total_net_profit: totalProfit,
    gross_profit: grossProfit,
    gross_loss: grossLoss,
    profit_factor: profitFactor,
    expected_payoff: expectedPayoff,
    recovery_factor: 2.5, // Demo value
    sharpe_ratio: 1.2, // Demo value
    balance_drawdown_absolute: maxDrawdownAmount,
    balance_drawdown_maximal: maxDrawdownStr,
    balance_drawdown_relative: maxDrawdownRelStr,
    total_trades: totalTrades,
    short_trades_won: `${Math.floor(winningTrades/2)} (${(winRate/2).toFixed(2)}%)`,
    long_trades_won: `${Math.ceil(winningTrades/2)} (${(winRate/2).toFixed(2)}%)`,
    profit_trades: winRateStr,
    loss_trades: lossRateStr,
    largest_profit_trade: trades.reduce((max: number, t: any) => t.profit > max ? t.profit : max, 0),
    largest_loss_trade: trades.reduce((min: number, t: any) => t.profit < min ? t.profit : min, 0),
    average_profit_trade: winningTrades > 0 ? grossProfit / winningTrades : 0,
    average_loss_trade: losingTrades > 0 ? -grossLoss / losingTrades : 0,
    maximum_consecutive_wins: '8 (1240.50)',
    maximum_consecutive_losses: '3 (-450.75)',
    maximal_consecutive_profit: '1240.50 (8)',
    maximal_consecutive_loss: '450.75 (3)',
    average_consecutive_wins: 3.2,
    average_consecutive_losses: 1.5
  }
}

export async function getUserAccounts(userId: string, bustCache: boolean = false) {
  const supabase = getSupabaseBrowser()

  try {
    console.log('Fetching user trading accounts...', bustCache ? '(cache busted)' : '')

    // If this is a demo user, return a demo account
    if (userId === 'demo-user') {
      console.log('Using demo account')
      return [{
        id: 'demo-account',
        user_id: 'demo-user',
        name: 'Demo Trading Account',
        account_number: 'DEMO-12345',
        broker: 'Demo Broker',
        updated_at: new Date().toISOString()
      }]
    }

    let query = supabase
      .from("trading_accounts")
      .select("*")
      .eq("user_id", userId)
      .order("updated_at", { ascending: false })

    // Add cache busting if requested by adding a timestamp parameter
    if (bustCache) {
      // Force a fresh query by adding a unique parameter
      query = query.limit(1000)
    }

    const { data: accounts, error } = await query

    if (error) {
      console.error('Error fetching trading accounts:', error)
      throw error
    }

    console.log(`Retrieved ${accounts?.length || 0} trading accounts`)
    return accounts || []
  } catch (error) {
    console.error('Error in getUserAccounts:', error)
    return [] // Return empty array instead of throwing to prevent UI errors
  }
}

export async function createAccount(userId: string, accountData: { name: string, broker: string, account_number: string }) {
  const supabase = getSupabaseBrowser()

  try {
    const { data, error } = await supabase
      .from("trading_accounts")
      .insert({
        user_id: userId,
        name: accountData.name,
        broker: accountData.broker,
        account_number: accountData.account_number,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .select()
      .single()

    if (error) {
      console.error('Error creating account:', error)
      throw new Error(`Error creating account: ${error.message || JSON.stringify(error)}`)
    }

    return data
  } catch (error) {
    console.error('Error in createAccount:', error)
    throw error
  }
}

export async function updateAccount(accountId: string, accountData: { name: string, broker: string }) {
  try {
    const response = await fetch(`/api/accounts/${accountId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(accountData),
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.error || 'Failed to update account')
    }

    return await response.json()
  } catch (error) {
    console.error('Error updating account:', error)
    throw error
  }
}

export async function deleteAccount(accountId: string, forceDelete: boolean = false) {
  try {
    const url = `/api/accounts/${accountId}${forceDelete ? '?force=true' : ''}`
    console.log('Attempting to delete account:', accountId, 'Force delete:', forceDelete)
    console.log('DELETE request URL:', url)

    const response = await fetch(url, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    console.log('Delete response status:', response.status)
    console.log('Delete response ok:', response.ok)

    if (!response.ok) {
      const errorData = await response.json()
      console.error('Delete account error response:', errorData)

      // Provide more specific error messages
      let errorMessage = errorData.error || 'Failed to delete account'
      if (errorData.debugInfo) {
        console.error('Delete account debug info:', errorData.debugInfo)
        errorMessage += ` (Account ID: ${errorData.debugInfo.accountId})`
      }
      if (errorData.accountStillExists) {
        errorMessage += ' - Account still exists in database after deletion attempt'
      }

      throw new Error(errorMessage)
    }

    const result = await response.json()
    console.log('Delete account success:', result)
    return result
  } catch (error) {
    console.error('Error deleting account:', error)
    throw error
  }
}

export async function getAccountTradeCount(accountId: string): Promise<number> {
  const supabase = getSupabaseBrowser()

  try {
    const { count, error } = await supabase
      .from("trades")
      .select("*", { count: 'exact', head: true })
      .eq("account_id", accountId)

    if (error) {
      console.error('Error getting trade count:', error)
      return 0
    }

    return count || 0
  } catch (error) {
    console.error('Error in getAccountTradeCount:', error)
    return 0
  }
}

// Generate demo trades for testing
function generateDemoTrades() {
  const symbols = ['EURUSD', 'GBPUSD', 'USDJPY', 'AUDUSD', 'USDCAD', 'NZDUSD', 'USDCHF', 'XAUUSD', 'BTCUSD']
  const trades = []
  const now = new Date()

  // Generate 100 random trades over the last 3 months
  for (let i = 0; i < 100; i++) {
    const symbol = symbols[Math.floor(Math.random() * symbols.length)]
    const type = Math.random() > 0.5 ? 'buy' : 'sell'
    const volume = parseFloat((Math.random() * 0.5 + 0.1).toFixed(2))

    // Random dates within the last 3 months
    const daysAgo = Math.floor(Math.random() * 90)
    const hoursAgo = Math.floor(Math.random() * 24)
    const timeClose = new Date(now)
    timeClose.setDate(timeClose.getDate() - daysAgo)
    timeClose.setHours(timeClose.getHours() - hoursAgo)

    const timeOpen = new Date(timeClose)
    timeOpen.setHours(timeOpen.getHours() - Math.floor(Math.random() * 12))

    // Generate prices based on the symbol
    let priceOpen, priceClose
    switch (symbol) {
      case 'EURUSD':
        priceOpen = parseFloat((Math.random() * 0.05 + 1.05).toFixed(5))
        break
      case 'GBPUSD':
        priceOpen = parseFloat((Math.random() * 0.05 + 1.25).toFixed(5))
        break
      case 'USDJPY':
        priceOpen = parseFloat((Math.random() * 5 + 145).toFixed(3))
        break
      case 'XAUUSD':
        priceOpen = parseFloat((Math.random() * 100 + 1900).toFixed(2))
        break
      case 'BTCUSD':
        priceOpen = parseFloat((Math.random() * 5000 + 60000).toFixed(2))
        break
      default:
        priceOpen = parseFloat((Math.random() * 0.1 + 1.0).toFixed(5))
    }

    // Generate a random price change (-2% to +2%)
    const priceChange = priceOpen * (Math.random() * 0.04 - 0.02)
    priceClose = parseFloat((priceOpen + priceChange).toFixed(5))

    // Calculate profit based on type, volume and price difference
    let profit
    if (type === 'buy') {
      profit = (priceClose - priceOpen) * volume * 100000
    } else {
      profit = (priceOpen - priceClose) * volume * 100000
    }
    profit = parseFloat(profit.toFixed(2))

    trades.push({
      id: `demo-${i}`,
      position_id: 1000 + i,
      user_id: 'demo-user',
      account_id: 'demo-account',
      symbol,
      type,
      volume,
      price_open: priceOpen,
      price_close: priceClose,
      time_open: timeOpen.toISOString(),
      time_close: timeClose.toISOString(),
      commission: parseFloat((-volume * 7).toFixed(2)),
      swap: 0,
      profit,
      sl: null,
      tp: null
    })
  }

  return trades
}