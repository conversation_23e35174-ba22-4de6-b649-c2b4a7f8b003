"use client"

import { useState, useEffect, useCallback } from "react"
import { useQuery, useQueryClient } from "@tanstack/react-query"
import { toast } from "sonner"
import { useAccount } from "@/contexts/account-context"
import { getSupabaseBrowser } from "@/lib/supabase"

// Hook for trades with real-time updates and optimized performance
export function useTradesRealtime(
  initialData?: any,
  options?: {
    enabled?: boolean
    filters?: any
  }
) {
  const { selectedAccountId } = useAccount()
  const queryClient = useQueryClient()

  // Use React Query with optimized caching for trades
  const {
    data,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ["trades", selectedAccountId, options?.filters],
    queryFn: async () => {
      // This would typically call your existing trade fetching logic
      // For now, return the initial data to avoid breaking existing functionality
      return initialData || { trades: [], count: 0 }
    },
    initialData,
    enabled: options?.enabled !== false && !!selectedAccountId,
    staleTime: 30 * 1000, // 30 seconds - reduce excessive refetching
    refetchOnMount: false, // Don't refetch on mount if we have data
    refetchOnWindowFocus: false, // Rely on real-time updates instead
    refetchInterval: false // Disable polling - use real-time updates
  })

  // Set up real-time subscription for trades (UI updates only)
  useEffect(() => {
    if (!selectedAccountId) return

    const supabase = getSupabaseBrowser()
    console.log('Setting up real-time subscription for trades (UI updates only)')

    const subscription = supabase
      .channel('trades_changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'trades',
          filter: `account_id=eq.${selectedAccountId}`
        },
        (payload) => {
          console.log('Real-time trade change (UI update only):', payload)

          // Only invalidate and refetch trades data for UI updates
          // No automatic syncing - that happens manually on save
          queryClient.invalidateQueries({ queryKey: ["trades"] })
          queryClient.invalidateQueries({ queryKey: ["journal"] })
          queryClient.invalidateQueries({ queryKey: ["dashboard-trades"] })

          // Minimal toast notifications to avoid spam during editing
          if (payload.eventType === 'INSERT') {
            toast.success('New trade added', { duration: 2000 })
          } else if (payload.eventType === 'DELETE') {
            toast.success('Trade deleted', { duration: 2000 })
          }
          // Don't show toast for updates as they happen frequently during editing
        }
      )
      .subscribe()

    return () => {
      console.log('Cleaning up trades subscription')
      subscription.unsubscribe()
    }
  }, [selectedAccountId, queryClient])

  return {
    data,
    isLoading,
    error,
    refetch
  }
}

// Hook for optimistic updates to trades
export function useOptimisticTradeUpdate() {
  const queryClient = useQueryClient()

  const updateTradeOptimistically = useCallback((tradeId: string, updates: any) => {
    // Optimistically update the trade in all relevant queries
    queryClient.setQueryData(["trades"], (oldData: any) => {
      if (!oldData?.trades) return oldData

      return {
        ...oldData,
        trades: oldData.trades.map((trade: any) =>
          trade.id === tradeId ? { ...trade, ...updates } : trade
        )
      }
    })

    // Also update individual trade queries
    queryClient.setQueryData(["trade", tradeId], (oldTrade: any) => {
      if (!oldTrade) return oldTrade
      return { ...oldTrade, ...updates }
    })

    // Update journal data
    queryClient.invalidateQueries({ queryKey: ["journal"] })
  }, [queryClient])

  return { updateTradeOptimistically }
}

// Hook for real-time notebook-trade sync status
export function useNotebookTradeSync() {
  const queryClient = useQueryClient()
  const { selectedAccountId } = useAccount()

  // Set up subscription for both notebook and trade changes
  useEffect(() => {
    if (!selectedAccountId) return

    const supabase = getSupabaseBrowser()
    console.log('Setting up bidirectional sync monitoring')

    // Monitor notebook changes that affect trades
    const notebookSubscription = supabase
      .channel('notebook_sync_monitor')
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'notebook_entries',
          filter: `account_id=eq.${selectedAccountId}`
        },
        (payload) => {
          console.log('Notebook change detected, invalidating trade data')
          // Invalidate trade queries to reflect synced changes
          queryClient.invalidateQueries({ queryKey: ["trades"] })
          queryClient.invalidateQueries({ queryKey: ["journal"] })
        }
      )
      .subscribe()

    // Monitor trade changes that affect notebooks
    const tradeSubscription = supabase
      .channel('trade_sync_monitor')
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'trades',
          filter: `account_id=eq.${selectedAccountId}`
        },
        (payload) => {
          console.log('Trade change detected, invalidating notebook data')
          // Invalidate notebook queries to reflect synced changes
          queryClient.invalidateQueries({ queryKey: ["notebookEntries"] })
        }
      )
      .subscribe()

    return () => {
      console.log('Cleaning up sync monitoring subscriptions')
      notebookSubscription.unsubscribe()
      tradeSubscription.unsubscribe()
    }
  }, [selectedAccountId, queryClient])

  return null // This hook just sets up monitoring
}
