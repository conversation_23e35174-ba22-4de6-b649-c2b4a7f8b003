-- Create storage bucket for strategy images if it doesn't exist
INSERT INTO storage.buckets (id, name, public)
VALUES ('strategyimages', 'strategyimages', true)
ON CONFLICT (id) DO NOTHING;

-- Set up storage policies for strategy images
DROP POLICY IF EXISTS "Strategy images are publicly accessible" ON storage.objects;
DROP POLICY IF EXISTS "Users can upload their own strategy images" ON storage.objects;
DROP POLICY IF EXISTS "Users can update their own strategy images" ON storage.objects;
DROP POLICY IF EXISTS "Users can delete their own strategy images" ON storage.objects;

-- Create policies for the strategyimages bucket
-- 1. Allow anyone to view images in the strategyimages bucket
CREATE POLICY "Strategy images are publicly accessible"
  ON storage.objects FOR SELECT
  USING (bucket_id = 'strategyimages');

-- 2. Allow authenticated users to upload images to the strategyimages bucket
CREATE POLICY "Users can upload their own strategy images"
  ON storage.objects FOR INSERT
  TO authenticated
  WITH CHECK (
    bucket_id = 'strategyimages' AND
    auth.uid()::text = owner
  );

-- 3. Allow authenticated users to update their own images
CREATE POLICY "Users can update their own strategy images"
  ON storage.objects FOR UPDATE
  TO authenticated
  USING (
    bucket_id = 'strategyimages' AND
    auth.uid()::text = owner
  );

-- 4. Allow authenticated users to delete their own images
CREATE POLICY "Users can delete their own strategy images"
  ON storage.objects FOR DELETE
  TO authenticated
  USING (
    bucket_id = 'strategyimages' AND
    auth.uid()::text = owner
  );
