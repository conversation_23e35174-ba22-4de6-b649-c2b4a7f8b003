# GitHub Actions Workflows

This directory contains GitHub Actions workflows for the TradePivot application.

## 🚀 Deploy to Production Repository

**File**: `deploy-to-production.yml`

### Purpose
This workflow syncs the TradePivot Next.js application from the development repository to a separate production repository, including only production-necessary files and excluding development artifacts.

### Features
- ✅ **Manual Trigger Only**: Prevents accidental deployments
- ✅ **Master Branch Only**: Ensures only stable code is deployed
- ✅ **Build Verification**: Validates the application builds successfully before deployment
- ✅ **Smart File Filtering**: Automatically excludes development files
- ✅ **Secure Authentication**: Uses GitHub secrets for repository access
- ✅ **Comprehensive Logging**: Detailed status reporting and summaries
- ✅ **Error Handling**: Robust error handling with fallback options

### Required Repository Secrets

Before using this workflow, you must configure these secrets in your GitHub repository:

1. **`PRODUCTION_REPO_TOKEN`**
   - **Type**: GitHub Personal Access Token (Classic)
   - **Permissions Required**: `repo` (Full control of private repositories)
   - **How to Create**:
     1. Go to GitHub Settings → Developer settings → Personal access tokens → Tokens (classic)
     2. Click "Generate new token (classic)"
     3. Select scopes: `repo`, `workflow` (if needed)
     4. Copy the token and add it to your repository secrets

2. **`PRODUCTION_REPO_URL`**
   - **Type**: String
   - **Format**: `https://github.com/username/production-repo.git`
   - **Example**: `https://github.com/mycompany/tradepivot-production.git`

### How to Use

#### Step 1: Set Up Production Repository
1. Create a new GitHub repository for production deployment
2. This can be public or private (your choice)
3. Do not initialize with README, .gitignore, or license (the workflow will handle this)

#### Step 2: Configure Secrets
1. Go to your development repository → Settings → Secrets and variables → Actions
2. Click "New repository secret"
3. Add both required secrets (see above)

#### Step 3: Run the Workflow
1. Go to your repository → Actions tab
2. Find "Deploy to Production Repository" workflow
3. Click "Run workflow"
4. Choose options:
   - **Force push**: Use with caution, overwrites production repository
   - **Custom commit message**: Optional custom message for the deployment

### What Gets Deployed

#### ✅ Included Files:
- Next.js application source code (`src/`, `app/`, `pages/`)
- Configuration files (`package.json`, `next.config.js`, `tailwind.config.js`)
- Supabase migrations and Edge Functions (`supabase/`)
- UI components and styling
- TypeScript configuration
- Production environment configuration

#### ❌ Excluded Files:
- Test files (`*.test.*`, `*.spec.*`, `__tests__/`, `coverage/`)
- Documentation (`*.md`, `README*`, `docs/`)
- Development environment files (`.env.local`, `.env.development`)
- IDE configuration (`.vscode/`, `.idea/`)
- Build artifacts (`node_modules/`, `.next/`, `dist/`, `build/`)
- Development tools (`.eslintrc*`, `.prettierrc*`, `jest.config.*`)
- CI/CD files (`.github/`, `.travis.yml`, etc.)
- Log files and temporary directories

### Workflow Steps

1. **Branch Validation**: Ensures workflow only runs on master branch
2. **Secret Validation**: Verifies required secrets are configured
3. **Source Checkout**: Downloads the latest code from master branch
4. **Environment Setup**: Configures Node.js and installs dependencies
5. **Build Verification**: Runs `npm run build` to ensure code compiles
6. **File Filtering**: Creates clean production copy with only necessary files
7. **Repository Sync**: Initializes git and configures production repository
8. **Deployment**: Pushes production code to target repository
9. **Summary Report**: Generates detailed deployment summary
10. **Cleanup**: Removes temporary files

### Security Features

- **No Secrets in Logs**: Authentication tokens are never exposed in workflow logs
- **Secure Token Handling**: Uses GitHub's secret management system
- **Branch Protection**: Only allows deployment from master branch
- **Manual Trigger**: Prevents accidental deployments
- **Environment Isolation**: Development environment files are excluded

### Troubleshooting

#### Common Issues:

**1. "Required secrets not configured"**
- Solution: Add `PRODUCTION_REPO_TOKEN` and `PRODUCTION_REPO_URL` to repository secrets

**2. "Authentication failed"**
- Solution: Verify the Personal Access Token has `repo` permissions and hasn't expired

**3. "Build failed"**
- Solution: Ensure your code builds successfully locally with `npm run build`

**4. "Push failed"**
- Solution: Try running with "Force push" option enabled (use with caution)

**5. "Wrong branch"**
- Solution: Ensure you're running the workflow from the master branch

#### Getting Help:
- Check the workflow logs for detailed error messages
- Verify all secrets are correctly configured
- Ensure the production repository exists and is accessible
- Test the Personal Access Token permissions

### Best Practices

1. **Test First**: Always test your application locally before deploying
2. **Review Changes**: Check what files will be deployed in the workflow summary
3. **Use Custom Messages**: Provide meaningful commit messages for deployments
4. **Monitor Deployments**: Review workflow logs and summaries
5. **Backup Production**: Consider backing up your production repository before major deployments
6. **Token Security**: Regularly rotate your Personal Access Tokens
7. **Branch Strategy**: Only deploy from stable master branch commits

### Example Usage

```bash
# Typical deployment workflow:
1. Develop and test features in feature branches
2. Merge stable code to master branch
3. Run "Deploy to Production Repository" workflow
4. Monitor deployment in Actions tab
5. Verify production repository has been updated
```

This workflow ensures a clean, secure, and reliable deployment process for your TradePivot application.
