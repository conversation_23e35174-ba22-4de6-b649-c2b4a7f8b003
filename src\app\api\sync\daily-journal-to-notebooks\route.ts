import { NextRequest, NextResponse } from "next/server"
import { getSupabaseServer } from "@/lib/supabase-server"

export async function POST(request: NextRequest) {
  try {
    console.log('=== MANUAL DAILY JOURNAL TO NOTEBOOKS SYNC API CALLED ===')

    const { dailyJournalId } = await request.json()

    if (!dailyJournalId) {
      return NextResponse.json(
        { error: "Daily journal ID is required" },
        { status: 400 }
      )
    }

    // Get authenticated user
    const supabase = await getSupabaseServer()
    const { data: { user }, error: userError } = await supabase.auth.getUser()

    if (userError || !user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    // Fetch the daily journal entry to get current data
    const { data: dailyJournal, error: fetchError } = await supabase
      .from("daily_journal_entries")
      .select("*")
      .eq("id", dailyJournalId)
      .eq("user_id", user.id)
      .single()

    if (fetchError || !dailyJournal) {
      return NextResponse.json(
        { error: "Daily journal entry not found" },
        { status: 404 }
      )
    }

    // Call the sync Edge Function manually (following the same pattern as trades)
    const edgeFunctionUrl = `${process.env.NEXT_PUBLIC_SUPABASE_URL}/functions/v1/sync-daily-journal-changes`
    const serviceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

    if (!serviceKey) {
      console.error('SUPABASE_SERVICE_ROLE_KEY not found in environment variables')
      return NextResponse.json(
        { error: "Service key not configured - check environment variables" },
        { status: 500 }
      )
    }

    console.log('Calling Edge Function:', edgeFunctionUrl)
    console.log('Daily journal data:', {
      id: dailyJournal.id,
      date: dailyJournal.date,
      note: dailyJournal.note?.substring(0, 100) + '...',
      tags: dailyJournal.tags,
      screenshots: dailyJournal.screenshots?.length || 0
    })

    const syncResponse = await fetch(edgeFunctionUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${serviceKey}`,
      },
      body: JSON.stringify({
        type: 'UPDATE',
        record: dailyJournal,
        old_record: null // We don't have old record for manual sync, but the function handles this
      })
    })

    if (!syncResponse.ok) {
      const errorText = await syncResponse.text()
      console.error('Edge function sync failed:', errorText)
      return NextResponse.json(
        { error: "Sync failed", details: errorText },
        { status: 500 }
      )
    }

    const syncResult = await syncResponse.json()
    console.log('Manual sync completed successfully:', syncResult)

    return NextResponse.json({
      success: true,
      message: "Daily journal entry synced to notebooks successfully",
      syncResult
    })

  } catch (error) {
    console.error('Error in manual daily journal sync:', error)
    return NextResponse.json(
      { error: "Internal server error", details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}
