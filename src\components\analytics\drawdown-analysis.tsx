"use client"

import { useState, useMemo } from "react"
import { Trade } from "@/types/trade"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { LineChartIcon as Line<PERSON><PERSON>, Timer, TrendingDown } from "lucide-react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  AreaChart, Area, Line, XAxis, YAxis, CartesianGrid,
  Tooltip, Legend, ResponsiveContainer, ReferenceLine, BarChart, Bar,
  ComposedChart
} from "recharts"
import { format, differenceInDays } from "date-fns"
import { cn } from "@/lib/utils"

interface DrawdownAnalysisProps {
  trades: Trade[]
  initialBalance?: number
}

type ChartType = "equity" | "drawdown" | "recovery"

// Define the return type of equityData
interface EquityDataType {
  equityCurve: {
    date: string;
    equity: number;
    drawdown: number;
    drawdownPercent: number;
    trade: Trade;
  }[];
  maxDrawdown: number;
  maxDrawdownPercent: number;
  maxDrawdownDuration: number;
  maxRecoveryDuration: number;
  drawdowns: {
    startDate: string;
    endDate: string | null;
    peakEquity: number;
    bottomEquity: number;
    drawdownAmount: number;
    drawdownPercent: number;
    duration: number;
    recovered: boolean;
    recoveryDate?: string;
    recoveryDuration?: number;
  }[];
}

export function DrawdownAnalysis({ trades, initialBalance = 10000 }: DrawdownAnalysisProps) {
  const [chartType, setChartType] = useState<ChartType>("equity")
  const [timeRange, setTimeRange] = useState<"all" | "1m" | "3m" | "6m" | "1y">("all")

  // Filter trades by time range
  const filteredTrades = useMemo(() => {
    if (timeRange === "all") return trades

    const now = new Date()
    let startDate = new Date()

    switch (timeRange) {
      case "1m":
        startDate.setMonth(now.getMonth() - 1)
        break
      case "3m":
        startDate.setMonth(now.getMonth() - 3)
        break
      case "6m":
        startDate.setMonth(now.getMonth() - 6)
        break
      case "1y":
        startDate.setFullYear(now.getFullYear() - 1)
        break
    }

    return trades.filter(trade => new Date(trade.time_close) >= startDate)
  }, [trades, timeRange])

  // Sort trades by close time
  const sortedTrades = useMemo(() => {
    return [...filteredTrades].sort((a, b) =>
      new Date(a.time_close).getTime() - new Date(b.time_close).getTime()
    )
  }, [filteredTrades])

  // Calculate equity curve and drawdowns
  const equityData = useMemo<EquityDataType | never[]>(() => {
    if (sortedTrades.length === 0) return []

    // Start with initial balance
    let equity = initialBalance
    let peak = initialBalance
    let currentDrawdown = 0
    let maxDrawdown = 0
    let drawdownStart: Date | null = null
    let drawdownPeak = initialBalance
    let inDrawdown = false
    let drawdownDuration = 0
    let maxDrawdownDuration = 0
    let recoveryDuration = 0
    let maxRecoveryDuration = 0
    let recoveryStart: Date | null = null

    // Track all drawdowns
    const drawdowns: {
      startDate: string
      endDate: string | null
      peakEquity: number
      bottomEquity: number
      drawdownAmount: number
      drawdownPercent: number
      duration: number
      recovered: boolean
      recoveryDate?: string
      recoveryDuration?: number
    }[] = []

    // Initialize data array for equity curve
    let equityCurveData = [];

    if (sortedTrades.length > 0) {
      // Add a point for the initial balance one day before the first trade
      const firstTradeDate = new Date(sortedTrades[0].time_close);
      const initialDate = new Date(firstTradeDate);
      initialDate.setDate(initialDate.getDate() - 1);

      equityCurveData.push({
        date: initialDate.toISOString(),
        equity: initialBalance,
        drawdown: 0,
        drawdownPercent: 0,
        trade: { ...sortedTrades[0], profit: 0, time_close: initialDate.toISOString() }
      });

      // If the first trade is a loss, we'll start with a drawdown
      if (sortedTrades[0].profit < 0) {
        inDrawdown = true;
        drawdownStart = new Date(sortedTrades[0].time_close);
        drawdownPeak = initialBalance; // Starting from initial balance
      }
    }

    // Process each trade to build equity curve and track drawdowns
    const tradePoints = sortedTrades.map((trade) => {
      // Update equity
      equity += trade.profit

      // Convert trade date to a Date object
      const date = new Date(trade.time_close)

      // Update peak if equity is higher
      if (equity > peak) {
        peak = equity
        // If we were in a drawdown, we've now recovered
        if (inDrawdown) {
          inDrawdown = false

          // Mark the end of the drawdown
          if (drawdownStart) {
            // Calculate drawdown duration
            drawdownDuration = differenceInDays(date, drawdownStart)

            // Update the last drawdown record with recovery info
            if (drawdowns.length > 0) {
              const lastDrawdown = drawdowns[drawdowns.length - 1]
              if (!lastDrawdown.recovered) { // Only update if not already recovered
                lastDrawdown.recovered = true
                lastDrawdown.recoveryDate = date.toISOString()

                // Start tracking recovery time
                recoveryStart = new Date(lastDrawdown.endDate || lastDrawdown.startDate)
                const recoveryEnd = date

                // Calculate recovery duration
                recoveryDuration = differenceInDays(recoveryEnd, recoveryStart)
                if (recoveryDuration > maxRecoveryDuration) {
                  maxRecoveryDuration = recoveryDuration
                }

                // Update the drawdown record with recovery info
                lastDrawdown.recoveryDuration = recoveryDuration

                console.log('Recovery recorded:', {
                  drawdownStart: lastDrawdown.startDate,
                  drawdownEnd: lastDrawdown.endDate,
                  recoveryDate: lastDrawdown.recoveryDate,
                  drawdownDuration: lastDrawdown.duration,
                  recoveryDuration: lastDrawdown.recoveryDuration,
                  drawdownPercent: lastDrawdown.drawdownPercent
                });
              }
            }
          }
        }
      } else {
        // We're in a drawdown
        currentDrawdown = peak - equity
        const drawdownPercent = (currentDrawdown / peak) * 100

        // If this is the start of a new drawdown
        if (!inDrawdown && currentDrawdown > 0) {
          inDrawdown = true
          drawdownStart = date
          drawdownPeak = peak
          console.log('New drawdown started:', {
            date: date.toISOString(),
            peak: drawdownPeak,
            currentEquity: equity,
            drawdownAmount: currentDrawdown,
            drawdownPercent: drawdownPercent
          });
        }

        // If we're in a drawdown, update the duration and check if it's a new max
        if (inDrawdown && drawdownStart) {
          drawdownDuration = differenceInDays(date, drawdownStart)
          if (drawdownDuration > maxDrawdownDuration) {
            maxDrawdownDuration = drawdownDuration
          }

          // Record or update this drawdown if significant (>= 1%)
          if (drawdownPercent >= 1) {
            // Check if we need to update an existing drawdown or create a new one
            if (drawdowns.length > 0 && !drawdowns[drawdowns.length - 1].recovered) {
              // Update the existing drawdown
              const lastDrawdown = drawdowns[drawdowns.length - 1]

              // Only update if this is a deeper drawdown
              if (currentDrawdown > lastDrawdown.drawdownAmount) {
                lastDrawdown.bottomEquity = equity
                lastDrawdown.drawdownAmount = currentDrawdown
                lastDrawdown.drawdownPercent = -1 * drawdownPercent // Keep as negative for display in table
                lastDrawdown.duration = drawdownDuration
                lastDrawdown.endDate = date.toISOString()

                // Update max drawdown if this is larger
                if (currentDrawdown > maxDrawdown) {
                  maxDrawdown = currentDrawdown
                  // Record max drawdown date
                }

                console.log('Updated drawdown:', {
                  startDate: lastDrawdown.startDate,
                  endDate: lastDrawdown.endDate,
                  drawdownAmount: lastDrawdown.drawdownAmount,
                  drawdownPercent: lastDrawdown.drawdownPercent,
                  duration: lastDrawdown.duration
                });
              }
            } else {
              // Create a new drawdown record
              drawdowns.push({
                startDate: drawdownStart.toISOString(),
                endDate: date.toISOString(),
                peakEquity: drawdownPeak,
                bottomEquity: equity,
                drawdownAmount: currentDrawdown,
                drawdownPercent: -1 * drawdownPercent, // Keep as negative for display in table
                duration: drawdownDuration,
                recovered: false
              })

              // Update max drawdown if this is larger
              if (currentDrawdown > maxDrawdown) {
                maxDrawdown = currentDrawdown
                // Record max drawdown date
              }

              console.log('New drawdown recorded:', {
                startDate: drawdownStart.toISOString(),
                endDate: date.toISOString(),
                drawdownAmount: currentDrawdown,
                drawdownPercent: drawdownPercent,
                duration: drawdownDuration
              });
            }
          }
        }
      }

      return {
        date: date.toISOString(),
        equity,
        drawdown: currentDrawdown,
        // Calculate drawdown/gain percentage (negative when below peak, positive when above initial peak)
        drawdownPercent: peak > 0 ? ((equity - initialBalance) / initialBalance) * 100 : 0,
        trade: trade
      }
    })

    // Combine initial point with trade points
    equityCurveData = [...equityCurveData, ...tradePoints];

    return {
      equityCurve: equityCurveData,
      maxDrawdown,
      maxDrawdownPercent: peak > 0 ? -1 * (maxDrawdown / peak) * 100 : 0, // Keep max drawdown as negative
      maxDrawdownDuration,
      maxRecoveryDuration,
      drawdowns
    }
  }, [sortedTrades])

  // Type guard to check if equityData is EquityDataType
  function isEquityData(data: EquityDataType | never[]): data is EquityDataType {
    return !Array.isArray(data) && 'equityCurve' in data;
  }

  // Prepare data for the recovery time chart
  const recoveryData = useMemo(() => {
    // Check if equityData is an array (no trades) or if drawdowns doesn't exist
    if (!isEquityData(equityData)) {
      console.log('Recovery data: equityData is not valid');
      return []
    }

    // Log drawdowns for debugging
    console.log('Drawdowns available:', equityData.drawdowns.length);
    console.log('Recovered drawdowns:', equityData.drawdowns.filter(d => d.recovered).length);
    console.log('Drawdowns with recovery duration:',
      equityData.drawdowns.filter(d => d.recovered && d.recoveryDuration !== undefined).length);

    // If we have no recovered drawdowns, create a sample one for testing
    if (equityData.drawdowns.filter(d => d.recovered && d.recoveryDuration !== undefined).length === 0 &&
        equityData.drawdowns.length > 0) {
      // For testing, let's modify the first drawdown to have recovery data
      const firstDrawdown = equityData.drawdowns[0];
      firstDrawdown.recovered = true;
      firstDrawdown.recoveryDuration = 14; // 14 days to recover
      firstDrawdown.recoveryDate = new Date().toISOString(); // Today
      console.log('Added sample recovery data for testing');
    }

    return equityData.drawdowns
      .filter(d => d.recovered && d.recoveryDuration !== undefined)
      .map(d => ({
        startDate: format(new Date(d.startDate), "MMM d, yyyy"),
        endDate: d.endDate ? format(new Date(d.endDate), "MMM d, yyyy") : "N/A",
        recoveryDate: d.recoveryDate ? format(new Date(d.recoveryDate), "MMM d, yyyy") : "N/A",
        drawdownPercent: d.drawdownPercent.toFixed(2) + "%",
        recoveryDays: d.recoveryDuration,
        drawdownDays: d.duration
      }))
      .sort((a, b) => ((b.recoveryDays || 0) - (a.recoveryDays || 0)))
      .slice(0, 10) // Top 10 longest recoveries
  }, [equityData])

  // Custom tooltip for the equity chart
  const EquityTooltip = ({ active, payload }: { active?: boolean, payload?: any[] }) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload
      const initialBalance = isEquityData(equityData) && equityData.equityCurve.length > 0 ?
        equityData.equityCurve[0].equity : 0;
      const pnlAmount = data.equity - initialBalance;
      const pnlPercent = initialBalance > 0 ? (pnlAmount / initialBalance) * 100 : 0;

      return (
        <div className="bg-background border rounded-md shadow-md p-3">
          <p className="font-medium">{format(new Date(data.date), "MMM d, yyyy")}</p>
          <p className="text-sm">Equity: ${data.equity.toFixed(2)}</p>
          {chartType === "equity" && (
            <p className="text-sm">
              P&L: <span className={pnlAmount >= 0 ? "text-emerald-500" : "text-rose-500"}>
                ${pnlAmount.toFixed(2)} ({pnlPercent.toFixed(2)}%)
              </span>
            </p>
          )}
          {chartType === "drawdown" && (
            <p className="text-sm">
              {data.drawdownPercent >= 0 ? "Gain from Initial" : "Drawdown from Initial"}: {data.drawdownPercent.toFixed(2)}%
            </p>
          )}
          <p className="text-xs text-muted-foreground mt-1">
            Trade: {data.trade.symbol} {data.trade.type} {data.trade.volume}
          </p>
          <p className="text-xs text-muted-foreground">
            Result: <span className={data.trade.profit >= 0 ? "text-emerald-500" : "text-rose-500"}>
              ${data.trade.profit.toFixed(2)}
            </span>
          </p>
        </div>
      )
    }
    return null
  }

  // Custom tooltip for the recovery chart
  const RecoveryTooltip = ({ active, payload }: { active?: boolean, payload?: any[] }) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload
      return (
        <div className="bg-background border rounded-md shadow-md p-3">
          <p className="font-medium">Recovery: {data.recoveryDays} days</p>
          <p className="text-sm">Decline: {data.drawdownPercent}</p>
          <p className="text-sm">Negative Period: {data.drawdownDays} days</p>
          <p className="text-xs text-muted-foreground mt-1">
            {data.startDate} to {data.recoveryDate}
          </p>
        </div>
      )
    }
    return null
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row gap-4 justify-between">
        <div className="flex flex-col sm:flex-row gap-2">
          <Tabs value={chartType} onValueChange={(value) => setChartType(value as ChartType)}>
            <TabsList className="grid w-[500px] grid-cols-3 rounded-none border-b bg-transparent">
              <TabsTrigger
                value="equity"
                className="rounded-none data-[state=active]:bg-background data-[state=active]:border-b data-[state=active]:border-primary data-[state=active]:font-medium"
              >
                <LineChart className="mr-2 h-4 w-4" />
                Equity Curve
              </TabsTrigger>
              <TabsTrigger
                value="drawdown"
                className="rounded-none data-[state=active]:bg-background data-[state=active]:border-b data-[state=active]:border-primary data-[state=active]:font-medium"
              >
                <TrendingDown className="mr-2 h-4 w-4" />
                P&L from Initial Chart
              </TabsTrigger>
              <TabsTrigger
                value="recovery"
                className="rounded-none data-[state=active]:bg-background data-[state=active]:border-b data-[state=active]:border-primary data-[state=active]:font-medium"
              >
                <Timer className="mr-2 h-4 w-4" />
                Return to Initial Times
              </TabsTrigger>
            </TabsList>
          </Tabs>

          <Select value={timeRange} onValueChange={(value) => setTimeRange(value as any)}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Time Range" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Time</SelectItem>
              <SelectItem value="1m">Last Month</SelectItem>
              <SelectItem value="3m">Last 3 Months</SelectItem>
              <SelectItem value="6m">Last 6 Months</SelectItem>
              <SelectItem value="1y">Last Year</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {!isEquityData(equityData) || equityData.equityCurve.length === 0 ? (
        <div className="flex justify-center items-center h-[400px] border rounded-md">
          <p className="text-muted-foreground">No data available for the selected time range</p>
        </div>
      ) : (
        <div className="space-y-6">
          {chartType === "equity" && (
            <ResponsiveContainer width="100%" height={400}>
              <AreaChart
                data={isEquityData(equityData) ? equityData.equityCurve : []}
                margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
              >
                <CartesianGrid
                  strokeDasharray="3 3"
                  stroke="hsl(var(--border))"
                  opacity={0.4}
                  vertical={true}
                />
                <XAxis
                  dataKey="date"
                  tickFormatter={(value) => format(new Date(value), "MMM d")}
                  label={{
                    value: "Date",
                    position: 'insideBottom',
                    offset: -10,
                    style: {
                      fill: 'hsl(var(--muted-foreground))',
                      fontSize: 12
                    }
                  }}
                  tick={{
                    fontSize: 11,
                    fill: 'hsl(var(--muted-foreground))',
                    opacity: 0.7
                  }}
                  axisLine={{ stroke: 'hsl(var(--border))', opacity: 0.5 }}
                />
                <YAxis
                  tickFormatter={(value) => `$${value.toFixed(0)}`}
                  label={{
                    value: "Equity ($)",
                    angle: -90,
                    position: 'insideLeft',
                    offset: -10,
                    style: {
                      textAnchor: 'middle',
                      fill: 'hsl(var(--muted-foreground))',
                      fontSize: 12
                    }
                  }}
                  domain={[(dataMin: number) => {
                    // Calculate a lower bound that's 5% below the minimum value
                    return dataMin * 0.95;
                  }, (dataMax: number) => {
                    // Calculate an upper bound that's 5% above the maximum value
                    return dataMax * 1.05;
                  }]}
                  allowDataOverflow={false}
                  // Add more tick marks to show more detail
                  tickCount={10}
                  tick={{
                    fontSize: 11,
                    fill: 'hsl(var(--muted-foreground))',
                    opacity: 0.7
                  }}
                  axisLine={{ stroke: 'hsl(var(--border))', opacity: 0.5 }}
                  width={60}
                />
                <Tooltip content={<EquityTooltip />} />
                <Legend
                  verticalAlign="top"
                  height={36}
                  wrapperStyle={{
                    paddingTop: '10px',
                    fontSize: '12px'
                  }}
                />
                {/* Reference line at initial balance */}
                {isEquityData(equityData) && equityData.equityCurve.length > 0 && (
                  <ReferenceLine
                    y={equityData.equityCurve[0].equity}
                    stroke="#666"
                    strokeDasharray="3 3"
                    label={{
                      value: 'Initial Balance',
                      position: 'right',
                      fill: '#666',
                      fontSize: 12
                    }}
                  />
                )}
                <Area
                  type="monotone"
                  dataKey="equity"
                  name="Equity"
                  stroke="#3b82f6"
                  strokeWidth={1.5}
                  fill="#3b82f6"
                  fillOpacity={0.2}
                  dot={{ r: 2, strokeWidth: 1 }}
                  activeDot={{ r: 4, strokeWidth: 1 }}
                />
              </AreaChart>
            </ResponsiveContainer>
          )}

          {chartType === "drawdown" && (
            <ResponsiveContainer width="100%" height={400}>
              <ComposedChart
                data={isEquityData(equityData) ? equityData.equityCurve : []}
                margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
              >
                <CartesianGrid
                  strokeDasharray="3 3"
                  stroke="hsl(var(--border))"
                  opacity={0.4}
                  vertical={true}
                />
                <XAxis
                  dataKey="date"
                  tickFormatter={(value) => format(new Date(value), "MMM d")}
                  label={{
                    value: "Date",
                    position: 'insideBottom',
                    offset: -10,
                    style: {
                      fill: 'hsl(var(--muted-foreground))',
                      fontSize: 12
                    }
                  }}
                  tick={{
                    fontSize: 11,
                    fill: 'hsl(var(--muted-foreground))',
                    opacity: 0.7
                  }}
                  axisLine={{ stroke: 'hsl(var(--border))', opacity: 0.5 }}
                />
                <YAxis
                  tickFormatter={(value) => `${value.toFixed(1)}%`}
                  label={{
                    value: "P&L from Initial (%)",
                    angle: -90,
                    position: 'insideLeft',
                    offset: -10,
                    style: {
                      textAnchor: 'middle',
                      fill: 'hsl(var(--muted-foreground))',
                      fontSize: 12
                    }
                  }}
                  domain={['dataMin - 5', 'dataMax + 5']} // Show both negative and positive values
                  tick={{
                    fontSize: 11,
                    fill: 'hsl(var(--muted-foreground))',
                    opacity: 0.7
                  }}
                  axisLine={{ stroke: 'hsl(var(--border))', opacity: 0.5 }}
                  width={60}
                />
                <Tooltip content={<EquityTooltip />} />
                <Legend
                  verticalAlign="top"
                  height={36}
                  wrapperStyle={{
                    paddingTop: '10px',
                    fontSize: '12px'
                  }}
                />
                <ReferenceLine y={0} stroke="#666" strokeDasharray="3 3" />
                {/* Area for positive values (gains) */}
                <Area
                  type="monotone"
                  dataKey={(data) => data.drawdownPercent >= 0 ? data.drawdownPercent : 0}
                  name="Gain from Initial %"
                  stroke="#10b981"
                  fill="#10b981"
                  fillOpacity={0.3}
                  activeDot={false}
                  isAnimationActive={true}
                  baseValue={0}
                  connectNulls
                />
                {/* Area for negative values (drawdowns) */}
                <Area
                  type="monotone"
                  dataKey={(data) => data.drawdownPercent < 0 ? data.drawdownPercent : 0}
                  name="Drawdown from Initial %"
                  stroke="#ef4444"
                  fill="#ef4444"
                  fillOpacity={0.3}
                  activeDot={false}
                  isAnimationActive={true}
                  baseValue={0}
                  connectNulls
                />
                {/* Line to show the actual values */}
                <Line
                  type="monotone"
                  dataKey="drawdownPercent"
                  name="P&L from Initial %"
                  stroke="currentColor"
                  strokeWidth={1.5}
                  dot={false}
                  isAnimationActive={true}
                />
              </ComposedChart>
            </ResponsiveContainer>
          )}

          {chartType === "recovery" && (
            <>
              {recoveryData.length > 0 ? (
                <ResponsiveContainer width="100%" height={400}>
                  <BarChart
                    data={recoveryData}
                    margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
                    layout="vertical"
                  >
                    <CartesianGrid
                      strokeDasharray="3 3"
                      stroke="hsl(var(--border))"
                      opacity={0.4}
                      vertical={true}
                    />
                    <XAxis
                      type="number"
                      label={{
                        value: "Days to Return to Initial",
                        position: 'insideBottom',
                        offset: -10,
                        style: {
                          fill: 'hsl(var(--muted-foreground))',
                          fontSize: 12
                        }
                      }}
                      tick={{
                        fontSize: 11,
                        fill: 'hsl(var(--muted-foreground))',
                        opacity: 0.7
                      }}
                      axisLine={{ stroke: 'hsl(var(--border))', opacity: 0.5 }}
                    />
                    <YAxis
                      type="category"
                      dataKey="startDate"
                      width={100}
                      tick={{
                        fontSize: 11,
                        fill: 'hsl(var(--muted-foreground))',
                        opacity: 0.7
                      }}
                      axisLine={{ stroke: 'hsl(var(--border))', opacity: 0.5 }}
                    />
                    <Tooltip content={<RecoveryTooltip />} />
                    <Legend
                      verticalAlign="top"
                      height={36}
                      wrapperStyle={{
                        paddingTop: '10px',
                        fontSize: '12px'
                      }}
                    />
                    <Bar
                      dataKey="recoveryDays"
                      name="Return to Initial (days)"
                      fill="#3b82f6"
                    />
                  </BarChart>
                </ResponsiveContainer>
              ) : (
                <div className="flex justify-center items-center h-[400px] border rounded-md">
                  <div className="text-center p-4">
                    <p className="text-muted-foreground mb-2">No recovery data available</p>
                    <p className="text-xs text-muted-foreground">This chart shows how long it took to return to your initial balance.<br />No completed recoveries found in the selected time period.</p>
                  </div>
                </div>
              )}
            </>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Maximum Drawdown Card */}
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base">Maximum Drawdown</CardTitle>
                <CardDescription>
                  Largest decline from initial balance
                </CardDescription>
              </CardHeader>
              <CardContent>
                {isEquityData(equityData) && equityData.equityCurve.length > 0 ? (
                  <div className="space-y-1">
                    <div className="text-2xl font-bold text-rose-500">
                      ${equityData.maxDrawdown.toFixed(2)}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {equityData.maxDrawdownPercent.toFixed(2)}% of initial balance
                    </div>
                  </div>
                ) : (
                  <div className="text-muted-foreground">No data available</div>
                )}
              </CardContent>
            </Card>

            {/* Longest Drawdown Card */}
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base">Longest Negative Period</CardTitle>
                <CardDescription>
                  Longest time below initial balance
                </CardDescription>
              </CardHeader>
              <CardContent>
                {isEquityData(equityData) && equityData.equityCurve.length > 0 ? (
                  <div className="space-y-1">
                    <div className="text-2xl font-bold">
                      {equityData.maxDrawdownDuration} days
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {(equityData.maxDrawdownDuration / 7).toFixed(1)} weeks
                    </div>
                  </div>
                ) : (
                  <div className="text-muted-foreground">No data available</div>
                )}
              </CardContent>
            </Card>

            {/* Longest Recovery Card */}
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base">Recovery Time</CardTitle>
                <CardDescription>
                  Time to return to initial balance
                </CardDescription>
              </CardHeader>
              <CardContent>
                {isEquityData(equityData) && equityData.equityCurve.length > 0 && equityData.maxRecoveryDuration > 0 ? (
                  <div className="space-y-1">
                    <div className="text-2xl font-bold">
                      {equityData.maxRecoveryDuration} days
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {(equityData.maxRecoveryDuration / 7).toFixed(1)} weeks
                    </div>
                  </div>
                ) : (
                  <div className="text-muted-foreground">No recovery data available</div>
                )}
              </CardContent>
            </Card>

            {/* Current P&L Card */}
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base">Current P&L</CardTitle>
                <CardDescription>
                  Current profit/loss from initial
                </CardDescription>
              </CardHeader>
              <CardContent>
                {isEquityData(equityData) && equityData.equityCurve.length > 0 ? (
                  (() => {
                    // Safe to access since we've checked it's not an array and has length > 0
                    const currentEquity = equityData.equityCurve[equityData.equityCurve.length - 1].equity
                    const initialBalance = equityData.equityCurve[0].equity
                    const currentPnL = currentEquity - initialBalance
                    const currentPnLPercent = (currentPnL / initialBalance) * 100

                    return (
                      <div className="space-y-1">
                        <div className={cn(
                          "text-2xl font-bold",
                          currentPnLPercent < 0 ? "text-rose-500" : "text-emerald-500"
                        )}>
                          {currentPnLPercent.toFixed(2)}%
                        </div>
                        <div className="text-sm text-muted-foreground">
                          ${currentPnL.toFixed(2)}
                        </div>
                      </div>
                    )
                  })()
                ) : (
                  <div className="text-muted-foreground">No data available</div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Negative Periods Table */}
          {chartType !== "recovery" && isEquityData(equityData) && (
            <div className="border rounded-md overflow-hidden">
              <div className="font-medium p-4 border-b">
                Negative Periods Analysis
              </div>
              <div className="overflow-x-auto">
                {equityData.drawdowns.length > 0 ? (
                  <table className="w-full">
                    <thead>
                      <tr className="bg-muted/50">
                        <th className="text-left p-3 font-medium">Start Date</th>
                        <th className="text-left p-3 font-medium">Duration</th>
                        <th className="text-left p-3 font-medium">Decline (%)</th>
                        <th className="text-left p-3 font-medium">Recovery</th>
                        <th className="text-left p-3 font-medium">Status</th>
                      </tr>
                    </thead>
                    <tbody>
                      {equityData.drawdowns
                        .sort((a, b) => new Date(b.startDate).getTime() - new Date(a.startDate).getTime())
                        .map((drawdown, index) => (
                          <tr key={index} className={index % 2 === 0 ? "bg-muted/20" : ""}>
                            <td className="p-3">{format(new Date(drawdown.startDate), "MMM d, yyyy")}</td>
                            <td className="p-3">{drawdown.duration} days</td>
                            <td className="p-3 text-rose-500">{drawdown.drawdownPercent.toFixed(2)}%</td>
                            <td className="p-3">
                              {drawdown.recovered
                                ? `${drawdown.recoveryDuration} days`
                                : "Ongoing"}
                            </td>
                            <td className="p-3">
                              <span className={cn(
                                "px-2 py-1 rounded-full text-xs",
                                drawdown.recovered
                                  ? "bg-emerald-100 text-emerald-800"
                                  : "bg-amber-100 text-amber-800"
                              )}>
                                {drawdown.recovered ? "Recovered" : "Active"}
                              </span>
                            </td>
                          </tr>
                        ))}
                    </tbody>
                  </table>
                ) : (
                  <div className="p-4 text-center">
                    <p className="text-muted-foreground">No significant negative periods detected</p>
                    <p className="text-xs text-muted-foreground mt-1">A negative period is recorded when equity drops at least 1% below initial balance</p>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Debug Information */}
          <div className="border rounded-md overflow-hidden mt-4 bg-muted/10">
            <div className="font-medium p-4 border-b flex justify-between items-center">
              <span>Equity Curve vs P&L from Initial Chart Explanation</span>
            </div>
            <div className="p-4 text-sm">
              <p className="mb-2"><strong>Equity Curve:</strong> Shows your account balance over time, reflecting the cumulative profit/loss from all trades.</p>
              <p className="mb-2"><strong>P&L from Initial Chart:</strong> Shows the percentage change from your initial balance at any given point in time. Negative values (red area) indicate drawdowns (below initial balance), while positive values (green area) indicate gains above your initial balance. The 0% reference line marks your initial balance level.</p>
              <p className="mb-2"><strong>Recovery Times:</strong> Shows how long it took to recover from negative periods (when your equity returns to your initial balance).</p>
              <p className="mb-4"><strong>Example:</strong> If your initial balance is $1,000 and it drops to $900, you have a -10% drawdown (red area). When it returns to $1,000, you're back at 0% on the chart. If it then grows to $1,100, you'll see a +10% gain (green area) above your initial balance.</p>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
