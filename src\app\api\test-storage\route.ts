import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

export async function GET(request: Request) {
  try {
    // Create a Supabase client with the public key
    const supabasePublic = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL || '',
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || ''
    );

    // Create a Supabase client with admin privileges
    const supabaseAdmin = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL || '',
      process.env.SUPABASE_SERVICE_ROLE_KEY || ''
    );

    // Test public access to the bucket
    const { data: publicData, error: publicError } = await supabasePublic.storage
      .from('strategyimages')
      .list();

    // Test admin access to the bucket
    const { data: adminData, error: adminError } = await supabaseAdmin.storage
      .from('strategyimages')
      .list();

    // Get the RLS policies for the bucket
    let rlsPolicies = null;
    let rlsError = null;

    try {
      const { data, error } = await supabaseAdmin.rpc(
        'get_policies_for_table',
        { table_name: 'objects', schema_name: 'storage' }
      );

      rlsPolicies = data;
      rlsError = error;
    } catch (error) {
      console.error('Error getting RLS policies:', error);
      rlsError = { message: 'Failed to get RLS policies. The RPC function might not exist.' };
    }

    return NextResponse.json({
      publicAccess: {
        success: !publicError,
        error: publicError ? publicError.message : null,
        fileCount: publicData ? publicData.length : 0
      },
      adminAccess: {
        success: !adminError,
        error: adminError ? adminError.message : null,
        fileCount: adminData ? adminData.length : 0
      },
      rlsPolicies: {
        success: !rlsError,
        error: rlsError ? rlsError.message : null,
        policies: rlsPolicies || []
      }
    });
  } catch (error) {
    console.error('Error in test-storage route:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
