import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { createServerClient } from '@supabase/ssr';
import type { Database } from '@/types/supabase';

export async function GET(request: Request) {
  try {
    // Get URL parameters
    const url = new URL(request.url);
    const accountId = url.searchParams.get('accountId');
    const metricType = url.searchParams.get('type') || 'all'; // 'all', 'symbol', 'timeOfDay', etc.

    // Get cookies for server-side Supabase client
    const cookieStore = await cookies();
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
          set(_name: string, _value: string, _options: any) {
            // API routes can't set cookies directly
          },
          remove(_name: string, _options: any) {
            // API routes can't remove cookies directly
          }
        },
      }
    );

    // Get authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    const userId = user.id;

    // If no account ID is provided, return empty data
    if (!accountId) {
      return NextResponse.json({
        metrics: {},
        error: 'No account selected'
      });
    }

    // Fetch trades for the account
    const { data: trades, error: tradesError } = await supabase
      .from("trades")
      .select("*")
      .eq("user_id", userId)
      .eq("account_id", accountId)
      .order("time_close", { ascending: false });

    if (tradesError) {
      return NextResponse.json({ error: tradesError.message }, { status: 500 });
    }

    // Calculate metrics based on the requested type
    let metrics = {};
    
    switch (metricType) {
      case 'symbol':
        // Calculate symbol-specific metrics
        metrics = calculateSymbolMetrics(trades || []);
        break;
      case 'timeOfDay':
        // Calculate time of day metrics
        metrics = calculateTimeOfDayMetrics(trades || []);
        break;
      case 'duration':
        // Calculate duration metrics
        metrics = calculateDurationMetrics(trades || []);
        break;
      case 'all':
      default:
        // Calculate all metrics
        metrics = {
          symbol: calculateSymbolMetrics(trades || []),
          timeOfDay: calculateTimeOfDayMetrics(trades || []),
          duration: calculateDurationMetrics(trades || [])
        };
        break;
    }

    return NextResponse.json({
      metrics,
      tradeCount: trades?.length || 0
    });
  } catch (error) {
    console.error('Error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// Helper functions to calculate metrics
function calculateSymbolMetrics(trades: any[]) {
  // Group trades by symbol
  const symbolMap = new Map();
  
  trades.forEach(trade => {
    const symbol = trade.symbol;
    if (!symbolMap.has(symbol)) {
      symbolMap.set(symbol, {
        totalTrades: 0,
        winningTrades: 0,
        losingTrades: 0,
        totalProfit: 0,
        totalLoss: 0,
        netProfit: 0
      });
    }
    
    const stats = symbolMap.get(symbol);
    stats.totalTrades++;
    
    if (trade.profit > 0) {
      stats.winningTrades++;
      stats.totalProfit += trade.profit;
    } else {
      stats.losingTrades++;
      stats.totalLoss += trade.profit;
    }
    
    stats.netProfit += trade.profit;
  });
  
  // Convert map to array of objects
  return Array.from(symbolMap.entries()).map(([symbol, stats]) => ({
    symbol,
    totalTrades: stats.totalTrades,
    winRate: stats.totalTrades > 0 ? (stats.winningTrades / stats.totalTrades) * 100 : 0,
    netProfit: stats.netProfit,
    averageProfit: stats.totalTrades > 0 ? stats.netProfit / stats.totalTrades : 0
  }));
}

function calculateTimeOfDayMetrics(trades: any[]) {
  // Group trades by hour of day
  const hourMap = new Map();
  
  for (let i = 0; i < 24; i++) {
    hourMap.set(i, {
      totalTrades: 0,
      winningTrades: 0,
      losingTrades: 0,
      totalProfit: 0
    });
  }
  
  trades.forEach(trade => {
    const hour = new Date(trade.time_close).getHours();
    const stats = hourMap.get(hour);
    
    stats.totalTrades++;
    
    if (trade.profit > 0) {
      stats.winningTrades++;
    } else {
      stats.losingTrades++;
    }
    
    stats.totalProfit += trade.profit;
  });
  
  // Convert map to array of objects
  return Array.from(hourMap.entries()).map(([hour, stats]) => ({
    hour,
    totalTrades: stats.totalTrades,
    winRate: stats.totalTrades > 0 ? (stats.winningTrades / stats.totalTrades) * 100 : 0,
    totalProfit: stats.totalProfit,
    averageProfit: stats.totalTrades > 0 ? stats.totalProfit / stats.totalTrades : 0
  }));
}

function calculateDurationMetrics(trades: any[]) {
  // Define duration buckets in minutes
  const buckets = [
    { name: '< 5 min', max: 5 },
    { name: '5-15 min', min: 5, max: 15 },
    { name: '15-30 min', min: 15, max: 30 },
    { name: '30-60 min', min: 30, max: 60 },
    { name: '1-4 hours', min: 60, max: 240 },
    { name: '4+ hours', min: 240 }
  ];
  
  const bucketMap = new Map();
  
  buckets.forEach(bucket => {
    bucketMap.set(bucket.name, {
      totalTrades: 0,
      winningTrades: 0,
      losingTrades: 0,
      totalProfit: 0
    });
  });
  
  trades.forEach(trade => {
    const openTime = new Date(trade.time_open).getTime();
    const closeTime = new Date(trade.time_close).getTime();
    const durationMinutes = (closeTime - openTime) / (1000 * 60);
    
    // Find the appropriate bucket
    let bucketName = '';
    for (const bucket of buckets) {
      if (
        (bucket.min === undefined || durationMinutes >= bucket.min) &&
        (bucket.max === undefined || durationMinutes < bucket.max)
      ) {
        bucketName = bucket.name;
        break;
      }
    }
    
    if (bucketName && bucketMap.has(bucketName)) {
      const stats = bucketMap.get(bucketName);
      
      stats.totalTrades++;
      
      if (trade.profit > 0) {
        stats.winningTrades++;
      } else {
        stats.losingTrades++;
      }
      
      stats.totalProfit += trade.profit;
    }
  });
  
  // Convert map to array of objects
  return Array.from(bucketMap.entries()).map(([duration, stats]) => ({
    duration,
    totalTrades: stats.totalTrades,
    winRate: stats.totalTrades > 0 ? (stats.winningTrades / stats.totalTrades) * 100 : 0,
    totalProfit: stats.totalProfit,
    averageProfit: stats.totalTrades > 0 ? stats.totalProfit / stats.totalTrades : 0
  }));
}
