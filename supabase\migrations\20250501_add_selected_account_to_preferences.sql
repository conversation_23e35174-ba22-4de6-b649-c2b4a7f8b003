-- First, create the user_preferences table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.user_preferences (
  user_id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  theme TEXT DEFAULT 'system',
  email_notifications B<PERSON><PERSON><PERSON>N DEFAULT TRUE,
  trading_alerts BOOLEAN DEFAULT TRUE,
  weekly_reports BOOLEAN DEFAULT TRUE,
  default_currency TEXT DEFAULT 'USD',
  default_timezone TEXT DEFAULT 'UTC',
  dashboard_layout TEXT[] DEFAULT '{}',
  selected_account_id UUID REFERENCES public.trading_accounts(id) ON DELETE SET NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- If the table already exists, add the selected_account_id column
DO $$
BEGIN
  -- Check if the column already exists
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                WHERE table_schema = 'public'
                AND table_name = 'user_preferences'
                AND column_name = 'selected_account_id') THEN
    -- Add the column if it doesn't exist
    ALTER TABLE public.user_preferences
    ADD COLUMN selected_account_id UUID REFERENCES public.trading_accounts(id) ON DELETE SET NULL;
  END IF;
END $$;

-- Create index for faster lookups
DROP INDEX IF EXISTS user_preferences_selected_account_id_idx;
CREATE INDEX user_preferences_selected_account_id_idx ON public.user_preferences(selected_account_id);

-- Add comment to explain the column
COMMENT ON COLUMN public.user_preferences.selected_account_id IS 'The ID of the trading account that the user has selected as their default/active account';

-- Enable Row Level Security
ALTER TABLE public.user_preferences ENABLE ROW LEVEL SECURITY;

-- Create policies (first drop if they exist to avoid errors)
DROP POLICY IF EXISTS "Users can view their own preferences" ON public.user_preferences;
DROP POLICY IF EXISTS "Users can insert their own preferences" ON public.user_preferences;
DROP POLICY IF EXISTS "Users can update their own preferences" ON public.user_preferences;

-- Create policies
CREATE POLICY "Users can view their own preferences"
  ON public.user_preferences
  FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own preferences"
  ON public.user_preferences
  FOR INSERT
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own preferences"
  ON public.user_preferences
  FOR UPDATE
  USING (auth.uid() = user_id);
