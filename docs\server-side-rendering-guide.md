# Server-Side Rendering Guide for Next.js 15 with Supabase

This document provides a comprehensive guide for implementing server-side rendering in Next.js 15 with Supabase, based on our experience migrating the Playbook feature.

## Table of Contents

1. [Key Changes in Next.js 15](#key-changes-in-next-js-15)
2. [Server Component Implementation](#server-component-implementation)
3. [API Route Implementation](#api-route-implementation)
4. [Authentication Best Practices](#authentication-best-practices)
5. [Common Issues and Solutions](#common-issues-and-solutions)
6. [Pages Requiring Migration](#pages-requiring-migration)
7. [Migration Checklist](#migration-checklist)

## Key Changes in Next.js 15

Next.js 15 introduced several important changes that affect server-side rendering:

1. **Dynamic APIs Must Be Awaited**: Functions like `cookies()`, `headers()`, and other dynamic APIs must be awaited before use.
2. **Server Component Structure**: Server components should be structured to separate data fetching from UI rendering.
3. **Authentication Flow**: Authentication should use `getUser()` instead of `getSession()` for better security.

## Server Component Implementation

### Basic Structure

```tsx
// page.tsx (Server Component)
import { cookies } from 'next/headers';
import { createServerClient } from '@supabase/ssr';
import { redirect } from 'next/navigation';
import type { Database } from '@/types/supabase';
import ClientWrapper from './client-wrapper';

export default async function PageName() {
  // Get cookies for server-side Supabase client
  const cookieStore = await cookies();
  
  // Create a Supabase client with proper cookie handling
  const supabase = createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value;
        },
        set(_name: string, _value: string, _options: any) {
          // Server components can't set cookies directly
        },
        remove(_name: string, _options: any) {
          // Server components can't remove cookies directly
        }
      },
    }
  );

  // Get authenticated user
  const { data: { user }, error: userError } = await supabase.auth.getUser();
  if (userError || !user) {
    redirect('/auth');
  }
  
  const userId = user.id;

  // Fetch initial data
  const { data, error } = await supabase
    .from('your_table')
    .select('*')
    .eq('user_id', userId);

  // Render the client component with the fetched data
  return <ClientWrapper userId={userId} initialData={data || []} />;
}
```

### Client Wrapper Component

```tsx
// client-wrapper.tsx (Client Component)
"use client"

import dynamic from 'next/dynamic';
import { YourDataType } from '@/types/your-types';

// Dynamically import the client component with no SSR
const ClientComponent = dynamic(() => import('./client'), {
  ssr: false
});

interface ClientWrapperProps {
  userId: string;
  initialData: YourDataType[];
}

export default function ClientWrapper({ userId, initialData }: ClientWrapperProps) {
  return <ClientComponent userId={userId} initialData={initialData} />;
}
```

## API Route Implementation

### Basic Structure

```tsx
// route.ts (API Route)
import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { createServerClient } from '@supabase/ssr';
import type { Database } from '@/types/supabase';

export async function GET(request: Request) {
  try {
    // Get URL parameters
    const url = new URL(request.url);
    const paramId = url.searchParams.get('id');

    // Get cookies for server-side Supabase client
    const cookieStore = await cookies();
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
          set(_name: string, _value: string, _options: any) {
            // API routes can't set cookies directly
          },
          remove(_name: string, _options: any) {
            // API routes can't remove cookies directly
          }
        },
      }
    );

    // Get authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    const userId = user.id;

    // Fetch data
    const { data, error } = await supabase
      .from('your_table')
      .select('*')
      .eq('user_id', userId);

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json(data || []);
  } catch (error) {
    console.error('Error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
```

## Authentication Best Practices

1. **Use `getUser()` Instead of `getSession()`**: 
   - `getUser()` provides more secure authentication
   - Recommended by Supabase for server-side authentication

2. **Proper Cookie Handling**:
   - Always await `cookies()` before using it
   - Implement all required cookie methods (get, set, remove)
   - Use underscore prefixes for unused parameters to avoid TypeScript warnings

3. **Consistent Authentication Approach**:
   - Use the same authentication approach across all server components and API routes
   - This ensures that authentication works correctly throughout the application

## Common Issues and Solutions

### 1. `cookies()` Should Be Awaited

**Issue**: 
```
Error: Route "/api/route" used `cookies().get('cookie-name')`. `cookies()` should be awaited before using its value.
```

**Solution**:
```tsx
// Incorrect
const cookieStore = cookies();

// Correct
const cookieStore = await cookies();
```

### 2. Authentication Session Missing

**Issue**:
```
AuthSessionMissingError: Auth session missing!
```

**Solution**:
- Use `getUser()` instead of `getSession()`
- Ensure cookies are properly awaited
- Implement proper cookie handling in the middleware

### 3. Dynamic Import in Server Components

**Issue**:
```
Error: `ssr: false` is not allowed with `next/dynamic` in Server Components.
```

**Solution**:
- Create a separate client component wrapper
- Move the dynamic import to the client component wrapper
- Use the client component wrapper in the server component

## Pages Requiring Migration

Based on our analysis, the following pages need similar modifications:

1. **Dashboard Page**: `/dashboard`
   - Server component: `src/app/(dashboard)/dashboard/page.tsx`
   - API routes: `/api/dashboard-stats`, `/api/dashboard-metrics`

2. **Journal Page**: `/journal`
   - Server component: `src/app/(dashboard)/journal/page.tsx`
   - API routes: `/api/journal-entries`, `/api/journal-tags`

3. **Analytics Page**: `/analytics`
   - Server component: `src/app/(dashboard)/analytics/page.tsx`
   - API routes: `/api/analytics-data`, `/api/analytics-metrics`

4. **Trades Page**: `/trades`
   - Server component: `src/app/(dashboard)/trades/page.tsx`
   - API routes: `/api/trades` (already updated)

5. **Profile Page**: `/profile`
   - Server component: `src/app/(dashboard)/profile/page.tsx`
   - API routes: `/api/user-profile`, `/api/user-settings`

## Migration Checklist

When migrating a page to server-side rendering, follow these steps:

1. **Update Server Component**:
   - Await `cookies()` before use
   - Use `createServerClient` with proper cookie handling
   - Use `getUser()` for authentication
   - Fetch initial data on the server
   - Pass data to client component

2. **Create Client Wrapper**:
   - Add "use client" directive
   - Use dynamic import for client component
   - Pass data from server component to client component

3. **Update API Routes**:
   - Await `cookies()` before use
   - Use `createServerClient` with proper cookie handling
   - Use `getUser()` for authentication
   - Implement proper error handling

4. **Test Thoroughly**:
   - Check authentication flow
   - Verify data fetching
   - Test error handling
   - Ensure performance is acceptable

By following this guide, you can successfully implement server-side rendering in your Next.js 15 application with Supabase.
