"use client"

import { useState } from "react"
import { toast } from "sonner"
import { Goal, CustomMetric } from "@/types/metrics"
import { deleteGoal } from "@/lib/metrics-service"
import { formatDistanceToNow, format, differenceInDays } from "date-fns"

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { Badge } from "@/components/ui/badge"
import { MoreVertical, Edit, Trash, Plus, CheckCircle, Clock, Calendar } from "lucide-react"

interface GoalsListProps {
  userId: string
  goals: Goal[]
  metrics: CustomMetric[]
  onEdit: (goal: Goal) => void
  onDelete: (goalId: string) => void
  onAdd: () => void
}

export function GoalsList({ userId, goals, metrics, onEdit, onDelete, onAdd }: GoalsListProps) {
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [goalToDelete, setGoalToDelete] = useState<Goal | null>(null)

  const handleDeleteClick = (goal: Goal) => {
    setGoalToDelete(goal)
    setDeleteDialogOpen(true)
  }

  const confirmDelete = async () => {
    if (!goalToDelete) return

    try {
      const success = await deleteGoal(userId, goalToDelete.id)
      if (success) {
        toast.success("Goal deleted successfully")
        onDelete(goalToDelete.id)
      } else {
        toast.error("Failed to delete goal")
      }
    } catch (error) {
      console.error("Error deleting goal:", error)
      toast.error("An error occurred while deleting the goal")
    } finally {
      setDeleteDialogOpen(false)
      setGoalToDelete(null)
    }
  }

  // Calculate progress percentage for a goal
  const calculateProgress = (goal: Goal) => {
    if (goal.is_completed) return 100

    const metric = goal.metric_id ? metrics.find(m => m.id === goal.metric_id) : null

    if (!metric) {
      // If no metric is associated, use a simple percentage of current/target
      return Math.min(Math.round((goal.current_value / goal.target_value) * 100), 100)
    }

    // For metrics where lower is better, invert the progress calculation
    if (!metric.is_higher_better) {
      if (goal.current_value <= goal.target_value) return 100
      return Math.max(0, Math.round((goal.target_value / goal.current_value) * 100))
    }

    // For metrics where higher is better
    if (goal.current_value >= goal.target_value) return 100
    return Math.min(100, Math.round((goal.current_value / goal.target_value) * 100))
  }

  // Get the associated metric name for a goal
  const getMetricName = (goal: Goal) => {
    if (!goal.metric_id) return null
    const metric = metrics.find(m => m.id === goal.metric_id)
    return metric ? metric.name : null
  }

  // Format the target value with appropriate units
  const formatTargetValue = (goal: Goal) => {
    if (!goal.metric_id) return goal.target_value.toString()

    const metric = metrics.find(m => m.id === goal.metric_id)
    if (!metric) return goal.target_value.toString()

    const value = goal.target_value.toFixed(metric.display_precision)
    return metric.is_percentage ? `${value}%` : value
  }

  // Format the current value with appropriate units
  const formatCurrentValue = (goal: Goal) => {
    if (!goal.metric_id) return goal.current_value.toString()

    const metric = metrics.find(m => m.id === goal.metric_id)
    if (!metric) return goal.current_value.toString()

    const value = goal.current_value.toFixed(metric.display_precision)
    return metric.is_percentage ? `${value}%` : value
  }

  // Calculate days remaining until goal deadline
  const getDaysRemaining = (goal: Goal) => {
    const endDate = new Date(goal.end_date)
    const today = new Date()
    return Math.max(0, differenceInDays(endDate, today))
  }

  // Determine status badge for a goal
  const getStatusBadge = (goal: Goal) => {
    if (goal.is_completed) {
      return <Badge className="bg-green-500">Completed</Badge>
    }

    const daysRemaining = getDaysRemaining(goal)
    if (daysRemaining === 0) {
      return <Badge variant="destructive">Due Today</Badge>
    } else if (daysRemaining <= 7) {
      return <Badge variant="destructive">Due Soon</Badge>
    } else {
      return <Badge variant="outline">In Progress</Badge>
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold tracking-tight">Trading Goals</h2>
      </div>

      {goals.length === 0 ? (
        <Card className="border-dashed">
          <CardContent className="pt-6 text-center">
            <p className="text-muted-foreground mb-4">
              You haven't set any trading goals yet.
            </p>
            <Button onClick={onAdd} variant="outline">
              <Plus className="mr-2 h-4 w-4" /> Set Your First Goal
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {goals.map((goal) => {
            const progress = calculateProgress(goal)
            const daysRemaining = getDaysRemaining(goal)

            return (
              <Card key={goal.id} className={cn(
                "flex flex-col",
                goal.is_completed && "border-green-500/20 bg-green-500/5"
              )}>
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-start">
                    <div className="space-y-1">
                      <div className="flex items-center gap-2">
                        <CardTitle>{goal.title}</CardTitle>
                        {getStatusBadge(goal)}
                      </div>
                      <CardDescription>
                        {goal.description || "No description provided"}
                      </CardDescription>
                    </div>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreVertical className="h-4 w-4" />
                          <span className="sr-only">Open menu</span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => onEdit(goal)}>
                          <Edit className="mr-2 h-4 w-4" />
                          Edit
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          onClick={() => handleDeleteClick(goal)}
                          className="text-destructive focus:text-destructive"
                        >
                          <Trash className="mr-2 h-4 w-4" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </CardHeader>
                <CardContent className="pb-2 flex-grow">
                  <div className="space-y-4">
                    {getMetricName(goal) && (
                      <div className="text-sm">
                        <span className="font-medium">Metric:</span>
                        <span className="ml-2">{getMetricName(goal)}</span>
                      </div>
                    )}

                    <div className="space-y-1">
                      <div className="flex justify-between text-sm">
                        <span>Progress</span>
                        <span>
                          {formatCurrentValue(goal)} / {formatTargetValue(goal)}
                        </span>
                      </div>
                      <Progress value={progress} className="h-2" />
                    </div>

                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div className="flex items-center">
                        <Calendar className="mr-2 h-4 w-4 text-muted-foreground" />
                        <span>
                          {format(new Date(goal.start_date), "MMM d")} - {format(new Date(goal.end_date), "MMM d, yyyy")}
                        </span>
                      </div>

                      {!goal.is_completed && (
                        <div className="flex items-center justify-end">
                          <Clock className="mr-2 h-4 w-4 text-muted-foreground" />
                          <span className={cn(
                            daysRemaining <= 3 && "text-destructive font-medium"
                          )}>
                            {daysRemaining === 0 ? "Due today" : `${daysRemaining} days left`}
                          </span>
                        </div>
                      )}

                      {goal.is_completed && (
                        <div className="flex items-center justify-end">
                          <CheckCircle className="mr-2 h-4 w-4 text-green-500" />
                          <span className="text-green-500 font-medium">Completed</span>
                        </div>
                      )}
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="pt-2 text-xs text-muted-foreground">
                  Updated {formatDistanceToNow(new Date(goal.updated_at), { addSuffix: true })}
                </CardFooter>
              </Card>
            )
          })}
        </div>
      )}

      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete the goal &quot;{goalToDelete?.title}&quot;.
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDelete} className="bg-destructive text-destructive-foreground">
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}

// Helper function to conditionally apply classes
function cn(...classes: (string | boolean | undefined)[]) {
  return classes.filter(Boolean).join(' ')
}
