export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      notebook_folders: {
        Row: {
          id: string
          user_id: string | null
          account_id: string | null
          name: string
          description: string | null
          color: string | null
          icon: string | null
          parent_id: string | null
          is_system: boolean
          is_category: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id?: string | null
          account_id?: string | null
          name: string
          description?: string | null
          color?: string | null
          icon?: string | null
          parent_id?: string | null
          is_system?: boolean
          is_category?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string | null
          account_id?: string | null
          name?: string
          description?: string | null
          color?: string | null
          icon?: string | null
          parent_id?: string | null
          is_system?: boolean
          is_category?: boolean
          created_at?: string
          updated_at?: string
        }
      },
      notebook_entries: {
        Row: {
          id: string
          user_id: string
          account_id: string | null
          title: string
          content: Json
          html_content: string | null
          category: string | null
          tags: string[]
          is_pinned: boolean
          is_template: boolean
          linked_trade_ids: string[]
          linked_strategy_ids: string[]
          linked_daily_journal_ids: string[]
          folder_id: string | null
          screenshots: string[]
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          account_id?: string | null
          title: string
          content?: Json
          html_content?: string | null
          category?: string | null
          tags?: string[]
          is_pinned?: boolean
          is_template?: boolean
          linked_trade_ids?: string[]
          linked_strategy_ids?: string[]
          linked_daily_journal_ids?: string[]
          folder_id?: string | null
          screenshots?: string[]
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          account_id?: string | null
          title?: string
          content?: Json
          html_content?: string | null
          category?: string | null
          tags?: string[]
          is_pinned?: boolean
          is_template?: boolean
          linked_trade_ids?: string[]
          linked_strategy_ids?: string[]
          linked_daily_journal_ids?: string[]
          folder_id?: string | null
          screenshots?: string[]
          created_at?: string
          updated_at?: string
        }
      },
      profiles: {
        Row: {
          id: string
          full_name: string | null
          email: string | null
          phone: string | null
          bio: string | null
          location: string | null
          website: string | null
          avatar_url: string | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id: string
          full_name?: string | null
          email?: string | null
          phone?: string | null
          bio?: string | null
          location?: string | null
          website?: string | null
          avatar_url?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          full_name?: string | null
          email?: string | null
          phone?: string | null
          bio?: string | null
          location?: string | null
          website?: string | null
          avatar_url?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
      },
      trading_accounts: {
        Row: {
          id: string
          user_id: string
          name: string | null
          account_number: string
          broker: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          name?: string | null
          account_number: string
          broker?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          name?: string | null
          account_number?: string
          broker?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      trades: {
        Row: {
          id: string
          user_id: string
          account_id: string
          position_id: number
          symbol: string
          type: string
          volume: number
          price_open: number
          price_close: number
          sl: number | null
          tp: number | null
          time_open: string
          time_close: string
          commission: number
          swap: number
          profit: number
          strategy_id: string | null
          setup_id: string | null
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          account_id: string
          position_id: number
          symbol: string
          type: string
          volume: number
          price_open: number
          price_close: number
          sl?: number | null
          tp?: number | null
          time_open: string
          time_close: string
          commission?: number
          swap?: number
          profit: number
          strategy_id?: string | null
          setup_id?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          account_id?: string
          position_id?: number
          symbol?: string
          type?: string
          volume?: number
          price_open?: number
          price_close?: number
          sl?: number | null
          tp?: number | null
          time_open?: string
          time_close?: string
          commission?: number
          swap?: number
          profit?: number
          strategy_id?: string | null
          setup_id?: string | null
          created_at?: string
        }
      }
      trading_summaries: {
        Row: {
          id: string
          user_id: string
          account_id: string
          total_net_profit: number
          gross_profit: number
          gross_loss: number
          profit_factor: number
          expected_payoff: number
          recovery_factor: number
          sharpe_ratio: number
          balance_drawdown_absolute: number
          balance_drawdown_maximal: string
          balance_drawdown_relative: string
          total_trades: number
          short_trades_won: string
          long_trades_won: string
          profit_trades: string
          loss_trades: string
          largest_profit_trade: number
          largest_loss_trade: number
          average_profit_trade: number
          average_loss_trade: number
          maximum_consecutive_wins: string
          maximum_consecutive_losses: string
          maximal_consecutive_profit: string
          maximal_consecutive_loss: string
          average_consecutive_wins: number
          average_consecutive_losses: number
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          account_id: string
          total_net_profit: number
          gross_profit: number
          gross_loss: number
          profit_factor: number
          expected_payoff: number
          recovery_factor: number
          sharpe_ratio: number
          balance_drawdown_absolute: number
          balance_drawdown_maximal: string
          balance_drawdown_relative: string
          total_trades: number
          short_trades_won: string
          long_trades_won: string
          profit_trades: string
          loss_trades: string
          largest_profit_trade: number
          largest_loss_trade: number
          average_profit_trade: number
          average_loss_trade: number
          maximum_consecutive_wins: string
          maximum_consecutive_losses: string
          maximal_consecutive_profit: string
          maximal_consecutive_loss: string
          average_consecutive_wins: number
          average_consecutive_losses: number
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          account_id?: string
          total_net_profit?: number
          gross_profit?: number
          gross_loss?: number
          profit_factor?: number
          expected_payoff?: number
          recovery_factor?: number
          sharpe_ratio?: number
          balance_drawdown_absolute?: number
          balance_drawdown_maximal?: string
          balance_drawdown_relative?: string
          total_trades?: number
          short_trades_won?: string
          long_trades_won?: string
          profit_trades?: string
          loss_trades?: string
          largest_profit_trade?: number
          largest_loss_trade?: number
          average_profit_trade?: number
          average_loss_trade?: number
          maximum_consecutive_wins?: string
          maximum_consecutive_losses?: string
          maximal_consecutive_profit?: string
          maximal_consecutive_loss?: string
          average_consecutive_wins?: number
          average_consecutive_losses?: number
          created_at?: string
        }
      }
      journal_entries: {
        Row: {
          id: string
          user_id: string
          trade_id: string | null
          title: string
          content: string
          tags: string[]
          entry_date: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          trade_id?: string | null
          title: string
          content: string
          tags?: string[]
          entry_date: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          trade_id?: string | null
          title?: string
          content?: string
          tags?: string[]
          entry_date?: string
          created_at?: string
          updated_at?: string
        }
      },
      user_preferences: {
        Row: {
          id: string
          user_id: string
          theme: string | null
          currency: string | null
          timezone: string | null
          notifications_enabled: boolean | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id?: string
          user_id: string
          theme?: string | null
          currency?: string | null
          timezone?: string | null
          notifications_enabled?: boolean | null
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          user_id?: string
          theme?: string | null
          currency?: string | null
          timezone?: string | null
          notifications_enabled?: boolean | null
          created_at?: string | null
          updated_at?: string | null
        }
      },
      custom_metrics: {
        Row: {
          id: string
          user_id: string
          name: string
          description: string | null
          formula: string
          is_percentage: boolean
          display_precision: number
          is_higher_better: boolean
          target_value: number | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          name: string
          description?: string | null
          formula: string
          is_percentage?: boolean
          display_precision?: number
          is_higher_better?: boolean
          target_value?: number | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          name?: string
          description?: string | null
          formula?: string
          is_percentage?: boolean
          display_precision?: number
          is_higher_better?: boolean
          target_value?: number | null
          created_at?: string
          updated_at?: string
        }
      },
      goals: {
        Row: {
          id: string
          user_id: string
          title: string
          description: string | null
          metric_id: string | null
          target_value: number
          current_value: number
          start_date: string
          end_date: string
          is_completed: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          title: string
          description?: string | null
          metric_id?: string | null
          target_value: number
          current_value?: number
          start_date: string
          end_date: string
          is_completed?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          title?: string
          description?: string | null
          metric_id?: string | null
          target_value?: number
          current_value?: number
          start_date?: string
          end_date?: string
          is_completed?: boolean
          created_at?: string
          updated_at?: string
        }
      },
      strategies: {
        Row: {
          id: string
          user_id: string
          name: string
          description: string | null
          market_conditions: string[]
          timeframes: string[]
          instruments: string[]
          risk_reward_ratio: number | null
          expected_win_rate: number | null
          status: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          name: string
          description?: string | null
          market_conditions?: string[]
          timeframes?: string[]
          instruments?: string[]
          risk_reward_ratio?: number | null
          expected_win_rate?: number | null
          status?: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          name?: string
          description?: string | null
          market_conditions?: string[]
          timeframes?: string[]
          instruments?: string[]
          risk_reward_ratio?: number | null
          expected_win_rate?: number | null
          status?: string
          created_at?: string
          updated_at?: string
        }
      },
      setups: {
        Row: {
          id: string
          strategy_id: string
          user_id: string
          name: string
          description: string | null
          visual_cues: string | null
          confirmation_criteria: string | null
          image_urls: string[]
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          strategy_id: string
          user_id: string
          name: string
          description?: string | null
          visual_cues?: string | null
          confirmation_criteria?: string | null
          image_urls?: string[]
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          strategy_id?: string
          user_id?: string
          name?: string
          description?: string | null
          visual_cues?: string | null
          confirmation_criteria?: string | null
          image_urls?: string[]
          created_at?: string
          updated_at?: string
        }
      },
      strategy_rules: {
        Row: {
          id: string
          strategy_id: string
          user_id: string
          rule_type: string
          name: string
          description: string | null
          priority: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          strategy_id: string
          user_id: string
          rule_type: string
          name: string
          description?: string | null
          priority?: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          strategy_id?: string
          user_id?: string
          rule_type?: string
          name?: string
          description?: string | null
          priority?: number
          created_at?: string
          updated_at?: string
        }
      },
      strategy_performance: {
        Row: {
          id: string
          strategy_id: string
          user_id: string
          period_start: string
          period_end: string
          total_trades: number
          winning_trades: number
          losing_trades: number
          win_rate: number
          profit_loss: number
          average_win: number
          average_loss: number
          largest_win: number
          largest_loss: number
          average_risk_reward: number
          expectancy: number
          profit_factor: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          strategy_id: string
          user_id: string
          period_start: string
          period_end: string
          total_trades?: number
          winning_trades?: number
          losing_trades?: number
          win_rate?: number
          profit_loss?: number
          average_win?: number
          average_loss?: number
          largest_win?: number
          largest_loss?: number
          average_risk_reward?: number
          expectancy?: number
          profit_factor?: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          strategy_id?: string
          user_id?: string
          period_start?: string
          period_end?: string
          total_trades?: number
          winning_trades?: number
          losing_trades?: number
          win_rate?: number
          profit_loss?: number
          average_win?: number
          average_loss?: number
          largest_win?: number
          largest_loss?: number
          average_risk_reward?: number
          expectancy?: number
          profit_factor?: number
          created_at?: string
          updated_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
  }
}