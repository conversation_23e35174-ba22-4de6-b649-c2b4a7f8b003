"use client"

import { useState } from "react"
import {
  <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Cell,
  Pie<PERSON><PERSON>, <PERSON>, <PERSON>
} from "recharts"
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Bar<PERSON>hart3, <PERSON><PERSON><PERSON> as PieChartIcon } from "lucide-react"
import { cn } from "@/lib/utils"

interface SymbolMetric {
  symbol: string
  totalTrades: number
  winningTrades: number
  losingTrades: number
  winRate: number
  totalProfit: number
  averageProfit: number
  profitFactor: number
  volume: number
  avgDuration?: number
  riskReward?: number
}

interface SymbolComparisonProps {
  symbolMetrics: SymbolMetric[]
  allSymbols: string[]
  selectedSymbols: string[]
  setSelectedSymbols: (symbols: string[]) => void
}

export function SymbolComparison({
  symbolMetrics,
  allSymbols,
  selectedSymbols,
  setSelectedSymbols
}: SymbolComparisonProps) {
  const [chartType, setChartType] = useState<"bar" | "pie">("bar")
  const [activeTab, setActiveTab] = useState("pnl")

  // Define colors for the selected symbols
  const SYMBOL_COLORS = ['#3B82F6', '#10B981', '#F97316', '#EC4899']

  const toggleSymbol = (symbol: string) => {
    if (selectedSymbols.includes(symbol)) {
      setSelectedSymbols(selectedSymbols.filter(s => s !== symbol))
    } else {
      // Only allow up to 4 symbols
      if (selectedSymbols.length < 4) {
        setSelectedSymbols([...selectedSymbols, symbol])
      }
    }
  }

  // Filter metrics to only include selected symbols
  const filteredMetrics = symbolMetrics.filter(m => selectedSymbols.includes(m.symbol))

  // Prepare data for different metrics
  const getChartData = () => {
    // Get the color for a symbol based on its position in the selectedSymbols array
    const getSymbolColor = (symbol: string) => {
      const index = selectedSymbols.indexOf(symbol)
      return SYMBOL_COLORS[index % SYMBOL_COLORS.length]
    }

    switch (activeTab) {
      case "pnl":
        return filteredMetrics.map(m => ({
          symbol: m.symbol,
          value: parseFloat(m.totalProfit.toFixed(2)),
          fill: getSymbolColor(m.symbol),
          color: getSymbolColor(m.symbol)
        }))
      case "trades":
        return filteredMetrics.map(m => ({
          symbol: m.symbol,
          value: m.totalTrades,
          fill: getSymbolColor(m.symbol),
          color: getSymbolColor(m.symbol)
        }))
      case "winrate":
        return filteredMetrics.map(m => ({
          symbol: m.symbol,
          value: parseFloat(m.winRate.toFixed(2)),
          fill: getSymbolColor(m.symbol),
          color: getSymbolColor(m.symbol)
        }))
      case "riskReward":
        return filteredMetrics.map(m => ({
          symbol: m.symbol,
          value: parseFloat((m.riskReward || 0).toFixed(2)),
          fill: getSymbolColor(m.symbol),
          color: getSymbolColor(m.symbol)
        }))
      case "duration":
        return filteredMetrics.map(m => ({
          symbol: m.symbol,
          value: parseFloat((m.avgDuration || 0).toFixed(2)),
          fill: getSymbolColor(m.symbol),
          color: getSymbolColor(m.symbol)
        }))
      case "volume":
        return filteredMetrics.map(m => ({
          symbol: m.symbol,
          value: parseFloat(m.volume.toFixed(2)),
          fill: getSymbolColor(m.symbol),
          color: getSymbolColor(m.symbol)
        }))
      default:
        return []
    }
  }

  const chartData = getChartData()

  // Get the appropriate label for the Y-axis
  const getYAxisLabel = () => {
    switch (activeTab) {
      case "pnl": return "P&L ($)"
      case "trades": return "Number of Trades"
      case "winrate": return "Win Rate (%)"
      case "riskReward": return "Risk/Reward Ratio"
      case "duration": return "Avg. Duration (min)"
      case "volume": return "Volume"
      default: return ""
    }
  }

  // Format the tooltip value based on the active tab
  const formatTooltipValue = (value: number) => {
    switch (activeTab) {
      case "pnl": return `$${value.toFixed(2)}`
      case "winrate": return `${value.toFixed(2)}%`
      case "riskReward": return value.toFixed(2)
      case "duration": return `${value.toFixed(2)} min`
      case "volume": return value.toFixed(2)
      default: return value.toString()
    }
  }

  return (
    <Card className="bg-card/50 hover:bg-card/80 transition-colors">
      <CardHeader className="p-4 pb-2 flex flex-row items-center justify-between">
        <CardTitle className="text-base font-medium">Symbol Comparison</CardTitle>
        <div className="flex items-center space-x-2">
          <Button
            variant={chartType === "bar" ? "default" : "outline"}
            size="sm"
            onClick={() => setChartType("bar")}
            className="h-8 w-8 p-0"
          >
            <BarChart3 className="h-4 w-4" />
            <span className="sr-only">Bar Chart</span>
          </Button>
          <Button
            variant={chartType === "pie" ? "default" : "outline"}
            size="sm"
            onClick={() => setChartType("pie")}
            className="h-8 w-8 p-0"
          >
            <PieChartIcon className="h-4 w-4" />
            <span className="sr-only">Pie Chart</span>
          </Button>
        </div>
      </CardHeader>
      <CardContent className="p-4 pt-0">
        <div className="mb-4">
          <p className="text-sm text-muted-foreground mb-2">Select up to 4 symbols to compare:</p>
          <div className="flex flex-wrap gap-2">
            {allSymbols.filter(s => s !== "all").map((symbol) => (
              <Badge
                key={symbol}
                variant={selectedSymbols.includes(symbol) ? "default" : "outline"}
                className={cn(
                  "cursor-pointer",
                  selectedSymbols.includes(symbol) &&
                  `bg-[${SYMBOL_COLORS[selectedSymbols.indexOf(symbol) % SYMBOL_COLORS.length]}] hover:bg-[${SYMBOL_COLORS[selectedSymbols.indexOf(symbol) % SYMBOL_COLORS.length]}/80]`
                )}
                style={{
                  backgroundColor: selectedSymbols.includes(symbol)
                    ? SYMBOL_COLORS[selectedSymbols.indexOf(symbol) % SYMBOL_COLORS.length]
                    : undefined,
                  borderColor: selectedSymbols.includes(symbol)
                    ? SYMBOL_COLORS[selectedSymbols.indexOf(symbol) % SYMBOL_COLORS.length]
                    : undefined,
                }}
                onClick={() => toggleSymbol(symbol)}
              >
                {symbol}
              </Badge>
            ))}
          </div>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-6 rounded-none border-b bg-transparent">
            <TabsTrigger
              value="pnl"
              className="rounded-none data-[state=active]:bg-muted data-[state=active]:border-b data-[state=active]:border-primary data-[state=active]:font-medium"
            >
              P&L
            </TabsTrigger>
            <TabsTrigger
              value="trades"
              className="rounded-none data-[state=active]:bg-muted data-[state=active]:border-b data-[state=active]:border-primary data-[state=active]:font-medium"
            >
              Trades
            </TabsTrigger>
            <TabsTrigger
              value="winrate"
              className="rounded-none data-[state=active]:bg-muted data-[state=active]:border-b data-[state=active]:border-primary data-[state=active]:font-medium"
            >
              Win Rate
            </TabsTrigger>
            <TabsTrigger
              value="riskReward"
              className="rounded-none data-[state=active]:bg-muted data-[state=active]:border-b data-[state=active]:border-primary data-[state=active]:font-medium"
            >
              Risk/Reward
            </TabsTrigger>
            <TabsTrigger
              value="duration"
              className="rounded-none data-[state=active]:bg-muted data-[state=active]:border-b data-[state=active]:border-primary data-[state=active]:font-medium"
            >
              Duration
            </TabsTrigger>
            <TabsTrigger
              value="volume"
              className="rounded-none data-[state=active]:bg-muted data-[state=active]:border-b data-[state=active]:border-primary data-[state=active]:font-medium"
            >
              Volume
            </TabsTrigger>
          </TabsList>

          <div className="mt-4">
            {selectedSymbols.length === 0 ? (
              <div className="h-[300px] flex items-center justify-center text-muted-foreground">
                Select symbols to compare
              </div>
            ) : (
              <div className="h-[300px]">
                {chartType === "bar" ? (
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={chartData}
                      margin={{ top: 20, right: 30, left: 40, bottom: 60 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" opacity={0.3} />
                      <XAxis
                        dataKey="symbol"
                        angle={-45}
                        textAnchor="end"
                        height={60}
                        tick={{ fill: 'hsl(var(--muted-foreground))', fontSize: 11, opacity: 0.7 }}
                        stroke="hsl(var(--border))"
                        strokeOpacity={0.5}
                      />
                      <YAxis
                        label={{
                          value: getYAxisLabel(),
                          angle: -90,
                          position: 'insideLeft',
                          style: { fill: 'hsl(var(--muted-foreground))', fontSize: 11, opacity: 0.7 }
                        }}
                        tick={{ fill: 'hsl(var(--muted-foreground))', fontSize: 11, opacity: 0.7 }}
                        stroke="hsl(var(--border))"
                        strokeOpacity={0.5}
                      />
                      <Tooltip
                        content={({ active, payload }) => {
                          if (!active || !payload?.length) return null
                          const data = payload[0].payload
                          return (
                            <div className="rounded-lg border bg-card/90 p-2 shadow-sm text-xs">
                              <div className="grid grid-cols-2 gap-1">
                                <div className="font-medium text-muted-foreground">Symbol:</div>
                                <div>{data.symbol}</div>
                                <div className="font-medium text-muted-foreground">{activeTab === "pnl" ? "P&L:" : activeTab === "winrate" ? "Win Rate:" : activeTab === "riskReward" ? "Risk/Reward:" : activeTab === "duration" ? "Duration:" : activeTab === "volume" ? "Volume:" : "Trades:"}</div>
                                <div style={{ color: data.color }}>{formatTooltipValue(data.value)}</div>
                              </div>
                            </div>
                          )
                        }}
                      />
                      <Bar dataKey="value">
                        {chartData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.fill} />
                        ))}
                      </Bar>
                    </BarChart>
                  </ResponsiveContainer>
                ) : (
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={chartData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        outerRadius={80}
                        dataKey="value"
                        label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                      >
                        {chartData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.fill} />
                        ))}
                      </Pie>
                      <Tooltip
                        content={({ active, payload }) => {
                          if (!active || !payload?.length) return null
                          const data = payload[0].payload
                          return (
                            <div className="rounded-lg border bg-card/90 p-2 shadow-sm text-xs">
                              <div className="grid grid-cols-2 gap-1">
                                <div className="font-medium text-muted-foreground">Symbol:</div>
                                <div>{data.symbol}</div>
                                <div className="font-medium text-muted-foreground">{activeTab === "pnl" ? "P&L:" : activeTab === "winrate" ? "Win Rate:" : activeTab === "riskReward" ? "Risk/Reward:" : activeTab === "duration" ? "Duration:" : activeTab === "volume" ? "Volume:" : "Trades:"}</div>
                                <div style={{ color: data.color }}>{formatTooltipValue(data.value)}</div>
                              </div>
                            </div>
                          )
                        }}
                      />
                      <Legend />
                    </PieChart>
                  </ResponsiveContainer>
                )}
              </div>
            )}
          </div>
        </Tabs>
      </CardContent>
    </Card>
  )
}
