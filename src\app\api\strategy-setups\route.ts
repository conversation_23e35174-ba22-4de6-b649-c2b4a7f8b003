import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { createServerClient } from '@supabase/ssr';
import type { Database } from '@/types/supabase';
import { Setup } from '@/types/playbook';

// GET handler to fetch strategy setups
export async function GET(request: Request) {
  try {
    // Get URL parameters
    const url = new URL(request.url);
    const setupId = url.searchParams.get('id');
    const strategyId = url.searchParams.get('strategyId');

    // Get cookies for server-side Supabase client
    const cookieStore = await cookies();
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
          set(_name: string, _value: string, _options: any) {
            // API routes can't set cookies directly
          },
          remove(_name: string, _options: any) {
            // API routes can't remove cookies directly
          }
        },
      }
    );

    // Get authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const userId = user.id;

    // If setupId is provided, fetch a single setup
    if (setupId) {
      const { data, error } = await supabase
        .from('setups')
        .select('*, strategies(*)')
        .eq('id', setupId)
        .eq('user_id', userId)
        .single();

      if (error) {
        return NextResponse.json({ error: error.message }, { status: 500 });
      }

      return NextResponse.json(data);
    }

    // Build query for setups
    let query = supabase
      .from('setups')
      .select('*, strategies(*)')
      .eq('user_id', userId);

    // Filter by strategy if specified
    if (strategyId) {
      query = query.eq('strategy_id', strategyId);
    }

    // Execute query
    const { data, error } = await query.order('created_at', { ascending: false });

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json(data || []);
  } catch (error) {
    console.error('Error fetching strategy setups:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// POST handler to create a setup
export async function POST(request: Request) {
  try {
    const body = await request.json();

    // Get cookies for server-side Supabase client
    const cookieStore = await cookies();
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
          set(_name: string, _value: string, _options: any) {
            // API routes can't set cookies directly
          },
          remove(_name: string, _options: any) {
            // API routes can't remove cookies directly
          }
        },
      }
    );

    // Get authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const userId = user.id;

    // Validate required fields
    if (!body.strategy_id) {
      return NextResponse.json({ error: 'Strategy ID is required' }, { status: 400 });
    }

    if (!body.name) {
      return NextResponse.json({ error: 'Setup name is required' }, { status: 400 });
    }

    // Ensure image_urls is an array
    const setupData = {
      ...body,
      image_urls: Array.isArray(body.image_urls) ? body.image_urls : []
    };

    // Create setup
    const { data, error } = await supabase
      .from('setups')
      .insert([
        {
          user_id: userId,
          ...setupData
        }
      ])
      .select()
      .single();

    if (error) {
      // Check for unique constraint violation
      if (error.code === '23505' && error.message.includes('setups_strategy_id_name_key')) {
        return NextResponse.json({
          error: 'A setup with this name already exists for this strategy. Please use a different name.'
        }, { status: 409 });
      }

      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error('Error creating setup:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// PATCH handler to update a setup
export async function PATCH(request: Request) {
  try {
    const body = await request.json();

    // Get cookies for server-side Supabase client
    const cookieStore = await cookies();
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
          set(_name: string, _value: string, _options: any) {
            // API routes can't set cookies directly
          },
          remove(_name: string, _options: any) {
            // API routes can't remove cookies directly
          }
        },
      }
    );

    // Get authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const userId = user.id;

    // Validate required fields
    if (!body.id) {
      return NextResponse.json({ error: 'Setup ID is required' }, { status: 400 });
    }

    // First, get the current setup to compare image URLs
    const { data: currentSetup, error: fetchError } = await supabase
      .from('setups')
      .select('image_urls')
      .eq('id', body.id)
      .eq('user_id', userId)
      .single();

    if (fetchError) {
      return NextResponse.json({ error: fetchError.message }, { status: 500 });
    }

    // Update setup
    const { data, error } = await supabase
      .from('setups')
      .update({
        name: body.name,
        description: body.description,
        visual_cues: body.visual_cues,
        confirmation_criteria: body.confirmation_criteria,
        image_urls: body.image_urls,
        updated_at: new Date().toISOString()
      })
      .eq('id', body.id)
      .eq('user_id', userId)
      .select()
      .single();

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error('Error updating setup:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// DELETE handler to delete a setup
export async function DELETE(request: Request) {
  try {
    // Get URL parameters
    const url = new URL(request.url);
    const setupId = url.searchParams.get('id');

    if (!setupId) {
      return NextResponse.json({ error: 'Setup ID is required' }, { status: 400 });
    }

    // Get cookies for server-side Supabase client
    const cookieStore = await cookies();
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
          set(_name: string, _value: string, _options: any) {
            // API routes can't set cookies directly
          },
          remove(_name: string, _options: any) {
            // API routes can't remove cookies directly
          }
        },
      }
    );

    // Get authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const userId = user.id;

    // First, get the setup to retrieve its image URLs
    const { data: setup, error: fetchError } = await supabase
      .from('setups')
      .select('image_urls')
      .eq('id', setupId)
      .eq('user_id', userId)
      .single();

    if (fetchError) {
      return NextResponse.json({ error: fetchError.message }, { status: 500 });
    }

    // Delete setup
    const { error } = await supabase
      .from('setups')
      .delete()
      .eq('id', setupId)
      .eq('user_id', userId);

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      message: 'Setup deleted successfully',
      imageUrls: setup?.image_urls || []
    });
  } catch (error) {
    console.error('Error deleting setup:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
