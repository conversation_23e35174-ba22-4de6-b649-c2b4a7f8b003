# Server-Side Rendering Troubleshooting Guide

This document provides solutions to common issues encountered when implementing server-side rendering in Next.js 15 with Supabase.

## Table of Contents

1. [Cookie Handling Issues](#cookie-handling-issues)
2. [Authentication Issues](#authentication-issues)
3. [Data Fetching Issues](#data-fetching-issues)
4. [Dynamic Import Issues](#dynamic-import-issues)
5. [TypeScript Issues](#typescript-issues)
6. [Performance Issues](#performance-issues)

## Cookie Handling Issues

### Issue: `cookies()` Should Be Awaited

**Error Message**:
```
Error: Route "/api/route" used `cookies().get('cookie-name')`. `cookies()` should be awaited before using its value.
```

**Solution**:
```tsx
// Incorrect
const cookieStore = cookies();

// Correct
const cookieStore = await cookies();
```

### Issue: Missing Cookie Methods

**Error Message**:
```
TypeError: Cannot read properties of undefined (reading 'set')
```

**Solution**:
```tsx
const supabase = createServerClient<Database>(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
  {
    cookies: {
      get(name: string) {
        return cookieStore.get(name)?.value;
      },
      set(_name: string, _value: string, _options: any) {
        // API routes can't set cookies directly
      },
      remove(_name: string, _options: any) {
        // API routes can't remove cookies directly
      }
    },
  }
);
```

## Authentication Issues

### Issue: Auth Session Missing

**Error Message**:
```
AuthSessionMissingError: Auth session missing!
```

**Solution**:
```tsx
// Incorrect
const { data: { session } } = await supabase.auth.getSession();
if (!session) {
  return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
}

const userId = session.user.id;

// Correct
const { data: { user }, error: userError } = await supabase.auth.getUser();
if (userError || !user) {
  return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
}

const userId = user.id;
```

### Issue: Redirect Not Working

**Error Message**:
```
Error: Redirects in Server Components must be used before any data is sent to the client.
```

**Solution**:
```tsx
// Incorrect - Redirect after some rendering
const { data: { user } } = await supabase.auth.getUser();
if (!user) {
  // Some rendering or logging here
  console.log('User not authenticated');
  redirect('/auth');
}

// Correct - Redirect immediately
const { data: { user }, error: userError } = await supabase.auth.getUser();
if (userError || !user) {
  redirect('/auth');
}
```

## Data Fetching Issues

### Issue: Data Not Available in Client Component

**Symptom**: Client component receives empty data even though data is fetched in server component.

**Solution**:
```tsx
// Server Component
export default async function PageName() {
  // ... authentication and data fetching ...
  
  // Make sure to handle the case where data is null or undefined
  return <ClientWrapper initialData={data || []} />;
}

// Client Component
export default function ClientComponent({ initialData }) {
  // Initialize state with initialData
  const [data, setData] = useState(initialData);
  
  // Rest of the component
}
```

### Issue: Prefetched Data Not Used

**Symptom**: Client component fetches data again even though it was prefetched in server component.

**Solution**:
```tsx
// Client Component
export default function ClientComponent({ initialData, prefetchedData }) {
  // Use prefetched data if available
  const [relatedData, setRelatedData] = useState(prefetchedData?.relatedData || []);
  
  // Only fetch if we don't have prefetched data
  useEffect(() => {
    if (!prefetchedData?.relatedData) {
      // Fetch related data
    }
  }, [prefetchedData]);
  
  // Rest of the component
}
```

## Dynamic Import Issues

### Issue: SSR False Not Allowed in Server Components

**Error Message**:
```
Error: `ssr: false` is not allowed with `next/dynamic` in Server Components.
```

**Solution**:
```tsx
// Incorrect - Using dynamic import with ssr: false in server component
import dynamic from 'next/dynamic';

const ClientComponent = dynamic(() => import('./client'), {
  ssr: false
});

export default async function ServerComponent() {
  // ... server component code ...
  return <ClientComponent />;
}

// Correct - Create a client wrapper component
// server-component.tsx
export default async function ServerComponent() {
  // ... server component code ...
  return <ClientWrapper />;
}

// client-wrapper.tsx
"use client"
import dynamic from 'next/dynamic';

const ClientComponent = dynamic(() => import('./client'), {
  ssr: false
});

export default function ClientWrapper() {
  return <ClientComponent />;
}
```

## TypeScript Issues

### Issue: Type Errors in Form Components

**Error Message**:
```
Type 'Control<{ ... }, any, TFieldValues>' is not assignable to type 'Control<{ ... }, any, { ... }>'.
```

**Solution**:
```tsx
// Define a specific type for your form values
interface FormValues {
  field1: string;
  field2: number;
  // ... other fields
}

// Use the specific type in your form
const form = useForm<FormValues>({
  resolver: zodResolver(formSchema),
  defaultValues: {
    field1: "",
    field2: 0,
    // ... other fields
  }
});
```

### Issue: Type Errors in Supabase Client

**Error Message**:
```
Property 'your_table' does not exist on type 'Database["public"]["Tables"]'.
```

**Solution**:
```tsx
// Make sure your Database type includes all tables
// types/supabase.ts
export type Database = {
  public: {
    Tables: {
      your_table: {
        Row: {
          id: string;
          // ... other fields
        };
        Insert: {
          id?: string;
          // ... other fields
        };
        Update: {
          id?: string;
          // ... other fields
        };
      };
      // ... other tables
    };
  };
};
```

## Performance Issues

### Issue: Slow Server-Side Rendering

**Symptom**: Pages take a long time to load when using server-side rendering.

**Solutions**:

1. **Optimize Data Fetching**:
   ```tsx
   // Fetch only the data you need
   const { data } = await supabase
     .from('your_table')
     .select('id, name, created_at') // Select only needed fields
     .eq('user_id', userId)
     .limit(10); // Limit the number of rows
   ```

2. **Use Parallel Data Fetching**:
   ```tsx
   // Fetch multiple data sources in parallel
   const [
     { data: data1 },
     { data: data2 }
   ] = await Promise.all([
     supabase.from('table1').select('*').eq('user_id', userId),
     supabase.from('table2').select('*').eq('user_id', userId)
   ]);
   ```

3. **Implement Caching**:
   ```tsx
   // Use Next.js fetch with caching
   const response = await fetch('/api/data', { next: { revalidate: 60 } });
   const data = await response.json();
   ```

### Issue: Client-Side Hydration Mismatch

**Error Message**:
```
Warning: Text content did not match. Server: "Server value" Client: "Client value"
```

**Solution**:
```tsx
// Use useEffect to update client-side values after hydration
"use client"
import { useState, useEffect } from 'react';

export default function Component() {
  const [isClient, setIsClient] = useState(false);
  
  useEffect(() => {
    setIsClient(true);
  }, []);
  
  return (
    <div>
      {isClient ? 'Client value' : 'Server value'}
    </div>
  );
}
```

## Additional Resources

- [Next.js Documentation on Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components)
- [Supabase Documentation on Server-Side Rendering](https://supabase.com/docs/guides/auth/server-side-rendering)
- [Next.js Documentation on Dynamic Imports](https://nextjs.org/docs/app/building-your-application/optimizing/lazy-loading)
- [Next.js Documentation on Data Fetching](https://nextjs.org/docs/app/building-your-application/data-fetching)

If you encounter any issues not covered in this guide, please refer to the official documentation or seek help from the development team.
