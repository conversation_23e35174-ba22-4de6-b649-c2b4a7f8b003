"use client"

import { useState, useMemo } from "react"
import { Trade } from "@/types/trade"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { DollarSign, Hash, Percent as PercentIcon, TrendingUp } from "lucide-react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Scatter<PERSON>hart, Scatter, XAxis, YAxis, CartesianGrid,
  Tooltip, Legend, ResponsiveContainer, ZAxis, Cell,
  BarChart, Bar, ReferenceLine
} from "recharts"
import { differenceInMinutes } from "date-fns"

interface TradeDurationAnalysisProps {
  trades: Trade[]
}

type MetricType = "profit" | "winRate" | "tradeCount" | "averageProfit"
type DurationGrouping = "minutes" | "hours" | "days" | "scatter"

export function TradeDurationAnalysis({ trades }: TradeDurationAnalysisProps) {
  const [selectedMetric, setSelectedMetric] = useState<MetricType>("profit")
  const [durationGrouping, setDurationGrouping] = useState<DurationGrouping>("minutes")
  const [timeRange, setTimeRange] = useState<"all" | "1m" | "3m" | "6m" | "1y">("all")

  // Filter trades by time range
  const filteredTrades = useMemo(() => {
    if (timeRange === "all") return trades

    const now = new Date()
    let startDate = new Date()

    switch (timeRange) {
      case "1m":
        startDate.setMonth(now.getMonth() - 1)
        break
      case "3m":
        startDate.setMonth(now.getMonth() - 3)
        break
      case "6m":
        startDate.setMonth(now.getMonth() - 6)
        break
      case "1y":
        startDate.setFullYear(now.getFullYear() - 1)
        break
    }

    return trades.filter(trade => new Date(trade.time_close) >= startDate)
  }, [trades, timeRange])

  // Calculate duration for each trade
  const tradesWithDuration = useMemo(() => {
    return filteredTrades.map(trade => {
      const openTime = new Date(trade.time_open)
      const closeTime = new Date(trade.time_close)
      const durationMinutes = differenceInMinutes(closeTime, openTime)

      return {
        ...trade,
        durationMinutes,
        durationHours: durationMinutes / 60,
        durationDays: durationMinutes / (60 * 24)
      }
    })
  }, [filteredTrades])

  // Add a small random jitter to prevent overlapping points
  const addJitter = (value: number, jitterAmount: number = 0.05) => {
    // Add a small random offset (±5% by default)
    const jitter = (Math.random() - 0.5) * 2 * jitterAmount * value;
    return value + jitter;
  };

  // Group trades by duration
  const durationData = useMemo(() => {
    if (durationGrouping === "scatter") {
      // For scatter plot, return individual trades with jitter
      return tradesWithDuration.map(trade => ({
        // Add small jitter to duration and profit to prevent exact overlaps
        durationMinutes: addJitter(trade.durationMinutes),
        profit: addJitter(trade.profit, 0.02), // Less jitter for profit
        isWin: trade.profit >= 0,
        symbol: trade.symbol,
        volume: trade.volume,
        type: trade.type,
        // Store original values for tooltip
        originalDuration: trade.durationMinutes,
        originalProfit: trade.profit
      }))
    }

    const durationMap = new Map<string, {
      durationLabel: string
      durationMinutes: number
      totalProfit: number
      wins: number
      losses: number
      tradeCount: number
      sortOrder: number
    }>()

    // Define duration buckets based on grouping
    const getDurationBuckets = () => {
      if (durationGrouping === "minutes") {
        return [
          { label: "< 5 min", max: 5, sortOrder: 0 },
          { label: "5-15 min", max: 15, sortOrder: 1 },
          { label: "15-30 min", max: 30, sortOrder: 2 },
          { label: "30-60 min", max: 60, sortOrder: 3 },
          { label: "1-2 hours", max: 120, sortOrder: 4 },
          { label: "2-4 hours", max: 240, sortOrder: 5 },
          { label: "> 4 hours", max: Infinity, sortOrder: 6 }
        ]
      } else if (durationGrouping === "hours") {
        return [
          { label: "< 1 hour", max: 60, sortOrder: 0 },
          { label: "1-3 hours", max: 180, sortOrder: 1 },
          { label: "3-6 hours", max: 360, sortOrder: 2 },
          { label: "6-12 hours", max: 720, sortOrder: 3 },
          { label: "12-24 hours", max: 1440, sortOrder: 4 },
          { label: "> 24 hours", max: Infinity, sortOrder: 5 }
        ]
      } else { // days
        return [
          { label: "< 1 day", max: 1440, sortOrder: 0 },
          { label: "1-3 days", max: 4320, sortOrder: 1 },
          { label: "3-7 days", max: 10080, sortOrder: 2 },
          { label: "1-2 weeks", max: 20160, sortOrder: 3 },
          { label: "2-4 weeks", max: 40320, sortOrder: 4 },
          { label: "> 4 weeks", max: Infinity, sortOrder: 5 }
        ]
      }
    }

    const durationBuckets = getDurationBuckets()

    // Initialize duration buckets
    durationBuckets.forEach(bucket => {
      durationMap.set(bucket.label, {
        durationLabel: bucket.label,
        durationMinutes: bucket.max,
        totalProfit: 0,
        wins: 0,
        losses: 0,
        tradeCount: 0,
        sortOrder: bucket.sortOrder
      })
    })

    tradesWithDuration.forEach(trade => {
      const isWin = trade.profit > 0
      const durationMinutes = trade.durationMinutes

      // Find the appropriate bucket
      const bucket = durationBuckets.find((b, index) => {
        if (index === 0) return durationMinutes < b.max
        const prevMax = durationBuckets[index - 1].max
        return durationMinutes >= prevMax && durationMinutes < b.max
      }) || durationBuckets[durationBuckets.length - 1]

      const data = durationMap.get(bucket.label)!
      data.totalProfit += trade.profit
      data.tradeCount += 1

      if (isWin) {
        data.wins += 1
      } else {
        data.losses += 1
      }
    })

    // Convert map to array and calculate additional metrics
    return Array.from(durationMap.values())
      .map(data => ({
        ...data,
        winRate: data.tradeCount > 0 ? (data.wins / data.tradeCount) * 100 : 0,
        averageProfit: data.tradeCount > 0 ? data.totalProfit / data.tradeCount : 0
      }))
      .sort((a, b) => a.sortOrder - b.sortOrder)
  }, [tradesWithDuration, durationGrouping])

  // Format the metric value for display
  const formatMetricValue = (value: number, metric: MetricType) => {
    switch (metric) {
      case "profit":
        return `$${value.toFixed(2)}`
      case "winRate":
        return `${value.toFixed(2)}%`
      case "tradeCount":
        return value.toString()
      case "averageProfit":
        return `$${value.toFixed(2)}`
      default:
        return value.toString()
    }
  }

  // Get the label for the selected metric
  const getMetricLabel = (metric: MetricType) => {
    switch (metric) {
      case "profit":
        return "Total Profit"
      case "winRate":
        return "Win Rate (%)"
      case "tradeCount":
        return "Number of Trades"
      case "averageProfit":
        return "Average Profit per Trade"
      default:
        return ""
    }
  }

  // Custom tooltip for the chart
  const CustomTooltip = ({ active, payload }: { active?: boolean, payload?: any[] }) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload

      if (durationGrouping === "scatter") {
        const isProfit = data.profit >= 0;
        return (
          <div className="bg-background border rounded-md p-3 shadow-md">
            <div className="flex items-center gap-2 mb-1">
              <div className={`w-3 h-3 rounded-full ${isProfit ? 'bg-emerald-500' : 'bg-rose-500'}`}></div>
              <p className="font-medium">{data.symbol} {data.type.toUpperCase()}</p>
            </div>
            <div className="space-y-1 mt-2">
              <p className="text-sm flex justify-between">
                <span className="text-muted-foreground">Duration:</span>
                <span className="font-medium">{formatDuration(data.originalDuration || data.durationMinutes)}</span>
              </p>
              <p className="text-sm flex justify-between">
                <span className="text-muted-foreground">Profit:</span>
                <span className={`font-medium ${isProfit ? 'text-emerald-500' : 'text-rose-500'}`}>
                  ${(data.originalProfit || data.profit).toFixed(2)}
                </span>
              </p>
              <p className="text-sm flex justify-between">
                <span className="text-muted-foreground">Volume:</span>
                <span className="font-medium">{data.volume}</span>
              </p>
            </div>
          </div>
        )
      }

      return (
        <div className="bg-background border rounded-md p-3 shadow-md">
          <p className="font-medium">{data.durationLabel}</p>
          <p className="text-sm text-muted-foreground">
            {getMetricLabel(selectedMetric)}: {formatMetricValue(data[selectedMetric === "profit" ? "totalProfit" : selectedMetric], selectedMetric)}
          </p>
          <p className="text-sm text-muted-foreground">Win Rate: {data.winRate.toFixed(2)}%</p>
          <p className="text-sm text-muted-foreground">Trades: {data.tradeCount}</p>
        </div>
      )
    }
    return null
  }

  // Format duration for display
  const formatDuration = (minutes: number) => {
    if (minutes < 60) {
      // Round to 2 decimal places for minutes
      const roundedMinutes = Math.round(minutes * 100) / 100;
      return `${roundedMinutes.toFixed(2)}m`
    } else if (minutes < 60 * 24) {
      const hours = Math.floor(minutes / 60)
      const mins = Math.round(minutes % 60)
      return `${hours}h ${mins}m`
    } else {
      const days = Math.floor(minutes / (60 * 24))
      const hours = Math.floor((minutes % (60 * 24)) / 60)
      return `${days}d ${hours}h`
    }
  }

  // Get colors for the bars/scatter points based on profit
  const getBarColor = (data: any) => {
    if (durationGrouping === "scatter") {
      return data.profit >= 0 ? "#10b981" : "#ef4444"
    }

    const value = data[selectedMetric === "profit" ? "totalProfit" : selectedMetric]
    if (selectedMetric === "profit" || selectedMetric === "averageProfit") {
      return value >= 0 ? "#10b981" : "#ef4444"
    }
    return "#3b82f6"
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row gap-4 justify-between">
        <div className="flex flex-col sm:flex-row gap-2">
          {durationGrouping !== "scatter" && (
            <Tabs value={selectedMetric} onValueChange={(value) => setSelectedMetric(value as MetricType)}>
              <TabsList className="grid w-[450px] grid-cols-4 rounded-none border-b bg-transparent">
                <TabsTrigger
                  value="profit"
                  className="rounded-none data-[state=active]:bg-background data-[state=active]:border-b data-[state=active]:border-primary data-[state=active]:font-medium"
                >
                  <DollarSign className="mr-2 h-4 w-4" />
                  Total Profit
                </TabsTrigger>
                <TabsTrigger
                  value="winRate"
                  className="rounded-none data-[state=active]:bg-background data-[state=active]:border-b data-[state=active]:border-primary data-[state=active]:font-medium"
                >
                  <PercentIcon className="mr-2 h-4 w-4" />
                  Win Rate
                </TabsTrigger>
                <TabsTrigger
                  value="tradeCount"
                  className="rounded-none data-[state=active]:bg-background data-[state=active]:border-b data-[state=active]:border-primary data-[state=active]:font-medium"
                >
                  <Hash className="mr-2 h-4 w-4" />
                  Trade Count
                </TabsTrigger>
                <TabsTrigger
                  value="averageProfit"
                  className="rounded-none data-[state=active]:bg-background data-[state=active]:border-b data-[state=active]:border-primary data-[state=active]:font-medium"
                >
                  <TrendingUp className="mr-2 h-4 w-4" />
                  Avg Profit
                </TabsTrigger>
              </TabsList>
            </Tabs>
          )}

          <Select value={durationGrouping} onValueChange={(value) => setDurationGrouping(value as DurationGrouping)}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Duration Grouping" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="minutes">Minutes</SelectItem>
              <SelectItem value="hours">Hours</SelectItem>
              <SelectItem value="days">Days</SelectItem>
              <SelectItem value="scatter">Scatter Plot</SelectItem>
            </SelectContent>
          </Select>

          <Select value={timeRange} onValueChange={(value) => setTimeRange(value as any)}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Time Range" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Time</SelectItem>
              <SelectItem value="1m">Last Month</SelectItem>
              <SelectItem value="3m">Last 3 Months</SelectItem>
              <SelectItem value="6m">Last 6 Months</SelectItem>
              <SelectItem value="1y">Last Year</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {durationData.length === 0 ? (
        <div className="flex justify-center items-center h-[400px] border rounded-md">
          <p className="text-muted-foreground">No data available for the selected time range</p>
        </div>
      ) : (
        <div className="h-[500px]">
          {durationGrouping === "scatter" ? (
            <ResponsiveContainer width="100%" height="100%">
              <ScatterChart
                margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
              >
                <CartesianGrid
                  strokeDasharray="3 3"
                  stroke="hsl(var(--border))"
                  opacity={0.4}
                  vertical={true}
                />
                <XAxis
                  type="number"
                  dataKey="durationMinutes"
                  name="Duration"
                  label={{
                    value: "Duration (minutes)",
                    position: 'insideBottom',
                    offset: -10,
                    style: {
                      fill: 'hsl(var(--muted-foreground))',
                      fontSize: 12
                    }
                  }}
                  tickFormatter={(value) => formatDuration(value)}
                  domain={['auto', 'auto']}
                  tick={{
                    fontSize: 11,
                    fill: 'hsl(var(--muted-foreground))',
                    opacity: 0.7
                  }}
                  axisLine={{ stroke: 'hsl(var(--border))', opacity: 0.5 }}
                />
                <YAxis
                  type="number"
                  dataKey="profit"
                  name="Profit"
                  label={{
                    value: "Profit ($)",
                    angle: -90,
                    position: 'insideLeft',
                    offset: -10,
                    style: {
                      textAnchor: 'middle',
                      fill: 'hsl(var(--muted-foreground))',
                      fontSize: 12
                    }
                  }}
                  tick={{
                    fontSize: 11,
                    fill: 'hsl(var(--muted-foreground))',
                    opacity: 0.7
                  }}
                  axisLine={{ stroke: 'hsl(var(--border))', opacity: 0.5 }}
                  width={60}
                />
                <ZAxis
                  type="number"
                  dataKey="volume"
                  range={[20, 100]}
                  name="Volume"
                />
                <Tooltip content={<CustomTooltip />} />
                <Legend
                  verticalAlign="bottom"
                  height={36}
                  wrapperStyle={{
                    paddingBottom: '10px',
                    fontSize: '12px'
                  }}
                />
                <ReferenceLine y={0} stroke="#666" />
                <Scatter
                  name="Trades"
                  data={durationData}
                  fill="#8884d8"
                  shape={(props: any) => {
                    const { cx, cy, fill, isActive } = props;
                    return (
                      <circle
                        cx={cx}
                        cy={cy}
                        r={isActive ? 6 : 4}
                        fill={fill}
                        stroke={isActive ? "#fff" : "transparent"}
                        strokeWidth={isActive ? 2 : 0}
                        style={{
                          transition: 'r 0.2s, stroke 0.2s, stroke-width 0.2s',
                          cursor: 'pointer',
                          opacity: isActive ? 1 : 0.8
                        }}
                      />
                    );
                  }}
                >
                  {durationData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={getBarColor(entry)} />
                  ))}
                </Scatter>
              </ScatterChart>
            </ResponsiveContainer>
          ) : (
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={durationData}
                margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
              >
                <CartesianGrid
                  strokeDasharray="3 3"
                  stroke="hsl(var(--border))"
                  opacity={0.4}
                  vertical={true}
                />
                <XAxis
                  dataKey="durationLabel"
                  tick={{
                    fontSize: 11,
                    fill: 'hsl(var(--muted-foreground))',
                    opacity: 0.7
                  }}
                  axisLine={{ stroke: 'hsl(var(--border))', opacity: 0.5 }}
                />
                <YAxis
                  tickFormatter={(value) => formatMetricValue(value, selectedMetric)}
                  label={{
                    value: getMetricLabel(selectedMetric),
                    angle: -90,
                    position: 'insideLeft',
                    offset: -10,
                    style: {
                      textAnchor: 'middle',
                      fill: 'hsl(var(--muted-foreground))',
                      fontSize: 12
                    }
                  }}
                  tick={{
                    fontSize: 11,
                    fill: 'hsl(var(--muted-foreground))',
                    opacity: 0.7
                  }}
                  axisLine={{ stroke: 'hsl(var(--border))', opacity: 0.5 }}
                  width={60}
                />
                <Tooltip content={<CustomTooltip />} />
                <Legend
                  verticalAlign="bottom"
                  height={36}
                  wrapperStyle={{
                    paddingBottom: '10px',
                    fontSize: '12px'
                  }}
                />
                {selectedMetric === "profit" || selectedMetric === "averageProfit" ? (
                  <ReferenceLine y={0} stroke="#666" />
                ) : null}
                <Bar
                  dataKey={selectedMetric === "profit" ? "totalProfit" :
                          selectedMetric === "averageProfit" ? "averageProfit" :
                          selectedMetric}
                  name={getMetricLabel(selectedMetric)}
                >
                  {durationData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={getBarColor(entry)} />
                  ))}
                </Bar>
              </BarChart>
            </ResponsiveContainer>
          )}
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Most Profitable Duration */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-base">Most Profitable</CardTitle>
            <CardDescription>
              Highest total profit
            </CardDescription>
          </CardHeader>
          <CardContent>
            {durationData.length > 0 && durationGrouping !== "scatter" ? (
              (() => {
                // Make sure we're only working with grouped data, not scatter plot data
                const groupedData = durationData.filter(d => 'durationLabel' in d && 'tradeCount' in d)

                const profitableDurations = [...groupedData]
                  .filter(d => d.tradeCount > 0)
                  .sort((a, b) => b.totalProfit - a.totalProfit)

                if (profitableDurations.length === 0) {
                  return <div className="text-muted-foreground">No profitable durations</div>
                }

                const mostProfitableDuration = profitableDurations[0]

                return (
                  <div className="space-y-1">
                    <div className="text-2xl font-bold">{mostProfitableDuration.durationLabel}</div>
                    <div className="text-sm text-muted-foreground">
                      Total Profit: ${mostProfitableDuration.totalProfit.toFixed(2)}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      Win Rate: {mostProfitableDuration.winRate.toFixed(2)}% ({mostProfitableDuration.wins}/{mostProfitableDuration.tradeCount})
                    </div>
                  </div>
                )
              })()
            ) : (
              <div className="text-muted-foreground">No grouped data available</div>
            )}
          </CardContent>
        </Card>

        {/* Highest Win Rate Duration */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-base">Highest Win Rate</CardTitle>
            <CardDescription>
              Best success percentage
            </CardDescription>
          </CardHeader>
          <CardContent>
            {durationData.length > 0 && durationGrouping !== "scatter" ? (
              (() => {
                // Filter durations with at least 5 trades for statistical significance
                // Make sure we're only working with grouped data, not scatter plot data
                const groupedData = durationData.filter(d => 'durationLabel' in d && 'tradeCount' in d)

                const significantDurations = groupedData.filter(d => d.tradeCount >= 5)

                if (significantDurations.length === 0) {
                  return <div className="text-muted-foreground">Insufficient data</div>
                }

                const sortedDurations = [...significantDurations]
                  .sort((a, b) => b.winRate - a.winRate)

                if (sortedDurations.length === 0) {
                  return <div className="text-muted-foreground">No significant durations</div>
                }

                const highestWinRateDuration = sortedDurations[0]

                return (
                  <div className="space-y-1">
                    <div className="text-2xl font-bold">{highestWinRateDuration.durationLabel}</div>
                    <div className="text-sm text-muted-foreground">
                      Win Rate: {highestWinRateDuration.winRate.toFixed(2)}%
                    </div>
                    <div className="text-sm text-muted-foreground">
                      Trades: {highestWinRateDuration.wins}/{highestWinRateDuration.tradeCount}
                    </div>
                  </div>
                )
              })()
            ) : (
              <div className="text-muted-foreground">No grouped data available</div>
            )}
          </CardContent>
        </Card>

        {/* Average Trade Duration */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-base">Average Duration</CardTitle>
            <CardDescription>
              Typical trade length
            </CardDescription>
          </CardHeader>
          <CardContent>
            {tradesWithDuration.length > 0 ? (
              (() => {
                const totalMinutes = tradesWithDuration.reduce((sum, trade) => sum + trade.durationMinutes, 0)
                const avgMinutes = totalMinutes / tradesWithDuration.length

                return (
                  <div className="space-y-1">
                    <div className="text-2xl font-bold">{formatDuration(avgMinutes)}</div>
                    <div className="text-sm text-muted-foreground">
                      Based on {tradesWithDuration.length} trades
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {(avgMinutes / 60).toFixed(2)} hours
                    </div>
                  </div>
                )
              })()
            ) : (
              <div className="text-muted-foreground">No data available</div>
            )}
          </CardContent>
        </Card>

        {/* Longest Profitable Trade */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-base">Longest Profitable</CardTitle>
            <CardDescription>
              Longest trade with profit
            </CardDescription>
          </CardHeader>
          <CardContent>
            {tradesWithDuration.length > 0 ? (
              (() => {
                const profitableTrades = tradesWithDuration.filter(trade => trade.profit > 0)

                if (profitableTrades.length === 0) {
                  return <div className="text-muted-foreground">No profitable trades</div>
                }

                const longestProfitableTrade = [...profitableTrades]
                  .sort((a, b) => b.durationMinutes - a.durationMinutes)[0]

                return (
                  <div className="space-y-1">
                    <div className="text-2xl font-bold">{formatDuration(longestProfitableTrade.durationMinutes)}</div>
                    <div className="text-sm text-muted-foreground">
                      Profit: ${longestProfitableTrade.profit.toFixed(2)}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      Symbol: {longestProfitableTrade.symbol}
                    </div>
                  </div>
                )
              })()
            ) : (
              <div className="text-muted-foreground">No data available</div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
