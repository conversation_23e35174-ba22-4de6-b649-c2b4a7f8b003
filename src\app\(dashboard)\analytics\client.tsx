"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { SymbolPerformance } from "@/components/analytics/symbol-performance"
import { TimeOfDayAnalysis } from "@/components/analytics/time-of-day-analysis"
import { TradeDurationAnalysis } from "@/components/analytics/trade-duration-analysis"
import { DrawdownAnalysis } from "@/components/analytics/drawdown-analysis"
import { ConsecutiveTradesAnalysis } from "@/components/analytics/consecutive-trades-analysis"
import { RiskManagementMetrics } from "@/components/analytics/risk-management-metrics"
import { BarChart3, Clock, Loader2, ShieldAlert, Timer, TrendingDown, Zap } from "lucide-react"
import { Trade } from "@/types/trade"
import { useAccount } from "@/contexts/account-context"
import { toast } from "sonner"
import { useAnalyticsData } from "@/hooks/use-analytics-data"

interface AnalyticsClientProps {
  userId: string;
  initialTrades: Trade[];
  initialSummary: any | null;
  selectedAccountId: string | null;
}

export default function AnalyticsClient({
  userId,
  initialTrades,
  initialSummary,
  selectedAccountId: initialAccountId
}: AnalyticsClientProps) {
  const router = useRouter()
  const [activeTab, setActiveTab] = useState("symbol-performance")
  const { selectedAccountId } = useAccount()

  // Use React Query to fetch analytics data
  const {
    data: analyticsData,
    isLoading,
    error
  } = useAnalyticsData(selectedAccountId)

  // Extract trades and initial balance from query result
  const trades = analyticsData?.trades || initialTrades
  const initialBalance = analyticsData?.summary?.initial_balance ||
    initialSummary?.initial_balance || 10000

  // Show error toast if query fails
  if (error) {
    console.error("Error fetching analytics data:", error)
    toast.error("Failed to load analytics data")
  }

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-[calc(100vh-200px)]">
        <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
      </div>
    )
  }

  if (selectedAccountId === null) {
    return (
      <div className="container py-6 space-y-6">
        <Card className="p-8 text-center">
          <div className="flex flex-col items-center justify-center space-y-4">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="h-12 w-12 text-muted-foreground mb-2"
            >
              <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
              <circle cx="9" cy="7" r="4" />
              <path d="M22 21v-2a4 4 0 0 0-3-3.87" />
              <path d="M16 3.13a4 4 0 0 1 0 7.75" />
            </svg>
            <h2 className="text-xl font-semibold">No Account Selected</h2>
            <p className="text-muted-foreground max-w-md">
              Please select an account from the dashboard to view your analytics. No data will be displayed until an account is selected.
            </p>
            <Button
              variant="outline"
              onClick={() => router.push('/dashboard')}
              className="mt-4"
            >
              Go to Dashboard
            </Button>
          </div>
        </Card>
      </div>
    )
  }

  return (
    <div className="container py-6 space-y-6">

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid grid-cols-6 w-full max-w-4xl rounded-none border-b bg-transparent">
          <TabsTrigger
            value="symbol-performance"
            className="rounded-none data-[state=active]:bg-background data-[state=active]:border-b data-[state=active]:border-primary data-[state=active]:font-medium"
          >
            <BarChart3 className="mr-2 h-4 w-4" />
            Symbol
          </TabsTrigger>
          <TabsTrigger
            value="time-of-day"
            className="rounded-none data-[state=active]:bg-background data-[state=active]:border-b data-[state=active]:border-primary data-[state=active]:font-medium"
          >
            <Clock className="mr-2 h-4 w-4" />
            Time of Day
          </TabsTrigger>
          <TabsTrigger
            value="duration"
            className="rounded-none data-[state=active]:bg-background data-[state=active]:border-b data-[state=active]:border-primary data-[state=active]:font-medium"
          >
            <Timer className="mr-2 h-4 w-4" />
            Duration
          </TabsTrigger>
          <TabsTrigger
            value="drawdown"
            className="rounded-none data-[state=active]:bg-background data-[state=active]:border-b data-[state=active]:border-primary data-[state=active]:font-medium"
          >
            <TrendingDown className="mr-2 h-4 w-4" />
            Drawdown
          </TabsTrigger>
          <TabsTrigger
            value="consecutive"
            className="rounded-none data-[state=active]:bg-background data-[state=active]:border-b data-[state=active]:border-primary data-[state=active]:font-medium"
          >
            <Zap className="mr-2 h-4 w-4" />
            Streaks
          </TabsTrigger>
          <TabsTrigger
            value="risk"
            className="rounded-none data-[state=active]:bg-background data-[state=active]:border-b data-[state=active]:border-primary data-[state=active]:font-medium"
          >
            <ShieldAlert className="mr-2 h-4 w-4" />
            Risk Metrics
          </TabsTrigger>
        </TabsList>

        <TabsContent value="symbol-performance">
          <Card>
            <CardHeader>
              <CardTitle>Symbol Performance</CardTitle>
              <CardDescription>
                Compare performance across different currency pairs and instruments
              </CardDescription>
            </CardHeader>
            <CardContent>
              <SymbolPerformance trades={trades} />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="time-of-day">
          <Card>
            <CardHeader>
              <CardTitle>Time of Day Analysis</CardTitle>
              <CardDescription>
                Analyze how your trading performance varies throughout the day
              </CardDescription>
            </CardHeader>
            <CardContent>
              <TimeOfDayAnalysis trades={trades} />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="duration">
          <Card>
            <CardHeader>
              <CardTitle>Trade Duration Analysis</CardTitle>
              <CardDescription>
                Understand how trade duration affects your performance
              </CardDescription>
            </CardHeader>
            <CardContent>
              <TradeDurationAnalysis trades={trades} />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="drawdown">
          <Card>
            <CardHeader>
              <CardTitle>Drawdown Analysis</CardTitle>
              <CardDescription>
                Track your historical drawdowns and recovery periods
              </CardDescription>
            </CardHeader>
            <CardContent>
              <DrawdownAnalysis trades={trades} initialBalance={initialBalance} />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="consecutive">
          <Card>
            <CardHeader>
              <CardTitle>Consecutive Trades Analysis</CardTitle>
              <CardDescription>
                Analyze winning and losing streaks and their impact
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ConsecutiveTradesAnalysis trades={trades} />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="risk">
          <Card>
            <CardHeader>
              <CardTitle>Risk Management Metrics</CardTitle>
              <CardDescription>
                Evaluate your risk management with advanced metrics
              </CardDescription>
            </CardHeader>
            <CardContent>
              <RiskManagementMetrics trades={trades} />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
