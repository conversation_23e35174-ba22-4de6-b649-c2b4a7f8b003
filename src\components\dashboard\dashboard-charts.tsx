"use client"

import { useState } from "react"
import { ChartCard } from "@/components/dashboard/chart-card"
import { EnhancedTradePerformanceChart } from "@/components/charts/enhanced-trade-performance-chart"
import { EnhancedDailyPnLChart } from "@/components/charts/enhanced-daily-pnl-chart"
import { EnhancedWinLossChart } from "@/components/charts/enhanced-win-loss-chart"
import { DailyCumulativePnLChart } from "@/components/charts/daily-cumulative-pnl-chart"
import { type ProcessedData } from "@/lib/excel-processor"
import type { Trade } from "@/types/trade"

interface DashboardChartsProps {
  trades: Trade[]
  initialBalance?: number
}

export function DashboardCharts({ trades, initialBalance = 0 }: DashboardChartsProps) {
  const [leftChartType, setLeftChartType] = useState<"daily" | "cumulative">("daily")
  const [rightChartType, setRightChartType] = useState<"equity" | "winLoss">("equity")

  const renderLeftChart = () => {
    if (!trades.length) return null

    switch (leftChartType) {
      case "daily":
        return <EnhancedDailyPnLChart trades={trades} />
      case "cumulative":
        return <DailyCumulativePnLChart trades={trades} />
      default:
        return <EnhancedDailyPnLChart trades={trades} />
    }
  }

  const renderRightChart = () => {
    if (!trades.length) return null

    switch (rightChartType) {
      case "equity":
        return <EnhancedTradePerformanceChart trades={trades} initialBalance={initialBalance} />
      case "winLoss":
        return <EnhancedWinLossChart trades={trades} />
      default:
        return <EnhancedTradePerformanceChart trades={trades} initialBalance={initialBalance} />
    }
  }

  return (
    <div className="grid gap-6 grid-cols-1 md:grid-cols-2">
      <ChartCard
        title={leftChartType === "daily" ? "Daily P&L" : "Cumulative P&L"}
        viewOptions={{
          currentView: leftChartType,
          options: [
            { id: "daily", label: "Daily" },
            { id: "cumulative", label: "Cumulative" }
          ],
          onViewChange: (view) => setLeftChartType(view as "daily" | "cumulative")
        }}
      >
        {renderLeftChart()}
      </ChartCard>

      <ChartCard
        title={rightChartType === "equity" ? "Equity Curve" : "Win/Loss Distribution"}
        viewOptions={{
          currentView: rightChartType,
          options: [
            { id: "equity", label: "Equity" },
            { id: "winLoss", label: "Win/Loss" }
          ],
          onViewChange: (view) => setRightChartType(view as "equity" | "winLoss")
        }}
      >
        {renderRightChart()}
      </ChartCard>
    </div>
  )
}
