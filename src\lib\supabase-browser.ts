import { createBrowserClient } from '@supabase/ssr'
import type { Database } from '@/types/supabase'

// Get environment variables with non-null assertions
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

// Validate environment variables at runtime
if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables')
}

// Create a single client component client instance
// Use a global variable to ensure we only create one instance
let clientInstance: ReturnType<typeof createBrowserClient<Database>> | null = null

/**
 * Get a Supabase client for browser/client components
 * This function ensures we only create one instance on the client side
 */
export function getSupabaseBrowser() {
  // Client-side: reuse the same instance
  if (!clientInstance) {
    // Create the client with explicit headers to fix 406 errors
    clientInstance = createBrowserClient<Database>(supabaseUrl, supabaseAnonKey, {
      global: {
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        }
      }
    })
  }
  return clientInstance
}

/**
 * Get the authenticated user from Supabase (client-side)
 */
export async function getAuthenticatedUser() {
  const supabase = getSupabaseBrowser()
  const { data: { user }, error } = await supabase.auth.getUser()
  
  if (error || !user) {
    return { user: null, error }
  }
  
  return { user, error: null }
}
