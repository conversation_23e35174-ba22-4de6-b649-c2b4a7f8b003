/**
 * Types for the journal filtering system
 */

export interface FilterParams {
  searchTerm?: string;
  tags?: string[];
  startDate?: string;
  endDate?: string;
  activeTab?: string;
  page?: number;
  pageSize?: number;
}

export interface JournalFilterResult {
  trades: any[];
  tradingDays: string[];
  tradesWithJournalContent: any[];
  strategies: any[];
  strategyMap: Record<string, string>;
  pagination: {
    currentPage: number;
    totalPages: number;
    pageSize: number;
    totalCount: number;
    hasMore: boolean;
  };
  availableTags: string[];
  tradesForSpecificDate: any[];
  metadata: {
    totalTrades: number;
    tradesWithJournalCount: number;
    uniqueDaysCount: number;
    tagsCount: number;
  };
}

// We're now using DateRange from react-day-picker instead of this custom interface
// export interface DateRange {
//   from?: Date;
//   to?: Date;
// }

export interface JournalEntry {
  id: string;
  user_id: string;
  title: string;
  content: string;
  entry_date: string;
  created_at: string;
  updated_at: string;
  trade_id?: string;
  tags?: string[];
}

export interface JournalEntryInsert {
  user_id: string;
  title: string;
  content: string;
  entry_date: string;
  trade_id?: string;
  tags?: string[];
}

export interface DailyJournalEntry {
  id: string;
  user_id: string;
  account_id?: string;
  date: string;
  note: string;
  screenshots?: string[];
  tags?: string[];
  created_at: string;
  updated_at: string;
}

export interface Trade {
  id: string;
  user_id: string;
  account_id: string;
  position_id: number;
  symbol: string;
  type: string;
  volume: number;
  price_open: number;
  price_close: number;
  sl?: number;
  tp?: number;
  time_open: string;
  time_close: string;
  commission?: number;
  swap?: number;
  profit: number;
  created_at: string;
  updated_at?: string;
  strategy_id?: string;
  setup_id?: string;
  followed_rules?: string[];
  followed_setup_criteria?: string[];
  notes?: string;
  screenshots?: string[];
  has_journal_content?: boolean;
  tags?: string[];
  journal_content?: string;
}
