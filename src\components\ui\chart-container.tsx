"use client"

import React from 'react'
import { cn } from '@/lib/utils'
import { motion } from 'framer-motion'

interface ChartContainerProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
  animate?: boolean
  height?: number | string
}

export function ChartContainer({
  children,
  className,
  animate = true,
  height = 300,
  ...props
}: ChartContainerProps) {
  return (
    <div
      className={cn("relative w-full overflow-hidden", className)}
      style={{ height: typeof height === 'number' ? `${height}px` : height }}
      {...props}
    >
      {animate ? (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, ease: "easeOut" }}
          className="h-full w-full"
        >
          {children}
        </motion.div>
      ) : (
        children
      )}
    </div>
  )
}
