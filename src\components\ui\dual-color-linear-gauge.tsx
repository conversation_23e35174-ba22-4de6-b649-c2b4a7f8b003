"use client"

import { cn } from "@/lib/utils"

interface DualColorLinearGaugeProps {
  leftValue: number
  rightValue: number
  height?: number
  className?: string
  leftColor?: string
  rightColor?: string
  backgroundColor?: string
  showLabels?: boolean
  leftLabel?: string
  rightLabel?: string
}

export function DualColorLinearGauge({
  leftValue,
  rightValue,
  height = 6,
  className,
  leftColor = "bg-emerald-500",
  rightColor = "bg-rose-500",
  backgroundColor = "bg-muted",
  showLabels = false,
  leftLabel = "",
  rightLabel = ""
}: DualColorLinearGaugeProps) {
  // Calculate total and percentages
  const total = leftValue + rightValue
  const leftPercentage = total > 0 ? (leftValue / total) * 100 : 0
  const rightPercentage = total > 0 ? (rightValue / total) * 100 : 0
  
  return (
    <div className={cn("w-full", className)}>
      {showLabels && (
        <div className="flex justify-between text-xs text-muted-foreground mb-1">
          <span>{leftLabel}</span>
          <span>{rightLabel}</span>
        </div>
      )}
      <div className={cn("w-full rounded-full overflow-hidden", backgroundColor)} style={{ height }}>
        <div className="flex h-full">
          <div 
            className={cn("h-full transition-all duration-500", leftColor)} 
            style={{ width: `${leftPercentage}%` }}
          />
          <div 
            className={cn("h-full transition-all duration-500", rightColor)} 
            style={{ width: `${rightPercentage}%` }}
          />
        </div>
      </div>
    </div>
  )
}
