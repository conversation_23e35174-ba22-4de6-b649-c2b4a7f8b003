"use client"

import { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import { getUserTrades } from "@/lib/trade-service"
import { getStrategies } from "@/lib/playbook-service"
import { useAccount } from "@/contexts/account-context"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Skeleton } from "@/components/ui/skeleton"
import { ArrowLeft } from "lucide-react"
import { cn } from "@/lib/utils"
import { subDays, subMonths, subYears } from "date-fns"
import { EnhancedSymbolPerformanceChart } from "@/components/charts/enhanced-symbol-performance-chart"
import { SymbolTradesTable } from "@/components/symbol-trades-table"

// Define the Trade interface to match what's expected by SymbolTradesTable
interface Trade {
  id: string
  symbol: string
  type: string
  volume: number
  price_open: number
  price_close: number
  time_open: string
  time_close: string
  profit: number
  commission: number
  swap: number
  position_id?: number
  strategy_id?: string | null
  strategy_name?: string | null
}

interface SymbolDetailClientProps {
  userId: string;
  symbol: string;
  initialTrades: Trade[];
  initialAccountId: string | null;
}

export default function SymbolDetailClient({
  userId,
  symbol,
  initialTrades,
  initialAccountId
}: SymbolDetailClientProps) {
  const { selectedAccountId } = useAccount()
  const router = useRouter()
  const [trades, setTrades] = useState<Trade[]>(initialTrades)
  const [loading, setLoading] = useState(false)
  const [timeFilter, setTimeFilter] = useState("all")

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true)
        // Only fetch if the selected account is different from the initial account
        if (selectedAccountId !== initialAccountId) {
          // Fetch trades and strategies in parallel
          const [tradesData, strategiesData] = await Promise.all([
            getUserTrades(userId, selectedAccountId),
            getStrategies(userId)
          ])

          // Create a map of strategy IDs to names for quick lookup
          const strategyMap = new Map()
          strategiesData.forEach(strategy => {
            strategyMap.set(strategy.id, strategy.name)
          })

          // Add id field to each trade and include strategy name if available
          const tradesWithId = tradesData
            .filter(trade => trade.symbol === symbol)
            .map((trade, index) => ({
              ...trade,
              id: trade.id?.toString() || trade.position_id?.toString() || `trade-${Date.now()}-${index}-${Math.floor(Math.random() * 1000000)}`,
              strategy_name: trade.strategy_id ? strategyMap.get(trade.strategy_id) : null
            }))

          setTrades(tradesWithId)
        }
      } catch (error) {
        console.error("Error fetching data:", error)
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [userId, symbol, selectedAccountId, initialAccountId])

  // Filter trades based on time period
  const filteredTrades = (() => {
    if (!trades.length) return []

    const now = new Date()
    let startDate = new Date(0) // Default to epoch start (all time)

    switch (timeFilter) {
      case "week":
        startDate = subDays(now, 7)
        break
      case "month":
        startDate = subMonths(now, 1)
        break
      case "quarter":
        startDate = subMonths(now, 3)
        break
      case "year":
        startDate = subYears(now, 1)
        break
      case "all":
      default:
        // Keep startDate as epoch start (all time)
        break
    }

    return trades.filter(trade => {
      const tradeDate = new Date(trade.time_close || trade.time_open)
      return tradeDate >= startDate
    })
  })()

  // Calculate metrics
  const metrics = (() => {
    if (!filteredTrades.length) return {
      totalTrades: 0,
      winningTrades: 0,
      losingTrades: 0,
      winRate: 0,
      totalProfit: 0,
      averageProfit: 0,
      profitFactor: 0,
      volume: 0
    }

    const totalTrades = filteredTrades.length
    const winningTrades = filteredTrades.filter(t => t.profit > 0).length
    const losingTrades = filteredTrades.filter(t => t.profit < 0).length
    const winRate = totalTrades > 0 ? (winningTrades / totalTrades) * 100 : 0
    const totalProfit = filteredTrades.reduce((sum, t) => sum + t.profit, 0)
    const averageProfit = totalTrades > 0 ? totalProfit / totalTrades : 0
    const volume = filteredTrades.reduce((sum, t) => sum + t.volume, 0)

    const grossProfit = filteredTrades
      .filter(t => t.profit > 0)
      .reduce((sum, t) => sum + t.profit, 0)

    const grossLoss = Math.abs(filteredTrades
      .filter(t => t.profit < 0)
      .reduce((sum, t) => sum + t.profit, 0))

    const profitFactor = grossLoss > 0 ? grossProfit / grossLoss : grossProfit > 0 ? 999 : 0

    return {
      totalTrades,
      winningTrades,
      losingTrades,
      winRate,
      totalProfit,
      averageProfit,
      profitFactor,
      volume
    }
  })()

  if (loading) {
    return (
      <div className="container py-6">
        <div className="flex items-center mb-6">
          <Button
            variant="ghost"
            size="sm"
            className="mr-2"
            onClick={() => router.push('/symbols')}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Symbols
          </Button>
          <h1 className="text-2xl font-bold">Symbol: {symbol}</h1>
        </div>
        <div className="grid gap-4">
          <Skeleton className="h-[200px] w-full" />
          <Skeleton className="h-[400px] w-full" />
        </div>
      </div>
    )
  }

  return (
    <div className="container py-6">
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-6">
        <div className="flex items-center mb-4 sm:mb-0">
          <Button
            variant="ghost"
            size="sm"
            className="mr-2"
            onClick={() => router.push('/symbols')}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Symbols
          </Button>
          <h1 className="text-2xl font-bold">Symbol: {symbol}</h1>
        </div>

        <Select
          value={timeFilter}
          onValueChange={setTimeFilter}
        >
          <SelectTrigger className="w-full sm:w-[180px] bg-card/50 hover:bg-card/80 transition-colors">
            <SelectValue placeholder="Time Period" />
          </SelectTrigger>
          <SelectContent className="bg-card/50">
            <SelectItem value="all">All Time</SelectItem>
            <SelectItem value="week">Last Week</SelectItem>
            <SelectItem value="month">Last Month</SelectItem>
            <SelectItem value="quarter">Last Quarter</SelectItem>
            <SelectItem value="year">Last Year</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {filteredTrades.length === 0 ? (
        <Card className="dashboard-card bg-card/50">
          <CardContent className="pt-6">
            <div className="text-center py-6">
              <p className="text-muted-foreground">No trades found for {symbol} in the selected time period.</p>
            </div>
          </CardContent>
        </Card>
      ) : (
        <>
          {/* Metrics Overview */}
          <div className="grid gap-4 grid-cols-2 sm:grid-cols-4 mb-6">
            <Card className="dashboard-card bg-card/50 hover:bg-card/80 transition-colors">
              <CardHeader className="p-4 pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">Total Trades</CardTitle>
              </CardHeader>
              <CardContent className="p-4 pt-0">
                <div className="text-2xl font-bold">{metrics.totalTrades}</div>
              </CardContent>
            </Card>

            <Card className="dashboard-card bg-card/50 hover:bg-card/80 transition-colors">
              <CardHeader className="p-4 pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">Win Rate</CardTitle>
              </CardHeader>
              <CardContent className="p-4 pt-0">
                <div className="text-2xl font-bold">{metrics.winRate.toFixed(1)}%</div>
                <div className="text-xs text-muted-foreground">
                  {metrics.winningTrades} wins / {metrics.losingTrades} losses
                </div>
              </CardContent>
            </Card>

            <Card className="dashboard-card bg-card/50 hover:bg-card/80 transition-colors">
              <CardHeader className="p-4 pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">Total Profit</CardTitle>
              </CardHeader>
              <CardContent className="p-4 pt-0">
                <div className={cn(
                  "text-2xl font-bold",
                  metrics.totalProfit >= 0 ? "text-emerald-500" : "text-rose-500"
                )}>
                  ${metrics.totalProfit.toFixed(2)}
                </div>
              </CardContent>
            </Card>

            <Card className="dashboard-card bg-card/50 hover:bg-card/80 transition-colors">
              <CardHeader className="p-4 pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">Profit Factor</CardTitle>
              </CardHeader>
              <CardContent className="p-4 pt-0">
                <div className="text-2xl font-bold">{metrics.profitFactor.toFixed(2)}</div>
              </CardContent>
            </Card>
          </div>

          {/* Performance Chart */}
          <Card className="dashboard-card bg-card/50 hover:bg-card/80 transition-colors mb-6">
            <CardHeader className="p-4 pb-2">
              <CardTitle className="text-base font-medium">Performance Over Time</CardTitle>
            </CardHeader>
            <CardContent className="p-4 pt-0">
              <div className="h-[300px]">
                <EnhancedSymbolPerformanceChart trades={filteredTrades} />
              </div>
            </CardContent>
          </Card>

          {/* Trades Table */}
          <Card className="dashboard-card bg-card/50 hover:bg-card/80 transition-colors">
            <CardHeader className="p-4 pb-2">
              <CardTitle className="text-base font-medium">Trade History</CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              <SymbolTradesTable trades={filteredTrades} />
            </CardContent>
          </Card>
        </>
      )}
    </div>
  )
}
