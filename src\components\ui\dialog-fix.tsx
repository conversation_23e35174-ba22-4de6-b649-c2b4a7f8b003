"use client"

import React, { useEffect, useRef } from 'react'
import { Dialog as RadixDialog } from '@radix-ui/react-dialog'

/**
 * A wrapper component for Radix UI Dialog that fixes the issue with aria-hidden
 * This component prevents the dialog from applying aria-hidden to its ancestors
 * when it's open, which can cause focus trapping issues.
 */
export function DialogFix({
  children,
  ...props
}: React.ComponentProps<typeof RadixDialog>) {
  useEffect(() => {

    // Function to find all elements with aria-hidden="true" in the document
    const findAriaHiddenElements = () => {
      return Array.from(document.querySelectorAll('[aria-hidden="true"], [data-aria-hidden="true"]')) as HTMLElement[]
    }

    // Function to fix aria-hidden on ancestors
    const fixAriaHidden = () => {
      const ariaHiddenElements = findAriaHiddenElements()

      ariaHiddenElements.forEach(element => {
        // Store the original value to restore later
        element.dataset.originalAriaHidden = element.getAttribute('aria-hidden') || 'true'
        element.setAttribute('aria-hidden', 'false')

        if (element.hasAttribute('data-aria-hidden')) {
          element.dataset.originalDataAriaHidden = element.getAttribute('data-aria-hidden') || 'true'
          element.setAttribute('data-aria-hidden', 'false')
        }
      })
    }

    // Function to restore aria-hidden on ancestors
    const restoreAriaHidden = () => {
      const elements = document.querySelectorAll('[data-original-aria-hidden], [data-original-data-aria-hidden]')

      elements.forEach(element => {
        const htmlElement = element as HTMLElement

        if (htmlElement.dataset.originalAriaHidden) {
          htmlElement.setAttribute('aria-hidden', htmlElement.dataset.originalAriaHidden)
          delete htmlElement.dataset.originalAriaHidden
        }

        if (htmlElement.dataset.originalDataAriaHidden) {
          htmlElement.setAttribute('data-aria-hidden', htmlElement.dataset.originalDataAriaHidden)
          delete htmlElement.dataset.originalDataAriaHidden
        }
      })
    }

    // Since we can't directly observe the dialog element, we'll use a simpler approach
    // and just fix aria-hidden when the component mounts and restore when it unmounts
    fixAriaHidden()

    // Clean up when the component unmounts
    return () => {
      restoreAriaHidden()
    }
  }, [])

  return (
    <RadixDialog {...props}>
      {children}
    </RadixDialog>
  )
}
