"use client"

import { motion } from 'framer-motion'
import { Star } from 'lucide-react'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Card, CardContent } from '@/components/ui/card'

const testimonials = [
    {
        name: "<PERSON>",
        role: "Forex Trader",
        avatar: "/avatars/avatar-1.png",
        content: "TradePivot has completely transformed my trading. The analytics helped me identify patterns in my losing trades that I couldn't see before. My win rate has improved by 15% since I started using it.",
        stars: 5
    },
    {
        name: "<PERSON>",
        role: "Day Trader",
        avatar: "/avatars/avatar-2.png",
        content: "The strategy management feature is a game-changer. I can now track which strategies work best in different market conditions and optimize accordingly. Highly recommended for serious traders.",
        stars: 5
    },
    {
        name: "<PERSON>",
        role: "Swing Trader",
        avatar: "/avatars/avatar-3.png",
        content: "I love the trading journal integration. Being able to document my thoughts alongside performance metrics has helped me overcome emotional trading. The calendar view is also incredibly useful.",
        stars: 4
    },
    {
        name: "<PERSON>",
        role: "Professional Trader",
        avatar: "/avatars/avatar-4.png",
        content: "After trying several trading journals, TradePivot stands out for its comprehensive analytics and ease of use. The custom metrics feature allows me to track exactly what matters for my trading style.",
        stars: 5
    }
]

export default function TestimonialsSection() {
    return (
        <section className="py-16 md:py-32 bg-background">
            <div className="mx-auto max-w-6xl px-6">
                <motion.div 
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true, margin: "-100px" }}
                    transition={{ duration: 0.6 }}
                    className="mx-auto max-w-2xl text-center mb-12">
                    <h2 className="text-3xl font-bold md:text-4xl lg:text-5xl">Trusted by Traders Worldwide</h2>
                    <p className="mt-4 text-muted-foreground">See what traders are saying about how TradePivot has improved their trading performance and decision-making.</p>
                </motion.div>

                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
                    {testimonials.map((testimonial, index) => (
                        <motion.div
                            key={index}
                            initial={{ opacity: 0, y: 20 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            viewport={{ once: true, margin: "-100px" }}
                            transition={{ duration: 0.6, delay: index * 0.1 }}
                        >
                            <Card className="h-full">
                                <CardContent className="p-6 flex flex-col h-full">
                                    <div className="flex items-center gap-1 mb-2">
                                        {Array(testimonial.stars).fill(0).map((_, i) => (
                                            <Star key={i} className="h-4 w-4 fill-primary text-primary" />
                                        ))}
                                        {Array(5 - testimonial.stars).fill(0).map((_, i) => (
                                            <Star key={i} className="h-4 w-4 text-muted" />
                                        ))}
                                    </div>
                                    <p className="text-sm flex-grow mb-4">{testimonial.content}</p>
                                    <div className="flex items-center gap-3 mt-auto pt-4 border-t">
                                        <Avatar>
                                            <AvatarFallback>{testimonial.name.charAt(0)}</AvatarFallback>
                                            <AvatarImage src={testimonial.avatar} alt={testimonial.name} />
                                        </Avatar>
                                        <div>
                                            <p className="text-sm font-medium">{testimonial.name}</p>
                                            <p className="text-xs text-muted-foreground">{testimonial.role}</p>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        </motion.div>
                    ))}
                </div>
            </div>
        </section>
    )
}
