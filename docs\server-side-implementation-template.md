# Server-Side Implementation Template

This document provides templates for implementing server-side rendering in Next.js 15 with Supabase. Use these templates to quickly migrate client-side components to server-side components.

## Table of Contents

1. [Server Component Template](#server-component-template)
2. [Client Wrapper Template](#client-wrapper-template)
3. [Client Component Template](#client-component-template)
4. [API Route Template](#api-route-template)
5. [Service Layer Template](#service-layer-template)

## Server Component Template

```tsx
// src/app/(dashboard)/[page-name]/page.tsx

import { cookies } from 'next/headers';
import { createServerClient } from '@supabase/ssr';
import { redirect } from 'next/navigation';
import type { Database } from '@/types/supabase';
import ClientWrapper from './client-wrapper';

export default async function PageName() {
  // Get cookies for server-side Supabase client
  const cookieStore = await cookies();
  
  // Create a Supabase client with proper cookie handling
  const supabase = createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value;
        },
        set(_name: string, _value: string, _options: any) {
          // Server components can't set cookies directly
        },
        remove(_name: string, _options: any) {
          // Server components can't remove cookies directly
        }
      },
    }
  );

  // Get authenticated user
  const { data: { user }, error: userError } = await supabase.auth.getUser();
  if (userError || !user) {
    console.error('Authentication error:', userError);
    redirect('/auth');
  }
  
  const userId = user.id;

  // Fetch initial data
  const { data, error } = await supabase
    .from('your_table')
    .select('*')
    .eq('user_id', userId);

  if (error) {
    console.error('Error fetching data:', error);
    // We'll still render the page, but with empty data
  }

  // Prefetch additional data if needed
  let prefetchedData: any = {};
  
  if (data && data.length > 0) {
    try {
      // Example of prefetching related data
      const { data: relatedData } = await supabase
        .from('related_table')
        .select('*')
        .eq('parent_id', data[0].id)
        .eq('user_id', userId);
        
      prefetchedData = {
        relatedData: relatedData || []
      };
    } catch (prefetchError) {
      console.error('Error prefetching additional data:', prefetchError);
      // Continue without the prefetched data
    }
  }

  // Render the client component with the fetched data
  return <ClientWrapper 
    userId={userId} 
    initialData={data || []} 
    prefetchedData={prefetchedData}
  />;
}
```

## Client Wrapper Template

```tsx
// src/app/(dashboard)/[page-name]/client-wrapper.tsx

"use client"

import dynamic from 'next/dynamic';
import { YourDataType } from '@/types/your-types';

// Dynamically import the client component with no SSR
const ClientComponent = dynamic(() => import('./client'), {
  ssr: false
});

interface ClientWrapperProps {
  userId: string;
  initialData: YourDataType[];
  prefetchedData?: any;
}

export default function ClientWrapper({ userId, initialData, prefetchedData }: ClientWrapperProps) {
  return <ClientComponent 
    userId={userId} 
    initialData={initialData} 
    prefetchedData={prefetchedData}
  />;
}
```

## Client Component Template

```tsx
// src/app/(dashboard)/[page-name]/client.tsx

"use client"

import { useState, useEffect } from "react";
import { YourDataType } from '@/types/your-types';
import { fetchData, updateData } from '@/lib/server/your-service';

interface ClientComponentProps {
  userId: string;
  initialData: YourDataType[];
  prefetchedData?: any;
}

export default function ClientComponent({ userId, initialData, prefetchedData }: ClientComponentProps) {
  // State management
  const [data, setData] = useState<YourDataType[]>(initialData);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Use prefetched data if available
  const [relatedData, setRelatedData] = useState(prefetchedData?.relatedData || []);

  // Example of fetching additional data
  useEffect(() => {
    const fetchAdditionalData = async () => {
      // Only fetch if we don't have prefetched data
      if (!prefetchedData?.relatedData && data.length > 0) {
        setLoading(true);
        try {
          const additionalData = await fetchData(data[0].id);
          setRelatedData(additionalData);
        } catch (err) {
          setError('Failed to load additional data');
          console.error(err);
        } finally {
          setLoading(false);
        }
      }
    };

    fetchAdditionalData();
  }, [data, prefetchedData]);

  // Example of handling data updates
  const handleUpdate = async (id: string, newData: Partial<YourDataType>) => {
    setLoading(true);
    try {
      const updatedItem = await updateData(id, newData);
      setData(prevData => prevData.map(item => 
        item.id === id ? { ...item, ...updatedItem } : item
      ));
    } catch (err) {
      setError('Failed to update data');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  // Render your UI
  return (
    <div>
      {/* Your UI components here */}
      {loading && <div>Loading...</div>}
      {error && <div>Error: {error}</div>}
      
      {/* Example of rendering data */}
      <div>
        {data.map(item => (
          <div key={item.id}>
            {/* Render item details */}
          </div>
        ))}
      </div>
    </div>
  );
}
```

## API Route Template

```tsx
// src/app/api/[resource-name]/route.ts

import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { createServerClient } from '@supabase/ssr';
import type { Database } from '@/types/supabase';

// GET handler to fetch data
export async function GET(request: Request) {
  try {
    // Get URL parameters
    const url = new URL(request.url);
    const resourceId = url.searchParams.get('id');

    // Get cookies for server-side Supabase client
    const cookieStore = await cookies();
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
          set(_name: string, _value: string, _options: any) {
            // API routes can't set cookies directly
          },
          remove(_name: string, _options: any) {
            // API routes can't remove cookies directly
          }
        },
      }
    );

    // Get authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    const userId = user.id;

    // If resourceId is provided, fetch a single resource
    if (resourceId) {
      const { data, error } = await supabase
        .from('your_table')
        .select('*')
        .eq('id', resourceId)
        .eq('user_id', userId)
        .single();

      if (error) {
        return NextResponse.json({ error: error.message }, { status: 500 });
      }

      return NextResponse.json(data);
    }

    // Otherwise, fetch all resources
    const { data, error } = await supabase
      .from('your_table')
      .select('*')
      .eq('user_id', userId);

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json(data || []);
  } catch (error) {
    console.error('Error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// POST handler to create a resource
export async function POST(request: Request) {
  try {
    const body = await request.json();

    // Get cookies for server-side Supabase client
    const cookieStore = await cookies();
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
          set(_name: string, _value: string, _options: any) {
            // API routes can't set cookies directly
          },
          remove(_name: string, _options: any) {
            // API routes can't remove cookies directly
          }
        },
      }
    );

    // Get authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    const userId = user.id;

    // Create resource
    const { data, error } = await supabase
      .from('your_table')
      .insert([
        {
          user_id: userId,
          ...body
        }
      ])
      .select()
      .single();

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error('Error creating resource:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
```

## Service Layer Template

```tsx
// src/lib/server/your-service.ts

/**
 * Service for handling your data operations
 */

// Fetch data from the API
export async function fetchData(id?: string) {
  try {
    const url = id ? `/api/your-resource?id=${id}` : '/api/your-resource';
    const response = await fetch(url);
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to fetch data');
    }
    
    return await response.json();
  } catch (error) {
    console.error('Error fetching data:', error);
    throw error;
  }
}

// Create new data
export async function createData(data: any) {
  try {
    const response = await fetch('/api/your-resource', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to create data');
    }
    
    return await response.json();
  } catch (error) {
    console.error('Error creating data:', error);
    throw error;
  }
}

// Update existing data
export async function updateData(id: string, data: any) {
  try {
    const response = await fetch('/api/your-resource', {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ id, ...data }),
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to update data');
    }
    
    return await response.json();
  } catch (error) {
    console.error('Error updating data:', error);
    throw error;
  }
}

// Delete data
export async function deleteData(id: string) {
  try {
    const response = await fetch(`/api/your-resource?id=${id}`, {
      method: 'DELETE',
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to delete data');
    }
    
    return await response.json();
  } catch (error) {
    console.error('Error deleting data:', error);
    throw error;
  }
}
```

Use these templates as a starting point for migrating your client-side components to server-side components. Customize them as needed for your specific use cases.
