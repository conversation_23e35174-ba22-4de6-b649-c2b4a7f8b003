import { NextRequest, NextResponse } from "next/server"
import { getSupabaseServer } from "@/lib/supabase-server"

export async function POST(request: NextRequest) {
  try {
    console.log('=== PROCESSING AUTO-MIGRATION QUEUE ===')

    // Get authenticated user (optional for queue processing)
    const supabase = await getSupabaseServer()
    const { data: { user }, error: userError } = await supabase.auth.getUser()

    // For queue processing, we can work without user authentication
    // since we're processing system-generated queue items

    // Get unprocessed queue items
    const { data: queueItems, error: queueError } = await supabase
      .from("auto_migrate_queue")
      .select("*")
      .eq("processed", false)
      .order("created_at", { ascending: true })
      .limit(10) // Process in batches

    if (queueError) {
      console.error('Error fetching queue items:', queueError)
      return NextResponse.json({ error: queueError.message }, { status: 500 })
    }

    if (!queueItems || queueItems.length === 0) {
      console.log('No queue items to process')
      return NextResponse.json({
        success: true,
        message: "No items in queue",
        processed: 0
      })
    }

    console.log(`Found ${queueItems.length} queue items to process`)

    const edgeFunctionUrl = `${process.env.NEXT_PUBLIC_SUPABASE_URL}/functions/v1/auto-migrate-to-notebook`
    const serviceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

    if (!serviceKey) {
      console.error('SUPABASE_SERVICE_ROLE_KEY not found in environment variables')
      return NextResponse.json(
        { error: "Service key not configured" },
        { status: 500 }
      )
    }

    let processedCount = 0
    let errorCount = 0

    // Process each queue item
    for (const item of queueItems) {
      try {
        console.log(`Processing queue item ${item.id} for ${item.table_name}:${item.record_id}`)

        // Call the auto-migrate Edge Function
        const migrateResponse = await fetch(edgeFunctionUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${serviceKey}`,
          },
          body: JSON.stringify({
            type: item.operation_type,
            table: item.table_name,
            record: item.record_data,
            old_record: item.old_record_data
          })
        })

        if (migrateResponse.ok) {
          const result = await migrateResponse.json()
          console.log(`Successfully processed queue item ${item.id}:`, result)

          // Mark as processed
          await supabase
            .from("auto_migrate_queue")
            .update({
              processed: true,
              processed_at: new Date().toISOString(),
              error_message: null
            })
            .eq("id", item.id)

          processedCount++
        } else {
          const errorText = await migrateResponse.text()
          console.error(`Failed to process queue item ${item.id}:`, errorText)

          // Mark as processed with error
          await supabase
            .from("auto_migrate_queue")
            .update({
              processed: true,
              processed_at: new Date().toISOString(),
              error_message: `HTTP ${migrateResponse.status}: ${errorText}`
            })
            .eq("id", item.id)

          errorCount++
        }
      } catch (error) {
        console.error(`Exception processing queue item ${item.id}:`, error)

        // Mark as processed with error
        await supabase
          .from("auto_migrate_queue")
          .update({
            processed: true,
            processed_at: new Date().toISOString(),
            error_message: error instanceof Error ? error.message : 'Unknown error'
          })
          .eq("id", item.id)

        errorCount++
      }
    }

    console.log(`Queue processing completed: ${processedCount} successful, ${errorCount} errors`)

    return NextResponse.json({
      success: true,
      message: `Processed ${processedCount + errorCount} queue items`,
      processed: processedCount,
      errors: errorCount,
      total: queueItems.length
    })

  } catch (error) {
    console.error('Error in queue processing:', error)
    return NextResponse.json(
      { error: "Internal server error", details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}
