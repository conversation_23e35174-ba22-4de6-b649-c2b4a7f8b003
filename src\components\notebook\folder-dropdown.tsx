"use client"

import { useState, useEffect } from "react"
import { Check, ChevronDown, Folder } from "lucide-react"
import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { NotebookFolderWithMeta } from "@/types/notebook"

interface FolderDropdownProps {
  folders: NotebookFolderWithMeta[]
  selectedFolderId: string | null | undefined
  onSelect: (folderId: string | null) => void
  className?: string
}

export function FolderDropdown({
  folders,
  selectedFolderId,
  onSelect,
  className,
}: FolderDropdownProps) {
  const [open, setOpen] = useState(false)
  const [searchTerm, setSearchTerm] = useState("")
  const [filteredFolders, setFilteredFolders] = useState<NotebookFolderWithMeta[]>(folders)

  // Get the selected folder name
  const selectedFolder = folders.find(folder => folder.id === selectedFolderId)

  // Filter folders when search term changes
  useEffect(() => {
    if (!searchTerm) {
      setFilteredFolders(folders)
      return
    }

    const filtered = folders.filter(folder =>
      folder.name.toLowerCase().includes(searchTerm.toLowerCase())
    )
    setFilteredFolders(filtered)
  }, [searchTerm, folders])

  // Group folders by category
  const categorizedFolders: Record<string, NotebookFolderWithMeta[]> = {
    "": [] // Uncategorized folders
  }

  // First, identify all categories (using database column for consistency)
  folders.forEach(folder => {
    if (folder.is_category || folder.description?.includes('IsCategory: true') || folder.icon === 'category') {
      categorizedFolders[folder.name] = []
    }
  })

  // Then, assign folders to their categories
  folders.forEach(folder => {
    if (folder.is_category || folder.description?.includes('IsCategory: true') || folder.icon === 'category') {
      // Skip category folders themselves
      return
    }

    const categoryMatch = folder.description?.match(/Category: (.+)/)
    const category = categoryMatch ? categoryMatch[1] : ""

    if (categorizedFolders[category]) {
      categorizedFolders[category].push(folder)
    } else {
      categorizedFolders[""].push(folder)
    }
  })

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn("w-full justify-between", className)}
        >
          {selectedFolder ? (
            <div className="flex items-center gap-2">
              <div
                className="h-3 w-3 rounded-sm"
                style={{ backgroundColor: selectedFolder.color || '#725ac1' }}
              />
              <span>{selectedFolder.name}</span>
            </div>
          ) : (
            <span className="text-muted-foreground">Select folder</span>
          )}
          <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[300px] p-0">
        <Command>
          <CommandInput
            placeholder="Search folders..."
            value={searchTerm}
            onValueChange={setSearchTerm}
          />
          <CommandList>
            <CommandEmpty>No folder found.</CommandEmpty>
            <CommandGroup>
              <CommandItem
                onSelect={() => {
                  onSelect(null)
                  setOpen(false)
                }}
                className="flex items-center gap-2"
              >
                <Folder className="h-4 w-4 text-muted-foreground" />
                <span>No folder</span>
                {!selectedFolderId && <Check className="ml-auto h-4 w-4" />}
              </CommandItem>
            </CommandGroup>

            {/* Uncategorized folders */}
            {categorizedFolders[""].length > 0 && (
              <CommandGroup heading="Folders">
                {categorizedFolders[""].map(folder => (
                  <CommandItem
                    key={folder.id}
                    onSelect={() => {
                      onSelect(folder.id)
                      setOpen(false)
                    }}
                    className="flex items-center gap-2"
                  >
                    <div
                      className="h-3 w-3 rounded-sm"
                      style={{ backgroundColor: folder.color || '#725ac1' }}
                    />
                    <span>{folder.name}</span>
                    {selectedFolderId === folder.id && <Check className="ml-auto h-4 w-4" />}
                  </CommandItem>
                ))}
              </CommandGroup>
            )}

            {/* Categorized folders */}
            {Object.entries(categorizedFolders).map(([category, folders]) => {
              if (category === "" || folders.length === 0) return null

              return (
                <CommandGroup key={category} heading={category}>
                  {folders.map(folder => (
                    <CommandItem
                      key={folder.id}
                      onSelect={() => {
                        onSelect(folder.id)
                        setOpen(false)
                      }}
                      className="flex items-center gap-2"
                    >
                      <div
                        className="h-3 w-3 rounded-sm"
                        style={{ backgroundColor: folder.color || '#725ac1' }}
                      />
                      <span>{folder.name}</span>
                      {selectedFolderId === folder.id && <Check className="ml-auto h-4 w-4" />}
                    </CommandItem>
                  ))}
                </CommandGroup>
              )
            })}
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  )
}
