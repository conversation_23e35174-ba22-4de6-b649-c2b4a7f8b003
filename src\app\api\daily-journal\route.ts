import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { createServerClient } from '@supabase/ssr';
import type { Database } from '@/types/supabase';
import { syncDailyJournalToNotebook } from '@/lib/sync-utils';

// GET handler for fetching daily journal entries
export async function GET(request: NextRequest) {
  try {
    // Get the URL search params
    const searchParams = request.nextUrl.searchParams;
    const tags = searchParams.getAll('tags');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const accountId = searchParams.get('accountId');
    const date = searchParams.get('date'); // Single date parameter for fetching a specific entry

    // Get cookies for server-side Supabase client
    const cookieStore = await cookies();
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
          set(_name: string, _value: string, _options: any) {
            // Server components can't set cookies directly
          },
          remove(_name: string, _options: any) {
            // Server components can't remove cookies directly
          }
        },
        global: {
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json'
          }
        }
      }
    );

    // Get authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // If a specific date is provided, fetch a single journal entry
    if (date) {
      console.log(`Fetching journal entry for date: ${date}, accountId: ${accountId || 'none'}`);

      // Build the query to fetch the journal entry
      let query = supabase
        .from('daily_journal_entries')
        .select('*')
        .eq('user_id', user.id)
        .eq('date', date);

      // Add account filter if provided
      if (accountId && accountId !== 'null' && accountId !== 'undefined') {
        query = query.eq('account_id', accountId);
      } else {
        query = query.is('account_id', null);
      }

      // Execute the query
      const { data: journalEntry, error: entryError } = await query.maybeSingle();

      if (entryError) {
        console.error('Error fetching journal entry:', entryError);
        return NextResponse.json({ error: entryError.message }, { status: 500 });
      }

      // Fetch trades for this date to include in the response, with strategy information
      const tradesQuery = supabase
        .from('trades')
        .select(`
          *,
          strategies (
            id,
            name
          )
        `)
        .eq('user_id', user.id)
        .gte('time_close', `${date}T00:00:00`)
        .lt('time_close', `${date}T23:59:59.999`);

      // Add account filter if provided
      if (accountId && accountId !== 'null' && accountId !== 'undefined') {
        tradesQuery.eq('account_id', accountId);
      }

      const { data: trades, error: tradesError } = await tradesQuery;

      if (tradesError) {
        console.error('Error fetching trades:', tradesError);
        // Continue without trades data
      }

      // Calculate trading statistics
      const stats = calculateTradingStats(trades || []);

      return NextResponse.json({
        entry: journalEntry || null,
        trades: trades || [],
        stats,
      });
    }

    // Otherwise, fetch multiple journal entries based on filters
    const { data, error } = await supabase.rpc(
      'get_daily_journal_entries',
      {
        p_user_id: user.id,
        p_account_id: accountId || null,
        p_start_date: startDate || null,
        p_end_date: endDate || null,
        p_tags: tags.length > 0 ? tags : null
      }
    );

    if (error) {
      console.error('Error fetching daily journal entries:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    // If tags are provided, filter entries that have at least one matching tag
    let filteredData = data || [];
    if (tags && tags.length > 0) {
      filteredData = filteredData.filter((entry: { tags?: string[] }) => {
        if (!Array.isArray(entry.tags)) return false;
        // Since we've checked that entry.tags is an array, we can safely use it
        const entryTags = entry.tags;
        return tags.some(tag => entryTags.includes(tag));
      });
    }

    return NextResponse.json(filteredData);
  } catch (error) {
    console.error('Error in daily journal API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// POST handler for updating a daily journal entry
export async function POST(request: NextRequest) {
  try {
    // Parse the request body
    const body = await request.json();
    const { date, account_id, notes: bodyNotes, screenshots, tags } = body;

    // Rename notes to match the database column name
    const note = bodyNotes;

    if (!date) {
      return NextResponse.json({ error: 'Date is required' }, { status: 400 });
    }

    // Get cookies for server-side Supabase client
    const cookieStore = await cookies();
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
          set(_name: string, _value: string, _options: any) {
            // Server components can't set cookies directly
          },
          remove(_name: string, _options: any) {
            // Server components can't remove cookies directly
          }
        },
        global: {
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json'
          }
        }
      }
    );

    // Get authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if a daily journal entry already exists for this date and account
    const { data: existingEntry, error: fetchError } = await supabase
      .from('daily_journal_entries')
      .select('id')
      .eq('user_id', user.id)
      .eq('date', date)
      .eq('account_id', account_id || null)
      .maybeSingle();

    if (fetchError) {
      console.error('Error checking for existing daily journal entry:', fetchError);
      return NextResponse.json({ error: fetchError.message }, { status: 500 });
    }

    let result;
    if (existingEntry) {
      // Update existing entry
      const { data, error } = await supabase
        .from('daily_journal_entries')
        .update({
          note,
          screenshots,
          tags,
          updated_at: new Date().toISOString()
        })
        .eq('id', existingEntry.id)
        .select()
        .single();

      if (error) {
        console.error('Error updating daily journal entry:', error);
        return NextResponse.json({ error: error.message }, { status: 500 });
      }

      result = data;

      // Perform reverse synchronization for update case
      try {
        await syncDailyJournalToNotebook(supabase, user.id, existingEntry.id, {
          html_content: note,
          tags,
          screenshots
        });
      } catch (syncError) {
        console.error('Error during reverse sync to notebook (update):', syncError);
        // Don't fail the request if sync fails, just log it
      }
    } else {
      // Create new entry
      const { data, error } = await supabase
        .from('daily_journal_entries')
        .insert({
          user_id: user.id,
          date,
          account_id: account_id || null,
          note,
          screenshots,
          tags
        })
        .select()
        .single();

      if (error) {
        console.error('Error creating daily journal entry:', error);
        return NextResponse.json({ error: error.message }, { status: 500 });
      }

      result = data;

      // Perform reverse synchronization for create case
      try {
        await syncDailyJournalToNotebook(supabase, user.id, result.id, {
          html_content: note,
          tags,
          screenshots
        });
      } catch (syncError) {
        console.error('Error during reverse sync to notebook (create):', syncError);
        // Don't fail the request if sync fails, just log it
      }
    }

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error in daily journal API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// Helper function to calculate trading statistics
function calculateTradingStats(trades: any[]) {
  if (!trades || trades.length === 0) {
    return {
      totalTrades: 0,
      winningTrades: 0,
      losingTrades: 0,
      winRate: 0,
      totalProfit: 0,
      averageProfit: 0,
      largestWin: 0,
      largestLoss: 0,
      totalVolume: 0,
      avgWinLossRatio: 0,
      profitFactor: 0,
      grossProfit: 0,
      grossLoss: 0,
      avgWin: 0,
      avgLoss: 0,
    };
  }

  const winningTrades = trades.filter(trade => Number(trade.profit) > 0);
  const losingTrades = trades.filter(trade => Number(trade.profit) < 0);
  const totalProfit = trades.reduce((sum, trade) => sum + (Number(trade.profit) || 0), 0);
  const totalVolume = trades.reduce((sum, trade) => sum + (Number(trade.volume) || 0), 0);

  const grossProfit = winningTrades.reduce((sum, trade) => sum + (Number(trade.profit) || 0), 0);
  const grossLoss = Math.abs(losingTrades.reduce((sum, trade) => sum + (Number(trade.profit) || 0), 0));

  const avgWin = winningTrades.length > 0 ? grossProfit / winningTrades.length : 0;
  const avgLoss = losingTrades.length > 0 ? grossLoss / losingTrades.length : 0;

  const largestWin = winningTrades.length > 0
    ? Math.max(...winningTrades.map(trade => Number(trade.profit) || 0))
    : 0;
  const largestLoss = losingTrades.length > 0
    ? Math.min(...losingTrades.map(trade => Number(trade.profit) || 0))
    : 0;

  // Calculate profit factor (gross profit / gross loss)
  const profitFactor = grossLoss > 0 ? grossProfit / grossLoss : grossProfit > 0 ? Infinity : 0;

  // Calculate average win/loss ratio
  const avgWinLossRatio = avgLoss > 0 ? avgWin / avgLoss : avgWin > 0 ? Infinity : 0;

  return {
    totalTrades: trades.length,
    winningTrades: winningTrades.length,
    losingTrades: losingTrades.length,
    winRate: trades.length > 0 ? (winningTrades.length / trades.length) * 100 : 0,
    totalProfit,
    averageProfit: trades.length > 0 ? totalProfit / trades.length : 0,
    largestWin,
    largestLoss,
    totalVolume,
    avgWinLossRatio,
    profitFactor,
    grossProfit,
    grossLoss,
    avgWin,
    avgLoss,
  };
}