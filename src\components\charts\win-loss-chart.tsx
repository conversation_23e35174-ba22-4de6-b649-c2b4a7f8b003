"use client"

import { type ProcessedData } from "@/lib/excel-processor"
import { ChartTooltip } from "@/components/ui/chart-tooltip"
import {
  <PERSON><PERSON>hart,
  Pie,
  Cell,
  ResponsiveContainer,
  Legend,
  Tooltip,
} from "recharts"

interface WinLossChartProps {
  trades: ProcessedData["trades"]
}

export function WinLossChart({ trades }: WinLossChartProps) {
  const winningTrades = trades.filter((trade) => trade.profit > 0).length
  const losingTrades = trades.filter((trade) => trade.profit < 0).length
  const breakEvenTrades = trades.filter((trade) => trade.profit === 0).length

  const data = [
    { name: "Winning", value: winningTrades },
    { name: "Losing", value: losingTrades },
    { name: "Break Even", value: breakEvenTrades },
  ].filter((item) => item.value > 0)

  const COLORS = ["#10b981", "#ef4444", "#6b7280"]
  const totalTrades = trades.length
  const winRate = totalTrades > 0 ? ((winningTrades / totalTrades) * 100).toFixed(1) : "0.0"

  return (
    <ResponsiveContainer width="100%" height="100%">
      <PieChart>
        <text
          x="50%"
          y="50%"
          textAnchor="middle"
          dominantBaseline="middle"
          className="text-2xl font-semibold fill-gray-200"
        >
          {winRate}%
        </text>
        <Pie
          data={data}
          cx="50%"
          cy="50%"
          innerRadius={60}
          outerRadius={80}
          paddingAngle={2}
          dataKey="value"
        >
          {data.map((entry, index) => (
            <Cell
              key={`cell-${index}`}
              fill={COLORS[index]}
              stroke="rgba(0,0,0,0.1)"
              strokeWidth={2}
            />
          ))}
        </Pie>
        <Tooltip
          content={({ active, payload }) => (
            <ChartTooltip active={active} payload={payload}
              formatter={(value, name) => `${value} Trades`}
            />
          )}
        />
        <Legend
          verticalAlign="bottom"
          height={36}
          formatter={(value) => (
            <span className="text-gray-400">{value}</span>
          )}
        />
      </PieChart>
    </ResponsiveContainer>
  )
}