"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { toast } from "sonner"
import { getSupabaseClient } from "@/lib/supabase-singleton"
import { Loader2, Upload, Trash2, RefreshCw } from "lucide-react"
import { getInitials } from "@/lib/utils"

interface ProfilePictureUploadProps {
  userId: string;
  userName: string;
  initialAvatarUrl: string | null;
}

export function ProfilePictureUpload({ userId, userName, initialAvatarUrl }: ProfilePictureUploadProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [isUploading, setIsUploading] = useState(false)
  const [avatarUrl, setAvatarUrl] = useState<string | null>(initialAvatarUrl)
  const supabase = getSupabaseClient()

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file || !userId) return

    // Validate file type
    if (!file.type.startsWith('image/')) {
      toast.error("Please upload an image file")
      return
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      toast.error("File size should not exceed 5MB")
      return
    }

    setIsUploading(true)

    try {
      // Create a FormData object to send the file
      const formData = new FormData();
      formData.append('file', file);
      formData.append('userId', userId);

      // Upload the file using the API route
      const response = await fetch('/api/user-avatar', {
        method: 'POST',
        body: formData,
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to upload avatar');
      }

      // Update the avatar URL with the one returned from the API
      if (result.avatarUrl) {
        setAvatarUrl(result.avatarUrl);
        toast.success("Profile picture updated successfully");
      } else {
        toast.error("Failed to get public URL for the uploaded image");
      }
    } catch (error: any) {
      console.error("Error:", error);

      if (error.message?.includes('bucket not found') || error.message?.includes('storage/avatars')) {
        toast.warning("Profile picture functionality is limited until database setup is complete.");
      } else if (error.message?.includes('42P01')) {
        toast.warning("Profile functionality is limited until database setup is complete.");
      } else {
        toast.error(error.message || "An unexpected error occurred");
      }
    } finally {
      setIsUploading(false);
      // Reset the file input
      event.target.value = "";
    }
  }

  const handleRemoveAvatar = async () => {
    if (!userId || !avatarUrl) return

    setIsUploading(true)

    try {
      // Call the API route to remove the avatar
      const response = await fetch('/api/user-avatar', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to remove avatar');
      }

      setAvatarUrl(null);
      toast.success("Profile picture removed successfully");
    } catch (error: any) {
      console.error("Error:", error);

      if (error.message?.includes('bucket not found') || error.message?.includes('storage/avatars')) {
        toast.warning("Profile picture functionality is limited until database setup is complete.");
      } else if (error.message?.includes('42P01')) {
        toast.warning("Profile functionality is limited until database setup is complete.");
      } else {
        toast.error(error.message || "An unexpected error occurred");
      }
    } finally {
      setIsUploading(false);
    }
  }

  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-12">
        <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col items-center space-y-4">
        <Avatar className="h-32 w-32">
          <AvatarImage src={avatarUrl || ""} alt={userName} />
          <AvatarFallback className="text-2xl">{getInitials(userName)}</AvatarFallback>
        </Avatar>

        <div className="flex space-x-2">
          <Button
            variant="outline"
            size="sm"
            disabled={isUploading}
            onClick={() => document.getElementById('avatar-upload')?.click()}
          >
            {isUploading ? (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <Upload className="mr-2 h-4 w-4" />
            )}
            {avatarUrl ? "Change Picture" : "Upload Picture"}
          </Button>

          {avatarUrl && (
            <Button
              variant="outline"
              size="sm"
              disabled={isUploading}
              onClick={handleRemoveAvatar}
            >
              {isUploading ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <Trash2 className="mr-2 h-4 w-4" />
              )}
              Remove
            </Button>
          )}
        </div>

        <input
          id="avatar-upload"
          type="file"
          accept="image/*"
          className="hidden"
          onChange={handleFileUpload}
        />
      </div>

      <div className="space-y-2">
        <h3 className="text-sm font-medium">Guidelines</h3>
        <ul className="text-sm text-muted-foreground space-y-1">
          <li>• Square images work best</li>
          <li>• Maximum file size: 5MB</li>
          <li>• Recommended dimensions: 400x400 pixels</li>
          <li>• Supported formats: JPEG, PNG, GIF</li>
        </ul>
      </div>
    </div>
  )
}

