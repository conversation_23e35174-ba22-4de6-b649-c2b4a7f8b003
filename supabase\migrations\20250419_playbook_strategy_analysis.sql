-- Create strategies table
CREATE TABLE IF NOT EXISTS public.strategies (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  name TEXT NOT NULL,
  description TEXT,
  market_conditions TEXT[], -- e.g., ['trending', 'ranging', 'volatile']
  timeframes TEXT[], -- e.g., ['1h', '4h', 'daily']
  instruments TEXT[], -- e.g., ['EURUSD', 'GBPUSD']
  risk_reward_ratio NUMERIC,
  expected_win_rate NUMERIC,
  status TEXT DEFAULT 'active', -- 'active', 'testing', 'archived'
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  UNIQUE(user_id, name)
);

-- Create setups table
CREATE TABLE IF NOT EXISTS public.setups (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  strategy_id UUID REFERENCES public.strategies(id) ON DELETE CASCADE NOT NULL,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  name TEXT NOT NULL,
  description TEXT,
  visual_cues TEXT,
  confirmation_criteria TEXT,
  image_urls TEXT[],
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  UNIQUE(strategy_id, name)
);

-- Create strategy_rules table
CREATE TABLE IF NOT EXISTS public.strategy_rules (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  strategy_id UUID REFERENCES public.strategies(id) ON DELETE CASCADE NOT NULL,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  rule_type TEXT NOT NULL, -- 'entry', 'exit', 'stop_loss', 'take_profit', 'position_sizing', 'other'
  name TEXT NOT NULL,
  description TEXT,
  priority INTEGER DEFAULT 0, -- for ordering rules
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Create strategy_performance table
CREATE TABLE IF NOT EXISTS public.strategy_performance (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  strategy_id UUID REFERENCES public.strategies(id) ON DELETE CASCADE NOT NULL,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  period_start DATE NOT NULL,
  period_end DATE NOT NULL,
  total_trades INTEGER DEFAULT 0,
  winning_trades INTEGER DEFAULT 0,
  losing_trades INTEGER DEFAULT 0,
  win_rate NUMERIC,
  profit_loss NUMERIC DEFAULT 0,
  average_win NUMERIC DEFAULT 0,
  average_loss NUMERIC DEFAULT 0,
  largest_win NUMERIC DEFAULT 0,
  largest_loss NUMERIC DEFAULT 0,
  average_risk_reward NUMERIC DEFAULT 0,
  expectancy NUMERIC DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  UNIQUE(strategy_id, period_start, period_end)
);

-- Add strategy_id to trades table
ALTER TABLE public.trades
ADD COLUMN IF NOT EXISTS strategy_id UUID REFERENCES public.strategies(id) ON DELETE SET NULL,
ADD COLUMN IF NOT EXISTS setup_id UUID REFERENCES public.setups(id) ON DELETE SET NULL;

-- Enable Row Level Security
ALTER TABLE public.strategies ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.setups ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.strategy_rules ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.strategy_performance ENABLE ROW LEVEL SECURITY;

-- Create policies for strategies
CREATE POLICY "Users can view their own strategies"
  ON public.strategies
  FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own strategies"
  ON public.strategies
  FOR INSERT
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own strategies"
  ON public.strategies
  FOR UPDATE
  USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own strategies"
  ON public.strategies
  FOR DELETE
  USING (auth.uid() = user_id);

-- Create policies for setups
CREATE POLICY "Users can view their own setups"
  ON public.setups
  FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own setups"
  ON public.setups
  FOR INSERT
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own setups"
  ON public.setups
  FOR UPDATE
  USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own setups"
  ON public.setups
  FOR DELETE
  USING (auth.uid() = user_id);

-- Create policies for strategy_rules
CREATE POLICY "Users can view their own strategy rules"
  ON public.strategy_rules
  FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own strategy rules"
  ON public.strategy_rules
  FOR INSERT
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own strategy rules"
  ON public.strategy_rules
  FOR UPDATE
  USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own strategy rules"
  ON public.strategy_rules
  FOR DELETE
  USING (auth.uid() = user_id);

-- Create policies for strategy_performance
CREATE POLICY "Users can view their own strategy performance"
  ON public.strategy_performance
  FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own strategy performance"
  ON public.strategy_performance
  FOR INSERT
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own strategy performance"
  ON public.strategy_performance
  FOR UPDATE
  USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own strategy performance"
  ON public.strategy_performance
  FOR DELETE
  USING (auth.uid() = user_id);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS strategies_user_id_idx ON public.strategies(user_id);
CREATE INDEX IF NOT EXISTS setups_strategy_id_idx ON public.setups(strategy_id);
CREATE INDEX IF NOT EXISTS setups_user_id_idx ON public.setups(user_id);
CREATE INDEX IF NOT EXISTS strategy_rules_strategy_id_idx ON public.strategy_rules(strategy_id);
CREATE INDEX IF NOT EXISTS strategy_rules_user_id_idx ON public.strategy_rules(user_id);
CREATE INDEX IF NOT EXISTS strategy_performance_strategy_id_idx ON public.strategy_performance(strategy_id);
CREATE INDEX IF NOT EXISTS strategy_performance_user_id_idx ON public.strategy_performance(user_id);
CREATE INDEX IF NOT EXISTS trades_strategy_id_idx ON public.trades(strategy_id);
CREATE INDEX IF NOT EXISTS trades_setup_id_idx ON public.trades(setup_id);
