# Account-Specific Metrics Calculation Fix Summary

## Issue Description
The enhanced metrics widgets were displaying incorrect data due to account-specific filtering issues. Metrics calculations were showing results from the wrong account (e.g., "my main account" data when "scalp account" was selected) because the components were using the account ID stored in the metric record instead of the currently selected account ID.

## Root Cause Analysis

### Primary Issue
The `calculateMetricValue` function calls in all metric components were using `metric.account_id` (the account where the metric was originally created) instead of the currently selected account ID from the account switcher.

### Affected Components
1. **MetricsDashboard** (`src/components/metrics-goals/metrics-dashboard.tsx`)
2. **EnhancedMetricsList** (`src/components/metrics-goals/enhanced-metrics-list.tsx`)
3. **EnhancedGoalsList** (`src/components/metrics-goals/enhanced-goals-list.tsx`)

### Data Flow Problem
```javascript
// BEFORE (Incorrect)
values[metric.id] = await calculateMetricValue(metric, trades, metric.account_id || undefined)

// AFTER (Correct)
values[metric.id] = await calculateMetricValue(metric, trades, accountId || undefined)
```

## Implemented Fixes

### 1. Enhanced calculateMetricValue Function ✅
**File**: `src/lib/metrics-service.ts`

**Changes**:
- Made function async to support trading_summaries database queries
- Added integration with `trading_summaries` table for pre-calculated metrics
- Enhanced error logging for better debugging
- Added proper account-specific data filtering

**Key Features**:
```javascript
// Get pre-calculated metrics from trading_summaries if available
if (accountId) {
  const supabase = getSupabaseBrowser()
  const { data } = await supabase
    .from('trading_summaries')
    .select('*')
    .eq('account_id', accountId)
    .single()
  
  tradingSummary = data
}
```

### 2. Component Interface Updates ✅

#### MetricsDashboard Component
**Changes**:
- Added `accountId: string | null` to props interface
- Updated `calculateMetricValue` calls to use selected account ID
- Added `accountId` to useEffect dependencies for proper re-calculation

#### EnhancedMetricsList Component
**Changes**:
- Added `accountId: string | null` to props interface
- Updated metric calculation logic to use selected account ID
- Enhanced dependency array for proper reactivity

#### EnhancedGoalsList Component
**Changes**:
- Added `accountId: string | null` to props interface
- Updated metric calculation to use selected account ID
- Proper account context propagation

### 3. Parent Component Updates ✅

#### Metrics & Goals Page
**File**: `src/app/(dashboard)/metrics-goals/page.tsx`
**Changes**:
- Pass `selectedAccountId` to all child components
- Ensure proper account context propagation

#### Dashboard Client
**File**: `src/app/(dashboard)/dashboard/client.tsx`
**Changes**:
- Pass `selectedAccountId` to MetricsDashboard component
- Updated both customizable and static dashboard layouts

### 4. Trading Summaries Integration ✅

**Database Table**: `trading_summaries`
**Key Columns Used**:
- `balance_drawdown_relative` - Maximum drawdown percentage
- `balance_drawdown_absolute` - Maximum drawdown absolute value
- `profit_factor` - Profit factor calculation
- `total_net_profit` - Net profit
- `average_profit_trade` - Average winning trade
- `average_loss_trade` - Average losing trade
- `initial_balance` - Account starting balance

**Benefits**:
- More accurate calculations using pre-calculated values
- Consistent with equity curve displays
- Better performance (no need to recalculate complex metrics)

### 5. Async Function Updates ✅

**Updated Components**:
- All metric calculation useEffect hooks now use async/await pattern
- Proper error handling for database queries
- Sequential processing to maintain calculation order

**Example Pattern**:
```javascript
useEffect(() => {
  const calculateValues = async () => {
    const values: Record<string, number> = {}

    for (const metric of metrics) {
      values[metric.id] = await calculateMetricValue(metric, trades, accountId || undefined)
    }

    setMetricValues(values)
  }

  calculateValues()
}, [metrics, trades, accountId])
```

## Expected Outcomes

### ✅ Account-Specific Data Filtering
- Metrics now calculate using data from the currently selected account
- Account switcher changes immediately update all metric displays
- No cross-contamination between different trading accounts

### ✅ Dynamic Account Selection
- Real-time recalculation when switching accounts
- Proper dependency management ensures updates trigger correctly
- Consistent behavior across dashboard and dedicated metrics pages

### ✅ Accurate Metric Calculations
- **Maximum Drawdown %**: Now shows correct 3.71% (from trading_summaries) instead of 118.56%
- **Average Trade Duration**: Properly calculates from time_open/time_close timestamps
- **All Templates**: Use pre-calculated values when available, fallback to manual calculation

### ✅ Performance Optimization
- Leverages pre-calculated trading_summaries data
- Reduces computational overhead for complex metrics
- Maintains consistency with other dashboard components

## Technical Implementation Details

### Database Integration
```javascript
// Extract percentage from trading_summaries format
const extractPercentage = (str: string): number => {
  const match = str.match(/(\d+\.?\d*)%/)
  return match ? parseFloat(match[1]) : 0
}

// Example: "3.71% (3705.36)" → 3.71
context.max_drawdown_pct = extractPercentage(tradingSummary.balance_drawdown_relative)
```

### Error Handling
- Graceful fallback to manual calculation if trading_summaries unavailable
- Enhanced error logging for debugging
- Proper null/undefined handling for account IDs

### State Management
- Added accountId to all relevant useEffect dependency arrays
- Proper cleanup and re-calculation on account changes
- Consistent state updates across all metric components

## Verification Steps

### ✅ Account Switching Test
1. Select "Scalp Account" in account switcher
2. Verify metrics show scalp account data (e.g., 3.71% drawdown)
3. Switch to "My Main Account"
4. Verify metrics immediately update to main account data
5. Confirm no delays or incorrect data display

### ✅ Metric Accuracy Test
1. Compare Maximum Drawdown % with equity curve visual
2. Verify Average Trade Duration matches actual trade timestamps
3. Confirm all template metrics use appropriate data sources
4. Check consistency between dashboard and metrics page

### ✅ Performance Test
1. Verify immediate updates when switching accounts
2. Confirm no loading delays for metric calculations
3. Check that pre-calculated values are used when available
4. Validate fallback behavior for missing trading_summaries

## Conclusion

The account-specific metrics calculation fix addresses the core issue of incorrect data filtering by ensuring that:

1. **Correct Account Context**: All metric calculations use the currently selected account ID
2. **Accurate Data Sources**: Integration with trading_summaries provides reliable pre-calculated values
3. **Real-time Updates**: Account switching triggers immediate recalculation and display updates
4. **Performance Optimization**: Leverages database pre-calculations while maintaining fallback capabilities

The fix ensures that users see accurate, account-specific metrics that match their selected trading account, providing reliable data for performance analysis and goal tracking.
