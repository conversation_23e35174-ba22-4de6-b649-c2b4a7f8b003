import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * Format a currency value with dollar sign
 * @param value Number to format as currency
 * @returns Formatted currency string
 */
export function formatCurrency(value: number) {
  if (value === 0) return "$0.00"

  return value > 0
    ? `$${value.toFixed(2)}`
    : `$-${Math.abs(value).toFixed(2)}`
}

/**
 * Get initials from a name
 * @param name The name to get initials from
 * @returns The initials (up to 2 characters)
 */
export function getInitials(name: string): string {
  if (!name) return ''

  // Split the name by spaces and get the first letter of each part
  const parts = name.split(' ').filter(Boolean)

  if (parts.length === 0) return ''

  if (parts.length === 1) {
    // If only one part, return the first letter
    return parts[0].charAt(0).toUpperCase()
  }

  // Return the first letter of the first part and the first letter of the last part
  return (parts[0].charAt(0) + parts[parts.length - 1].charAt(0)).toUpperCase()
}
