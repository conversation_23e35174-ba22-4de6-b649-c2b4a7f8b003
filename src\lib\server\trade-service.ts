import { Trade } from '@/types/trade';

// Get trades by strategy
export async function getTradesByStrategy(
  strategyId: string,
  accountId?: string | null
): Promise<Trade[]> {
  try {
    let url = `/api/trades?strategyId=${strategyId}`;
    
    if (accountId) {
      url += `&accountId=${accountId}`;
    }
    
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const error = await response.json();
      console.error('Error fetching trades by strategy:', error);
      return [];
    }

    return await response.json();
  } catch (error) {
    console.error('Error in getTradesByStrategy:', error);
    return [];
  }
}

// Get trade by ID
export async function getTradeById(tradeId: string): Promise<Trade | null> {
  try {
    const response = await fetch(`/api/trades?id=${tradeId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const error = await response.json();
      console.error('Error fetching trade:', error);
      return null;
    }

    return await response.json();
  } catch (error) {
    console.error('Error in getTradeById:', error);
    return null;
  }
}

// Update trade strategy
export async function updateTradeStrategy(
  tradeId: string,
  strategyId: string | null
): Promise<Trade | null> {
  try {
    const response = await fetch('/api/trades', {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        id: tradeId,
        strategy_id: strategyId,
      }),
    });

    if (!response.ok) {
      const error = await response.json();
      console.error('Error updating trade strategy:', error);
      return null;
    }

    return await response.json();
  } catch (error) {
    console.error('Error in updateTradeStrategy:', error);
    return null;
  }
}
