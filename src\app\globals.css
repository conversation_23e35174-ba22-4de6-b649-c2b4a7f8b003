@tailwind base;
@tailwind components;
@tailwind utilities;

@theme {
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@layer base {
:root {
    --background: 210 40% 98%;
    --foreground: 222 47% 11%;
    --card: 0 0% 100%;
    --card-foreground: 222 50% 10%;
    --popover: 0 0% 100%;
    --popover-foreground: 222 47% 11%;
    --primary: 255 53% 55%;
    --primary-rgb: 114, 90, 193;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222 47% 11%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215 16% 47%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222 47% 11%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 94%;
    --input: 214.3 31.8% 91.4%;
    --ring: 255 53% 55%;
    --radius: 0.5rem;

    --chart-1: oklch(0.646 0.222 41.116);
    --chart-2: oklch(0.6 0.118 184.704);
    --chart-3: oklch(0.398 0.07 227.392);
    --chart-4: oklch(0.828 0.189 84.429);
    --chart-5: oklch(0.769 0.188 70.08);

    --sidebar: 0 0% 100%;
    --sidebar-foreground: 222 47% 11%;
    --sidebar-primary: 255 53% 55%;
    --sidebar-primary-foreground: 210 40% 98%;
    --sidebar-accent: 210 40% 96.1%;
    --sidebar-accent-foreground: 222 47% 11%;
    --sidebar-border: 214.3 31.8% 91.4%;
    --sidebar-ring: 255 53% 55%;
  }

  .dark {
    --background: 240 10% 4%;
    --foreground: 0 0% 98%;
    --card: 240 10% 6%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 4%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-rgb: 250, 250, 250;
    --primary-foreground: 240 5.9% 10%;
    --secondary: 240 4% 12%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 4% 12%;
    --muted-foreground: 240 5% 65%;
    --accent: 240 4% 12%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 4% 16%;
    --input: 240 4% 16%;
    --ring: 240 4.8% 83.9%;

    --chart-1: oklch(0.488 0.243 264.376);
    --chart-2: oklch(0.696 0.17 162.48);
    --chart-3: oklch(0.769 0.188 70.08);
    --chart-4: oklch(0.627 0.265 303.9);
    --chart-5: oklch(0.645 0.246 16.439);

    --sidebar: oklch(0.205 0 0);
    --sidebar-foreground: oklch(0.985 0 0);
    --sidebar-primary: oklch(0.488 0.243 264.376);
    --sidebar-primary-foreground: oklch(0.985 0 0);
    --sidebar-accent: oklch(0.269 0 0);
    --sidebar-accent-foreground: oklch(0.985 0 0);
    --sidebar-border: oklch(0.269 0 0);
    --sidebar-ring: oklch(0.439 0 0);
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
body {
    @apply bg-background text-foreground;
  }
}

@layer utilities {
  /* Fix for aria-hidden and inert attributes */
  [aria-hidden="true"] :focus,
  [data-aria-hidden="true"] :focus {
    outline: none !important;
  }

  /* Properly style inert elements */
  [inert] {
    pointer-events: none;
    cursor: default;
    user-select: none;
    -webkit-user-select: none;
    opacity: 0.5;
  }

  /* Ensure elements with aria-hidden="false" are properly interactive */
  [aria-hidden="false"] {
    pointer-events: auto !important;
    user-select: auto !important;
    -webkit-user-select: auto !important;
    opacity: 1 !important;
  }

  .dark .shadow-glow {
    box-shadow: 0 0 8px 2px rgba(255, 255, 255, 0.15);
  }

  @keyframes progress-indeterminate {
    0% { transform: translateX(-100%); }
    50% { transform: translateX(0%); }
    100% { transform: translateX(100%); }
  }

  .animate-progress-indeterminate {
    animation: progress-indeterminate 1.5s infinite ease-in-out;
  }

  /* Custom animation for loading skeletons */
  @keyframes slow-pulse {
    0%, 100% { opacity: 0.7; }
    50% { opacity: 0.4; }
  }

  .slow-pulse {
    animation: slow-pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  .strategy-card-hover {
    transition: all 0.3s ease;
  }

  .strategy-card-hover:hover {
    transform: translateY(-4px);
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1);
  }

  .dark .strategy-card-hover:hover {
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.3), 0 8px 10px -6px rgba(0, 0, 0, 0.2);
  }

  /* Enhanced card styling for light mode */
  .light-card-enhanced {
    background-color: hsl(var(--card));
    border: 1px solid hsl(var(--border));
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    transition: all 0.3s ease;
  }

  .light-card-enhanced:hover {
    border-color: hsl(var(--border) / 0.8);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    transform: translateY(-1px);
  }

  /* Dashboard card borders for light mode */
  :root .dashboard-card {
    border: 1px solid hsl(var(--border) / 0.6);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  }

  :root .dashboard-card:hover {
    border-color: hsl(var(--border) / 0.8);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  }

  /* Remove borders in dark mode for cleaner look */
  .dark .dashboard-card {
    border: 1px solid transparent;
    box-shadow: none;
  }

  .dark .dashboard-card:hover {
    border-color: hsl(var(--border) / 0.3);
    box-shadow: none;
  }
}
