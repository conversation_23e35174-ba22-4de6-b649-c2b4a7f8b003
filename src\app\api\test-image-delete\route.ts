import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

export async function POST(request: Request) {
  try {
    const { imageUrl } = await request.json();
    
    if (!imageUrl) {
      return NextResponse.json({ error: 'No image URL provided' }, { status: 400 });
    }
    
    // Create a Supabase client with admin privileges
    const supabaseAdmin = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL || '',
      process.env.SUPABASE_SERVICE_ROLE_KEY || ''
    );
    
    // Extract the file path from the URL
    const filePath = imageUrl.replace(
      `${process.env.NEXT_PUBLIC_SUPABASE_URL}/storage/v1/object/public/strategyimages/`,
      ''
    );
    
    if (!filePath || filePath === imageUrl) {
      return NextResponse.json({ error: 'Invalid image URL format' }, { status: 400 });
    }
    
    // Delete the file using admin privileges
    const { error } = await supabaseAdmin.storage
      .from('strategyimages')
      .remove([filePath]);
    
    if (error) {
      console.error('Error deleting image from storage:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }
    
    return NextResponse.json({ success: true, message: 'Image deleted successfully' });
  } catch (error) {
    console.error('Error in test-image-delete route:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
