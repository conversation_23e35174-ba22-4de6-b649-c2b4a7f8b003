"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Footer, DialogDescription } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { format } from "date-fns"
import { type ProcessedData } from "@/lib/excel-processor"
import { cn } from "@/lib/utils"
import { AlertCircle, CheckCircle2, XCircle } from "lucide-react"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"

interface TradePreviewDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  data: ProcessedData | null
  onConfirm: () => void
  onCancel: () => void
  isLoading?: boolean
}

export function TradePreviewDialog({
  open,
  onOpenChange,
  data,
  onConfirm,
  onCancel,
  isLoading = false,
}: TradePreviewDialogProps) {
  const [activeTab, setActiveTab] = useState("summary")

  if (!data) return null

  const { account, trades, summary } = data

  // Format dates for display
  const formatDate = (dateStr: string) => {
    try {
      // Handle MT5 date format (YYYY.MM.DD HH:MM:SS)
      if (dateStr.includes('.')) {
        const [datePart, timePart] = dateStr.split(' ')
        const [year, month, day] = datePart.split('.')
        return `${year}-${month}-${day} ${timePart || '00:00:00'}`
      }

      // Try to parse as ISO date
      return format(new Date(dateStr), 'yyyy-MM-dd HH:mm:ss')
    } catch (error) {
      return dateStr
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Preview Trading Data</DialogTitle>
          <DialogDescription>
            Review your trading data before confirming the import.
          </DialogDescription>
        </DialogHeader>

        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Please review your data before importing</AlertTitle>
          <AlertDescription>
            Verify that your trading data has been correctly parsed before confirming the import.
          </AlertDescription>
        </Alert>

        <div className="mt-4">
          <Card className="mb-4">
            <CardHeader className="py-2">
              <CardTitle className="text-base">Account Information</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4 sm:grid-cols-4">
                <div>
                  <div className="text-sm font-medium text-muted-foreground">Name</div>
                  <div className="font-medium">{account.name}</div>
                </div>
                <div>
                  <div className="text-sm font-medium text-muted-foreground">Account</div>
                  <div className="font-medium">{account.account}</div>
                </div>
                <div>
                  <div className="text-sm font-medium text-muted-foreground">Broker</div>
                  <div className="font-medium">{account.company}</div>
                </div>
                <div>
                  <div className="text-sm font-medium text-muted-foreground">Date</div>
                  <div className="font-medium">{formatDate(account.date)}</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="summary">Summary</TabsTrigger>
              <TabsTrigger value="trades">Trades ({trades.length})</TabsTrigger>
            </TabsList>

            <TabsContent value="summary" className="mt-4">
              <Card>
                <CardHeader className="py-2">
                  <CardTitle className="text-base">Trading Summary</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4 sm:grid-cols-3">
                    <div>
                      <div className="text-sm font-medium text-muted-foreground">Total Trades</div>
                      <div className="font-medium">{summary.total_trades}</div>
                    </div>
                    <div>
                      <div className="text-sm font-medium text-muted-foreground">Win Rate</div>
                      <div className="font-medium">{summary.profit_trades}</div>
                    </div>
                    <div>
                      <div className="text-sm font-medium text-muted-foreground">Loss Rate</div>
                      <div className="font-medium">{summary.loss_trades}</div>
                    </div>
                    <div>
                      <div className="text-sm font-medium text-muted-foreground">Net Profit</div>
                      <div className={cn(
                        "font-medium",
                        summary.total_net_profit >= 0 ? "text-emerald-500" : "text-rose-500"
                      )}>
                        ${summary.total_net_profit.toFixed(2)}
                      </div>
                    </div>
                    <div>
                      <div className="text-sm font-medium text-muted-foreground">Max Drawdown</div>
                      <div className="font-medium">{summary.balance_drawdown_maximal}</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="trades" className="mt-4">
              <Card>
                <CardContent className="p-0">
                  <div className="rounded-md border">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Position</TableHead>
                          <TableHead>Symbol</TableHead>
                          <TableHead>Type</TableHead>
                          <TableHead>Open Time</TableHead>
                          <TableHead>Close Time</TableHead>
                          <TableHead>Volume</TableHead>
                          <TableHead className="text-right">Profit</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {trades.slice(0, 10).map((trade) => (
                          <TableRow key={trade.position_id}>
                            <TableCell className="font-medium">{trade.position_id}</TableCell>
                            <TableCell>{trade.symbol}</TableCell>
                            <TableCell className={cn(
                              "capitalize",
                              trade.type === "buy" ? "text-emerald-500" : "text-rose-500"
                            )}>
                              {trade.type}
                            </TableCell>
                            <TableCell>{formatDate(trade.time_open)}</TableCell>
                            <TableCell>{formatDate(trade.time_close)}</TableCell>
                            <TableCell>{trade.volume.toFixed(2)}</TableCell>
                            <TableCell className={cn(
                              "text-right font-medium",
                              trade.profit >= 0 ? "text-emerald-500" : "text-rose-500"
                            )}>
                              ${trade.profit.toFixed(2)}
                            </TableCell>
                          </TableRow>
                        ))}
                        {trades.length > 10 && (
                          <TableRow>
                            <TableCell colSpan={7} className="text-center text-muted-foreground">
                              Showing 10 of {trades.length} trades
                            </TableCell>
                          </TableRow>
                        )}
                      </TableBody>
                    </Table>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        <DialogFooter className="mt-4">
          <Button variant="outline" onClick={onCancel} disabled={isLoading}>
            Cancel
          </Button>
          <Button onClick={onConfirm} disabled={isLoading}>
            {isLoading ? "Importing..." : "Confirm Import"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
