// Import the necessary types and functions
import { createBrowserClient } from '@supabase/ssr'
import type { Database } from '@/types/supabase'

// Get environment variables with non-null assertions
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

// Validate environment variables at runtime
if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables')
}

// Create a single client component client instance
// Use a global variable to ensure we only create one instance
let clientInstance: ReturnType<typeof createBrowserClient<Database>> | null = null

// This function ensures we only create one Supabase client instance
export function getSupabaseBrowser() {
  if (typeof window === 'undefined') {
    // Server-side: create a new instance each time with custom headers
    return createBrowserClient<Database>(supabaseUrl, supabaseAnonKey, {
      global: {
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        }
      }
    })
  }

  // Client-side: reuse the same instance
  if (!clientInstance) {
    // Create the client with explicit headers to fix 406 errors
    clientInstance = createBrowserClient<Database>(supabaseUrl, supabaseAnonKey, {
      global: {
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        }
      }
    })

    // Log that we've created the client with custom headers
    console.log('[Supabase] Created client with custom headers');
  }
  return clientInstance
}