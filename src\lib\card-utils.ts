/**
 * Utility functions for card styling
 */

/**
 * Generate a gradient background based on a name
 * @param name The name to generate a gradient from
 * @returns An object with gradient styles for light and dark mode
 */
export function getCardGradient(name: string) {
  // Create a simple hash from the name
  const hash = name.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);

  // Use the hash to generate colors
  const hue1 = hash % 360;
  const hue2 = (hash * 1.5) % 360;

  return {
    // Light mode gradients - more subtle and elegant
    light: {
      background: `linear-gradient(135deg, hsla(${hue1}, 70%, 80%, 0.05), hsla(${hue2}, 70%, 80%, 0.1))`,
      border: `linear-gradient(135deg, hsla(${hue1}, 70%, 75%, 0.2), hsla(${hue2}, 70%, 75%, 0.3))`,
      accent: `hsla(${hue1}, 80%, 50%, 1)`,
      cardBg: 'bg-background/60',
      footerBg: 'bg-background/30'
    },
    // Dark mode gradients - more saturated and glowing
    dark: {
      background: `linear-gradient(135deg, hsla(${hue1}, 90%, 20%, 0.4), hsla(${hue2}, 90%, 15%, 0.5))`,
      border: `linear-gradient(135deg, hsla(${hue1}, 90%, 40%, 0.5), hsla(${hue2}, 90%, 35%, 0.6))`,
      accent: `hsla(${hue1}, 90%, 60%, 1)`,
      cardBg: 'bg-background/20',
      footerBg: 'bg-background/10'
    }
  };
}
