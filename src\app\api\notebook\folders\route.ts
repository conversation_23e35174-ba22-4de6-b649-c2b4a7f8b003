import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { createServerClient } from '@supabase/ssr';
import type { Database } from '@/types/supabase';
import { NotebookFolder } from '@/types/notebook';

// GET handler for fetching notebook folders
export async function GET(request: NextRequest) {
  try {
    // Get cookies for server-side Supabase client
    const cookieStore = await cookies();
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
          set(_name: string, _value: string, _options: any) {
            // API routes can't set cookies directly
          },
          remove(_name: string, _options: any) {
            // API routes can't remove cookies directly
          }
        },
      }
    );

    // Get authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get account filter from query parameters
    const url = new URL(request.url);
    const accountIdParam = url.searchParams.get('accountId');

    // Convert empty string back to null, handle undefined
    const accountId = accountIdParam === '' ? null : accountIdParam;

    // Build folder query - include system folders and account-specific user folders
    let folderQuery = supabase
      .from('notebook_folders')
      .select('*');

    if (accountIdParam !== null) { // If accountId parameter was provided (even if empty)
      if (accountId === null) {
        // Show system folders and user folders with no account_id
        folderQuery = folderQuery.or(`is_system.eq.true,and(user_id.eq.${user.id},account_id.is.null)`);
      } else {
        // Show system folders and user folders for specific account
        folderQuery = folderQuery.or(`is_system.eq.true,and(user_id.eq.${user.id},account_id.eq.${accountId})`);
      }
    } else {
      // No account filter - show system folders and all user folders
      folderQuery = folderQuery.or(`is_system.eq.true,user_id.eq.${user.id}`);
    }

    const { data: folders, error: foldersError } = await folderQuery.order('name');

    if (foldersError) {
      console.error('Error fetching notebook folders:', foldersError);
      return NextResponse.json({ error: foldersError.message }, { status: 500 });
    }

    // Count entries in each folder
    const folderCounts: Record<string, number> = {};

    // Get counts for each folder
    for (const folder of folders || []) {
      // Use a more accurate count query that matches the filtering logic used in the entries API
      let countQuery = supabase
        .from('notebook_entries')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', user.id)
        .eq('folder_id', folder.id);

      // Apply account filtering if specified
      if (accountId !== undefined) {
        if (accountId === 'null') {
          countQuery = countQuery.is('account_id', null);
        } else {
          countQuery = countQuery.eq('account_id', accountId);
        }
      }

      const { count, error: countError } = await countQuery;

      if (!countError) {
        folderCounts[folder.id] = count || 0;
      } else {
        console.error(`Error counting entries for folder ${folder.name}:`, countError);
      }
    }

    // Double-check the counts with a direct query for system folders (with account filtering)
    const systemFolders = (folders || []).filter(folder => folder.is_system);
    for (const folder of systemFolders) {
      // For system folders, do an additional check to ensure accuracy
      let entriesQuery = supabase
        .from('notebook_entries')
        .select('id')
        .eq('user_id', user.id)
        .eq('folder_id', folder.id);

      // Apply the same account filtering as the count query
      if (accountId !== undefined) {
        if (accountId === 'null') {
          entriesQuery = entriesQuery.is('account_id', null);
        } else {
          entriesQuery = entriesQuery.eq('account_id', accountId);
        }
      }

      const { data: entries, error: entriesError } = await entriesQuery;

      if (!entriesError) {
        const actualCount = entries?.length || 0;
        if (actualCount !== folderCounts[folder.id]) {
          console.log(`Count mismatch for folder "${folder.name}": API reports ${folderCounts[folder.id]}, actual count is ${actualCount} (account: ${accountId || 'all'})`);
          folderCounts[folder.id] = actualCount;
        }
      }
    }

    // Add counts to folders
    const foldersWithCounts = (folders || []).map(folder => ({
      ...folder,
      count: folderCounts[folder.id] || 0
    }));

    return NextResponse.json(foldersWithCounts);
  } catch (error) {
    console.error('Error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// POST handler for creating a new notebook folder
export async function POST(request: NextRequest) {
  try {
    // Parse the request body
    const body = await request.json();
    const { name, description, color, icon, parent_id, is_category, account_id } = body;

    if (!name) {
      return NextResponse.json({ error: 'Folder name is required' }, { status: 400 });
    }

    // Get cookies for server-side Supabase client
    const cookieStore = await cookies();
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
          set(_name: string, _value: string, _options: any) {
            // API routes can't set cookies directly
          },
          remove(_name: string, _options: any) {
            // API routes can't remove cookies directly
          }
        },
      }
    );

    // Get authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if folder with same name already exists for this user and account
    let existingFolderQuery = supabase
      .from('notebook_folders')
      .select('id')
      .eq('user_id', user.id)
      .eq('name', name);

    // Add account filter to check for duplicates within the same account
    if (account_id === null) {
      existingFolderQuery = existingFolderQuery.is('account_id', null);
    } else if (account_id !== undefined) {
      existingFolderQuery = existingFolderQuery.eq('account_id', account_id);
    }

    const { data: existingFolder, error: existingFolderError } = await existingFolderQuery.maybeSingle();

    if (existingFolder) {
      return NextResponse.json({ error: 'A folder with this name already exists in this account' }, { status: 400 });
    }

    // Prepare folder data
    let folderDescription = description || '';

    // If this is a category folder, add the IsCategory flag to the description
    if (is_category) {
      folderDescription = folderDescription ? `${folderDescription}, IsCategory: true` : 'IsCategory: true';
      console.log(`Creating category folder: ${name} with description: ${folderDescription}`);
    }

    // Create the folder
    const { data, error } = await supabase
      .from('notebook_folders')
      .insert({
        user_id: user.id,
        account_id: account_id || null, // Associate with current account
        name,
        description: folderDescription,
        color,
        icon,
        parent_id,
        is_system: false,
        is_category: is_category || false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating notebook folder:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error('Error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
