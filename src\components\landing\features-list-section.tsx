"use client"

import { motion } from 'framer-motion'
import { Activity, BookMarked, Calendar, FileSpreadsheet } from 'lucide-react'
import Image from 'next/image'

export default function FeaturesListSection() {
    return (
        <section className="py-16 md:py-32 bg-muted/30">
            <div className="mx-auto max-w-6xl px-6">
                <div className="grid items-center gap-12 md:grid-cols-2 md:gap-12 lg:grid-cols-5 lg:gap-24">
                    <motion.div 
                        initial={{ opacity: 0, x: -20 }}
                        whileInView={{ opacity: 1, x: 0 }}
                        viewport={{ once: true, margin: "-100px" }}
                        transition={{ duration: 0.6 }}
                        className="lg:col-span-2">
                        <div className="md:pr-6 lg:pr-0">
                            <h2 className="text-3xl font-bold md:text-4xl lg:text-5xl">Built for Serious Traders</h2>
                            <p className="mt-6 text-muted-foreground">TradePivot is designed to help traders of all levels improve their performance through data analysis, journaling, and strategy development.</p>
                        </div>
                        <ul className="mt-8 divide-y border-y *:flex *:items-center *:gap-3 *:py-3">
                            <li>
                                <FileSpreadsheet className="size-5 text-primary" />
                                Easy trade import from MT4/MT5
                            </li>
                            <li>
                                <Activity className="size-5 text-primary" />
                                Comprehensive performance metrics
                            </li>
                            <li>
                                <Calendar className="size-5 text-primary" />
                                Interactive trading calendar
                            </li>
                            <li>
                                <BookMarked className="size-5 text-primary" />
                                Strategy playbook development
                            </li>
                        </ul>
                    </motion.div>
                    <motion.div 
                        initial={{ opacity: 0, x: 20 }}
                        whileInView={{ opacity: 1, x: 0 }}
                        viewport={{ once: true, margin: "-100px" }}
                        transition={{ duration: 0.6, delay: 0.2 }}
                        className="relative rounded-3xl border border-border/50 p-3 lg:col-span-3">
                        <div className="relative aspect-video rounded-2xl bg-gradient-to-b from-muted/50 to-transparent p-px">
                            <div className="rounded-2xl bg-muted flex items-center justify-center h-full">
                                <p className="text-sm text-muted-foreground">Image placeholder for dashboard overview</p>
                            </div>
                        </div>
                    </motion.div>
                </div>
            </div>
        </section>
    )
}
