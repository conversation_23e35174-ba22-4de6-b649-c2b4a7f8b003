import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { createServerClient } from '@supabase/ssr';
import type { Database } from '@/types/supabase';
import { syncTradeToNotebook } from '@/lib/sync-utils';

// POST handler for updating trade journal entries (notes, tags, screenshots)
export async function POST(request: NextRequest) {
  try {
    // Parse the request body
    const body = await request.json();
    const { id, notes, tags, screenshots, has_journal_content } = body;

    console.log('Received trade journal update request:', { id, has_journal_content });

    if (!id) {
      return NextResponse.json({ error: 'Trade ID is required' }, { status: 400 });
    }

    // Get cookies for server-side Supabase client
    const cookieStore = await cookies();
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
          set(_name: string, _value: string, _options: any) {
            // Server components can't set cookies directly
          },
          remove(_name: string, _options: any) {
            // Server components can't remove cookies directly
          }
        },
      }
    );

    // Get authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Log the trade ID and user ID for debugging
    console.log('Looking for trade with ID:', id, 'Type:', typeof id);
    console.log('User ID:', user.id);

    // Verify the trade belongs to the user
    // First, try to find the trade without using .single() to avoid the error
    const { data: existingTrades, error: fetchError } = await supabase
      .from('trades')
      .select('id')
      .eq('id', id)
      .eq('user_id', user.id);

    if (fetchError) {
      console.error('Error finding trade:', fetchError);
      return NextResponse.json({ error: `Trade not found: ${fetchError.message}` }, { status: 404 });
    }

    if (!existingTrades || existingTrades.length === 0) {
      console.error('No trade found with ID:', id);
      return NextResponse.json({ error: 'Trade not found' }, { status: 404 });
    }

    // Use the first matching trade
    const existingTrade = existingTrades[0];
    console.log('Found trade:', existingTrade);

    // Update the trade with journal content
    const updateData: any = {};

    // Only include fields that are provided
    if (notes !== undefined) {
      updateData.notes = notes;
    }

    if (tags !== undefined) {
      updateData.tags = tags;
    }

    if (screenshots !== undefined) {
      updateData.screenshots = screenshots;
    }

    if (has_journal_content !== undefined) {
      updateData.has_journal_content = has_journal_content;
    }

    // Add updated_at timestamp
    updateData.updated_at = new Date().toISOString();

    console.log('Updating trade with data:', updateData);

    // Update the trade
    const { data, error } = await supabase
      .from('trades')
      .update(updateData)
      .eq('id', id)
      .eq('user_id', user.id)
      .select();

    if (error) {
      console.error('Error updating trade journal:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    // Check if we got data back
    if (!data || data.length === 0) {
      return NextResponse.json({ error: 'No trade was updated' }, { status: 404 });
    }

    // Return the first item in the array
    const updatedTrade = data[0];

    console.log('Trade journal updated successfully:', {
      id: updatedTrade.id,
      has_journal_content: updatedTrade.has_journal_content
    });

    // Trigger auto-migration queue processing first (for new notebook entries)
    try {
      const queueResponse = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL?.replace('/rest/v1', '')}/api/migrate/process-queue`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });

      if (queueResponse.ok) {
        console.log('Auto-migration queue processed successfully after trade update');
      } else {
        console.warn('Auto-migration queue processing failed after trade update');
      }
    } catch (error) {
      console.warn('Auto-migration queue processing error:', error);
    }

    // Perform reverse synchronization to linked notebook entries
    console.log('Starting reverse sync for trade:', { id, userId: user.id, notes, tags, screenshots });
    try {
      await syncTradeToNotebook(supabase, user.id, id, {
        html_content: notes,
        tags,
        screenshots
      });
      console.log('Reverse sync completed successfully for trade:', id);
    } catch (syncError) {
      console.error('Error during reverse sync to notebook:', syncError);
      // Don't fail the request if sync fails, just log it
    }

    return NextResponse.json(updatedTrade);
  } catch (error) {
    console.error('Error in trade journal API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
