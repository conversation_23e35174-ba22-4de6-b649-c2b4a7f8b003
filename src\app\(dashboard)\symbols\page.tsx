import { cookies } from 'next/headers';
import { createServerClient } from '@supabase/ssr';
import { redirect } from 'next/navigation';
import type { Database } from '@/types/supabase';
import SymbolsClient from './client';

export default async function SymbolsPage() {
  // Get cookies for server-side Supabase client
  const cookieStore = await cookies();
  const supabase = createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value;
        },
        set(_name: string, _value: string, _options: any) {
          // Server components can't set cookies directly
        },
        remove(_name: string, _options: any) {
          // Server components can't remove cookies directly
        }
      },
    }
  );

  // Get authenticated user
  const { data: { user }, error: userError } = await supabase.auth.getUser();
  if (userError || !user) {
    redirect('/login');
  }

  const userId = user.id;

  // Fetch user accounts
  const { data: accounts, error: accountsError } = await supabase
    .from("trading_accounts")
    .select("*")
    .eq("user_id", userId)
    .order("updated_at", { ascending: false });

  if (accountsError) {
    console.error('Error fetching trading accounts:', accountsError);
    // Continue with empty accounts array
  }

  // Get the first account ID (if any)
  // Note: The actual selected account will be determined by the client component
  // using the account context, but we provide initial data for the first account
  const initialAccountId = accounts && accounts.length > 0 ? accounts[0].id : null;

  // Fetch initial trades for the first account
  let initialTrades: any[] = [];
  if (initialAccountId) {
    const { data: tradesData, error: tradesError } = await supabase
      .from("trades")
      .select("*")
      .eq("user_id", userId)
      .eq("account_id", initialAccountId)
      .order("time_close", { ascending: false });

    if (tradesError) {
      console.error('Error fetching trades:', tradesError);
    } else {
      initialTrades = tradesData || [];
    }
  }

  // Pass the fetched data to the client component
  return (
    <SymbolsClient
      userId={userId}
      initialTrades={initialTrades}
      initialAccountId={initialAccountId}
    />
  );
}
