import { isWeekend } from "date-fns"
import { type ProcessedData } from "@/lib/excel-processor"
import { DashboardFilters } from "@/components/dashboard/dashboard-filters"
import type { Trade } from "@/types/trade"

/**
 * Filters trades based on dashboard filter settings
 */
export function filterTrades(
  trades: Trade[],
  filters: DashboardFilters
): Trade[] {
  if (!trades || trades.length === 0) return []

  return trades.filter(trade => {
    const tradeDate = new Date(trade.time_close || trade.time_open)

    // Filter by date range
    if (filters.dateRange?.from && tradeDate < filters.dateRange.from) {
      return false
    }
    if (filters.dateRange?.to && tradeDate > filters.dateRange.to) {
      return false
    }

    // Filter by strategy
    if (filters.strategyId && trade.strategy_id !== filters.strategyId) {
      return false
    }

    // Filter by trade type (winning/losing)
    if (filters.tradeType === "winning" && trade.profit <= 0) {
      return false
    }
    if (filters.tradeType === "losing" && trade.profit >= 0) {
      return false
    }

    // Filter by symbols
    if (filters.symbols.length > 0 && !filters.symbols.includes(trade.symbol)) {
      return false
    }

    // Filter by weekends
    if (!filters.showWeekends && isWeekend(tradeDate)) {
      return false
    }

    return true
  })
}
