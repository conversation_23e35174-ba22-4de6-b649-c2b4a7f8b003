-- Update the get_journal_entries function to improve search functionality
CREATE OR REPLACE FUNCTION public.get_journal_entries(
  p_user_id UUID,
  p_search_term TEXT DEFAULT NULL,
  p_tags TEXT[] DEFAULT NULL,
  p_start_date DATE DEFAULT NULL,
  p_end_date DATE DEFAULT NULL,
  p_account_id UUID DEFAULT NULL
)
RETURNS SETOF public.journal_entries
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  RETURN QUERY
  SELECT je.*
  FROM public.journal_entries je
  LEFT JOIN public.trades t ON je.trade_id = t.id
  WHERE je.user_id = p_user_id
    AND (
      -- Account filter (only applies to entries with trades)
      p_account_id IS NULL 
      OR je.trade_id IS NULL 
      OR t.account_id = p_account_id
    )
    AND (
      -- Search term filter - search in title, content, and trade notes
      p_search_term IS NULL
      OR p_search_term = ''
      OR je.title ILIKE '%' || p_search_term || '%'
      OR je.content ILIKE '%' || p_search_term || '%'
      OR (t.notes IS NOT NULL AND t.notes ILIKE '%' || p_search_term || '%')
      OR (t.symbol IS NOT NULL AND t.symbol ILIKE '%' || p_search_term || '%')
      -- Also search in tags
      OR (je.tags IS NOT NULL AND EXISTS (
          SELECT 1 FROM unnest(je.tags) tag
          WHERE tag ILIKE '%' || p_search_term || '%'
        ))
      OR (t.tags IS NOT NULL AND EXISTS (
          SELECT 1 FROM unnest(t.tags) tag
          WHERE tag ILIKE '%' || p_search_term || '%'
        ))
    )
    AND (
      -- Tag filter
      p_tags IS NULL
      OR p_tags = '{}'
      OR (je.tags && p_tags)
      OR (je.trade_id IS NOT NULL AND t.tags && p_tags)
    )
    AND (
      -- Date range filter
      (p_start_date IS NULL OR je.entry_date >= p_start_date)
      AND (p_end_date IS NULL OR je.entry_date <= p_end_date)
    )
  ORDER BY je.entry_date DESC, je.created_at DESC;
END;
$$;
