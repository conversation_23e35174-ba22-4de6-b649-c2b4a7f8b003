"use client"

import dynamic from 'next/dynamic';
import { Trade } from '@/types/trade';

// Dynamically import the client component with no SSR
const AnalyticsClient = dynamic(() => import('./client'), {
  ssr: false
});

interface ClientWrapperProps {
  userId: string;
  initialTrades: Trade[];
  initialSummary: any | null;
  selectedAccountId: string | null;
}

export default function ClientWrapper({ 
  userId, 
  initialTrades, 
  initialSummary, 
  selectedAccountId 
}: ClientWrapperProps) {
  return (
    <AnalyticsClient
      userId={userId}
      initialTrades={initialTrades}
      initialSummary={initialSummary}
      selectedAccountId={selectedAccountId}
    />
  );
}
