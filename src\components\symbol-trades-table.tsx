"use client"

import { useState } from "react"
import { format, formatDistanceStrict } from "date-fns"
import { cn } from "@/lib/utils"
import { useRouter } from "next/navigation"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Pa<PERSON>ation,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination"
import { Badge } from "@/components/ui/badge"
import { ArrowUpDown, BookOpen, ChevronDown, ChevronUp } from "lucide-react"
import { Button } from "@/components/ui/button"

interface Trade {
  id: string
  symbol: string
  type: string
  volume: number
  price_open: number
  price_close: number
  time_open: string
  time_close: string
  profit: number
  commission: number
  swap: number
  strategy_id?: string | null
  strategy_name?: string | null
}

interface SymbolTradesTableProps {
  trades: Trade[]
}

type SortField = "time_close" | "type" | "volume" | "price_open" | "price_close" | "profit"
type SortDirection = "asc" | "desc"

export function SymbolTradesTable({ trades }: SymbolTradesTableProps) {
  const router = useRouter()
  const [page, setPage] = useState(1)
  const [sortField, setSortField] = useState<SortField>("time_close")
  const [sortDirection, setSortDirection] = useState<SortDirection>("desc")

  const itemsPerPage = 10
  const totalPages = Math.ceil(trades.length / itemsPerPage)

  // Sort trades
  const sortedTrades = [...trades].sort((a, b) => {
    if (sortField === "time_close") {
      return sortDirection === "asc"
        ? new Date(a.time_close).getTime() - new Date(b.time_close).getTime()
        : new Date(b.time_close).getTime() - new Date(a.time_close).getTime()
    }

    if (typeof a[sortField] === "string" && typeof b[sortField] === "string") {
      return sortDirection === "asc"
        ? a[sortField].localeCompare(b[sortField])
        : b[sortField].localeCompare(a[sortField])
    }

    // For numeric fields
    if (typeof a[sortField] === "number" && typeof b[sortField] === "number") {
      return sortDirection === "asc"
        ? a[sortField] - b[sortField]
        : b[sortField] - a[sortField]
    }

    // Fallback for other types
    return 0
  })

  // Paginate trades
  const paginatedTrades = sortedTrades.slice(
    (page - 1) * itemsPerPage,
    page * itemsPerPage
  )

  const handleSort = (field: SortField) => {
    if (field === sortField) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc")
    } else {
      setSortField(field)
      setSortDirection("desc")
    }
  }

  const SortIcon = ({ field }: { field: SortField }) => {
    if (field !== sortField) return <ArrowUpDown className="ml-2 h-4 w-4" />
    return sortDirection === "asc" ? (
      <ChevronUp className="ml-2 h-4 w-4" />
    ) : (
      <ChevronDown className="ml-2 h-4 w-4" />
    )
  }

  return (
    <div>
      <div className="overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>
                <Button
                  variant="ghost"
                  onClick={() => handleSort("time_close")}
                  className="p-0 font-medium"
                >
                  Date/Time
                  <SortIcon field="time_close" />
                </Button>
              </TableHead>
              <TableHead>
                <Button
                  variant="ghost"
                  onClick={() => handleSort("type")}
                  className="p-0 font-medium"
                >
                  Type
                  <SortIcon field="type" />
                </Button>
              </TableHead>
              <TableHead>
                <Button
                  variant="ghost"
                  onClick={() => handleSort("volume")}
                  className="p-0 font-medium"
                >
                  Volume
                  <SortIcon field="volume" />
                </Button>
              </TableHead>
              <TableHead>
                <Button
                  variant="ghost"
                  onClick={() => handleSort("price_open")}
                  className="p-0 font-medium"
                >
                  Entry Price
                  <SortIcon field="price_open" />
                </Button>
              </TableHead>
              <TableHead>
                <Button
                  variant="ghost"
                  onClick={() => handleSort("price_close")}
                  className="p-0 font-medium"
                >
                  Exit Price
                  <SortIcon field="price_close" />
                </Button>
              </TableHead>
              <TableHead>Duration</TableHead>
              <TableHead>
                <Button
                  variant="ghost"
                  onClick={() => handleSort("profit")}
                  className="p-0 font-medium"
                >
                  Profit
                  <SortIcon field="profit" />
                </Button>
              </TableHead>
              <TableHead>Strategy</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {paginatedTrades.map((trade, index) => (
              <TableRow
                key={`${trade.id}-${index}`}
                className="hover:bg-muted/50 cursor-pointer"
                onClick={() => {
                  // Only navigate if it's a real database ID (not a generated one)
                  if (trade.id && !trade.id.startsWith('trade-')) {
                    // Include source=symbol parameter to indicate we came from symbol page
                    router.push(`/trades/${trade.id}?source=symbol&symbol=${trade.symbol}`);
                  } else {
                    // For generated IDs, show a message that this trade doesn't have details
                    alert("Trade details are not available for this trade.");
                  }
                }}
              >
                <TableCell className="font-medium">
                  {format(new Date(trade.time_close), "MMM d, yyyy h:mm a")}
                </TableCell>
                <TableCell>
                  <Badge
                    variant="outline"
                    className={cn(
                      trade.type.toLowerCase() === "buy"
                        ? "border-emerald-500 text-emerald-500"
                        : "border-rose-500 text-rose-500"
                    )}
                  >
                    {trade.type}
                  </Badge>
                </TableCell>
                <TableCell>{trade.volume.toFixed(2)}</TableCell>
                <TableCell>{trade.price_open.toFixed(5)}</TableCell>
                <TableCell>{trade.price_close.toFixed(5)}</TableCell>
                <TableCell>
                  {new Date(trade.time_open).getTime() !== new Date(trade.time_close).getTime() ?
                    formatDistanceStrict(
                      new Date(trade.time_open),
                      new Date(trade.time_close)
                    ) : "0 seconds"}
                </TableCell>
                <TableCell className={cn(
                  "font-medium",
                  trade.profit >= 0 ? "text-emerald-500" : "text-rose-500"
                )}>
                  ${trade.profit.toFixed(2)}
                </TableCell>
                <TableCell>
                  {trade.strategy_id ? (
                    <Badge
                      variant="outline"
                      className="flex items-center gap-1 border-purple-500 text-purple-500 hover:bg-purple-50 dark:hover:bg-purple-950/20"
                    >
                      <BookOpen className="h-3 w-3" />
                      {trade.strategy_name || "Strategy"}
                    </Badge>
                  ) : (
                    <span className="text-muted-foreground text-sm">—</span>
                  )}
                </TableCell>
              </TableRow>
            ))}

            {paginatedTrades.length === 0 && (
              <TableRow>
                <TableCell colSpan={8} className="text-center py-4">
                  No trades found
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {totalPages > 1 && (
        <div className="mt-4">
          <Pagination>
            <PaginationContent>
              <PaginationItem>
                <PaginationPrevious
                  onClick={() => setPage(p => Math.max(1, p - 1))}
                  className={cn(page === 1 && "pointer-events-none opacity-50")}
                />
              </PaginationItem>

              {Array.from({ length: totalPages }).map((_, i) => (
                <PaginationItem key={i}>
                  <PaginationLink
                    onClick={() => setPage(i + 1)}
                    isActive={page === i + 1}
                  >
                    {i + 1}
                  </PaginationLink>
                </PaginationItem>
              ))}

              <PaginationItem>
                <PaginationNext
                  onClick={() => setPage(p => Math.min(totalPages, p + 1))}
                  className={cn(page === totalPages && "pointer-events-none opacity-50")}
                />
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        </div>
      )}
    </div>
  )
}
