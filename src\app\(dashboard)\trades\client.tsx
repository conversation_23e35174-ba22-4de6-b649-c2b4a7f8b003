"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useSearchPara<PERSON> } from "next/navigation"

import { formatDistanceStrict } from "date-fns"
import { format } from "date-fns"
import { Strategy } from "@/types/playbook"
import { useAccount } from "@/contexts/account-context"
import { toast } from "sonner"
import { getSupabaseClient } from "@/lib/supabase-singleton"

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { DualColorLinearGauge } from "@/components/ui/dual-color-linear-gauge"
import { LinearProgress } from "@/components/ui/linear-progress"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Too<PERSON><PERSON><PERSON>rigger } from "@/components/ui/tooltip"
import { Download, ArrowUpDown, Info, Filter, Calendar, Search, BookOpen } from "lucide-react"

interface Trade {
  id: string
  user_id: string
  account_id: string
  position_id: number
  time_open: string
  time_close: string
  symbol: string
  type: string
  volume: number
  price_open: number
  price_close: number
  profit: number
  commission: number
  swap: number
  status: "open" | "closed"
  strategy_id?: string | null
  setup_id?: string | null
  notes?: string | null
  screenshots?: string[] | null
  followed_rules?: string[] | null
  followed_setup_criteria?: string[] | null
  strategy_name?: string | null
}

interface StatsCardProps {
  title: string
  value: string | number
  info?: string
  progress?: number
  progressColor?: string
  className?: string
  valueClassName?: string
  useLinearProgress?: boolean
  leftLabel?: string
  rightLabel?: string
  // Dual color gauge props
  gaugeValue?: number
  gaugeMax?: number
  gaugeColor?: string
  useDualColorGauge?: boolean
  useDualColorLinearGauge?: boolean
  leftValue?: number
  rightValue?: number
  leftColor?: string
  rightColor?: string
}

const StatsCard = ({
  title,
  value,
  info,
  progress = 0,
  progressColor = "bg-emerald-500",
  className,
  valueClassName = "",
  useLinearProgress = false,
  leftLabel,
  rightLabel,
  // Dual color gauge props
  useDualColorGauge = false,
  useDualColorLinearGauge = false,
  leftValue = 0,
  rightValue = 0,
  leftColor = "bg-emerald-500",
  rightColor = "bg-rose-500"
}: StatsCardProps) => (
  <Card className={`dashboard-card p-4 relative overflow-hidden ${className}`}>
    <div className="flex justify-between items-start mb-2">
      <h3 className="text-sm font-medium text-muted-foreground">{title}</h3>
      {info && (
        <Info className="h-4 w-4 text-muted-foreground hover:text-foreground cursor-help" />
      )}
    </div>

    {useDualColorGauge ? (
      <div className="flex items-center justify-between">
        <div className={`text-2xl font-bold ${valueClassName}`}>{value}</div>
        <div className="ml-2">
          <DualColorLinearGauge
            leftValue={leftValue}
            rightValue={rightValue}
            height={6}
            leftColor={leftColor}
            rightColor={rightColor}
            showLabels={false}
          />
        </div>
      </div>
    ) : useDualColorLinearGauge ? (
      <div className="flex items-center justify-between">
        <div className={`text-2xl font-bold ${valueClassName}`}>{value}</div>
        <div className="flex flex-col w-[120px]">
          {leftLabel && rightLabel && (
            <div className="text-xs text-muted-foreground flex justify-between w-full mb-1">
              <span>{leftLabel}</span>
              <span>{rightLabel}</span>
            </div>
          )}
          <DualColorLinearGauge
            leftValue={leftValue}
            rightValue={rightValue}
            height={6}
            leftColor={leftColor}
            rightColor={rightColor}
            showLabels={false}
          />
        </div>
      </div>
    ) : useLinearProgress ? (
      <div className="flex items-center justify-between">
        <div className={`text-2xl font-bold ${valueClassName}`}>{value}</div>
        <div className="flex flex-col w-[120px]">
          {leftLabel && rightLabel && (
            <div className="text-xs text-muted-foreground flex justify-between w-full mb-1">
              <span>{leftLabel}</span>
              <span>{rightLabel}</span>
            </div>
          )}
          <LinearProgress
            value={typeof progress === 'number' ? progress : 0}
            max={100}
            height={6}
            progressColor={progressColor}
            showLabels={false} // We're showing labels above the gauge now
          />
        </div>
      </div>
    ) : (
      <div>
        <div className={`text-2xl font-bold ${valueClassName} mb-1`}>{value}</div>
        {progress > 0 && (
          <div className="w-full h-1 bg-muted rounded-full overflow-hidden">
            <div
              className={`h-full ${progressColor} transition-all duration-500`}
              style={{ width: `${progress}%` }}
            />
          </div>
        )}
      </div>
    )}
  </Card>
)

type SortField = keyof Trade
type SortOrder = "asc" | "desc"
type FilterType = "buy" | "sell" | null

interface TradesClientProps {
  userId: string;
  initialTrades: Trade[];
  initialFilteredTrades: Trade[];
  initialPaginatedTrades: Trade[];
  initialStrategies: Strategy[];
  initialStats: {
    totalTrades: number;
    winRate: string;
    totalProfit: string;
    avgWin: string;
    avgLoss: string;
    winLossRatio: string;
    winningTradesCount: number;
    losingTradesCount: number;
  };
  initialFilters: {
    symbol: string;
    type: string | null;
    dateFrom: string;
    dateTo: string;
    profitMin: string;
    profitMax: string;
    volumeMin: string;
    volumeMax: string;
    durationMin: string;
    durationMax: string;
    status: string;
    strategy: string;
  };
  initialSortField: string;
  initialSortOrder: 'asc' | 'desc';
  initialPage: number;
  initialPageSize: number;
  totalPages: number;
  totalItems: number;
  selectedAccountId: string | null;
}

export default function TradesClient({
  userId,
  initialTrades,
  initialFilteredTrades,
  initialPaginatedTrades,
  initialStrategies,
  initialStats,
  initialFilters,
  initialSortField,
  initialSortOrder,
  initialPage,
  initialPageSize,
  totalPages,
  totalItems,
  selectedAccountId: initialAccountId
}: TradesClientProps) {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [trades, setTrades] = useState<Trade[]>(initialTrades)
  const [loading, setLoading] = useState(false)
  const [isInitializing, setIsInitializing] = useState(true)
  const [sortField, setSortField] = useState<SortField>(initialSortField as SortField)
  const [sortOrder, setSortOrder] = useState<SortOrder>(initialSortOrder)
  const [currentPage, setCurrentPage] = useState(initialPage)
  const [pageSize, setPageSize] = useState<number>(initialPageSize)

  // Filters
  const [filterSymbol, setFilterSymbol] = useState(initialFilters.symbol)
  const [filterType, setFilterType] = useState<FilterType>(initialFilters.type as FilterType)
  const [filterDateFrom, setFilterDateFrom] = useState<string>(initialFilters.dateFrom)
  const [filterDateTo, setFilterDateTo] = useState<string>(initialFilters.dateTo)
  const [filterProfitMin, setFilterProfitMin] = useState<string>(initialFilters.profitMin)
  const [filterProfitMax, setFilterProfitMax] = useState<string>(initialFilters.profitMax)
  const [filterDurationMin, setFilterDurationMin] = useState<string>(initialFilters.durationMin)
  const [filterDurationMax, setFilterDurationMax] = useState<string>(initialFilters.durationMax)
  const [filterVolumeMin, setFilterVolumeMin] = useState<string>(initialFilters.volumeMin)
  const [filterVolumeMax, setFilterVolumeMax] = useState<string>(initialFilters.volumeMax)
  const [filterStatus, setFilterStatus] = useState<string | null>(initialFilters.status || null)
  const [filterStrategy, setFilterStrategy] = useState<string | null>(initialFilters.strategy || null)

  const [strategies, setStrategies] = useState<Strategy[]>(initialStrategies)
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false)
  const supabase = getSupabaseClient()
  const { selectedAccountId } = useAccount()

  // Function to update URL with current filters and sorting
  const updateUrl = () => {
    const params = new URLSearchParams()

    // Add filters to URL
    if (filterSymbol) params.set('symbol', filterSymbol)
    if (filterType) params.set('type', filterType)
    if (filterDateFrom) params.set('dateFrom', filterDateFrom)
    if (filterDateTo) params.set('dateTo', filterDateTo)
    if (filterProfitMin) params.set('profitMin', filterProfitMin)
    if (filterProfitMax) params.set('profitMax', filterProfitMax)
    if (filterVolumeMin) params.set('volumeMin', filterVolumeMin)
    if (filterVolumeMax) params.set('volumeMax', filterVolumeMax)
    if (filterDurationMin) params.set('durationMin', filterDurationMin)
    if (filterDurationMax) params.set('durationMax', filterDurationMax)
    if (filterStatus) params.set('status', filterStatus)
    if (filterStrategy) params.set('strategy', filterStrategy)

    // Add sorting to URL
    params.set('sortField', sortField.toString())
    params.set('sortOrder', sortOrder)

    // Add pagination to URL
    params.set('page', currentPage.toString())
    params.set('pageSize', pageSize.toString())

    // Add account ID to URL
    if (selectedAccountId) params.set('accountId', selectedAccountId)

    // Update URL without refreshing the page
    router.push(`/trades?${params.toString()}`)
  }

  // Initialize component
  useEffect(() => {
    setIsInitializing(false)
  }, [])

  // Fetch trades when selectedAccountId changes
  useEffect(() => {
    console.log("Selected account changed, fetching trades for account:", selectedAccountId)
    if (!isInitializing && selectedAccountId !== initialAccountId) {
      fetchTrades()
    }
  }, [selectedAccountId, isInitializing, initialAccountId])

  // Listen for strategy assignment changes and other global events
  useEffect(() => {
    // Flag to track if a refresh is already in progress
    let isRefreshing = false;

    // Function to refresh trades with debouncing
    const refreshTradesWithDebounce = () => {
      if (isRefreshing) {
        console.log("Refresh already in progress, skipping")
        return
      }

      isRefreshing = true
      console.log("Starting trades refresh with debounce")

      // Add a delay to ensure the database has been updated
      setTimeout(() => {
        fetchTrades().finally(() => {
          // Reset the flag after the refresh is complete
          setTimeout(() => {
            isRefreshing = false
            console.log("Refresh complete, reset debounce flag")
          }, 500)
        })
      }, 300)
    }

    const handleStrategyAssignmentChange = (event: Event) => {
      console.log("Strategy assignment changed event received, refreshing trades data")

      // Force a complete refresh of trades data and strategies
      const customEvent = event as CustomEvent;
      console.log("Strategy assignment details:", customEvent.detail)

      // Refresh trades with debounce
      refreshTradesWithDebounce()
    }

    const handleRefreshTradesData = (event: Event) => {
      const customEvent = event as CustomEvent;
      console.log("Global refresh-trades-data event received", customEvent.detail || "")

      // Refresh trades with debounce
      refreshTradesWithDebounce()
    }

    const handleGlobalDataChange = (event: Event) => {
      const customEvent = event as CustomEvent;
      console.log("Global data change event received:", customEvent.detail)

      if (customEvent.detail?.type === 'strategy-assignment') {
        console.log("Strategy assignment changed via global event, refreshing trades data")

        // Refresh trades with debounce
        refreshTradesWithDebounce()
      }
    }

    // Add event listeners
    document.addEventListener('strategy-assignment-changed', handleStrategyAssignmentChange)
    window.addEventListener('refresh-trades-data', handleRefreshTradesData)
    window.addEventListener('global-data-change', handleGlobalDataChange)

    // Clean up
    return () => {
      document.removeEventListener('strategy-assignment-changed', handleStrategyAssignmentChange)
      window.removeEventListener('refresh-trades-data', handleRefreshTradesData)
      window.removeEventListener('global-data-change', handleGlobalDataChange)
    }
  }, [])

  const fetchTrades = async () => {
    // Return a promise so we can use .finally() with this function
    return new Promise<void>(async (resolve, reject) => {
      try {
        // Only set loading if we're not in the initialization phase
        // This prevents flickering when the app first loads
        if (!isInitializing) {
          setLoading(true)
        }

        const { data: { session } } = await supabase.auth.getSession()
        if (!session?.user) {
          throw new Error("No authenticated user")
        }

        // First fetch strategies to ensure we have the latest data
        console.log("Fetching strategies...")
        const { data: strategiesData, error: strategiesError } = await supabase
          .from("strategies")
          .select("*")
          .eq("user_id", session.user.id)

        if (strategiesError) {
          console.error("Error fetching strategies:", strategiesError)
          throw strategiesError
        }

        setStrategies(strategiesData || [])
        console.log(`Fetched ${strategiesData?.length || 0} strategies directly from database`)

        // Fetch trades for the selected account or all accounts
        console.log("Fetching trades...")

        // Use direct Supabase query to ensure we get the latest data
        let query = supabase
          .from("trades")
          .select("*")

        // Get the current selected account ID directly from localStorage as a backup
        const localStorageAccountId = localStorage.getItem('selectedAccountId')
        console.log("Current selectedAccountId from context:", selectedAccountId)
        console.log("Current selectedAccountId from localStorage:", localStorageAccountId)

        // Use the account ID from context, or from localStorage as a fallback
        const accountIdToUse = selectedAccountId || localStorageAccountId

        if (accountIdToUse) {
          console.log("Filtering trades by account ID:", accountIdToUse)
          query = query.eq("account_id", accountIdToUse)
        } else {
          console.log("No account ID available, fetching all user accounts")
          // Get the user's accounts
          const { data: accounts } = await supabase
            .from("trading_accounts")
            .select("id")
            .eq("user_id", session.user.id)

          if (accounts && accounts.length > 0) {
            const accountIds = accounts.map(account => account.id)
            console.log("Filtering trades by multiple account IDs:", accountIds)
            query = query.in("account_id", accountIds)
          } else {
            console.log("No accounts found, filtering by user ID only")
            query = query.eq("user_id", session.user.id)
          }
        }

        // Execute the query with cache control to ensure we get the latest data
        const { data: trades, error } = await query
          .order("time_close", { ascending: false })
          .limit(1000) // Add a reasonable limit

        if (error) {
          console.error("Error fetching trades:", error)
          throw error
        }

        console.log(`Fetched ${trades?.length || 0} trades directly from database`)

        // Log strategy_id for debugging
        trades?.forEach(trade => {
          if (trade.strategy_id) {
            const strategyName = strategiesData?.find(s => s.id === trade.strategy_id)?.name || "Unknown"
            console.log(`Trade ${trade.id} has strategy_id: ${trade.strategy_id} (${strategyName})`)
          }
        })

        // Process the trades with proper strategy information
        const processedTrades = (trades || []).map(trade => {
          // Find the strategy for this trade
          const strategy = strategiesData?.find(s => s.id === trade.strategy_id)

          return {
            ...trade,
            status: "closed",
            user_id: session.user.id,
            account_id: trade.account_id || selectedAccountId || "",
            notes: trade.notes || null,
            screenshots: trade.screenshots || [],
            followed_rules: trade.followed_rules || [],
            followed_setup_criteria: trade.followed_setup_criteria || [],
            // Add strategy name for easier access
            strategy_name: strategy?.name || null
          }
        })

        // Update the trades state
        setTrades(processedTrades)

        // Log the processed trades for debugging
        console.log("Processed trades:", processedTrades.map(t => ({
          id: t.id,
          strategy_id: t.strategy_id,
          strategy_name: t.strategy_name
        })))

        // Resolve the promise
        resolve()
      } catch (error) {
        console.error("Error fetching data:", error)
        toast.error("Failed to fetch data")
        // Reject the promise
        reject(error)
      } finally {
        setLoading(false)
      }
    })
  }

  // Add a listener to refresh data when returning to the trades page
  useEffect(() => {
    // Track last refresh time to prevent too frequent refreshes
    let lastRefreshTime = Date.now()
    let tabHiddenTime: number | null = null
    const REFRESH_THRESHOLD = 5000 // 5 seconds between refreshes
    const AWAY_THRESHOLD = 180000   // 3 minutes away to trigger refresh

    // Function to handle visibility changes with time away tracking
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'hidden') {
        // User left the tab - record the time
        tabHiddenTime = Date.now()
        console.log(`Trades: Tab hidden at: ${new Date(tabHiddenTime).toLocaleTimeString()}`)
      }
      else if (document.visibilityState === 'visible') {
        // User returned to the tab
        const now = Date.now()

        if (tabHiddenTime) {
          const timeAway = now - tabHiddenTime
          console.log(`Trades: Tab visible again. Was hidden for ${Math.round(timeAway/1000)} seconds. Threshold is ${AWAY_THRESHOLD/1000} seconds.`)

          // Only refresh if they've been away for longer than AWAY_THRESHOLD
          // AND enough time has passed since last refresh
          if (timeAway > AWAY_THRESHOLD && (now - lastRefreshTime > REFRESH_THRESHOLD)) {
            console.log(`Trades: Tab was hidden for ${Math.round(timeAway/1000)} seconds (>3 min), refreshing trades data...`)
            lastRefreshTime = now
            fetchTrades()
          } else {
            console.log(`Trades: Tab was hidden for ${Math.round(timeAway/1000)} seconds, skipping trades refresh (below 3 min threshold)`)
          }
        } else {
          console.log('Trades: Tab became visible but no hidden time was recorded')
        }

        // Reset the hidden time
        tabHiddenTime = null
      }
    }

    // Listen for visibility changes
    document.addEventListener('visibilitychange', handleVisibilityChange)

    // Also refresh when the window gets focus, but with the same threshold
    const handleFocus = () => {
      // We'll disable the focus handler since it's causing unwanted refreshes
      // The visibility change handler will take care of refreshes
      console.log('Trades: Window focused event - refresh disabled to prevent unwanted refreshes')
      // No refresh happens here anymore
    }

    window.addEventListener('focus', handleFocus)

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange)
      window.removeEventListener('focus', handleFocus)
    }
  }, [])

  const calculateStats = () => {
    const totalTrades = trades.length
    const winningTrades = trades.filter(t => t.profit > 0)
    const losingTrades = trades.filter(t => t.profit < 0)
    const winRate = totalTrades > 0 ? (winningTrades.length / totalTrades) * 100 : 0
    const totalProfit = trades.reduce((sum, t) => sum + t.profit, 0)

    // Calculate average win and average loss
    const avgWin = winningTrades.length > 0 ?
      winningTrades.reduce((sum, t) => sum + t.profit, 0) / winningTrades.length : 0
    const avgLoss = losingTrades.length > 0 ?
      Math.abs(losingTrades.reduce((sum, t) => sum + t.profit, 0) / losingTrades.length) : 0
    const winLossRatio = avgLoss > 0 ? avgWin / avgLoss : 0

    return {
      totalTrades,
      winRate: winRate.toFixed(2),
      totalProfit: totalProfit.toFixed(2),
      avgWin: avgWin.toFixed(2),
      avgLoss: avgLoss.toFixed(2),
      winLossRatio: winLossRatio.toFixed(2)
    }
  }

  // Use the server-calculated stats from initialStats or calculate them on the client
  const stats = initialStats || calculateStats()



  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortOrder(sortOrder === "asc" ? "desc" : "asc")
    } else {
      setSortField(field)
      setSortOrder("asc")
    }
  }

  const resetFilters = () => {
    setFilterSymbol("")
    setFilterType(null)
    setFilterDateFrom("")
    setFilterDateTo("")
    setFilterProfitMin("")
    setFilterProfitMax("")
    setFilterDurationMin("")
    setFilterDurationMax("")
    setFilterVolumeMin("")
    setFilterVolumeMax("")
    setFilterStatus(null)
    setFilterStrategy(null)
    setCurrentPage(1)
  }

  const filteredTrades = trades
    .filter(trade => {
      const matchesSymbol = filterSymbol ? trade.symbol.toLowerCase().includes(filterSymbol.toLowerCase()) : true
      const matchesType = filterType ? trade.type.toLowerCase() === filterType.toLowerCase() : true

      // Date filters
      const tradeDate = new Date(trade.time_close)
      const matchesDateFrom = filterDateFrom ? tradeDate >= new Date(filterDateFrom) : true
      const matchesDateTo = filterDateTo ? tradeDate <= new Date(filterDateTo + 'T23:59:59') : true

      // Profit filters
      const profit = trade.profit
      const matchesProfitMin = filterProfitMin ? profit >= parseFloat(filterProfitMin) : true
      const matchesProfitMax = filterProfitMax ? profit <= parseFloat(filterProfitMax) : true

      // Volume filters
      const volume = trade.volume
      const matchesVolumeMin = filterVolumeMin ? volume >= parseFloat(filterVolumeMin) : true
      const matchesVolumeMax = filterVolumeMax ? volume <= parseFloat(filterVolumeMax) : true

      // Duration filters
      const durationMs = new Date(trade.time_close).getTime() - new Date(trade.time_open).getTime()
      const durationMinutes = durationMs / (1000 * 60)
      const matchesDurationMin = filterDurationMin ? durationMinutes >= parseFloat(filterDurationMin) : true
      const matchesDurationMax = filterDurationMax ? durationMinutes <= parseFloat(filterDurationMax) : true

      // Status filter
      const matchesStatus = filterStatus ? trade.status === filterStatus : true

      // Strategy filter
      const matchesStrategy = filterStrategy === "none" ? !trade.strategy_id :
                             filterStrategy ? trade.strategy_id === filterStrategy : true

      return matchesSymbol && matchesType && matchesDateFrom && matchesDateTo &&
             matchesProfitMin && matchesProfitMax && matchesVolumeMin && matchesVolumeMax &&
             matchesDurationMin && matchesDurationMax && matchesStatus && matchesStrategy
    })
    .sort((a, b) => {
      const aValue = a[sortField]
      const bValue = b[sortField]
      const modifier = sortOrder === "asc" ? 1 : -1

      if (typeof aValue === "string" && typeof bValue === "string") {
        return aValue.localeCompare(bValue) * modifier
      }
      return ((aValue as number) - (bValue as number)) * modifier
    })

  const paginatedTrades = filteredTrades.slice(
    (currentPage - 1) * pageSize,
    currentPage * pageSize
  )

  // Use the server-calculated total pages or calculate them on the client
  const clientTotalPages = Math.ceil(filteredTrades.length / pageSize)

  const getTradeData = (trades: Trade[]) => {
    const headers = ["Entry Date", "Exit Date", "Symbol", "Type", "Volume", "Entry Price", "Exit Price", "Duration", "NET P/L", "Commission", "Swap", "Status"]
    const data = trades.map(trade => [
      format(new Date(trade.time_open), "MMM d, yyyy HH:mm"),
      format(new Date(trade.time_close), "MMM d, yyyy HH:mm"),
      trade.symbol,
      trade.type.toUpperCase(),
      trade.volume,
      trade.price_open.toFixed(5),
      trade.price_close.toFixed(5),
      formatDistanceStrict(new Date(trade.time_open), new Date(trade.time_close)),
      trade.profit.toFixed(2),
      trade.commission.toFixed(2),
      trade.swap.toFixed(2),
      trade.status
    ])

    return { headers, data }
  }

  const exportToCSV = () => {
    const { headers, data } = getTradeData(filteredTrades)

    const csvContent = [
      headers.join(","),
      ...data.map(row => row.join(","))
    ].join("\n")

    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" })
    const url = URL.createObjectURL(blob)
    const link = document.createElement("a")
    link.setAttribute("href", url)
    link.setAttribute("download", `trades_${format(new Date(), "yyyy-MM-dd")}.csv`)
    link.style.visibility = "hidden"
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  const exportToExcel = () => {
    import('xlsx').then(XLSX => {
      const { headers, data } = getTradeData(filteredTrades)

      // Create worksheet with headers
      const ws = XLSX.utils.aoa_to_sheet([headers, ...data])

      // Create workbook and add the worksheet
      const wb = XLSX.utils.book_new()
      XLSX.utils.book_append_sheet(wb, ws, 'Trades')

      // Generate Excel file and trigger download
      XLSX.writeFile(wb, `trades_${format(new Date(), "yyyy-MM-dd")}.xlsx`)
    })
  }

  return (
    <div className="space-y-6">


      {selectedAccountId === null && !isInitializing ? (
        <Card className="p-8 text-center">
          <div className="flex flex-col items-center justify-center space-y-4">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="h-12 w-12 text-muted-foreground mb-2"
            >
              <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
              <circle cx="9" cy="7" r="4" />
              <path d="M22 21v-2a4 4 0 0 0-3-3.87" />
              <path d="M16 3.13a4 4 0 0 1 0 7.75" />
            </svg>
            <h2 className="text-xl font-semibold">No Account Selected</h2>
            <p className="text-muted-foreground max-w-md">
              Please select an account from the dashboard to view your trades. No data will be displayed until an account is selected.
            </p>
            <Button
              variant="outline"
              onClick={() => router.push('/dashboard')}
              className="mt-4"
            >
              Go to Dashboard
            </Button>
          </div>
        </Card>
      ) : loading || isInitializing ? (
        <div className="space-y-6">
          {/* Loading Skeleton for Header */}
          <div className="border-b pb-4">
            <div className="h-6 w-1/4 bg-muted rounded mb-2 slow-pulse"></div>
            <div className="h-4 w-1/3 bg-muted rounded slow-pulse"></div>
          </div>

          {/* Loading Skeleton for Stats */}
          <div className="grid grid-cols-4 gap-4">
            {[...Array(4)].map((_, i) => (
              <Card key={i} className="p-4 h-[120px]">
                <div className="h-4 w-1/3 bg-muted rounded mb-4 slow-pulse"></div>
                <div className="h-8 w-1/2 bg-muted rounded mb-2 slow-pulse"></div>
                <div className="h-2 w-full bg-muted rounded-full slow-pulse"></div>
              </Card>
            ))}
          </div>

          {/* Loading Skeleton for Table */}
          <div className="border rounded-md">
            <div className="p-4 border-b">
              <div className="h-8 w-full bg-muted rounded slow-pulse"></div>
            </div>
            <div className="p-4">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="h-12 w-full bg-muted rounded-md mb-2 slow-pulse"></div>
              ))}
            </div>
          </div>
        </div>
      ) : (
        <div className="space-y-6">

          {/* Header with filters */}
          <div className="flex flex-col space-y-4 border-b pb-4" data-section="trades-header">
            <div className="flex flex-col lg:flex-row items-start lg:items-center justify-between gap-4">
              <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4 w-full lg:w-auto">
                <div className="relative w-full sm:w-[200px]">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Filter by symbol..."
                    value={filterSymbol}
                    onChange={(e) => setFilterSymbol(e.target.value)}
                    className="w-full pl-8"
                  />
                </div>
                <Select
                  value={filterType || "all"}
                  onValueChange={(value) => setFilterType(value === "all" ? null : value as FilterType)}
                >
                  <SelectTrigger className="w-full sm:w-[180px]">
                    <SelectValue placeholder="Filter by type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Types</SelectItem>
                    <SelectItem value="buy">Buy</SelectItem>
                    <SelectItem value="sell">Sell</SelectItem>
                  </SelectContent>
                </Select>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
                  className="w-full sm:w-auto"
                >
                  <Filter className="mr-2 h-4 w-4" />
                  {showAdvancedFilters ? "Hide Filters" : "Advanced Filters"}
                </Button>
              </div>
              <div className="flex items-center space-x-2 w-full lg:w-auto justify-end">

                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={exportToCSV}
                  >
                    <Download className="mr-2 h-4 w-4" />
                    CSV
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={exportToExcel}
                  >
                    <Download className="mr-2 h-4 w-4" />
                    Excel
                  </Button>
                </div>
              </div>
            </div>

            {showAdvancedFilters && (
              <div className="space-y-4 pt-2">
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Date From</label>
                    <div className="relative">
                      <Calendar className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                      <Input
                        type="date"
                        value={filterDateFrom}
                        onChange={(e) => setFilterDateFrom(e.target.value)}
                        className="pl-8"
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Date To</label>
                    <div className="relative">
                      <Calendar className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                      <Input
                        type="date"
                        value={filterDateTo}
                        onChange={(e) => setFilterDateTo(e.target.value)}
                        className="pl-8"
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Min Profit</label>
                    <div className="relative">
                      <span className="absolute left-2.5 top-2.5 text-muted-foreground">$</span>
                      <Input
                        type="number"
                        placeholder="Min"
                        value={filterProfitMin}
                        onChange={(e) => setFilterProfitMin(e.target.value)}
                        className="pl-7"
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Max Profit</label>
                    <div className="relative">
                      <span className="absolute left-2.5 top-2.5 text-muted-foreground">$</span>
                      <Input
                        type="number"
                        placeholder="Max"
                        value={filterProfitMax}
                        onChange={(e) => setFilterProfitMax(e.target.value)}
                        className="pl-7"
                      />
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Min Volume</label>
                    <div className="relative">
                      <Input
                        type="number"
                        placeholder="Min"
                        value={filterVolumeMin}
                        onChange={(e) => setFilterVolumeMin(e.target.value)}
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Max Volume</label>
                    <div className="relative">
                      <Input
                        type="number"
                        placeholder="Max"
                        value={filterVolumeMax}
                        onChange={(e) => setFilterVolumeMax(e.target.value)}
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Min Duration (min)</label>
                    <div className="relative">
                      <Input
                        type="number"
                        placeholder="Min"
                        value={filterDurationMin}
                        onChange={(e) => setFilterDurationMin(e.target.value)}
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Max Duration (min)</label>
                    <div className="relative">
                      <Input
                        type="number"
                        placeholder="Max"
                        value={filterDurationMax}
                        onChange={(e) => setFilterDurationMax(e.target.value)}
                      />
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Status</label>
                    <Select
                      value={filterStatus || "all"}
                      onValueChange={(value) => setFilterStatus(value === "all" ? null : value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="All Statuses" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Statuses</SelectItem>
                        <SelectItem value="open">Open</SelectItem>
                        <SelectItem value="closed">Closed</SelectItem>
                        <SelectItem value="pending">Pending</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Strategy</label>
                    <Select
                      value={filterStrategy || "all"}
                      onValueChange={(value) => setFilterStrategy(value === "all" ? null : value === "none" ? "none" : value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="All Strategies" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Strategies</SelectItem>
                        <SelectItem value="none">No Strategy</SelectItem>
                        {strategies.map(strategy => (
                          <SelectItem key={strategy.id} value={strategy.id}>{strategy.name}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Rows Per Page</label>
                    <Select
                      value={pageSize.toString()}
                      onValueChange={(value) => setPageSize(parseInt(value))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="10" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="10">10</SelectItem>
                        <SelectItem value="25">25</SelectItem>
                        <SelectItem value="50">50</SelectItem>
                        <SelectItem value="100">100</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="sm:col-span-2 lg:col-span-1 flex items-end">
                    <Button
                      variant="secondary"
                      onClick={resetFilters}
                      className="w-full"
                    >
                      Reset Filters
                    </Button>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Stats Cards */}
          <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 trades-stats" data-section="trades-stats">
            <StatsCard
              title="Total Trades"
              value={stats.totalTrades}
              valueClassName="text-blue-500"
            />
            <StatsCard
              title="Win Rate"
              value={`${stats.winRate}%`}
              useDualColorLinearGauge={true}
              leftValue={trades.filter(t => t.profit > 0).length}
              rightValue={trades.filter(t => t.profit < 0).length}
              leftColor="bg-emerald-500"
              rightColor="bg-rose-500"
              leftLabel={`${trades.filter(t => t.profit > 0).length} Win`}
              rightLabel={`${trades.filter(t => t.profit < 0).length} Loss`}
            />
            <StatsCard
              title="Total P&L"
              value={`$${stats.totalProfit}`}
              valueClassName={parseFloat(stats.totalProfit) >= 0 ? "text-emerald-500" : "text-rose-500"}
            />
            <StatsCard
              title="Avg Win/Avg Loss"
              value={stats.winLossRatio}
              useDualColorLinearGauge={true}
              leftValue={parseFloat(stats.avgWin)}
              rightValue={parseFloat(stats.avgLoss)}
              leftColor="bg-emerald-500"
              rightColor="bg-rose-500"
              leftLabel={`$${stats.avgWin}`}
              rightLabel={`$${stats.avgLoss}`}
            />
          </div>

          {/* Trades Table */}
          <Card className="dashboard-card overflow-hidden bg-card" data-section="trades-table">
            <div className="overflow-x-auto" data-section="trades-table-content">
              <Table>
                <TableHeader>
                  <TableRow className="border-b border-border hover:bg-transparent">
                    <TableHead className="cursor-pointer" onClick={() => handleSort("time_open")}>
                      Entry Date <ArrowUpDown className="ml-2 h-4 w-4 inline" />
                    </TableHead>
                    <TableHead className="cursor-pointer hidden md:table-cell" onClick={() => handleSort("time_close")}>
                      Exit Date <ArrowUpDown className="ml-2 h-4 w-4 inline" />
                    </TableHead>
                    <TableHead className="cursor-pointer" onClick={() => handleSort("symbol")}>
                      Symbol <ArrowUpDown className="ml-2 h-4 w-4 inline" />
                    </TableHead>
                    <TableHead className="cursor-pointer" onClick={() => handleSort("type")}>
                      Type <ArrowUpDown className="ml-2 h-4 w-4 inline" />
                    </TableHead>
                    <TableHead className="hidden lg:table-cell">Volume</TableHead>
                    <TableHead className="hidden lg:table-cell">Entry Price</TableHead>
                    <TableHead className="hidden lg:table-cell">Exit Price</TableHead>
                    <TableHead className="hidden md:table-cell">Duration</TableHead>
                    <TableHead className="cursor-pointer" onClick={() => handleSort("profit")}>
                      NET P/L <ArrowUpDown className="ml-2 h-4 w-4 inline" />
                    </TableHead>
                    <TableHead className="hidden md:table-cell">Status</TableHead>
                    <TableHead className="hidden lg:table-cell">Strategy</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {paginatedTrades.map((trade) => (
                    <TableRow
                      key={trade.id}
                      className="cursor-pointer hover:bg-muted/50 border-b border-border"
                      onClick={() => router.push(`/trades/${trade.id}`)}
                    >
                      <TableCell className="font-medium">
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <span className="cursor-help">
                                {format(new Date(trade.time_open), "MMM d, yyyy HH:mm")}
                              </span>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>{format(new Date(trade.time_open), "EEEE, MMMM d, yyyy 'at' HH:mm:ss")}</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </TableCell>
                      <TableCell className="font-medium hidden md:table-cell">
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <span className="cursor-help">
                                {format(new Date(trade.time_close), "MMM d, yyyy HH:mm")}
                              </span>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>{format(new Date(trade.time_close), "EEEE, MMMM d, yyyy 'at' HH:mm:ss")}</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </TableCell>
                      <TableCell>{trade.symbol}</TableCell>
                      <TableCell className={trade.type === "buy" ? "text-emerald-500" : "text-rose-500"}>
                        {trade.type.toUpperCase()}
                      </TableCell>
                      <TableCell className="hidden lg:table-cell">{trade.volume}</TableCell>
                      <TableCell className="hidden lg:table-cell">${trade.price_open.toFixed(5)}</TableCell>
                      <TableCell className="hidden lg:table-cell">${trade.price_close.toFixed(5)}</TableCell>
                      <TableCell className="hidden md:table-cell">
                        {new Date(trade.time_open).getTime() !== new Date(trade.time_close).getTime() ?
                          formatDistanceStrict(
                            new Date(trade.time_open),
                            new Date(trade.time_close)
                          ) : "0 seconds"}
                      </TableCell>
                      <TableCell className={trade.profit >= 0 ? "text-emerald-500" : "text-rose-500"}>
                        ${trade.profit.toFixed(2)}
                      </TableCell>
                      <TableCell className="hidden md:table-cell">{trade.status}</TableCell>
                      <TableCell className="hidden lg:table-cell">
                        {trade.strategy_id ? (
                          <div className="inline-flex items-center px-2.5 py-0.5 rounded-md text-xs font-medium bg-gradient-to-r from-purple-50 to-indigo-50 dark:from-purple-900/30 dark:to-indigo-900/30 text-purple-700 dark:text-foreground border border-purple-200 dark:border-purple-700/50 shadow-sm">
                            <BookOpen className="h-3 w-3 mr-1.5 text-purple-500 dark:text-purple-400" />
                            {strategies.find(s => s.id === trade.strategy_id)?.name || "Unknown"}
                          </div>
                        ) : (
                          <span className="text-muted-foreground text-sm">None</span>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>

            <div className="flex flex-col sm:flex-row items-center justify-between p-4 border-t border-border gap-4" data-section="trades-pagination">
              <div className="text-sm text-muted-foreground text-center sm:text-left">
                Showing {((currentPage - 1) * pageSize) + 1} to {Math.min(currentPage * pageSize, filteredTrades.length)} of{" "}
                {filteredTrades.length} entries
              </div>
              <div className="flex items-center space-x-2 flex-wrap justify-center">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setCurrentPage(1);
                    updateUrl();
                  }}
                  disabled={currentPage === 1}
                >
                  First
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setCurrentPage(p => Math.max(1, p - 1));
                    updateUrl();
                  }}
                  disabled={currentPage === 1}
                >
                  Previous
                </Button>

                <div className="flex items-center space-x-1">
                  {Array.from({ length: Math.min(5, totalPages || clientTotalPages) }, (_, i) => {
                    // Show pages around current page
                    const maxPages = totalPages || clientTotalPages;
                    let pageNum;
                    if (maxPages <= 5) {
                      // If 5 or fewer pages, show all
                      pageNum = i + 1;
                    } else if (currentPage <= 3) {
                      // If near start, show first 5 pages
                      pageNum = i + 1;
                    } else if (currentPage >= maxPages - 2) {
                      // If near end, show last 5 pages
                      pageNum = maxPages - 4 + i;
                    } else {
                      // Otherwise show 2 before and 2 after current page
                      pageNum = currentPage - 2 + i;
                    }

                    return (
                      <Button
                        key={pageNum}
                        variant={currentPage === pageNum ? "default" : "outline"}
                        size="sm"
                        className="w-8 h-8 p-0"
                        onClick={() => {
                          setCurrentPage(pageNum);
                          updateUrl();
                        }}
                      >
                        {pageNum}
                      </Button>
                    );
                  })}
                </div>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setCurrentPage(p => Math.min(totalPages || clientTotalPages, p + 1));
                    updateUrl();
                  }}
                  disabled={currentPage === (totalPages || clientTotalPages)}
                >
                  Next
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setCurrentPage(totalPages || clientTotalPages);
                    updateUrl();
                  }}
                  disabled={currentPage === (totalPages || clientTotalPages)}
                >
                  Last
                </Button>
              </div>
            </div>
          </Card>
        </div>
      )}
    </div>
  )
}