"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { toast } from "sonner"
import { Setup, Strategy } from "@/types/playbook"
import { createSetup, updateSetup } from "@/lib/playbook-service"

import { But<PERSON> } from "@/components/ui/button"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { FallbackImage } from "@/components/ui/fallback-image"
import { X } from "lucide-react"

// Define the form schema
const formSchema = z.object({
  strategy_id: z.string().min(1, "Strategy is required"),
  name: z.string().min(1, "Name is required").max(100, "Name must be less than 100 characters"),
  description: z.string().optional(),
  visual_cues: z.string().optional(),
  confirmation_criteria: z.string().optional(),
  image_urls: z.array(z.string()).optional().default([]),
})

type FormValues = {
  strategy_id: string
  name: string
  description?: string
  visual_cues?: string
  confirmation_criteria?: string
  image_urls?: string[]
}

interface SetupFormProps {
  userId: string
  setup?: Setup
  strategies: Strategy[]
  preSelectedStrategyId?: string
  onSuccess?: (setup: Setup) => void
  onCancel?: () => void
}

export function SetupForm({ userId, setup, strategies, preSelectedStrategyId, onSuccess, onCancel }: SetupFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [imageUrlInput, setImageUrlInput] = useState("")
  const [imageUrls, setImageUrls] = useState<string[]>(
    setup?.image_urls || []
  )

  const isEditing = !!setup

  // Initialize form with default values or existing setup values
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      strategy_id: setup?.strategy_id || preSelectedStrategyId || "",
      name: setup?.name || "",
      description: setup?.description || "",
      visual_cues: setup?.visual_cues || "",
      confirmation_criteria: setup?.confirmation_criteria || "",
      image_urls: setup?.image_urls || [],
    },
  })

  // Handle form submission
  const onSubmit = async (values: FormValues) => {
    setIsSubmitting(true)

    try {
      let result: Setup | null

      // Ensure image_urls is always an array
      const setupData = {
        ...values,
        image_urls: values.image_urls || []
      }

      if (isEditing && setup) {
        // Update existing setup
        result = await updateSetup(userId, setup.id, setupData)
        if (result) {
          toast.success("Setup updated successfully")
        }
      } else {
        // Create new setup
        result = await createSetup(userId, setupData)
        if (result) {
          toast.success("Setup created successfully")
          form.reset() // Reset form after successful creation
          setImageUrls([])
        }
      }

      if (result && onSuccess) {
        onSuccess(result)
      }
    } catch (error) {
      console.error("Error saving setup:", error)
      toast.error("Failed to save setup")
    } finally {
      setIsSubmitting(false)
    }
  }

  // Handle adding a new image URL
  const handleAddImageUrl = () => {
    if (imageUrlInput.trim() && !imageUrls.includes(imageUrlInput.trim())) {
      const newImageUrls = [...imageUrls, imageUrlInput.trim()]
      setImageUrls(newImageUrls)
      form.setValue("image_urls", newImageUrls)
      setImageUrlInput("")
    }
  }

  // Handle removing an image URL
  const handleRemoveImageUrl = async (url: string) => {
    try {
      // Import the deleteImageFromStorage function dynamically to avoid circular dependencies
      const { deleteImageFromStorage } = await import('@/lib/image-service')

      // Attempt to delete the image from storage
      const deleted = await deleteImageFromStorage(url)

      if (!deleted) {
        console.warn('Failed to delete image from storage:', url)
        // Continue with removing the URL from the form even if storage deletion fails
      } else {
        toast.success('Image removed successfully')
      }

      // Remove the URL from the form state
      const newImageUrls = imageUrls.filter(i => i !== url)

      // Force a refresh of the image list
      setTimeout(() => {
        setImageUrls([...newImageUrls])
        form.setValue("image_urls", [...newImageUrls])
      }, 100)
    } catch (error) {
      console.error('Error removing image:', error)
      toast.error('Failed to remove image')

      // Still remove the URL from the form state to maintain UI consistency
      const newImageUrls = imageUrls.filter(i => i !== url)
      setImageUrls([...newImageUrls])
      form.setValue("image_urls", [...newImageUrls])
    }
  }

  return (
    <Card className="w-full max-w-3xl mx-auto">
      <CardHeader>
        <CardTitle>{isEditing ? "Edit Setup" : "Create New Setup"}</CardTitle>
        <CardDescription>
          {isEditing
            ? "Update your trading setup details"
            : "Document a specific setup for your trading strategy"}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="strategy_id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Strategy</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    disabled={!!preSelectedStrategyId || isEditing}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a strategy" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {strategies.map((strategy) => (
                        <SelectItem key={strategy.id} value={strategy.id}>
                          {strategy.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    The strategy this setup belongs to
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Setup Name</FormLabel>
                  <FormControl>
                    <Input placeholder="Double Bottom" {...field} />
                  </FormControl>
                  <FormDescription>
                    A clear, descriptive name for this setup
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="This setup occurs when price forms a double bottom pattern at a key support level..."
                      className="min-h-[100px]"
                      {...field}
                      value={field.value || ""}
                    />
                  </FormControl>
                  <FormDescription>
                    Describe this setup and when it typically appears
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="visual_cues"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Visual Cues</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="1. Price makes a low and bounces
2. Price retraces and then returns to test the previous low
3. Price forms a second bottom at or near the first bottom
4. Volume typically decreases on the second bottom"
                      className="min-h-[100px]"
                      {...field}
                      value={field.value || ""}
                    />
                  </FormControl>
                  <FormDescription>
                    List the visual patterns or indicators that identify this setup
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="confirmation_criteria"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Confirmation Criteria</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="1. Price breaks above the neckline (the peak between the two bottoms)
2. Increased volume on the breakout
3. RSI shows bullish divergence between the two bottoms"
                      className="min-h-[100px]"
                      {...field}
                      value={field.value || ""}
                    />
                  </FormControl>
                  <FormDescription>
                    Criteria that confirm this setup is valid and ready to trade
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="image_urls"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Example Images</FormLabel>
                  <div className="flex gap-2">
                    <FormControl>
                      <Input
                        placeholder="https://example.com/image.jpg"
                        value={imageUrlInput}
                        onChange={(e) => setImageUrlInput(e.target.value)}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter') {
                            e.preventDefault()
                            handleAddImageUrl()
                          }
                        }}
                      />
                    </FormControl>
                    <Button
                      type="button"
                      onClick={handleAddImageUrl}
                      variant="secondary"
                    >
                      Add
                    </Button>
                  </div>
                  <FormDescription>
                    Add URLs to example images of this setup
                  </FormDescription>
                  <FormMessage />
                  {imageUrls.length > 0 && (
                    <div className="space-y-2 mt-2">
                      {imageUrls.map((url, index) => (
                        <div key={index} className="flex items-center justify-between p-2 border rounded-md">
                          <div className="truncate flex-1 mr-2">
                            <a href={url} target="_blank" rel="noopener noreferrer" className="text-blue-500 hover:underline text-sm">
                              {url}
                            </a>
                          </div>
                          <Button
                            type="button"
                            variant="ghost"
                            size="icon"
                            onClick={() => handleRemoveImageUrl(url)}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      ))}
                      <div className="grid grid-cols-2 gap-2 mt-4">
                        {imageUrls.map((url, index) => (
                          <div key={`img-${index}`} className="relative aspect-video rounded-md overflow-hidden border">
                            <FallbackImage
                              src={url}
                              alt={`Setup example ${index + 1}`}
                              className="object-cover w-full h-full"
                              fallbackSrc="https://placehold.co/600x400?text=Image+Not+Found"
                            />
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </FormItem>
              )}
            />

            <CardFooter className="px-0 pb-0 pt-6 flex justify-between">
              {onCancel && (
                <Button
                  type="button"
                  variant="outline"
                  onClick={onCancel}
                  disabled={isSubmitting}
                >
                  Cancel
                </Button>
              )}
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting
                  ? "Saving..."
                  : isEditing
                  ? "Update Setup"
                  : "Create Setup"}
              </Button>
            </CardFooter>
          </form>
        </Form>
      </CardContent>
    </Card>
  )
}
