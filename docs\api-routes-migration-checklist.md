# API Routes Migration Checklist

This document provides a checklist for migrating API routes to properly use `await cookies()` and `getUser()` in Next.js 15 with Supabase.

## API Routes to Update

| API Route | Status | Cookie Handling | Authentication Method | Notes |
|-----------|--------|----------------|----------------------|-------|
| `/api/trades` | ✅ Updated | ✅ `await cookies()` | ✅ `getUser()` | Already updated |
| `/api/strategy-rules` | ✅ Updated | ✅ `await cookies()` | ✅ `getUser()` | Already updated |
| `/api/strategy-setups` | ✅ Updated | ✅ `await cookies()` | ✅ `getUser()` | Already updated |
| `/api/strategies` | ⚠️ Partial | ✅ `await cookies()` | ✅ `getUser()` | Cookie handling updated, but may need to check all handlers |
| `/api/strategy-performance` | ⚠️ Partial | ✅ `await cookies()` | ✅ `getUser()` | Cookie handling updated, but may need to check all handlers |
| `/api/journal-entries` | ❌ Not Updated | ❌ Needs update | ❌ Needs update | Needs to be created or updated |
| `/api/journal-tags` | ❌ Not Updated | ❌ Needs update | ❌ Needs update | Needs to be created or updated |
| `/api/analytics-data` | ❌ Not Updated | ❌ Needs update | ❌ Needs update | Needs to be created or updated |
| `/api/analytics-metrics` | ❌ Not Updated | ❌ Needs update | ❌ Needs update | Needs to be created or updated |
| `/api/user-profile` | ❌ Not Updated | ❌ Needs update | ❌ Needs update | Needs to be created or updated |
| `/api/user-settings` | ❌ Not Updated | ❌ Needs update | ❌ Needs update | Needs to be created or updated |
| `/api/test-storage` | ⚠️ Review | ❌ N/A | ❌ N/A | Uses direct Supabase client, may not need cookies |
| `/api/create-storage-policy` | ⚠️ Review | ❌ N/A | ❌ N/A | Uses direct Supabase client, may not need cookies |

## Update Checklist for Each API Route

### 1. Cookie Handling

- [ ] Update `cookies()` to be awaited:
  ```tsx
  // Before
  const cookieStore = cookies();
  
  // After
  const cookieStore = await cookies();
  ```

- [ ] Implement all required cookie methods:
  ```tsx
  const supabase = createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value;
        },
        set(_name: string, _value: string, _options: any) {
          // API routes can't set cookies directly
        },
        remove(_name: string, _options: any) {
          // API routes can't remove cookies directly
        }
      },
    }
  );
  ```

### 2. Authentication Method

- [ ] Update authentication method to use `getUser()`:
  ```tsx
  // Before
  const { data: { session } } = await supabase.auth.getSession();
  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  
  const userId = session.user.id;
  
  // After
  const { data: { user }, error: userError } = await supabase.auth.getUser();
  if (userError || !user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  
  const userId = user.id;
  ```

### 3. Error Handling

- [ ] Implement proper error handling for authentication:
  ```tsx
  if (userError || !user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  ```

- [ ] Implement proper error handling for database operations:
  ```tsx
  if (error) {
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
  ```

- [ ] Implement proper error handling for the overall request:
  ```tsx
  try {
    // Request handling
  } catch (error) {
    console.error('Error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
  ```

## Template for New API Routes

```tsx
import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { createServerClient } from '@supabase/ssr';
import type { Database } from '@/types/supabase';

export async function GET(request: Request) {
  try {
    // Get URL parameters
    const url = new URL(request.url);
    const resourceId = url.searchParams.get('id');

    // Get cookies for server-side Supabase client
    const cookieStore = await cookies();
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
          set(_name: string, _value: string, _options: any) {
            // API routes can't set cookies directly
          },
          remove(_name: string, _options: any) {
            // API routes can't remove cookies directly
          }
        },
      }
    );

    // Get authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    const userId = user.id;

    // Fetch data
    const { data, error } = await supabase
      .from('your_table')
      .select('*')
      .eq('user_id', userId);

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json(data || []);
  } catch (error) {
    console.error('Error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
```

## Testing Checklist

After updating an API route, test the following:

1. **Authentication**:
   - [ ] Verify that unauthenticated requests are rejected with a 401 status code
   - [ ] Verify that authenticated requests are processed correctly

2. **Data Access**:
   - [ ] Verify that users can only access their own data
   - [ ] Verify that data is filtered correctly based on query parameters

3. **Error Handling**:
   - [ ] Verify that errors are handled gracefully
   - [ ] Verify that appropriate error messages are returned

4. **Performance**:
   - [ ] Verify that the API route performs well under load
   - [ ] Verify that the API route returns data in a timely manner
