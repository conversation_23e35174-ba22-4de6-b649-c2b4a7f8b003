# 🎉 MAJOR BREAKTHROUGH: CORRECT REPO + N<PERSON><PERSON>CKS SYNTAX FIX!

## ⏰ Latest Status:
- **Latest Error**: 2025-Jun-03 15:08:14Z ⚠️
- **BREAKTHROUGH**: Coolify using CORRECT repository! ✅
- **BREAKTHROUGH**: Nixpacks detecting our nixpacks.toml! ✅
- **Issue**: `invalid type: map, expected a sequence for key 'providers'`
- **Solution**: Fixed nixpacks.toml syntax ✅

## 🎉 DOUBLE BREAKTHROUGH ANALYSIS:

### **✅ SUCCESS #1: Correct Repository Configuration!**
Coolify is now successfully:
1. **✅ Using CORRECT repository** - `shawa0507/trade_journal_production:main`
2. **✅ Cloning successfully** - No more wrong repository errors
3. **✅ Finding our files** - GitHub Actions sync working perfectly
4. **✅ Detecting nixpacks.toml** - Nixpacks trying to parse our configuration

### **✅ SUCCESS #2: Nixpacks Detection Working!**
The error shows Nixpacks is:
1. **✅ Finding nixpacks.toml** - `Failed to parse Nixpacks config file nixpacks.toml`
2. **✅ Attempting to use it** - No more auto-detection of PNPM
3. **✅ Following our configuration** - This is exactly what we wanted!

### **⚠️ SYNTAX FIXES APPLIED:**
The error `invalid type: map, expected a sequence for key 'providers'` was caused by:
1. **Fixed providers syntax**: `[providers] node = "18"` → `providers = ["node"]`
2. **Fixed NODE_VERSION syntax**: `[variables.NODE_VERSION] value = "18"` → `NIXPACKS_NODE_VERSION = "18"`
3. **Moved to main variables section**: According to Nixpacks docs

### **✅ CURRENT NIXPACKS.TOML (CORRECTED):**
```toml
[variables]
NODE_ENV = "production"
NIXPACKS_NO_CACHE = "1"
NPM_CONFIG_PACKAGE_MANAGER = "npm"
COREPACK_ENABLE_STRICT = "0"
NIXPACKS_NODE_VERSION = "18"

providers = ["node"]

[phases.setup]
nixPkgs = ["nodejs_18", "npm"]
cmds = [
  "echo 'Forcing npm usage - disabling corepack and pnpm'",
  "npm config set package-manager npm",
  "rm -f pnpm-lock.yaml || true",
  "rm -f yarn.lock || true",
  "which npm && npm --version"
]

[phases.install]
cmd = "npm ci --include=dev --no-fund --no-audit"

[phases.build]
cmd = "npm run build"

[start]
cmd = "npm start"
```

## 💥 NUCLEAR SOLUTION IMPLEMENTED:

### **Complete Nixpacks Bypass:**
- ❌ **Removed nixpacks.toml** to force Dockerfile detection
- ✅ **Created bulletproof Dockerfile** with Node.js 18 Alpine
- ✅ **Multiple npm enforcement layers**
- ✅ **Aggressive PNPM removal** at every step
- ✅ **Optimized .dockerignore** for clean builds
- ✅ **Health checks** and proper caching

## 🛠️ SOLUTION IMPLEMENTED:

### **✅ Added Dockerfile to Repository Root:**
- Created bulletproof Dockerfile directly in repository
- Node.js 18 Alpine with aggressive npm enforcement
- Multi-stage build optimization and health checks
- Verbose logging for debugging

### **Expected Result:**
Coolify should now successfully find and use the Dockerfile, completely eliminating both the Nixpacks and PNPM issues.

## 📋 NEXT STEPS:
1. **Trigger GitHub Actions workflow** to sync Dockerfile to production
2. **Monitor for successful Dockerfile build** instead of Nixpacks
3. **Verify npm commands** in build logs (should see `npm ci`)
4. **Confirm successful deployment** without errors

## 🎯 SUCCESS INDICATORS TO WATCH FOR:
- ✅ **Dockerfile detection**: `FROM node:18-alpine` in logs
- ✅ **npm usage**: `npm ci --include=dev` commands
- ✅ **No PNPM references**: No `pnpm-9.15.9.tgz` downloads
- ✅ **Successful build**: Application builds and starts correctly

---

## Previous Error (NIXPACKS FAILURE - 14:25:16Z):
2025-Jun-03 14:25:16.178504
#0 building with "default" instance using docker driver
2025-Jun-03 14:25:16.***********-Jun-03 14:25:16.178504
#1 [internal] load build definition from Dockerfile
2025-Jun-03 14:25:16.178504
#1 transferring dockerfile: 1.04kB done
2025-Jun-03 14:25:16.178504
#1 DONE 0.1s
2025-Jun-03 14:25:16.***********-Jun-03 14:25:16.178504
#2 [internal] load metadata for ghcr.io/railwayapp/nixpacks:ubuntu-1741046653
2025-Jun-03 14:25:16.178504
#2 DONE 0.3s
2025-Jun-03 14:25:16.***********-Jun-03 14:25:16.178504
#3 [internal] load .dockerignore
2025-Jun-03 14:25:16.178504
#3 transferring context: 2B done
2025-Jun-03 14:25:16.178504
#3 DONE 0.0s
2025-Jun-03 14:25:16.***********-Jun-03 14:25:16.178504
#4 [ 1/12] FROM ghcr.io/railwayapp/nixpacks:ubuntu-1741046653@sha256:ed406b77fb751927991b8655e76c33a4521c4957c2afeab293be7c63c2a373d2
2025-Jun-03 14:25:16.178504
#4 DONE 0.0s
2025-Jun-03 14:25:16.***********-Jun-03 14:25:16.178504
#5 [ 2/12] WORKDIR /app/
2025-Jun-03 14:25:16.178504
#5 CACHED
2025-Jun-03 14:25:16.***********-Jun-03 14:25:16.178504
#6 [internal] load build context
2025-Jun-03 14:25:16.178504
#6 transferring context: 1.26MB 0.1s done
2025-Jun-03 14:25:16.178504
#6 DONE 0.1s
2025-Jun-03 14:25:16.***********-Jun-03 14:25:16.178504
#7 [ 3/12] COPY .nixpacks/nixpkgs-ffeebf0acf3ae8b29f8c7049cd911b9636efd7e7.nix .nixpacks/nixpkgs-ffeebf0acf3ae8b29f8c7049cd911b9636efd7e7.nix
2025-Jun-03 14:25:16.178504
#7 DONE 0.1s
2025-Jun-03 14:25:16.***********-Jun-03 14:25:16.178504
#8 [ 4/12] RUN nix-env -if .nixpacks/nixpkgs-ffeebf0acf3ae8b29f8c7049cd911b9636efd7e7.nix && nix-collect-garbage -d
2025-Jun-03 14:25:16.178504
#8 0.304 unpacking 'https://github.com/NixOS/nixpkgs/archive/ffeebf0acf3ae8b29f8c7049cd911b9636efd7e7.tar.gz' into the Git cache...
2025-Jun-03 14:25:16.178504
#8 72.97 unpacking 'https://github.com/railwayapp/nix-npm-overlay/archive/main.tar.gz' into the Git cache...
2025-Jun-03 14:25:16.178504
#8 74.05 installing 'ffeebf0acf3ae8b29f8c7049cd911b9636efd7e7-env'
2025-Jun-03 14:25:16.178504
#8 75.37 these 6 derivations will be built:
2025-Jun-03 14:25:16.178504
#8 75.37   /nix/store/crmicnx1sb63i6q6md7i8vz9mxb5cair-pnpm-9.15.9.tgz.drv
2025-Jun-03 14:25:16.178504
#8 75.37   /nix/store/39q7099jg9cxpbhan0w51gs7m1drhrwg-pnpm.drv
2025-Jun-03 14:25:16.178504
#8 75.37   /nix/store/9smjjb5pkmcbykz8p4786s3a4nq6m030-builder.pl.drv
2025-Jun-03 14:25:16.178504
#8 75.37   /nix/store/cjdjkmr6gy2h8l0cra71whgrvy030kx1-libraries.drv
2025-Jun-03 14:25:16.178504
#8 75.37   /nix/store/bs6g8vhkfynvlzidhlqbsvnc9wijbaaz-ffeebf0acf3ae8b29f8c7049cd911b9636efd7e7-env.drv
2025-Jun-03 14:25:16.178504
#8 75.37   /nix/store/l2729dz3h90v78yrv436lv4wm0slgycw-ffeebf0acf3ae8b29f8c7049cd911b9636efd7e7-env.drv
2025-Jun-03 14:25:16.178504
#8 75.37 these 74 paths will be fetched (116.25 MiB download, 554.75 MiB unpacked):
2025-Jun-03 14:25:16.178504
#8 75.37   /nix/store/cf7gkacyxmm66lwl5nj6j6yykbrg4q5c-acl-2.3.2
2025-Jun-03 14:25:16.178504
#8 75.37   /nix/store/a9jgnlhkjkxav6qrc3rzg2q84pkl2wvr-attr-2.5.2
2025-Jun-03 14:25:16.178504
#8 75.37   /nix/store/5mh7kaj2fyv8mk4sfq1brwxgc02884wi-bash-5.2p37
2025-Jun-03 14:25:16.178504
#8 75.37   /nix/store/j7p46r8v9gcpbxx89pbqlh61zhd33gzv-binutils-2.43.1
2025-Jun-03 14:25:16.178504
#8 75.37   /nix/store/df2a8k58k00f2dh2x930dg6xs6g6mliv-binutils-2.43.1-lib
2025-Jun-03 14:25:16.178504
#8 75.37   /nix/store/srcmmqi8kxjfygd0hyy42c8hv6cws83b-binutils-wrapper-2.43.1
2025-Jun-03 14:25:16.178504
#8 75.37   /nix/store/ivl2v8rgg7qh1jkj5pwpqycax3rc2hnl-bzip2-1.0.8
2025-Jun-03 14:25:16.178504
#8 75.37   /nix/store/mglixp03lsp0w986svwdvm7vcy17rdax-bzip2-1.0.8-bin
2025-Jun-03 14:25:16.178504
#8 75.37   /nix/store/4s9rah4cwaxflicsk5cndnknqlk9n4p3-coreutils-9.5
2025-Jun-03 14:25:16.178504
#8 75.37   /nix/store/pkc7mb4a4qvyz73srkqh4mwl70w98dsv-curl-8.11.0
2025-Jun-03 14:25:16.178504
#8 75.37   /nix/store/p123cq20klajcl9hj8jnkjip5nw6awhz-curl-8.11.0-bin
2025-Jun-03 14:25:16.178504
#8 75.37   /nix/store/5f5linrxzhhb3mrclkwdpm9bd8ygldna-curl-8.11.0-dev
2025-Jun-03 14:25:16.178504
#8 75.37   /nix/store/agvks3qmzja0yj54szi3vja6vx3cwkkw-curl-8.11.0-man
2025-Jun-03 14:25:16.178504
#8 75.37   /nix/store/00g69vw7c9lycy63h45ximy0wmzqx5y6-diffutils-3.10
2025-Jun-03 14:25:16.178504
#8 75.37   /nix/store/74h4z8k82pmp24xryflv4lxkz8jlpqqd-ed-1.20.2
2025-Jun-03 14:25:16.178504
#8 75.37   /nix/store/qbry6090vlr9ar33kdmmbq2p5apzbga8-expand-response-params
2025-Jun-03 14:25:16.178504
#8 75.37   /nix/store/c4rj90r2m89rxs64hmm857mipwjhig5d-file-5.46
2025-Jun-03 14:25:16.178504
#8 75.37   /nix/store/jqrz1vq5nz4lnv9pqzydj0ir58wbjfy1-findutils-4.10.0
2025-Jun-03 14:25:16.178504
#8 75.37   /nix/store/a3c47r5z1q2c4rz0kvq8hlilkhx2s718-gawk-5.3.1
2025-Jun-03 14:25:16.178504
#8 75.38   /nix/store/l89iqc7am6i60y8vk507zwrzxf0wcd3v-gcc-14-20241116
2025-Jun-03 14:25:16.178504
#8 75.38   /nix/store/bpq1s72cw9qb2fs8mnmlw6hn2c7iy0ss-gcc-14-20241116-lib
2025-Jun-03 14:25:16.178504
#8 75.38   /nix/store/17v0ywnr3akp85pvdi56gwl99ljv95kx-gcc-14-20241116-libgcc
2025-Jun-03 14:25:16.178504
#8 75.38   /nix/store/xcn9p4xxfbvlkpah7pwchpav4ab9d135-gcc-wrapper-14-20241116
2025-Jun-03 14:25:16.178504
#8 75.38   /nix/store/65h17wjrrlsj2rj540igylrx7fqcd6vq-glibc-2.40-36
2025-Jun-03 14:25:16.178504
#8 75.38   /nix/store/1c6bmxrrhm8bd26ai2rjqld2yyjrxhds-glibc-2.40-36-bin
2025-Jun-03 14:25:16.178504
#8 75.38   /nix/store/kj8hbqx4ds9qm9mq7hyikxyfwwg13kzj-glibc-2.40-36-dev
2025-Jun-03 14:25:16.178504
#8 75.38   /nix/store/kryrg7ds05iwcmy81amavk8w13y4lxbs-gmp-6.3.0
2025-Jun-03 14:25:16.178504
#8 75.38   /nix/store/a2byxfv4lc8f2g5xfzw8cz5q8k05wi29-gmp-with-cxx-6.3.0
2025-Jun-03 14:25:16.178504
#8 75.38   /nix/store/1m67ipsk39xvhyqrxnzv2m2p48pil8kl-gnu-config-2024-01-01
2025-Jun-03 14:25:16.178504
#8 75.38   /nix/store/aap6cq56amx4mzbyxp2wpgsf1kqjcr1f-gnugrep-3.11
2025-Jun-03 14:25:16.178504
#8 75.38   /nix/store/fp6cjl1zcmm6mawsnrb5yak1wkz2ma8l-gnumake-4.4.1
2025-Jun-03 14:25:16.178504
#8 75.38   /nix/store/abm77lnrkrkb58z6xp1qwjcr1xgkcfwm-gnused-4.9
2025-Jun-03 14:25:16.178504
#8 75.38   /nix/store/9cwwj1c9csmc85l2cqzs3h9hbf1vwl6c-gnutar-1.35
2025-Jun-03 14:25:16.178504
#8 75.38   /nix/store/nvvj6sk0k6px48436drlblf4gafgbvzr-gzip-1.13
2025-Jun-03 14:25:16.178504
#8 75.38   /nix/store/wwipgdqb4p2fr46kmw9c5wlk799kbl68-icu4c-74.2
2025-Jun-03 14:25:16.178504
#8 75.38   /nix/store/m8w3mf0i4862q22bxad0wspkgdy4jnkk-icu4c-74.2-dev
2025-Jun-03 14:25:16.178504
#8 75.38   /nix/store/xmbv8s4p4i4dbxgkgdrdfb0ym25wh6gk-isl-0.20
2025-Jun-03 14:25:16.178504
#8 75.38   /nix/store/2wh1gqyzf5xsvxpdz2k0bxiz583wwq29-keyutils-1.6.3-lib
2025-Jun-03 14:25:16.178504
#8 75.38   /nix/store/milph81dilrh96isyivh5n50agpx39k2-krb5-1.21.3
2025-Jun-03 14:25:16.178504
#8 75.38   /nix/store/b56mswksrql15knpb1bnhv3ysif340kd-krb5-1.21.3-dev
2025-Jun-03 14:25:16.178504
#8 75.38   /nix/store/v9c1s50x7magpiqgycxxkn36avzbcg0g-krb5-1.21.3-lib
2025-Jun-03 14:25:16.178504
#8 75.38   /nix/store/34z2792zyd4ayl5186vx0s98ckdaccz9-libidn2-2.3.7
2025-Jun-03 14:25:16.178504
#8 75.38   /nix/store/2a3anh8vl3fcgk0fvaravlimrqawawza-libmpc-1.3.1
2025-Jun-03 14:25:16.178504
#8 75.38   /nix/store/8675pnfr4fqnwv4pzjl67hdwls4q13aa-libssh2-1.11.1
2025-Jun-03 14:25:16.178504
#8 75.38   /nix/store/d7zhcrcc7q3yfbm3qkqpgc3daq82spwi-libssh2-1.11.1-dev
2025-Jun-03 14:25:16.178504
#8 75.38   /nix/store/xcqcgqazykf6s7fsn08k0blnh0wisdcl-libunistring-1.3
2025-Jun-03 14:25:16.178504
#8 75.38   /nix/store/r9ac2hwnmb0nxwsrvr6gi9wsqf2whfqj-libuv-1.49.2
2025-Jun-03 14:25:16.178504
#8 75.38   /nix/store/ll14czvpxglf6nnwmmrmygplm830fvlv-libuv-1.49.2-dev
2025-Jun-03 14:25:16.178504
#8 75.38   /nix/store/6cr0spsvymmrp1hj5n0kbaxw55w1lqyp-libxcrypt-4.4.36
2025-Jun-03 14:25:16.178504
#8 75.38   /nix/store/pc74azbkr19rkd5bjalq2xwx86cj3cga-linux-headers-6.12
2025-Jun-03 14:25:16.178504
#8 75.38   /nix/store/fv7gpnvg922frkh81w5hkdhpz0nw3iiz-mirrors-list
2025-Jun-03 14:25:16.178504
#8 75.38   /nix/store/qs22aazzrdd4dnjf9vffl0n31hvls43h-mpfr-4.2.1
2025-Jun-03 14:25:16.178504
#8 75.38   /nix/store/grixvx878884hy8x3xs0c0s1i00j632k-nghttp2-1.64.0
2025-Jun-03 14:25:16.178504
#8 75.38   /nix/store/dz97fw51rm5bl9kz1vg0haj1j1a7r1mr-nghttp2-1.64.0-dev
2025-Jun-03 14:25:16.178504
#8 75.38   /nix/store/qcghigzrz56vczwlzg9c02vbs6zr9jkz-nghttp2-1.64.0-lib
2025-Jun-03 14:25:16.178504
#8 75.38   /nix/store/wlpq101dsifq98z2bk300x4dk80wcybr-nodejs-18.20.5
2025-Jun-03 14:25:16.178504
#8 75.38   /nix/store/9l9n7a0v4aibcz0sgd0crs209an9p7dz-openssl-3.3.2
2025-Jun-03 14:25:16.178504
#8 75.38   /nix/store/h1ydpxkw9qhjdxjpic1pdc2nirggyy6f-openssl-3.3.2
2025-Jun-03 14:25:16.178504
#8 75.38   /nix/store/lygl27c44xv73kx1spskcgvzwq7z337c-openssl-3.3.2-bin
2025-Jun-03 14:25:16.178504
#8 75.38   /nix/store/qq5q0alyzywdazhmybi7m69akz0ppk05-openssl-3.3.2-bin
2025-Jun-03 14:25:16.178504
#8 75.38   /nix/store/kqm7wpqkzc4bwjlzqizcbz0mgkj06a9x-openssl-3.3.2-dev
2025-Jun-03 14:25:16.178504
#8 75.38   /nix/store/pp2zf8bdgyz60ds8vcshk2603gcjgp72-openssl-3.3.2-dev
2025-Jun-03 14:25:16.178504
#8 75.38   /nix/store/5yja5dpk2qw1v5mbfbl2d7klcdfrh90w-patch-2.7.6
2025-Jun-03 14:25:16.178504
#8 75.38   /nix/store/srfxqk119fijwnprgsqvn68ys9kiw0bn-patchelf-0.15.0
2025-Jun-03 14:25:16.178504
#8 75.38   /nix/store/3j1p598fivxs69wx3a657ysv3rw8k06l-pcre2-10.44
2025-Jun-03 14:25:16.178504
#8 75.38   /nix/store/1i003ijlh9i0mzp6alqby5hg3090pjdx-perl-5.40.0
2025-Jun-03 14:25:16.178504
#8 75.38   /nix/store/4ig84cyqi6qy4n0sanrbzsw1ixa497jx-stdenv-linux
2025-Jun-03 14:25:16.178504
#8 75.38   /nix/store/d0gfdcag8bxzvg7ww4s7px4lf8sxisyx-stdenv-linux
2025-Jun-03 14:25:16.178504
#8 75.38   /nix/store/d29r1bdmlvwmj52apgcdxfl1mm9c5782-update-autotools-gnu-config-scripts-hook
2025-Jun-03 14:25:16.178504
#8 75.38   /nix/store/acfkqzj5qrqs88a4a6ixnybbjxja663d-xgcc-14-20241116-libgcc
2025-Jun-03 14:25:16.178504
#8 75.38   /nix/store/c2njy6bv84kw1i4bjf5k5gn7gz8hn57n-xz-5.6.3
2025-Jun-03 14:25:16.178504
#8 75.38   /nix/store/h18s640fnhhj2qdh5vivcfbxvz377srg-xz-5.6.3-bin
2025-Jun-03 14:25:16.178504
#8 75.38   /nix/store/cqlaa2xf6lslnizyj9xqa8j0ii1yqw0x-zlib-1.3.1
2025-Jun-03 14:25:16.178504
#8 75.38   /nix/store/1lggwqzapn5mn49l9zy4h566ysv9kzdb-zlib-1.3.1-dev
2025-Jun-03 14:25:16.178504
#8 75.51 copying path '/nix/store/17v0ywnr3akp85pvdi56gwl99ljv95kx-gcc-14-20241116-libgcc' from 'https://cache.nixos.org'...
2025-Jun-03 14:25:16.178504
#8 75.51 copying path '/nix/store/acfkqzj5qrqs88a4a6ixnybbjxja663d-xgcc-14-20241116-libgcc' from 'https://cache.nixos.org'...
2025-Jun-03 14:25:16.178504
#8 75.51 copying path '/nix/store/xcqcgqazykf6s7fsn08k0blnh0wisdcl-libunistring-1.3' from 'https://cache.nixos.org'...
2025-Jun-03 14:25:16.178504
#8 75.51 copying path '/nix/store/1m67ipsk39xvhyqrxnzv2m2p48pil8kl-gnu-config-2024-01-01' from 'https://cache.nixos.org'...
2025-Jun-03 14:25:16.178504
#8 75.54 copying path '/nix/store/pc74azbkr19rkd5bjalq2xwx86cj3cga-linux-headers-6.12' from 'https://cache.nixos.org'...
2025-Jun-03 14:25:16.178504
#8 75.54 copying path '/nix/store/fv7gpnvg922frkh81w5hkdhpz0nw3iiz-mirrors-list' from 'https://cache.nixos.org'...
2025-Jun-03 14:25:16.178504
#8 75.54 copying path '/nix/store/grixvx878884hy8x3xs0c0s1i00j632k-nghttp2-1.64.0' from 'https://cache.nixos.org'...
2025-Jun-03 14:25:16.178504
#8 75.54 copying path '/nix/store/agvks3qmzja0yj54szi3vja6vx3cwkkw-curl-8.11.0-man' from 'https://cache.nixos.org'...
2025-Jun-03 14:25:16.178504
#8 75.62 copying path '/nix/store/d29r1bdmlvwmj52apgcdxfl1mm9c5782-update-autotools-gnu-config-scripts-hook' from 'https://cache.nixos.org'...
2025-Jun-03 14:25:16.178504
#8 75.65 copying path '/nix/store/34z2792zyd4ayl5186vx0s98ckdaccz9-libidn2-2.3.7' from 'https://cache.nixos.org'...
2025-Jun-03 14:25:16.178504
#8 75.71 copying path '/nix/store/65h17wjrrlsj2rj540igylrx7fqcd6vq-glibc-2.40-36' from 'https://cache.nixos.org'...
2025-Jun-03 14:25:16.178504
#8 76.43 copying path '/nix/store/5mh7kaj2fyv8mk4sfq1brwxgc02884wi-bash-5.2p37' from 'https://cache.nixos.org'...
2025-Jun-03 14:25:16.178504
#8 76.43 copying path '/nix/store/a9jgnlhkjkxav6qrc3rzg2q84pkl2wvr-attr-2.5.2' from 'https://cache.nixos.org'...
2025-Jun-03 14:25:16.178504
#8 76.43 copying path '/nix/store/a3c47r5z1q2c4rz0kvq8hlilkhx2s718-gawk-5.3.1' from 'https://cache.nixos.org'...
2025-Jun-03 14:25:16.178504
#8 76.43 copying path '/nix/store/1c6bmxrrhm8bd26ai2rjqld2yyjrxhds-glibc-2.40-36-bin' from 'https://cache.nixos.org'...
2025-Jun-03 14:25:16.178504
#8 76.43 copying path '/nix/store/ivl2v8rgg7qh1jkj5pwpqycax3rc2hnl-bzip2-1.0.8' from 'https://cache.nixos.org'...
2025-Jun-03 14:25:16.178504
#8 76.43 copying path '/nix/store/fp6cjl1zcmm6mawsnrb5yak1wkz2ma8l-gnumake-4.4.1' from 'https://cache.nixos.org'...
2025-Jun-03 14:25:16.178504
#8 76.43 copying path '/nix/store/74h4z8k82pmp24xryflv4lxkz8jlpqqd-ed-1.20.2' from 'https://cache.nixos.org'...
2025-Jun-03 14:25:16.178504
#8 76.43 copying path '/nix/store/qbry6090vlr9ar33kdmmbq2p5apzbga8-expand-response-params' from 'https://cache.nixos.org'...
2025-Jun-03 14:25:16.178504
#8 76.43 copying path '/nix/store/kryrg7ds05iwcmy81amavk8w13y4lxbs-gmp-6.3.0' from 'https://cache.nixos.org'...
2025-Jun-03 14:25:16.178504
#8 76.43 copying path '/nix/store/bpq1s72cw9qb2fs8mnmlw6hn2c7iy0ss-gcc-14-20241116-lib' from 'https://cache.nixos.org'...
2025-Jun-03 14:25:16.178504
#8 76.44 copying path '/nix/store/abm77lnrkrkb58z6xp1qwjcr1xgkcfwm-gnused-4.9' from 'https://cache.nixos.org'...
2025-Jun-03 14:25:16.178504
#8 76.44 copying path '/nix/store/2wh1gqyzf5xsvxpdz2k0bxiz583wwq29-keyutils-1.6.3-lib' from 'https://cache.nixos.org'...
2025-Jun-03 14:25:16.178504
#8 76.44 copying path '/nix/store/r9ac2hwnmb0nxwsrvr6gi9wsqf2whfqj-libuv-1.49.2' from 'https://cache.nixos.org'...
2025-Jun-03 14:25:16.178504
#8 76.44 copying path '/nix/store/6cr0spsvymmrp1hj5n0kbaxw55w1lqyp-libxcrypt-4.4.36' from 'https://cache.nixos.org'...
2025-Jun-03 14:25:16.178504
#8 76.45 copying path '/nix/store/9l9n7a0v4aibcz0sgd0crs209an9p7dz-openssl-3.3.2' from 'https://cache.nixos.org'...
2025-Jun-03 14:25:16.178504
#8 76.45 copying path '/nix/store/qcghigzrz56vczwlzg9c02vbs6zr9jkz-nghttp2-1.64.0-lib' from 'https://cache.nixos.org'...
2025-Jun-03 14:25:16.178504
#8 76.46 copying path '/nix/store/h1ydpxkw9qhjdxjpic1pdc2nirggyy6f-openssl-3.3.2' from 'https://cache.nixos.org'...
2025-Jun-03 14:25:16.178504
#8 76.47 copying path '/nix/store/cf7gkacyxmm66lwl5nj6j6yykbrg4q5c-acl-2.3.2' from 'https://cache.nixos.org'...
2025-Jun-03 14:25:16.178504
#8 76.47 copying path '/nix/store/5yja5dpk2qw1v5mbfbl2d7klcdfrh90w-patch-2.7.6' from 'https://cache.nixos.org'...
2025-Jun-03 14:25:16.178504
#8 76.50 copying path '/nix/store/mglixp03lsp0w986svwdvm7vcy17rdax-bzip2-1.0.8-bin' from 'https://cache.nixos.org'...
2025-Jun-03 14:25:16.178504
#8 76.50 copying path '/nix/store/3j1p598fivxs69wx3a657ysv3rw8k06l-pcre2-10.44' from 'https://cache.nixos.org'...
2025-Jun-03 14:25:16.178504
#8 76.52 copying path '/nix/store/nvvj6sk0k6px48436drlblf4gafgbvzr-gzip-1.13' from 'https://cache.nixos.org'...
2025-Jun-03 14:25:16.178504
#8 76.55 copying path '/nix/store/c2njy6bv84kw1i4bjf5k5gn7gz8hn57n-xz-5.6.3' from 'https://cache.nixos.org'...
2025-Jun-03 14:25:16.178504
#8 76.56 copying path '/nix/store/cqlaa2xf6lslnizyj9xqa8j0ii1yqw0x-zlib-1.3.1' from 'https://cache.nixos.org'...
2025-Jun-03 14:25:16.178504
#8 76.56 copying path '/nix/store/ll14czvpxglf6nnwmmrmygplm830fvlv-libuv-1.49.2-dev' from 'https://cache.nixos.org'...
2025-Jun-03 14:25:16.178504
#8 76.56 copying path '/nix/store/xmbv8s4p4i4dbxgkgdrdfb0ym25wh6gk-isl-0.20' from 'https://cache.nixos.org'...
2025-Jun-03 14:25:16.178504
#8 76.58 copying path '/nix/store/9cwwj1c9csmc85l2cqzs3h9hbf1vwl6c-gnutar-1.35' from 'https://cache.nixos.org'...
2025-Jun-03 14:25:16.178504
#8 76.59 copying path '/nix/store/qs22aazzrdd4dnjf9vffl0n31hvls43h-mpfr-4.2.1' from 'https://cache.nixos.org'...
2025-Jun-03 14:25:16.178504
#8 76.60 copying path '/nix/store/dz97fw51rm5bl9kz1vg0haj1j1a7r1mr-nghttp2-1.64.0-dev' from 'https://cache.nixos.org'...
2025-Jun-03 14:25:16.178504
#8 76.65 copying path '/nix/store/df2a8k58k00f2dh2x930dg6xs6g6mliv-binutils-2.43.1-lib' from 'https://cache.nixos.org'...
2025-Jun-03 14:25:16.178504
#8 76.66 copying path '/nix/store/c4rj90r2m89rxs64hmm857mipwjhig5d-file-5.46' from 'https://cache.nixos.org'...
2025-Jun-03 14:25:16.178504
#8 76.66 copying path '/nix/store/1lggwqzapn5mn49l9zy4h566ysv9kzdb-zlib-1.3.1-dev' from 'https://cache.nixos.org'...
2025-Jun-03 14:25:16.178504
#8 76.70 copying path '/nix/store/kj8hbqx4ds9qm9mq7hyikxyfwwg13kzj-glibc-2.40-36-dev' from 'https://cache.nixos.org'...
2025-Jun-03 14:25:16.178504
#8 76.72 copying path '/nix/store/aap6cq56amx4mzbyxp2wpgsf1kqjcr1f-gnugrep-3.11' from 'https://cache.nixos.org'...
2025-Jun-03 14:25:16.178504
#8 76.73 copying path '/nix/store/h18s640fnhhj2qdh5vivcfbxvz377srg-xz-5.6.3-bin' from 'https://cache.nixos.org'...
2025-Jun-03 14:25:16.178504
#8 76.81 copying path '/nix/store/2a3anh8vl3fcgk0fvaravlimrqawawza-libmpc-1.3.1' from 'https://cache.nixos.org'...
2025-Jun-03 14:25:16.178504
#8 77.05 copying path '/nix/store/v9c1s50x7magpiqgycxxkn36avzbcg0g-krb5-1.21.3-lib' from 'https://cache.nixos.org'...
2025-Jun-03 14:25:16.178504
#8 77.05 copying path '/nix/store/8675pnfr4fqnwv4pzjl67hdwls4q13aa-libssh2-1.11.1' from 'https://cache.nixos.org'...
2025-Jun-03 14:25:16.178504
#8 77.05 copying path '/nix/store/qq5q0alyzywdazhmybi7m69akz0ppk05-openssl-3.3.2-bin' from 'https://cache.nixos.org'...
2025-Jun-03 14:25:16.178504
#8 77.06 copying path '/nix/store/srfxqk119fijwnprgsqvn68ys9kiw0bn-patchelf-0.15.0' from 'https://cache.nixos.org'...
2025-Jun-03 14:25:16.178504
#8 77.06 copying path '/nix/store/j7p46r8v9gcpbxx89pbqlh61zhd33gzv-binutils-2.43.1' from 'https://cache.nixos.org'...
2025-Jun-03 14:25:16.178504
#8 77.06 copying path '/nix/store/wwipgdqb4p2fr46kmw9c5wlk799kbl68-icu4c-74.2' from 'https://cache.nixos.org'...
2025-Jun-03 14:25:16.178504
#8 77.07 copying path '/nix/store/a2byxfv4lc8f2g5xfzw8cz5q8k05wi29-gmp-with-cxx-6.3.0' from 'https://cache.nixos.org'...
2025-Jun-03 14:25:16.178504
#8 77.08 copying path '/nix/store/lygl27c44xv73kx1spskcgvzwq7z337c-openssl-3.3.2-bin' from 'https://cache.nixos.org'...
2025-Jun-03 14:25:16.178504
#8 77.14 copying path '/nix/store/pkc7mb4a4qvyz73srkqh4mwl70w98dsv-curl-8.11.0' from 'https://cache.nixos.org'...
2025-Jun-03 14:25:16.178504
#8 77.14 copying path '/nix/store/milph81dilrh96isyivh5n50agpx39k2-krb5-1.21.3' from 'https://cache.nixos.org'...
2025-Jun-03 14:25:16.178504
#8 77.16 copying path '/nix/store/kqm7wpqkzc4bwjlzqizcbz0mgkj06a9x-openssl-3.3.2-dev' from 'https://cache.nixos.org'...
2025-Jun-03 14:25:16.178504
#8 77.17 copying path '/nix/store/l89iqc7am6i60y8vk507zwrzxf0wcd3v-gcc-14-20241116' from 'https://cache.nixos.org'...
2025-Jun-03 14:25:16.178504
#8 77.23 copying path '/nix/store/4s9rah4cwaxflicsk5cndnknqlk9n4p3-coreutils-9.5' from 'https://cache.nixos.org'...
2025-Jun-03 14:25:16.178504
#8 77.26 copying path '/nix/store/pp2zf8bdgyz60ds8vcshk2603gcjgp72-openssl-3.3.2-dev' from 'https://cache.nixos.org'...
2025-Jun-03 14:25:16.178504
#8 77.42 copying path '/nix/store/p123cq20klajcl9hj8jnkjip5nw6awhz-curl-8.11.0-bin' from 'https://cache.nixos.org'...
2025-Jun-03 14:25:16.178504
#8 77.44 copying path '/nix/store/b56mswksrql15knpb1bnhv3ysif340kd-krb5-1.21.3-dev' from 'https://cache.nixos.org'...
2025-Jun-03 14:25:16.178504
#8 77.47 copying path '/nix/store/d7zhcrcc7q3yfbm3qkqpgc3daq82spwi-libssh2-1.11.1-dev' from 'https://cache.nixos.org'...
2025-Jun-03 14:25:16.178504
#8 77.61 copying path '/nix/store/00g69vw7c9lycy63h45ximy0wmzqx5y6-diffutils-3.10' from 'https://cache.nixos.org'...
2025-Jun-03 14:25:16.178504
#8 77.61 copying path '/nix/store/jqrz1vq5nz4lnv9pqzydj0ir58wbjfy1-findutils-4.10.0' from 'https://cache.nixos.org'...
2025-Jun-03 14:25:16.178504
#8 77.61 copying path '/nix/store/1i003ijlh9i0mzp6alqby5hg3090pjdx-perl-5.40.0' from 'https://cache.nixos.org'...
2025-Jun-03 14:25:16.178504
#8 77.77 copying path '/nix/store/5f5linrxzhhb3mrclkwdpm9bd8ygldna-curl-8.11.0-dev' from 'https://cache.nixos.org'...
2025-Jun-03 14:25:16.178504
#8 77.99 copying path '/nix/store/srcmmqi8kxjfygd0hyy42c8hv6cws83b-binutils-wrapper-2.43.1' from 'https://cache.nixos.org'...
2025-Jun-03 14:25:16.178504
#8 78.07 copying path '/nix/store/4ig84cyqi6qy4n0sanrbzsw1ixa497jx-stdenv-linux' from 'https://cache.nixos.org'...
2025-Jun-03 14:25:16.178504
#8 78.57 building '/nix/store/9smjjb5pkmcbykz8p4786s3a4nq6m030-builder.pl.drv'...
2025-Jun-03 14:25:16.178504
#8 78.66 copying path '/nix/store/m8w3mf0i4862q22bxad0wspkgdy4jnkk-icu4c-74.2-dev' from 'https://cache.nixos.org'...
2025-Jun-03 14:25:16.178504
#8 79.01 building '/nix/store/cjdjkmr6gy2h8l0cra71whgrvy030kx1-libraries.drv'...
2025-Jun-03 14:25:16.178504
#8 79.61 building '/nix/store/crmicnx1sb63i6q6md7i8vz9mxb5cair-pnpm-9.15.9.tgz.drv'...
2025-Jun-03 14:25:16.178504
#8 79.62 copying path '/nix/store/wlpq101dsifq98z2bk300x4dk80wcybr-nodejs-18.20.5' from 'https://cache.nixos.org'...
2025-Jun-03 14:25:16.178504
#8 80.97
2025-Jun-03 14:25:16.178504
#8 80.97 trying https://registry.npmjs.org/pnpm/-/pnpm-9.15.9.tgz
2025-Jun-03 14:25:16.178504
#8 81.00   % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current
2025-Jun-03 14:25:16.178504
#8 81.00                                  Dload  Upload   Total   Spent    Left  Speed
2025-Jun-03 14:25:16.178504
#8 81.43 100 4210k  100 4210k    0     0  9667k      0 --:--:-- --:--:-- --:--:-- 9679k
2025-Jun-03 14:25:16.178504
#8 81.82 building '/nix/store/bs6g8vhkfynvlzidhlqbsvnc9wijbaaz-ffeebf0acf3ae8b29f8c7049cd911b9636efd7e7-env.drv'...
2025-Jun-03 14:25:16.178504
#8 84.86 copying path '/nix/store/xcn9p4xxfbvlkpah7pwchpav4ab9d135-gcc-wrapper-14-20241116' from 'https://cache.nixos.org'...
2025-Jun-03 14:25:16.178504
#8 84.88 copying path '/nix/store/d0gfdcag8bxzvg7ww4s7px4lf8sxisyx-stdenv-linux' from 'https://cache.nixos.org'...
2025-Jun-03 14:25:16.178504
#8 84.98 building '/nix/store/39q7099jg9cxpbhan0w51gs7m1drhrwg-pnpm.drv'...
2025-Jun-03 14:25:16.178504
#8 85.13 Running phase: unpackPhase
2025-Jun-03 14:25:16.178504
#8 85.14 unpacking source archive /nix/store/f4glllk86xq0kygag8bljhnazpa5q6w2-pnpm-9.15.9.tgz
2025-Jun-03 14:25:16.178504
#8 85.63 source root is package
2025-Jun-03 14:25:16.178504
#8 85.74 setting SOURCE_DATE_EPOCH to timestamp 499162500 of file package/package.json
2025-Jun-03 14:25:16.178504
#8 85.75 Running phase: installPhase
2025-Jun-03 14:25:16.178504
#8 86.50 building '/nix/store/l2729dz3h90v78yrv436lv4wm0slgycw-ffeebf0acf3ae8b29f8c7049cd911b9636efd7e7-env.drv'...
2025-Jun-03 14:25:16.178504
#8 86.71 created 14 symlinks in user environment
2025-Jun-03 14:25:16.178504
#8 86.86 building '/nix/store/6sfb53yaqwk03s2xnh9makrckax2j0yi-user-environment.drv'...
2025-Jun-03 14:25:16.178504
#8 87.50 removing old generations of profile /nix/var/nix/profiles/per-user/root/channels
2025-Jun-03 14:25:16.178504
#8 87.50 removing old generations of profile /nix/var/nix/profiles/per-user/root/profile
2025-Jun-03 14:25:16.178504
#8 87.50 removing profile version 1
2025-Jun-03 14:25:16.178504
#8 87.50 removing old generations of profile /nix/var/nix/profiles/per-user/root/channels
2025-Jun-03 14:25:16.178504
#8 87.50 removing old generations of profile /nix/var/nix/profiles/per-user/root/profile
2025-Jun-03 14:25:16.178504
#8 87.55 finding garbage collector roots...
2025-Jun-03 14:25:16.178504
#8 87.56 removing stale link from '/nix/var/nix/gcroots/auto/lzjbmb2ry0z7lma2fvpqprb12921pnb5' to '/nix/var/nix/profiles/per-user/root/profile-1-link'
2025-Jun-03 14:25:16.178504
#8 87.58 deleting garbage...
2025-Jun-03 14:25:16.178504
#8 87.59 deleting '/nix/store/b9rj4wk1cxh7g2ib89aqbcapzzar8p2s-user-environment'
2025-Jun-03 14:25:16.178504
#8 87.60 deleting '/nix/store/ir9fki7838bmk4hlj0zmwbw45q101j66-user-environment.drv'
2025-Jun-03 14:25:16.178504
#8 87.60 deleting '/nix/store/xxyn8jfxcpr5ac9dvismfzx39ijh9kiv-env-manifest.nix'
2025-Jun-03 14:25:16.178504
#8 87.65 deleting '/nix/store/5f5linrxzhhb3mrclkwdpm9bd8ygldna-curl-8.11.0-dev'
2025-Jun-03 14:25:16.178504
#8 87.66 deleting '/nix/store/b56mswksrql15knpb1bnhv3ysif340kd-krb5-1.21.3-dev'
2025-Jun-03 14:25:16.178504
#8 87.69 deleting '/nix/store/d0gfdcag8bxzvg7ww4s7px4lf8sxisyx-stdenv-linux'
2025-Jun-03 14:25:16.178504
#8 87.69 deleting '/nix/store/4ig84cyqi6qy4n0sanrbzsw1ixa497jx-stdenv-linux'
2025-Jun-03 14:25:16.178504
#8 87.69 deleting '/nix/store/jqrz1vq5nz4lnv9pqzydj0ir58wbjfy1-findutils-4.10.0'
2025-Jun-03 14:25:16.178504
#8 87.69 deleting '/nix/store/5yja5dpk2qw1v5mbfbl2d7klcdfrh90w-patch-2.7.6'
2025-Jun-03 14:25:16.178504
#8 87.69 deleting '/nix/store/xcn9p4xxfbvlkpah7pwchpav4ab9d135-gcc-wrapper-14-20241116'
2025-Jun-03 14:25:16.178504
#8 87.69 deleting '/nix/store/l89iqc7am6i60y8vk507zwrzxf0wcd3v-gcc-14-20241116'
2025-Jun-03 14:25:16.178504
#8 87.99 deleting '/nix/store/2a3anh8vl3fcgk0fvaravlimrqawawza-libmpc-1.3.1'
2025-Jun-03 14:25:16.178504
#8 88.00 deleting '/nix/store/qs22aazzrdd4dnjf9vffl0n31hvls43h-mpfr-4.2.1'
2025-Jun-03 14:25:16.178504
#8 88.00 deleting '/nix/store/aap6cq56amx4mzbyxp2wpgsf1kqjcr1f-gnugrep-3.11'
2025-Jun-03 14:25:16.178504
#8 88.02 deleting '/nix/store/fp6cjl1zcmm6mawsnrb5yak1wkz2ma8l-gnumake-4.4.1'
2025-Jun-03 14:25:16.178504
#8 88.03 deleting '/nix/store/9cwwj1c9csmc85l2cqzs3h9hbf1vwl6c-gnutar-1.35'
2025-Jun-03 14:25:16.178504
#8 88.05 deleting '/nix/store/xmbv8s4p4i4dbxgkgdrdfb0ym25wh6gk-isl-0.20'
2025-Jun-03 14:25:16.178504
#8 88.06 deleting '/nix/store/kryrg7ds05iwcmy81amavk8w13y4lxbs-gmp-6.3.0'
2025-Jun-03 14:25:16.178504
#8 88.06 deleting '/nix/store/3j1p598fivxs69wx3a657ysv3rw8k06l-pcre2-10.44'
2025-Jun-03 14:25:16.178504
#8 88.07 deleting '/nix/store/p123cq20klajcl9hj8jnkjip5nw6awhz-curl-8.11.0-bin'
2025-Jun-03 14:25:16.178504
#8 88.07 deleting '/nix/store/pkc7mb4a4qvyz73srkqh4mwl70w98dsv-curl-8.11.0'
2025-Jun-03 14:25:16.178504
#8 88.07 deleting '/nix/store/milph81dilrh96isyivh5n50agpx39k2-krb5-1.21.3'
2025-Jun-03 14:25:16.178504
#8 88.08 deleting '/nix/store/v9c1s50x7magpiqgycxxkn36avzbcg0g-krb5-1.21.3-lib'
2025-Jun-03 14:25:16.178504
#8 88.09 deleting '/nix/store/2wh1gqyzf5xsvxpdz2k0bxiz583wwq29-keyutils-1.6.3-lib'
2025-Jun-03 14:25:16.178504
#8 88.09 deleting '/nix/store/dz97fw51rm5bl9kz1vg0haj1j1a7r1mr-nghttp2-1.64.0-dev'
2025-Jun-03 14:25:16.178504
#8 88.09 deleting '/nix/store/qcghigzrz56vczwlzg9c02vbs6zr9jkz-nghttp2-1.64.0-lib'
2025-Jun-03 14:25:16.178504
#8 88.09 deleting '/nix/store/srcmmqi8kxjfygd0hyy42c8hv6cws83b-binutils-wrapper-2.43.1'
2025-Jun-03 14:25:16.178504
#8 88.10 deleting '/nix/store/j7p46r8v9gcpbxx89pbqlh61zhd33gzv-binutils-2.43.1'
2025-Jun-03 14:25:16.178504
#8 88.15 deleting '/nix/store/df2a8k58k00f2dh2x930dg6xs6g6mliv-binutils-2.43.1-lib'
2025-Jun-03 14:25:16.178504
#8 88.15 deleting '/nix/store/qbry6090vlr9ar33kdmmbq2p5apzbga8-expand-response-params'
2025-Jun-03 14:25:16.178504
#8 88.15 deleting '/nix/store/f4glllk86xq0kygag8bljhnazpa5q6w2-pnpm-9.15.9.tgz'
2025-Jun-03 14:25:16.178504
#8 88.15 deleting '/nix/store/d7zhcrcc7q3yfbm3qkqpgc3daq82spwi-libssh2-1.11.1-dev'
2025-Jun-03 14:25:16.178504
#8 88.16 deleting '/nix/store/kqm7wpqkzc4bwjlzqizcbz0mgkj06a9x-openssl-3.3.2-dev'
2025-Jun-03 14:25:16.178504
#8 88.17 deleting '/nix/store/qq5q0alyzywdazhmybi7m69akz0ppk05-openssl-3.3.2-bin'
2025-Jun-03 14:25:16.178504
#8 88.17 deleting '/nix/store/8675pnfr4fqnwv4pzjl67hdwls4q13aa-libssh2-1.11.1'
2025-Jun-03 14:25:16.178504
#8 88.17 deleting '/nix/store/9l9n7a0v4aibcz0sgd0crs209an9p7dz-openssl-3.3.2'
2025-Jun-03 14:25:16.178504
#8 88.18 deleting '/nix/store/abm77lnrkrkb58z6xp1qwjcr1xgkcfwm-gnused-4.9'
2025-Jun-03 14:25:16.178504
#8 88.19 deleting '/nix/store/na4c03201p0gmhn3bqr089x0xqia157w-source'
2025-Jun-03 14:25:16.178504
#8 88.20 deleting '/nix/store/grixvx878884hy8x3xs0c0s1i00j632k-nghttp2-1.64.0'
2025-Jun-03 14:25:16.178504
#8 88.20 deleting '/nix/store/lwi59jcfwk2lnrakmm1y5vw85hj3n1bi-source'
2025-Jun-03 14:25:16.178504
#8 96.49 deleting '/nix/store/74h4z8k82pmp24xryflv4lxkz8jlpqqd-ed-1.20.2'
2025-Jun-03 14:25:16.178504
#8 96.50 deleting '/nix/store/a3c47r5z1q2c4rz0kvq8hlilkhx2s718-gawk-5.3.1'
2025-Jun-03 14:25:16.178504
#8 96.52 deleting '/nix/store/mglixp03lsp0w986svwdvm7vcy17rdax-bzip2-1.0.8-bin'
2025-Jun-03 14:25:16.178504
#8 96.53 deleting '/nix/store/ivl2v8rgg7qh1jkj5pwpqycax3rc2hnl-bzip2-1.0.8'
2025-Jun-03 14:25:16.178504
#8 96.53 deleting '/nix/store/00g69vw7c9lycy63h45ximy0wmzqx5y6-diffutils-3.10'
2025-Jun-03 14:25:16.178504
#8 96.54 deleting '/nix/store/c4rj90r2m89rxs64hmm857mipwjhig5d-file-5.46'
2025-Jun-03 14:25:16.178504
#8 96.55 deleting '/nix/store/srfxqk119fijwnprgsqvn68ys9kiw0bn-patchelf-0.15.0'
2025-Jun-03 14:25:16.178504
#8 96.55 deleting '/nix/store/h18s640fnhhj2qdh5vivcfbxvz377srg-xz-5.6.3-bin'
2025-Jun-03 14:25:16.178504
#8 96.55 deleting '/nix/store/c2njy6bv84kw1i4bjf5k5gn7gz8hn57n-xz-5.6.3'
2025-Jun-03 14:25:16.178504
#8 96.56 deleting '/nix/store/kj8hbqx4ds9qm9mq7hyikxyfwwg13kzj-glibc-2.40-36-dev'
2025-Jun-03 14:25:16.178504
#8 96.62 deleting '/nix/store/pc74azbkr19rkd5bjalq2xwx86cj3cga-linux-headers-6.12'
2025-Jun-03 14:25:16.178504
#8 96.79 deleting '/nix/store/1i003ijlh9i0mzp6alqby5hg3090pjdx-perl-5.40.0'
2025-Jun-03 14:25:16.178504
#8 97.03 deleting '/nix/store/6cr0spsvymmrp1hj5n0kbaxw55w1lqyp-libxcrypt-4.4.36'
2025-Jun-03 14:25:16.178504
#8 97.04 deleting '/nix/store/1c6bmxrrhm8bd26ai2rjqld2yyjrxhds-glibc-2.40-36-bin'
2025-Jun-03 14:25:16.178504
#8 97.05 deleting '/nix/store/fv7gpnvg922frkh81w5hkdhpz0nw3iiz-mirrors-list'
2025-Jun-03 14:25:16.178504
#8 97.05 deleting '/nix/store/agvks3qmzja0yj54szi3vja6vx3cwkkw-curl-8.11.0-man'
2025-Jun-03 14:25:16.178504
#8 97.05 deleting '/nix/store/1c0dv2pdlshjz5kmjd4dfp3c96yncr23-libraries'
2025-Jun-03 14:25:16.178504
#8 97.05 deleting '/nix/store/d29r1bdmlvwmj52apgcdxfl1mm9c5782-update-autotools-gnu-config-scripts-hook'
2025-Jun-03 14:25:16.178504
#8 97.05 deleting '/nix/store/1m67ipsk39xvhyqrxnzv2m2p48pil8kl-gnu-config-2024-01-01'
2025-Jun-03 14:25:16.178504
#8 97.05 deleting '/nix/store/wf5zj2gbib3gjqllkabxaw4dh0gzcla3-builder.pl'
2025-Jun-03 14:25:16.178504
#8 97.05 deleting '/nix/store/nvvj6sk0k6px48436drlblf4gafgbvzr-gzip-1.13'
2025-Jun-03 14:25:16.178504
#8 97.05 deleting unused links...
2025-Jun-03 14:25:16.178504
#8 97.05 note: currently hard linking saves -0.00 MiB
2025-Jun-03 14:25:16.178504
#8 97.14 61 store paths deleted, 564.38 MiB freed
2025-Jun-03 14:25:16.178504
#8 DONE 97.5s
2025-Jun-03 14:25:16.***********-Jun-03 14:25:16.178504
#9 [ 5/12] RUN sudo apt-get update && sudo apt-get install -y --no-install-recommends curl wget
2025-Jun-03 14:25:16.178504
#9 0.586 Get:1 http://security.ubuntu.com/ubuntu jammy-security InRelease [129 kB]
2025-Jun-03 14:25:16.178504
#9 0.589 Get:2 http://archive.ubuntu.com/ubuntu jammy InRelease [270 kB]
2025-Jun-03 14:25:16.178504
#9 1.128 Get:3 http://archive.ubuntu.com/ubuntu jammy-updates InRelease [128 kB]
2025-Jun-03 14:25:16.178504
#9 1.280 Get:4 http://archive.ubuntu.com/ubuntu jammy-backports InRelease [127 kB]
2025-Jun-03 14:25:16.178504
#9 1.450 Get:5 http://security.ubuntu.com/ubuntu jammy-security/restricted amd64 Packages [4468 kB]
2025-Jun-03 14:25:16.178504
#9 1.777 Get:6 http://archive.ubuntu.com/ubuntu jammy/multiverse amd64 Packages [266 kB]
2025-Jun-03 14:25:16.178504
#9 1.952 Get:7 http://archive.ubuntu.com/ubuntu jammy/universe amd64 Packages [17.5 MB]
2025-Jun-03 14:25:16.178504
#9 2.149 Get:8 http://security.ubuntu.com/ubuntu jammy-security/universe amd64 Packages [1245 kB]
2025-Jun-03 14:25:16.178504
#9 2.210 Get:9 http://security.ubuntu.com/ubuntu jammy-security/main amd64 Packages [2979 kB]
2025-Jun-03 14:25:16.178504
#9 2.408 Get:10 http://security.ubuntu.com/ubuntu jammy-security/multiverse amd64 Packages [47.7 kB]
2025-Jun-03 14:25:16.178504
#9 3.227 Get:11 http://archive.ubuntu.com/ubuntu jammy/main amd64 Packages [1792 kB]
2025-Jun-03 14:25:16.178504
#9 3.321 Get:12 http://archive.ubuntu.com/ubuntu jammy/restricted amd64 Packages [164 kB]
2025-Jun-03 14:25:16.178504
#9 3.328 Get:13 http://archive.ubuntu.com/ubuntu jammy-updates/main amd64 Packages [3290 kB]
2025-Jun-03 14:25:16.178504
#9 3.481 Get:14 http://archive.ubuntu.com/ubuntu jammy-updates/restricted amd64 Packages [4622 kB]
2025-Jun-03 14:25:16.178504
#9 3.698 Get:15 http://archive.ubuntu.com/ubuntu jammy-updates/universe amd64 Packages [1553 kB]
2025-Jun-03 14:25:16.178504
#9 3.760 Get:16 http://archive.ubuntu.com/ubuntu jammy-updates/multiverse amd64 Packages [55.7 kB]
2025-Jun-03 14:25:16.178504
#9 3.761 Get:17 http://archive.ubuntu.com/ubuntu jammy-backports/main amd64 Packages [83.2 kB]
2025-Jun-03 14:25:16.178504
#9 3.764 Get:18 http://archive.ubuntu.com/ubuntu jammy-backports/universe amd64 Packages [35.2 kB]
2025-Jun-03 14:25:16.178504
#9 4.690 Fetched 38.7 MB in 4s (8997 kB/s)
2025-Jun-03 14:25:16.178504
#9 4.690 Reading package lists...
2025-Jun-03 14:25:16.178504
#9 6.722 Reading package lists...
2025-Jun-03 14:25:16.178504
#9 8.829 Building dependency tree...
2025-Jun-03 14:25:16.178504
#9 9.338 Reading state information...
2025-Jun-03 14:25:16.178504
#9 10.18 curl is already the newest version (7.81.0-1ubuntu1.20).
2025-Jun-03 14:25:16.178504
#9 10.18 The following NEW packages will be installed:
2025-Jun-03 14:25:16.178504
#9 10.18   wget
2025-Jun-03 14:25:16.178504
#9 10.44 0 upgraded, 1 newly installed, 0 to remove and 20 not upgraded.
2025-Jun-03 14:25:16.178504
#9 10.44 Need to get 339 kB of archives.
2025-Jun-03 14:25:16.178504
#9 10.44 After this operation, 950 kB of additional disk space will be used.
2025-Jun-03 14:25:16.178504
#9 10.44 Get:1 http://archive.ubuntu.com/ubuntu jammy-updates/main amd64 wget amd64 1.21.2-2ubuntu1.1 [339 kB]
2025-Jun-03 14:25:16.178504
#9 11.45 debconf: delaying package configuration, since apt-utils is not installed
2025-Jun-03 14:25:16.178504
#9 11.79 Fetched 339 kB in 1s (485 kB/s)
2025-Jun-03 14:25:16.178504
#9 11.93 Selecting previously unselected package wget.
2025-Jun-03 14:25:16.178504
#9 11.93 (Reading database ... 
(Reading database ... 5%
(Reading database ... 10%
(Reading database ... 15%
(Reading database ... 20%
(Reading database ... 25%
(Reading database ... 30%
(Reading database ... 35%
(Reading database ... 40%
(Reading database ... 45%
(Reading database ... 50%
(Reading database ... 55%
(Reading database ... 60%
(Reading database ... 65%
(Reading database ... 70%
(Reading database ... 75%
(Reading database ... 80%
(Reading database ... 85%
(Reading database ... 90%
(Reading database ... 95%
(Reading database ... 100%
(Reading database ... 10745 files and directories currently installed.)
2025-Jun-03 14:25:16.178504
#9 11.95 Preparing to unpack .../wget_1.21.2-2ubuntu1.1_amd64.deb ...
2025-Jun-03 14:25:16.178504
#9 11.99 Unpacking wget (1.21.2-2ubuntu1.1) ...
2025-Jun-03 14:25:16.178504
#9 12.39 Setting up wget (1.21.2-2ubuntu1.1) ...
2025-Jun-03 14:25:16.178504
#9 DONE 12.7s
2025-Jun-03 14:25:16.***********-Jun-03 14:25:16.178504
#10 [ 6/12] COPY . /app/.
2025-Jun-03 14:25:16.178504
#10 DONE 0.2s
2025-Jun-03 14:25:16.***********-Jun-03 14:25:16.178504
#11 [ 7/12] RUN  npm install -g corepack@0.24.1 && corepack enable
2025-Jun-03 14:25:16.178504
#11 0.607 npm warn config production Use `--omit=dev` instead.
2025-Jun-03 14:25:16.178504
#11 5.720
2025-Jun-03 14:25:16.178504
#11 5.720 changed 1 package in 5s
2025-Jun-03 14:25:16.178504
#11 DONE 6.4s
2025-Jun-03 14:25:16.***********-Jun-03 14:25:16.178504
#12 [ 8/12] RUN  pnpm i --frozen-lockfile
2025-Jun-03 14:25:16.178504
#12 8.026  WARN  Ignoring not compatible lockfile at /app/pnpm-lock.yaml
2025-Jun-03 14:25:16.178504
#12 8.040  ERR_PNPM_NO_LOCKFILE  Cannot install with "frozen-lockfile" because pnpm-lock.yaml is present
2025-Jun-03 14:25:16.178504
#12 8.040
2025-Jun-03 14:25:16.178504
#12 8.040 Note that in CI environments this setting is true by default. If you still need to run install in such cases, use "pnpm install --no-frozen-lockfile"
2025-Jun-03 14:25:16.178504
#12 ERROR: process "/bin/bash -ol pipefail -c pnpm i --frozen-lockfile" did not complete successfully: exit code: 1
2025-Jun-03 14:25:16.178504
------
2025-Jun-03 14:25:16.178504
> [ 8/12] RUN  pnpm i --frozen-lockfile:
2025-Jun-03 14:25:16.178504
8.026  WARN  Ignoring not compatible lockfile at /app/pnpm-lock.yaml
2025-Jun-03 14:25:16.178504
8.040  ERR_PNPM_NO_LOCKFILE  Cannot install with "frozen-lockfile" because pnpm-lock.yaml is present
2025-Jun-03 14:25:16.178504
8.040
2025-Jun-03 14:25:16.178504
8.040 Note that in CI environments this setting is true by default. If you still need to run install in such cases, use "pnpm install --no-frozen-lockfile"
2025-Jun-03 14:25:16.178504
------
2025-Jun-03 14:25:16.***********-Jun-03 14:25:16.178504
1 warning found (use docker --debug to expand):
2025-Jun-03 14:25:16.178504
- UndefinedVar: Usage of undefined variable '$NIXPACKS_PATH' (line 18)
2025-Jun-03 14:25:16.178504
Dockerfile:21
2025-Jun-03 14:25:16.178504
--------------------
2025-Jun-03 14:25:16.178504
19 |     COPY . /app/.
2025-Jun-03 14:25:16.178504
20 |     RUN  npm install -g corepack@0.24.1 && corepack enable
2025-Jun-03 14:25:16.178504
21 | >>> RUN  pnpm i --frozen-lockfile
2025-Jun-03 14:25:16.178504
22 |
2025-Jun-03 14:25:16.178504
23 |     # build phase
2025-Jun-03 14:25:16.178504
--------------------
2025-Jun-03 14:25:16.178504
ERROR: failed to solve: process "/bin/bash -ol pipefail -c pnpm i --frozen-lockfile" did not complete successfully: exit code: 1
2025-Jun-03 14:25:16.178504
exit status 1
2025-Jun-03 14:25:16.245464
Deployment failed. Removing the new version of your application.