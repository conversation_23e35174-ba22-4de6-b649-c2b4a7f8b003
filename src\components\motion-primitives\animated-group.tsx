"use client"

import React from "react"
import { motion, Variants } from "framer-motion"
import { cn } from "@/lib/utils"

type AnimatedGroupProps = {
  children: React.ReactNode
  className?: string
  variants: Variants
  initial?: string
  animate?: string
  as?: React.ElementType
}

export function AnimatedGroup({
  children,
  className,
  variants,
  initial = "hidden",
  animate = "visible",
  as: Component = "div",
  ...props
}: AnimatedGroupProps) {
  return (
    <motion.div
      className={cn(className)}
      initial={initial}
      animate={animate}
      variants={{
        hidden: {},
        visible: {
          transition: {
            staggerChildren: 0.05,
          },
        },
        ...variants,
      }}
      {...props}
    >
      {React.Children.map(children, (child, index) => {
        if (!React.isValidElement(child)) return child

        return React.cloneElement(child as React.ReactElement, {
          key: index,
          variants: variants.item,
        })
      })}
    </motion.div>
  )
}
