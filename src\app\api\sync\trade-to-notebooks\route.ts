import { NextRequest, NextResponse } from "next/server"
import { getSupabaseServer } from "@/lib/supabase-server"

export async function POST(request: NextRequest) {
  try {
    console.log('=== MANUAL TRADE TO NOTEBOOKS SYNC API CALLED ===')

    const { tradeId } = await request.json()

    if (!tradeId) {
      return NextResponse.json(
        { error: "Trade ID is required" },
        { status: 400 }
      )
    }

    // Get authenticated user
    const supabase = await getSupabaseServer()
    const { data: { user }, error: userError } = await supabase.auth.getUser()

    if (userError || !user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    // Fetch the trade to get current data
    const { data: trade, error: fetchError } = await supabase
      .from("trades")
      .select("*")
      .eq("id", tradeId)
      .eq("user_id", user.id)
      .single()

    if (fetchError || !trade) {
      return NextResponse.json(
        { error: "Trade not found" },
        { status: 404 }
      )
    }

    // Call the sync Edge Function manually
    const edgeFunctionUrl = `${process.env.NEXT_PUBLIC_SUPABASE_URL}/functions/v1/sync-trade-changes`
    const serviceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

    if (!serviceKey) {
      console.error('SUPABASE_SERVICE_ROLE_KEY not found in environment variables')
      console.log('Available env vars:', Object.keys(process.env).filter(key => key.includes('SUPABASE')))
      return NextResponse.json(
        { error: "Service key not configured - check environment variables" },
        { status: 500 }
      )
    }

    console.log('Calling Edge Function:', edgeFunctionUrl)
    console.log('Trade data:', { id: trade.id, notes: trade.notes?.substring(0, 100) })

    const syncResponse = await fetch(edgeFunctionUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${serviceKey}`,
      },
      body: JSON.stringify({
        type: 'UPDATE',
        record: trade,
        old_record: null // We don't have old record for manual sync, but the function handles this
      })
    })

    if (!syncResponse.ok) {
      const errorText = await syncResponse.text()
      console.error('Edge function sync failed:', errorText)
      return NextResponse.json(
        { error: "Sync failed", details: errorText },
        { status: 500 }
      )
    }

    const syncResult = await syncResponse.json()
    console.log('Manual sync completed successfully:', syncResult)

    return NextResponse.json({
      success: true,
      message: "Trade synced to notebooks successfully",
      syncResult
    })

  } catch (error) {
    console.error('Error in manual trade sync:', error)
    return NextResponse.json(
      { error: "Internal server error", details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}
