import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

export async function POST(request: Request) {
  try {
    // Only allow this in development mode
    if (process.env.NODE_ENV !== 'development') {
      return NextResponse.json({ error: 'This endpoint is only available in development mode' }, { status: 403 });
    }

    // Create a Supabase client with admin privileges
    const supabaseAdmin = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL || '',
      process.env.SUPABASE_SERVICE_ROLE_KEY || ''
    );

    // First, check if the RPC function exists
    let rpcExists = false;
    let rpcError = null;
    try {
      const result = await supabaseAdmin.rpc(
        'function_exists',
        { function_name: 'get_policies_for_table' }
      );
      rpcExists = result.data || false;
      rpcError = result.error;
    } catch (error) {
      rpcExists = false;
      rpcError = null;
    }

    // If the RPC function doesn't exist, create it
    if (!rpcExists) {
      // Create the RPC function to get policies for a table
      let createRpcError = null;
      try {
        const result = await supabaseAdmin.rpc(
          'create_rpc_function',
          {
            function_name: 'get_policies_for_table',
            function_definition: `
              CREATE OR REPLACE FUNCTION get_policies_for_table(table_name text, schema_name text)
              RETURNS TABLE (
                policyname name,
                permissive text,
                roles name[],
                cmd text,
                qual text,
                with_check text
              )
              LANGUAGE sql
              SECURITY DEFINER
              AS $$
                SELECT
                  p.policyname,
                  p.permissive,
                  p.roles,
                  p.cmd,
                  p.qual,
                  p.with_check
                FROM pg_policies p
                WHERE p.tablename = table_name
                  AND p.schemaname = schema_name;
              $$;
            `
          }
        );
        createRpcError = result.error;
      } catch (err) {
        createRpcError = err;
      }

      if (createRpcError) {
        return NextResponse.json({ error: 'Failed to create RPC function', details: createRpcError }, { status: 500 });
      }
    }

    // Check if the strategyimages bucket exists
    const { data: buckets, error: bucketsError } = await supabaseAdmin.storage
      .listBuckets();

    const bucketExists = buckets?.some(bucket => bucket.name === 'strategyimages');

    // If the bucket doesn't exist, create it
    if (!bucketExists) {
      const { error: createBucketError } = await supabaseAdmin.storage
        .createBucket('strategyimages', {
          public: true,
          fileSizeLimit: 5242880, // 5MB
          allowedMimeTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp']
        });

      if (createBucketError) {
        return NextResponse.json({ error: 'Failed to create bucket', details: createBucketError }, { status: 500 });
      }
    }

    // Get the current RLS policies for the storage.objects table
    const { data: policies, error: policiesError } = await supabaseAdmin.rpc(
      'get_policies_for_table',
      { table_name: 'objects', schema_name: 'storage' }
    );

    // Check if we need to create RLS policies
    const needsReadPolicy = !policies?.some((p: any) =>
      p.policyname === 'Allow public read access to strategyimages' &&
      p.cmd === 'SELECT'
    );

    const needsWritePolicy = !policies?.some((p: any) =>
      p.policyname === 'Allow authenticated users to upload to strategyimages' &&
      p.cmd === 'INSERT'
    );

    const needsDeletePolicy = !policies?.some((p: any) =>
      p.policyname === 'Allow authenticated users to delete from strategyimages' &&
      p.cmd === 'DELETE'
    );

    // Create the necessary RLS policies
    const results = {
      readPolicy: { needed: needsReadPolicy, success: false, error: null as string | null },
      writePolicy: { needed: needsWritePolicy, success: false, error: null as string | null },
      deletePolicy: { needed: needsDeletePolicy, success: false, error: null as string | null }
    };

    if (needsReadPolicy) {
      let error = null;
      try {
        const result = await supabaseAdmin.rpc(
          'create_storage_policy',
          {
            policy_name: 'Allow public read access to strategyimages',
            bucket_name: 'strategyimages',
            operation: 'SELECT',
            definition: "bucket_id = 'strategyimages'::text"
          }
        );
        error = result.error;
      } catch (err) {
        error = err;
      }

      results.readPolicy.success = !error;
      results.readPolicy.error = error ? (typeof error === 'object' && error !== null && 'message' in error ? String(error.message) : String(error)) : null;
    }

    if (needsWritePolicy) {
      let error = null;
      try {
        const result = await supabaseAdmin.rpc(
          'create_storage_policy',
          {
            policy_name: 'Allow authenticated users to upload to strategyimages',
            bucket_name: 'strategyimages',
            operation: 'INSERT',
            definition: "bucket_id = 'strategyimages'::text AND auth.role() = 'authenticated'"
          }
        );
        error = result.error;
      } catch (err) {
        error = err;
      }

      results.writePolicy.success = !error;
      results.writePolicy.error = error ? (typeof error === 'object' && error !== null && 'message' in error ? String(error.message) : String(error)) : null;
    }

    if (needsDeletePolicy) {
      let error = null;
      try {
        const result = await supabaseAdmin.rpc(
          'create_storage_policy',
          {
            policy_name: 'Allow authenticated users to delete from strategyimages',
            bucket_name: 'strategyimages',
            operation: 'DELETE',
            definition: "bucket_id = 'strategyimages'::text AND auth.role() = 'authenticated'"
          }
        );
        error = result.error;
      } catch (err) {
        error = err;
      }

      results.deletePolicy.success = !error;
      results.deletePolicy.error = error ? (typeof error === 'object' && error !== null && 'message' in error ? String(error.message) : String(error)) : null;
    }

    return NextResponse.json({
      message: 'Storage RLS setup completed',
      bucketExists: bucketExists || !bucketsError,
      policies: results
    });
  } catch (error) {
    console.error('Error in setup-storage-rls route:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
