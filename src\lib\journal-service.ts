import { getSupabaseBrowser } from "@/lib/supabase"
import { Database } from "@/types/supabase"

export type JournalEntry = Database["public"]["Tables"]["journal_entries"]["Row"]
export type JournalEntryInsert = Database["public"]["Tables"]["journal_entries"]["Insert"]
export type JournalEntryUpdate = Database["public"]["Tables"]["journal_entries"]["Update"]

export async function getJournalEntries(userId: string, options?: {
  tradeId?: string;
  startDate?: string;
  endDate?: string;
  tags?: string[];
  searchTerm?: string;
  accountId?: string | null;
}) {
  const supabase = getSupabaseBrowser()

  try {
    console.log('Fetching journal entries...')

    // If this is a demo user, return sample data
    if (userId === 'demo-user') {
      console.log('Using demo journal entries')
      return generateDemoJournalEntries()
    }

    // Check if the table exists by trying to get the count
    const { count, error: countError } = await supabase
      .from("journal_entries")
      .select("*", { count: 'exact', head: true })

    // If there's an error with the count query, it might be because the table doesn't exist
    // In that case, return demo entries for now
    if (countError) {
      console.warn('Journal entries table might not exist yet:', countError.message)
      console.log('Using demo journal entries as fallback')
      return generateDemoJournalEntries()
    }

    let query = supabase
      .from("journal_entries")
      .select("*")
      .eq("user_id", userId)
      .order("entry_date", { ascending: false })

    // Apply filters if provided
    if (options?.tradeId) {
      query = query.eq("trade_id", options.tradeId)
    }

    // If accountId is explicitly null (user has unselected all accounts), return empty array
    if (options?.accountId === null) {
      console.log('No account selected, returning empty journal entries array')
      return []
    }

    // If an account ID is specified, we need to filter trades by that account
    // and then filter journal entries by those trade IDs
    if (options?.accountId) {
      // First, get all trades for the specified account
      const { data: accountTrades, error: tradesError } = await supabase
        .from("trades")
        .select("id")
        .eq("account_id", options.accountId)

      if (tradesError) {
        console.error('Error fetching trades for account:', tradesError)
      } else if (accountTrades && accountTrades.length > 0) {
        // Get all trade IDs for this account
        const tradeIds = accountTrades.map(trade => trade.id)
        // Filter journal entries to only include those linked to these trades
        // or entries with no trade_id (general entries)
        query = query.or(`trade_id.in.(${tradeIds.join(',')}),trade_id.is.null`)
      }
    }

    if (options?.startDate) {
      query = query.gte("entry_date", options.startDate)
    }

    if (options?.endDate) {
      query = query.lte("entry_date", options.endDate)
    }

    if (options?.tags && options.tags.length > 0) {
      // Filter entries that contain any of the specified tags
      query = query.overlaps("tags", options.tags)
    }

    if (options?.searchTerm) {
      // Search in title and content
      query = query.or(`title.ilike.%${options.searchTerm}%,content.ilike.%${options.searchTerm}%`)
    }

    const { data: entries, error } = await query

    if (error) {
      console.error('Error fetching journal entries:', error)
      // Return demo entries instead of throwing
      console.log('Using demo journal entries as fallback after error')
      return generateDemoJournalEntries()
    }

    console.log(`Retrieved ${entries?.length || 0} journal entries`)
    return entries || []
  } catch (error) {
    console.error('Error in getJournalEntries:', error)
    // Return demo entries instead of empty array
    console.log('Using demo journal entries as fallback after exception')
    return generateDemoJournalEntries()
  }
}

export async function getJournalEntryById(userId: string, entryId: string) {
  const supabase = getSupabaseBrowser()

  try {
    console.log(`Fetching journal entry with ID: ${entryId}`)

    // If this is a demo user, return a demo entry
    if (userId === 'demo-user') {
      const demoEntries = generateDemoJournalEntries()
      const demoEntry = demoEntries.find(entry => entry.id === entryId)
      return demoEntry || null
    }

    // Check if the table exists
    const { error: countError } = await supabase
      .from("journal_entries")
      .select("*", { count: 'exact', head: true })

    // If there's an error with the count query, it might be because the table doesn't exist
    if (countError) {
      console.warn('Journal entries table might not exist yet:', countError.message)
      // Return a demo entry as fallback
      const demoEntries = generateDemoJournalEntries()
      const demoEntry = demoEntries.find(entry => entry.id === entryId)
      return demoEntry || null
    }

    const { data: entry, error } = await supabase
      .from("journal_entries")
      .select("*")
      .eq("id", entryId)
      .eq("user_id", userId)
      .single()

    if (error) {
      console.error('Error fetching journal entry:', error)
      // Return a demo entry as fallback
      const demoEntries = generateDemoJournalEntries()
      const demoEntry = demoEntries.find(entry => entry.id === entryId)
      return demoEntry || null
    }

    return entry
  } catch (error) {
    console.error('Error in getJournalEntryById:', error)
    // Return a demo entry as fallback
    const demoEntries = generateDemoJournalEntries()
    const demoEntry = demoEntries.find(entry => entry.id === entryId)
    return demoEntry || null
  }
}

export async function createJournalEntry(userId: string, entry: Omit<JournalEntryInsert, "user_id">) {
  const supabase = getSupabaseBrowser()

  try {
    console.log('Creating journal entry...')

    // If this is a demo user, return a mock success response
    if (userId === 'demo-user') {
      console.log('Demo user - simulating journal entry creation')
      const mockEntry: JournalEntry = {
        id: `demo-journal-${Date.now()}`,
        user_id: 'demo-user',
        trade_id: entry.trade_id || null,
        title: entry.title,
        content: entry.content,
        tags: entry.tags || [],
        entry_date: entry.entry_date,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
      return mockEntry
    }

    // Check if the table exists
    const { error: countError } = await supabase
      .from("journal_entries")
      .select("*", { count: 'exact', head: true })

    // If there's an error with the count query, it might be because the table doesn't exist
    if (countError) {
      console.warn('Journal entries table might not exist yet:', countError.message)
      // Return a mock entry as fallback
      const mockEntry: JournalEntry = {
        id: `demo-journal-${Date.now()}`,
        user_id: 'demo-user',
        trade_id: entry.trade_id || null,
        title: entry.title,
        content: entry.content,
        tags: entry.tags || [],
        entry_date: entry.entry_date,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
      return mockEntry
    }

    const { data, error } = await supabase
      .from("journal_entries")
      .insert({
        ...entry,
        user_id: userId,
        updated_at: new Date().toISOString()
      })
      .select()
      .single()

    if (error) {
      console.error('Error creating journal entry:', error)
      // Return a mock entry as fallback
      const mockEntry: JournalEntry = {
        id: `demo-journal-${Date.now()}`,
        user_id: 'demo-user',
        trade_id: entry.trade_id || null,
        title: entry.title,
        content: entry.content,
        tags: entry.tags || [],
        entry_date: entry.entry_date,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
      return mockEntry
    }

    console.log('Journal entry created successfully')
    return data
  } catch (error) {
    console.error('Error in createJournalEntry:', error)
    // Return a mock entry as fallback
    const mockEntry: JournalEntry = {
      id: `demo-journal-${Date.now()}`,
      user_id: 'demo-user',
      trade_id: entry.trade_id || null,
      title: entry.title,
      content: entry.content,
      tags: entry.tags || [],
      entry_date: entry.entry_date,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }
    return mockEntry
  }
}

export async function updateJournalEntry(userId: string, entryId: string, updates: JournalEntryUpdate) {
  const supabase = getSupabaseBrowser()

  try {
    console.log(`Updating journal entry with ID: ${entryId}`)

    // If this is a demo user, return a mock success response
    if (userId === 'demo-user' || entryId.startsWith('demo-')) {
      console.log('Demo user or entry - simulating journal entry update')
      const mockEntry: JournalEntry = {
        id: entryId,
        user_id: userId,
        trade_id: updates.trade_id || null,
        title: updates.title || 'Updated Entry',
        content: updates.content || 'Updated content',
        tags: updates.tags || [],
        entry_date: updates.entry_date || new Date().toISOString().split('T')[0],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
      return mockEntry
    }

    // Check if the table exists
    const { error: countError } = await supabase
      .from("journal_entries")
      .select("*", { count: 'exact', head: true })

    // If there's an error with the count query, it might be because the table doesn't exist
    if (countError) {
      console.warn('Journal entries table might not exist yet:', countError.message)
      // Return a mock entry as fallback
      const mockEntry: JournalEntry = {
        id: entryId,
        user_id: userId,
        trade_id: updates.trade_id || null,
        title: updates.title || 'Updated Entry',
        content: updates.content || 'Updated content',
        tags: updates.tags || [],
        entry_date: updates.entry_date || new Date().toISOString().split('T')[0],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
      return mockEntry
    }

    const { data, error } = await supabase
      .from("journal_entries")
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq("id", entryId)
      .eq("user_id", userId)
      .select()
      .single()

    if (error) {
      console.error('Error updating journal entry:', error)
      // Return a mock entry as fallback
      const mockEntry: JournalEntry = {
        id: entryId,
        user_id: userId,
        trade_id: updates.trade_id || null,
        title: updates.title || 'Updated Entry',
        content: updates.content || 'Updated content',
        tags: updates.tags || [],
        entry_date: updates.entry_date || new Date().toISOString().split('T')[0],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
      return mockEntry
    }

    console.log('Journal entry updated successfully')
    return data
  } catch (error) {
    console.error('Error in updateJournalEntry:', error)
    // Return a mock entry as fallback
    const mockEntry: JournalEntry = {
      id: entryId,
      user_id: userId,
      trade_id: updates.trade_id || null,
      title: updates.title || 'Updated Entry',
      content: updates.content || 'Updated content',
      tags: updates.tags || [],
      entry_date: updates.entry_date || new Date().toISOString().split('T')[0],
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }
    return mockEntry
  }
}

export async function deleteJournalEntry(userId: string, entryId: string) {
  const supabase = getSupabaseBrowser()

  try {
    console.log(`Deleting journal entry with ID: ${entryId}`)

    // If this is a demo user or entry, return a mock success response
    if (userId === 'demo-user' || entryId.startsWith('demo-')) {
      console.log('Demo user or entry - simulating journal entry deletion')
      return true
    }

    // Check if the table exists
    const { error: countError } = await supabase
      .from("journal_entries")
      .select("*", { count: 'exact', head: true })

    // If there's an error with the count query, it might be because the table doesn't exist
    if (countError) {
      console.warn('Journal entries table might not exist yet:', countError.message)
      // Return success as fallback
      return true
    }

    const { error } = await supabase
      .from("journal_entries")
      .delete()
      .eq("id", entryId)
      .eq("user_id", userId)

    if (error) {
      console.error('Error deleting journal entry:', error)
      // Return success as fallback to prevent UI errors
      return true
    }

    console.log('Journal entry deleted successfully')
    return true
  } catch (error) {
    console.error('Error in deleteJournalEntry:', error)
    // Return success as fallback to prevent UI errors
    return true
  }
}

// Global tag cache to prevent redundant requests
const tagCache: Record<string, { tags: string[], timestamp: number }> = {};
const TAG_CACHE_TTL = 60000; // 1 minute cache TTL

export async function getAllTags(userId: string) {
  // Check if we have a valid cached result
  const cachedResult = tagCache[userId];
  const now = Date.now();

  if (cachedResult && (now - cachedResult.timestamp < TAG_CACHE_TTL)) {
    // Use cached tags if they're still valid
    console.log(`Using cached tags for user ${userId}`);
    return cachedResult.tags;
  }

  const supabase = getSupabaseBrowser()

  try {
    console.log('Fetching all tags...')

    // If this is a demo user, return demo tags
    if (userId === 'demo-user') {
      const demoTags = ['strategy', 'psychology', 'market-analysis', 'mistake', 'success', 'lesson', 'EURUSD', 'GBPUSD', 'USDJPY', 'breakout', 'resistance', 'weekly-review', 'development', 'technical-analysis'];

      // Cache demo tags too
      tagCache[userId] = {
        tags: demoTags,
        timestamp: now
      };

      return demoTags;
    }

    // Check if the table exists
    const { error: countError } = await supabase
      .from("journal_entries")
      .select("*", { count: 'exact', head: true })

    // If there's an error with the count query, it might be because the table doesn't exist
    if (countError) {
      console.warn('Journal entries table might not exist yet:', countError.message)
      // Return demo tags as fallback
      return ['strategy', 'psychology', 'market-analysis', 'mistake', 'success', 'lesson', 'EURUSD', 'GBPUSD', 'USDJPY', 'breakout', 'resistance', 'weekly-review', 'development', 'technical-analysis']
    }

    const { data: entries, error } = await supabase
      .from("journal_entries")
      .select("tags")
      .eq("user_id", userId)

    if (error) {
      console.error('Error fetching tags:', error)
      // Return demo tags as fallback
      return ['strategy', 'psychology', 'market-analysis', 'mistake', 'success', 'lesson', 'EURUSD', 'GBPUSD', 'USDJPY', 'breakout', 'resistance', 'weekly-review', 'development', 'technical-analysis']
    }

    // Extract all tags and remove duplicates
    const allTags = entries?.flatMap(entry => entry.tags || []) || []
    const uniqueTags = [...new Set(allTags)]

    const result = uniqueTags.length > 0
      ? uniqueTags
      : ['strategy', 'psychology', 'market-analysis', 'mistake', 'success', 'lesson'];

    // Cache the result
    tagCache[userId] = {
      tags: result,
      timestamp: now
    };

    console.log(`Retrieved ${result.length} unique tags`)
    return result;
  } catch (error) {
    console.error('Error in getAllTags:', error)
    // Return demo tags as fallback
    const fallbackTags = ['strategy', 'psychology', 'market-analysis', 'mistake', 'success', 'lesson', 'EURUSD', 'GBPUSD', 'USDJPY', 'breakout', 'resistance', 'weekly-review', 'development', 'technical-analysis'];

    // Cache the fallback tags too
    tagCache[userId] = {
      tags: fallbackTags,
      timestamp: now
    };

    return fallbackTags;
  }
}

// Generate demo journal entries for testing
function generateDemoJournalEntries(): JournalEntry[] {
  const now = new Date()

  return [
    {
      id: 'demo-journal-1',
      user_id: 'demo-user',
      trade_id: null,
      title: 'Market Analysis - EURUSD',
      content: "Today I analyzed the EURUSD pair and noticed a strong resistance level at 1.0850. The price has been testing this level multiple times but failed to break through. I believe this could be a good opportunity for a short position if the price approaches this level again.",
      tags: ['market-analysis', 'EURUSD', 'resistance'],
      entry_date: new Date(now.getFullYear(), now.getMonth(), now.getDate() - 1).toISOString().split('T')[0],
      created_at: new Date(now.getFullYear(), now.getMonth(), now.getDate() - 1).toISOString(),
      updated_at: new Date(now.getFullYear(), now.getMonth(), now.getDate() - 1).toISOString()
    },
    {
      id: 'demo-journal-2',
      user_id: 'demo-user',
      trade_id: 'demo-0',
      title: 'Successful GBPUSD Trade',
      content: "I executed a buy trade on GBPUSD today based on the breakout strategy I've been developing. The price broke above the key resistance level with strong momentum and I entered at 1.2650. The trade worked out well and I closed with a profit of $120. I need to continue refining this strategy as it shows promise.",
      tags: ['success', 'GBPUSD', 'breakout'],
      entry_date: new Date(now.getFullYear(), now.getMonth(), now.getDate() - 3).toISOString().split('T')[0],
      created_at: new Date(now.getFullYear(), now.getMonth(), now.getDate() - 3).toISOString(),
      updated_at: new Date(now.getFullYear(), now.getMonth(), now.getDate() - 3).toISOString()
    },
    {
      id: 'demo-journal-3',
      user_id: 'demo-user',
      trade_id: 'demo-5',
      title: 'Emotional Trading Mistake',
      content: "I made a mistake today by entering a trade without proper analysis. After losing the previous trade, I was trying to recover my losses quickly and entered a USDJPY sell position without confirming my signals. This resulted in another loss. I need to work on my emotional discipline and stick to my trading plan.",
      tags: ['mistake', 'psychology', 'USDJPY'],
      entry_date: new Date(now.getFullYear(), now.getMonth(), now.getDate() - 5).toISOString().split('T')[0],
      created_at: new Date(now.getFullYear(), now.getMonth(), now.getDate() - 5).toISOString(),
      updated_at: new Date(now.getFullYear(), now.getMonth(), now.getDate() - 5).toISOString()
    },
    {
      id: 'demo-journal-4',
      user_id: 'demo-user',
      trade_id: null,
      title: 'Weekly Review',
      content: "This week I took 12 trades with 7 winners and 5 losers, resulting in a net profit of $350. My win rate was 58% which is slightly below my target of 60%. I noticed that my trades based on support/resistance levels performed better than my trend-following trades. I'll focus more on support/resistance setups next week.",
      tags: ['weekly-review', 'strategy'],
      entry_date: new Date(now.getFullYear(), now.getMonth(), now.getDate() - 7).toISOString().split('T')[0],
      created_at: new Date(now.getFullYear(), now.getMonth(), now.getDate() - 7).toISOString(),
      updated_at: new Date(now.getFullYear(), now.getMonth(), now.getDate() - 7).toISOString()
    },
    {
      id: 'demo-journal-5',
      user_id: 'demo-user',
      trade_id: null,
      title: 'New Strategy Development',
      content: "I'm developing a new strategy based on the 200 EMA and RSI indicator. The idea is to look for RSI divergence when price is near the 200 EMA. Initial backtesting shows promising results with a win rate of 65% and a risk-reward ratio of 1:2. I'll continue to refine this strategy before implementing it in live trading.",
      tags: ['strategy', 'development', 'technical-analysis'],
      entry_date: new Date(now.getFullYear(), now.getMonth(), now.getDate() - 10).toISOString().split('T')[0],
      created_at: new Date(now.getFullYear(), now.getMonth(), now.getDate() - 10).toISOString(),
      updated_at: new Date(now.getFullYear(), now.getMonth(), now.getDate() - 10).toISOString()
    }
  ]
}
