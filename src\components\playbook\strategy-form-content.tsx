"use client"

import { useState, useEffect } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { MARKET_CONDITIONS, TIMEFRAMES, STRATEGY_STATUSES } from "@/types/playbook"

import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Button } from "@/components/ui/button"
import { MultiSelect } from "@/components/ui/multi-select"
import { X, BookMarked, FileText, BarChart3, Target, Layers, Tag, Activity, Clock } from "lucide-react"
import { cn } from "@/lib/utils"
import { Badge } from "@/components/ui/badge"

// Define the form schema
const formSchema = z.object({
  name: z.string().min(1, "Name is required").max(100, "Name must be less than 100 characters"),
  description: z.string().optional(),
  market_conditions: z.array(z.string()).min(1, "Select at least one market condition"),
  timeframes: z.array(z.string()).min(1, "Select at least one timeframe"),
  instruments: z.array(z.string()).min(1, "Enter at least one instrument"),
  risk_reward_ratio: z.coerce.number().positive().optional().nullable(),
  expected_win_rate: z.coerce.number().min(0).max(100).optional().nullable(),
  status: z.enum(["active", "testing", "archived"]).optional().default("active"),
})

type FormValues = {
  name: string
  description?: string
  market_conditions: string[]
  timeframes: string[]
  instruments: string[]
  risk_reward_ratio?: number | null
  expected_win_rate?: number | null
  status?: 'active' | 'testing' | 'archived'
}

interface StrategyFormContentProps {
  onSubmit: (data: FormValues) => void
  initialData?: FormValues | null
  isSubmitting?: boolean
}

export function StrategyFormContent({
  onSubmit,
  initialData = null,
  isSubmitting = false
}: StrategyFormContentProps) {
  const [instrumentInput, setInstrumentInput] = useState("")
  const [selectedInstruments, setSelectedInstruments] = useState<string[]>(
    initialData?.instruments || []
  )

  // Initialize form with default values or existing strategy values
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: initialData || {
      name: "",
      description: "",
      market_conditions: [],
      timeframes: [],
      instruments: [],
      risk_reward_ratio: null,
      expected_win_rate: null,
      status: "active",
    },
  })

  // Update form when initialData changes
  useEffect(() => {
    if (initialData) {
      form.reset(initialData)
      setSelectedInstruments(initialData.instruments || [])
    }
  }, [initialData, form])

  // Handle adding an instrument
  const handleAddInstrument = () => {
    if (instrumentInput.trim() && !selectedInstruments.includes(instrumentInput.trim())) {
      const newInstruments = [...selectedInstruments, instrumentInput.trim()]
      setSelectedInstruments(newInstruments)
      form.setValue("instruments", newInstruments)
      setInstrumentInput("")
    }
  }

  // Handle removing an instrument
  const handleRemoveInstrument = (instrument: string) => {
    const newInstruments = selectedInstruments.filter(i => i !== instrument)
    setSelectedInstruments(newInstruments)
    form.setValue("instruments", newInstruments)
  }

  // Handle form submission
  const handleSubmit = (values: FormValues) => {
    // Ensure status always has a value
    const submissionData = {
      ...values,
      status: values.status || "active"
    }
    onSubmit(submissionData)
  }

  return (
    <div className="space-y-6">
      <div className="space-y-3 mb-6">
        <div className="flex items-center gap-2">
          <div className="p-2 rounded-full bg-blue-100 dark:bg-blue-950/50">
            <BookMarked className="h-5 w-5 text-blue-600 dark:text-blue-400" />
          </div>
          <div>
            <h3 className="text-lg font-medium">Strategy Details</h3>
            <p className="text-sm text-muted-foreground">
              Define the core details of your trading strategy
            </p>
          </div>
        </div>
        <div className="h-1 w-full bg-gradient-to-r from-blue-200 to-indigo-200 dark:from-blue-900/40 dark:to-indigo-900/40 rounded-full"></div>
      </div>

      <Form {...form}>
        <form id="strategy-form" onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem className="bg-card/30 p-4 rounded-lg border border-border/50 shadow-sm">
                <div className="flex items-center gap-2 mb-2">
                  <FileText className="h-4 w-4 text-blue-500" />
                  <FormLabel className="text-base">Strategy Name</FormLabel>
                </div>
                <FormControl>
                  <Input placeholder="Breakout Strategy" {...field} className="border-blue-200 dark:border-blue-900/50 focus-visible:ring-blue-500" />
                </FormControl>
                <FormDescription className="mt-2">
                  A clear, descriptive name for your strategy
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="description"
            render={({ field }) => (
              <FormItem className="bg-card/30 p-4 rounded-lg border border-border/50 shadow-sm">
                <div className="flex items-center gap-2 mb-2">
                  <BarChart3 className="h-4 w-4 text-indigo-500" />
                  <FormLabel className="text-base">Description</FormLabel>
                </div>
                <FormControl>
                  <Textarea
                    placeholder="This strategy focuses on trading breakouts from key support and resistance levels..."
                    className="min-h-[120px] border-blue-200 dark:border-blue-900/50 focus-visible:ring-blue-500"
                    {...field}
                    value={field.value || ""}
                  />
                </FormControl>
                <FormDescription className="mt-2">
                  Describe your strategy, its core principles, and when to use it
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormField
              control={form.control}
              name="market_conditions"
              render={({ field }) => (
                <FormItem className="bg-card/30 p-4 rounded-lg border border-border/50 shadow-sm">
                  <div className="flex items-center gap-2 mb-2">
                    <Activity className="h-4 w-4 text-green-500" />
                    <FormLabel className="text-base">Market Conditions</FormLabel>
                  </div>
                  <FormControl>
                    <MultiSelect
                      placeholder="Select conditions"
                      selected={field.value}
                      options={MARKET_CONDITIONS.map(condition => ({
                        label: condition.charAt(0).toUpperCase() + condition.slice(1).replace('_', ' '),
                        value: condition
                      }))}
                      onChange={field.onChange}
                      className="border-blue-200 dark:border-blue-900/50 focus-visible:ring-blue-500"
                    />
                  </FormControl>
                  <FormDescription className="mt-2">
                    Market conditions where this strategy works best
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="timeframes"
              render={({ field }) => (
                <FormItem className="bg-card/30 p-4 rounded-lg border border-border/50 shadow-sm">
                  <div className="flex items-center gap-2 mb-2">
                    <Clock className="h-4 w-4 text-purple-500" />
                    <FormLabel className="text-base">Timeframes</FormLabel>
                  </div>
                  <FormControl>
                    <MultiSelect
                      placeholder="Select timeframes"
                      selected={field.value}
                      options={TIMEFRAMES.map(timeframe => ({
                        label: timeframe.toUpperCase(),
                        value: timeframe
                      }))}
                      onChange={field.onChange}
                      className="border-blue-200 dark:border-blue-900/50 focus-visible:ring-blue-500"
                    />
                  </FormControl>
                  <FormDescription className="mt-2">
                    Timeframes where this strategy is applicable
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <FormField
            control={form.control}
            name="instruments"
            render={({ field }) => (
              <FormItem className="bg-card/30 p-4 rounded-lg border border-border/50 shadow-sm">
                <div className="flex items-center gap-2 mb-2">
                  <Tag className="h-4 w-4 text-amber-500" />
                  <FormLabel className="text-base">Instruments</FormLabel>
                </div>
                <div className="flex gap-2">
                  <FormControl>
                    <Input
                      placeholder="EURUSD, AAPL, etc."
                      value={instrumentInput}
                      onChange={(e) => setInstrumentInput(e.target.value)}
                      onKeyDown={(e) => {
                        if (e.key === "Enter") {
                          e.preventDefault()
                          handleAddInstrument()
                        }
                      }}
                      className="border-blue-200 dark:border-blue-900/50 focus-visible:ring-blue-500"
                    />
                  </FormControl>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleAddInstrument}
                    className="border-blue-200 hover:bg-blue-50 dark:border-blue-900/50 dark:hover:bg-blue-900/20"
                  >
                    Add
                  </Button>
                </div>
                <div className="flex flex-wrap gap-2 mt-3 min-h-[40px] p-2 bg-muted/30 rounded-md border border-border/50">
                  {selectedInstruments.length === 0 ? (
                    <p className="text-xs text-muted-foreground w-full text-center py-1">No instruments added yet</p>
                  ) : (
                    selectedInstruments.map((instrument) => (
                      <Badge
                        key={instrument}
                        variant="secondary"
                        className="flex items-center gap-1 bg-amber-100 dark:bg-amber-900/30 text-amber-800 dark:text-amber-300"
                      >
                        {instrument}
                        <Button
                          type="button"
                          variant="ghost"
                          size="icon"
                          className={cn(
                            "h-4 w-4 p-0 text-amber-600 hover:text-amber-800 dark:text-amber-400 dark:hover:text-amber-300"
                          )}
                          onClick={() => handleRemoveInstrument(instrument)}
                        >
                          <X className="h-3 w-3" />
                          <span className="sr-only">Remove {instrument}</span>
                        </Button>
                      </Badge>
                    ))
                  )}
                </div>
                <FormDescription className="mt-2">
                  Financial instruments this strategy is designed for
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormField
              control={form.control}
              name="risk_reward_ratio"
              render={({ field }) => (
                <FormItem className="bg-card/30 p-4 rounded-lg border border-border/50 shadow-sm">
                  <div className="flex items-center gap-2 mb-2">
                    <Target className="h-4 w-4 text-rose-500" />
                    <FormLabel className="text-base">Risk/Reward Ratio</FormLabel>
                  </div>
                  <FormControl>
                    <Input
                      type="number"
                      step="0.1"
                      placeholder="1.5"
                      {...field}
                      value={field.value === null ? "" : field.value}
                      onChange={(e) => {
                        const value = e.target.value === "" ? null : parseFloat(e.target.value)
                        field.onChange(value)
                      }}
                      className="border-blue-200 dark:border-blue-900/50 focus-visible:ring-blue-500"
                    />
                  </FormControl>
                  <FormDescription className="mt-2">
                    Target risk/reward ratio (e.g., 1.5 means 1.5:1 reward to risk)
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="expected_win_rate"
              render={({ field }) => (
                <FormItem className="bg-card/30 p-4 rounded-lg border border-border/50 shadow-sm">
                  <div className="flex items-center gap-2 mb-2">
                    <BarChart3 className="h-4 w-4 text-emerald-500" />
                    <FormLabel className="text-base">Expected Win Rate (%)</FormLabel>
                  </div>
                  <FormControl>
                    <Input
                      type="number"
                      step="1"
                      min="0"
                      max="100"
                      placeholder="60"
                      {...field}
                      value={field.value === null ? "" : field.value}
                      onChange={(e) => {
                        const value = e.target.value === "" ? null : parseFloat(e.target.value)
                        field.onChange(value)
                      }}
                      className="border-blue-200 dark:border-blue-900/50 focus-visible:ring-blue-500"
                    />
                  </FormControl>
                  <FormDescription className="mt-2">
                    Expected win rate as a percentage
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <FormField
            control={form.control}
            name="status"
            render={({ field }) => (
              <FormItem className="bg-card/30 p-4 rounded-lg border border-border/50 shadow-sm">
                <div className="flex items-center gap-2 mb-2">
                  <Layers className="h-4 w-4 text-cyan-500" />
                  <FormLabel className="text-base">Status</FormLabel>
                </div>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger className="border-blue-200 dark:border-blue-900/50 focus-visible:ring-blue-500">
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {STRATEGY_STATUSES.map((status) => (
                      <SelectItem key={status.value} value={status.value}>
                        {status.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormDescription className="mt-2">
                  Current status of this strategy
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Submit button moved to wizard footer */}
        </form>
      </Form>
    </div>
  )
}
