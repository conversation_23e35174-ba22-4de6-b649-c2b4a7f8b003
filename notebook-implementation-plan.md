# Notebook Feature Implementation Plan

## 1. Database Schema

### 1.1. Create Tables
- `notebook_entries`: Stores all notebook entries
- `notebook_tags`: Stores all available tags
- `notebook_entry_tags`: Junction table for entry-tag relationships
- `notebook_folders`: Stores user-defined folders
- `notebook_deleted_entries`: Stores deleted entries for recovery

### 1.2. Migration File
Create a migration file with:
- Table definitions
- Row-level security policies
- Triggers for updated_at timestamps
- Functions for tag and folder counts

## 2. UI Components

### 2.1. Layout Components
- `NotebookLayout`: Three-column layout wrapper
- `NotebookSidebar`: Left sidebar with folders and tags
- `NoteList`: Middle section showing note list
- `NoteEditor`: Right section with editor

### 2.2. Editor Components
- `NotebookEditor`: TipTap-based rich text editor
- `EditorToolbar`: Formatting options toolbar
- `TagSelector`: Component for adding/removing tags
- `TemplateSelector`: Component for template selection

### 2.3. List Components
- `NoteCard`: Card component for note list items
- `FolderItem`: Component for folder items in sidebar
- `TagItem`: Component for tag items in sidebar

## 3. API Routes

### 3.1. Notebook Entries
- `GET /api/notebook`: List entries with filtering
- `POST /api/notebook`: Create new entry
- `GET /api/notebook/[id]`: Get single entry
- `PUT /api/notebook/[id]`: Update entry
- `DELETE /api/notebook/[id]`: Delete entry

### 3.2. Folders and Tags
- `GET /api/notebook/folders`: List folders
- `POST /api/notebook/folders`: Create folder
- `GET /api/notebook/tags`: List tags
- `POST /api/notebook/tags`: Create tag

### 3.3. Templates
- `GET /api/notebook/templates`: List templates
- `POST /api/notebook/templates`: Create template

### 3.4. Deleted Entries
- `GET /api/notebook/deleted`: List deleted entries
- `POST /api/notebook/deleted/restore`: Restore deleted entry

## 4. Data Integration

### 4.1. Journal Import
- Create function to import trade journal entries
- Create function to import daily journal entries
- Create system folders for "Trade Notes" and "Daily Journal"
- Implement two-way sync for journal entries

### 4.2. Templates System
- Create default templates
- Implement template saving functionality
- Add "Recently used templates" tracking

## 5. State Management

### 5.1. Custom Hooks
- `useNotebook`: Main hook for notebook operations
- `useNotebookFolders`: Hook for folder operations
- `useNotebookTags`: Hook for tag operations
- `useNotebookTemplates`: Hook for template operations

### 5.2. Context Providers
- `NotebookProvider`: Context for notebook state

## 6. Implementation Steps

### 6.1. Database Setup
1. Create migration file with all tables and policies
2. Apply migration to Supabase

### 6.2. TypeScript Types
1. Define types for all notebook entities
2. Create type definitions for API responses

### 6.3. API Implementation
1. Create API routes for all notebook operations
2. Implement journal import and sync functionality

### 6.4. UI Implementation
1. Create the three-column layout
2. Implement the sidebar with folders and tags
3. Implement the note list with filtering
4. Implement the rich text editor with TipTap
5. Add template selection functionality
6. Implement tagging system

### 6.5. Integration
1. Add notebook link to main navigation
2. Import existing journal entries
3. Set up two-way sync with journal entries

## 7. Testing

### 7.1. Unit Tests
1. Test API routes
2. Test custom hooks
3. Test UI components

### 7.2. Integration Tests
1. Test journal import and sync
2. Test template system
3. Test tagging system

## 8. Deployment

1. Deploy database changes
2. Deploy application changes
3. Verify functionality in production
