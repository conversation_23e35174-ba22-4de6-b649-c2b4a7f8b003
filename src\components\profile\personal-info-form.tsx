"use client"

import { useState, useEffect } from "react"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { z } from "zod"
import { Button } from "@/components/ui/button"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { toast } from "sonner"
import { getSupabaseBrowser } from "@/lib/supabase"
import { Loader2 } from "lucide-react"

const personalInfoSchema = z.object({
  full_name: z.string().min(2, {
    message: "Full name must be at least 2 characters.",
  }),
  email: z.string().email({
    message: "Please enter a valid email address.",
  }),
  phone: z.string().optional(),
  bio: z.string().max(500, {
    message: "<PERSON><PERSON> must not exceed 500 characters.",
  }).optional(),
  location: z.string().optional(),
  website: z.string().url({
    message: "Please enter a valid URL.",
  }).optional().or(z.literal("")),
})

type PersonalInfoValues = z.infer<typeof personalInfoSchema>

interface PersonalInfoFormProps {
  userId: string;
  userEmail?: string;
  initialData?: {
    full_name: string;
    email: string;
    phone: string;
    bio: string;
    location: string;
    website: string;
  };
}

export function PersonalInfoForm({ userId, userEmail, initialData }: PersonalInfoFormProps) {
  const [isLoading, setIsLoading] = useState(false)
  const supabase = getSupabaseBrowser()

  const form = useForm<PersonalInfoValues>({
    resolver: zodResolver(personalInfoSchema),
    defaultValues: initialData || {
      full_name: "",
      email: userEmail || "",
      phone: "",
      bio: "",
      location: "",
      website: "",
    },
  })

  async function onSubmit(data: PersonalInfoValues) {
    if (!userId) {
      toast.error("You must be logged in to update your profile")
      return
    }

    setIsLoading(true)

    try {
      // Use the API route to update the profile
      const response = await fetch('/api/user-profile', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId,
          profileData: {
            ...data,
            updated_at: new Date().toISOString(),
          }
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to update profile');
      }

      // Update auth metadata
      const { error: authUpdateError } = await supabase.auth.updateUser({
        data: { full_name: data.full_name }
      });

      if (authUpdateError) {
        console.error("Error updating auth metadata:", authUpdateError);
        // Don't return, as the profile was updated successfully
      }

      toast.success("Profile updated successfully");
    } catch (error: any) {
      // Log detailed error information
      console.error("Error saving profile:", {
        message: error?.message || 'No error message',
        code: error?.code,
        name: error?.name,
        details: error?.details || error
      });

      // Show appropriate error message based on the error type
      if (error?.message?.includes('auth')) {
        toast.error("Authentication error. Please try signing out and back in.");
      } else if (error?.message?.includes('network')) {
        toast.error("Network error. Please check your internet connection.");
      } else if (error?.message?.includes('23505')) {
        toast.error("A profile with this information already exists.");
      } else if (error?.message?.includes('42')) { // Database object errors
        toast.error("Database error. Please contact support if this persists.");
      } else {
        toast.error(error?.message || "Could not save your profile. Please try again later.");
      }
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="full_name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Full Name</FormLabel>
                <FormControl>
                  <Input placeholder="John Doe" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Email</FormLabel>
                <FormControl>
                  <Input placeholder="<EMAIL>" {...field} disabled />
                </FormControl>
                <FormDescription>
                  Email cannot be changed
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="phone"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Phone Number</FormLabel>
                <FormControl>
                  <Input placeholder="+****************" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="location"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Location</FormLabel>
                <FormControl>
                  <Input placeholder="New York, USA" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="website"
            render={({ field }) => (
              <FormItem className="col-span-2">
                <FormLabel>Website</FormLabel>
                <FormControl>
                  <Input placeholder="https://example.com" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="bio"
            render={({ field }) => (
              <FormItem className="col-span-2">
                <FormLabel>Bio</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Tell us a bit about yourself"
                    className="resize-none min-h-[120px]"
                    {...field}
                  />
                </FormControl>
                <FormDescription>
                  {field.value?.length || 0}/500 characters
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <Button type="submit" disabled={isLoading}>
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Saving...
            </>
          ) : (
            "Save Changes"
          )}
        </Button>
      </form>
    </Form>
  )
}
