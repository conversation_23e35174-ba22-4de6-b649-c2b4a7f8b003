import { cookies } from 'next/headers';
import { createServerClient } from '@supabase/ssr';
import { redirect } from 'next/navigation';
import type { Database } from '@/types/supabase';
import ProfileClient from './client';

export default async function ProfilePage() {
  // Get cookies for server-side Supabase client
  const cookieStore = await cookies();
  const supabase = createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value;
        },
        set(_name: string, _value: string, _options: any) {
          // Server components can't set cookies directly
        },
        remove(_name: string, _options: any) {
          // Server components can't remove cookies directly
        }
      },
    }
  );

  // Get authenticated user
  const { data: { user }, error: userError } = await supabase.auth.getUser();
  if (userError || !user) {
    redirect('/login');
  }

  const userId = user.id;
  const userEmail = user.email;
  const userName = user.user_metadata?.full_name || '';

  // Fetch user profile data
  let profileData = null;
  try {
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single();

    if (!profileError && profile) {
      profileData = profile;
    } else if (profileError && profileError.code !== 'PGRST116') {
      // Log error if it's not just "no rows returned"
      console.error('Error fetching profile:', profileError);
    }
  } catch (error) {
    console.error('Error accessing profiles table:', error);
  }

  // Fetch user preferences
  let preferencesData = null;
  try {
    const { data: preferences, error: preferencesError } = await supabase
      .from('user_preferences')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (!preferencesError && preferences) {
      preferencesData = preferences;
    } else if (preferencesError && preferencesError.code !== 'PGRST116') {
      // Log error if it's not just "no rows returned"
      console.error('Error fetching preferences:', preferencesError);
    }
  } catch (error) {
    console.error('Error accessing user_preferences table:', error);
  }

  // Check if avatar exists and get public URL
  let avatarUrl = null;
  if (profileData?.avatar_url) {
    try {
      const { data: publicUrlData } = await supabase
        .storage
        .from('avatars')
        .getPublicUrl(profileData.avatar_url);

      if (publicUrlData) {
        avatarUrl = publicUrlData.publicUrl;
      }
    } catch (error) {
      console.error('Error getting avatar URL:', error);
    }
  }

  // Pass the fetched data to the client component
  return (
    <ProfileClient
      userId={userId}
      userEmail={userEmail}
      userName={userName}
      profileData={profileData}
      preferencesData={preferencesData}
      avatarUrl={avatarUrl}
    />
  );
}
