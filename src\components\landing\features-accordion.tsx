"use client"

import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion'
import { BarChart3, BookOpen, Calendar, LineChart } from 'lucide-react'
import Image from 'next/image'
import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'

export default function FeaturesAccordion() {
    type ImageKey = 'analytics' | 'journal' | 'calendar' | 'strategies'
    const [activeItem, setActiveItem] = useState<ImageKey>('analytics')

    const images = {
        'analytics': {
            image: '/placeholder-chart.png',
            alt: 'Advanced analytics dashboard',
        },
        'journal': {
            image: '/placeholder-journal.png',
            alt: 'Trading journal interface',
        },
        'calendar': {
            image: '/placeholder-calendar.png',
            alt: 'Trading calendar view',
        },
        'strategies': {
            image: '/placeholder-strategy.png',
            alt: 'Strategy management interface',
        },
    }

    return (
        <section id="features" className="py-16 md:py-24 lg:py-32 relative">
            <div className="bg-gradient-to-b from-background to-muted/30 absolute inset-0 -z-10"></div>
            <div className="mx-auto max-w-5xl space-y-8 px-6 md:space-y-16 lg:space-y-20">
                <div className="relative z-10 mx-auto max-w-2xl space-y-6 text-center">
                    <h2 className="text-balance text-3xl font-bold md:text-4xl lg:text-5xl">Powerful Tools for Serious Traders</h2>
                    <p className="text-muted-foreground">TradePivot offers a comprehensive suite of tools designed to help you understand your trading patterns, identify strengths and weaknesses, and improve your overall performance.</p>
                </div>

                <div className="grid gap-12 sm:px-12 md:grid-cols-2 lg:gap-20 lg:px-0">
                    <Accordion
                        type="single"
                        value={activeItem}
                        onValueChange={(value) => setActiveItem(value as ImageKey)}
                        className="w-full">
                        <AccordionItem value="analytics">
                            <AccordionTrigger>
                                <div className="flex items-center gap-2 text-base">
                                    <BarChart3 className="size-4" />
                                    Advanced Analytics
                                </div>
                            </AccordionTrigger>
                            <AccordionContent>Gain deep insights into your trading performance with comprehensive analytics and visualizations. Track key metrics like win rate, profit factor, and drawdown to understand what's working and what needs improvement.</AccordionContent>
                        </AccordionItem>
                        <AccordionItem value="journal">
                            <AccordionTrigger>
                                <div className="flex items-center gap-2 text-base">
                                    <BookOpen className="size-4" />
                                    Trading Journal
                                </div>
                            </AccordionTrigger>
                            <AccordionContent>Document your trading journey with a comprehensive journal system. Record your thoughts, emotions, and market observations alongside your trades to develop a deeper understanding of your decision-making process.</AccordionContent>
                        </AccordionItem>
                        <AccordionItem value="calendar">
                            <AccordionTrigger>
                                <div className="flex items-center gap-2 text-base">
                                    <Calendar className="size-4" />
                                    Trading Calendar
                                </div>
                            </AccordionTrigger>
                            <AccordionContent>Visualize your trading activity across days, weeks, and months with our interactive calendar. Identify patterns in your trading frequency and performance to optimize your trading schedule.</AccordionContent>
                        </AccordionItem>
                        <AccordionItem value="strategies">
                            <AccordionTrigger>
                                <div className="flex items-center gap-2 text-base">
                                    <LineChart className="size-4" />
                                    Strategy Management
                                </div>
                            </AccordionTrigger>
                            <AccordionContent>Create, test, and refine your trading strategies with our strategy management system. Track performance by strategy to identify which approaches work best in different market conditions.</AccordionContent>
                        </AccordionItem>
                    </Accordion>

                    <div className="relative flex items-center justify-center">
                        <div className="relative aspect-video w-full overflow-hidden rounded-xl border bg-background p-2">
                            <AnimatePresence mode="wait">
                                <motion.div
                                    key={activeItem}
                                    initial={{ opacity: 0, y: 10 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    exit={{ opacity: 0, y: -10 }}
                                    transition={{ duration: 0.3 }}
                                    className="relative h-full w-full overflow-hidden rounded-lg bg-muted"
                                >
                                    <div className="flex h-full items-center justify-center">
                                        <p className="text-sm text-muted-foreground">Image placeholder for {images[activeItem].alt}</p>
                                    </div>
                                </motion.div>
                            </AnimatePresence>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    )
}
