"use client"

import { useState, useEffect } from "react"
import { NotebookEntry, NotebookViewMode, NotebookFolderWithMeta } from "@/types/notebook"
import { NoteCard } from "./note-card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { ScrollArea } from "@/components/ui/scroll-area"
import { useSidebar } from "@/contexts/sidebar-context"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu"
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink
} from "@/components/ui/pagination"
import { ChevronLeft, ChevronRight } from "lucide-react"
import {
  Grid2X2,
  List,
  Plus,
  Search,
  SlidersHorizontal,
  SortAsc,
  SortDesc,
  X
} from "lucide-react"
import { cn } from "@/lib/utils"

interface NotebookListProps {
  entries: NotebookEntry[]
  count: number
  page: number
  limit: number
  folders?: NotebookFolderWithMeta[]
  onPageChange: (page: number) => void
  onSearch: (term: string) => void
  onCreateNew: () => void
  onViewEntry: (id: string) => void
  searchTerm?: string
  selectedNoteId?: string | null
  selectedFolderId?: string | null
  isLoading?: boolean
  className?: string
  defaultViewMode?: NotebookViewMode
  // New search action props
  onSearchFolder?: () => void
  onSearchTag?: () => void
}

export function NotebookList({
  entries,
  count,
  page,
  limit,
  folders = [],
  onPageChange,
  onSearch,
  onCreateNew,
  onViewEntry,
  searchTerm = "",
  selectedNoteId = null,
  selectedFolderId = null,
  isLoading = false,
  className,
  defaultViewMode = "list",
  onSearchFolder,
  onSearchTag
}: NotebookListProps) {
  // Removed unused router
  const [viewMode, setViewMode] = useState<NotebookViewMode>(defaultViewMode)
  const [localSearchTerm, setLocalSearchTerm] = useState(searchTerm)
  const [sortField, setSortField] = useState<"updated_at" | "title">("updated_at")
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc")
  const { isSidebarCollapsed } = useSidebar()

  // Custom hook to detect if device should use list view only
  const useIsMobileOrTablet = () => {
    const [isMobileOrTablet, setIsMobileOrTablet] = useState(false)

    useEffect(() => {
      const checkScreenSize = () => {
        if (typeof window !== 'undefined') {
          setIsMobileOrTablet(window.innerWidth < 1024)
        }
      }

      // Check on mount
      checkScreenSize()

      // Add resize listener
      window.addEventListener('resize', checkScreenSize)
      return () => window.removeEventListener('resize', checkScreenSize)
    }, [])

    return isMobileOrTablet
  }

  const isMobileOrTablet = useIsMobileOrTablet()

  // Sync local search term with parent search term
  useEffect(() => {
    setLocalSearchTerm(searchTerm)
  }, [searchTerm])

  // Calculate total pages
  const totalPages = Math.ceil(count / limit)

  // Determine grid classes based on screen size and sidebar state
  const getGridClasses = () => {
    // Base: 1 column on mobile
    // md (768px+): 2 columns by default for laptops
    // lg (1024px+): 2 columns when sidebar expanded, 3 when collapsed
    // xl (1280px+): Always 3 columns regardless of sidebar state
    if (isSidebarCollapsed) {
      return "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3 gap-3"
    } else {
      return "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3 gap-3"
    }
  }

  // Force list view on mobile and tablet devices
  useEffect(() => {
    if (isMobileOrTablet) {
      setViewMode('list')
    }
  }, [isMobileOrTablet])

  // Debounced search effect
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (localSearchTerm !== searchTerm) {
        onSearch(localSearchTerm)
      }
    }, 300) // 300ms debounce

    return () => clearTimeout(timeoutId)
  }, [localSearchTerm, searchTerm, onSearch])

  // Handle search input with real-time search
  const handleSearchInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value
    setLocalSearchTerm(newValue)
  }

  // Handle search submit (for Enter key)
  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onSearch(localSearchTerm)
  }

  // Handle sort change
  const handleSortChange = (field: "updated_at" | "title") => {
    if (sortField === field) {
      // Toggle order if same field
      setSortOrder(sortOrder === "asc" ? "desc" : "asc")
    } else {
      // Set new field with default desc order
      setSortField(field)
      setSortOrder("desc")
    }

    // TODO: Implement sorting logic with API
  }

  return (
    <div className={cn("h-full flex flex-col", className)}>
      {/* Toolbar */}
      <div className="flex flex-col p-3 border-b space-y-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <input
              type="checkbox"
              id="select-all"
              className="mr-2"
            />
            <label htmlFor="select-all" className="text-sm">Select All</label>
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              className="text-xs h-8"
              onClick={onCreateNew}
            >
              <Plus className="h-3.5 w-3.5 mr-1" />
              New note
            </Button>

            <div className="hidden lg:flex border rounded-md overflow-hidden">
              <Button
                variant="ghost"
                size="icon"
                className={cn("h-8 w-8 rounded-none", viewMode === "list" && "bg-muted")}
                onClick={() => setViewMode("list")}
                title="List view"
              >
                <List className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                className={cn(
                  "h-8 w-8 rounded-none",
                  viewMode === "grid" && "bg-muted",
                  isMobileOrTablet && "opacity-50 cursor-not-allowed"
                )}
                onClick={() => {
                  // Only allow grid view on desktop screens (1024px and above)
                  if (!isMobileOrTablet) {
                    setViewMode("grid")
                  }
                }}
                title={isMobileOrTablet ? "Grid view (Desktop only)" : "Grid view"}
                disabled={isMobileOrTablet}
              >
                <Grid2X2 className="h-4 w-4" />
              </Button>
            </div>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8"
                >
                  <SlidersHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => handleSortChange("updated_at")}>
                  {sortField === "updated_at" ? (
                    sortOrder === "desc" ? (
                      <SortDesc className="h-4 w-4 mr-2" />
                    ) : (
                      <SortAsc className="h-4 w-4 mr-2" />
                    )
                  ) : null}
                  Sort by date
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleSortChange("title")}>
                  {sortField === "title" ? (
                    sortOrder === "desc" ? (
                      <SortDesc className="h-4 w-4 mr-2" />
                    ) : (
                      <SortAsc className="h-4 w-4 mr-2" />
                    )
                  ) : null}
                  Sort by title
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        <div className="space-y-2">
          <form onSubmit={handleSearchSubmit} className="relative">
            <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search notes, regular folders, or tags..."
              className="pl-8 pr-8 h-9"
              value={localSearchTerm}
              onChange={handleSearchInput}
            />
            {localSearchTerm && (
              <button
                type="button"
                onClick={() => {
                  setLocalSearchTerm("")
                  onSearch("")
                }}
                className="absolute right-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground hover:text-foreground"
              >
                <X className="h-4 w-4" />
              </button>
            )}
          </form>

          {/* Search Action Buttons */}
          {localSearchTerm.trim() && (
            <div className="flex items-center gap-2 text-xs">
              <span className="text-muted-foreground">Quick actions:</span>
              <button
                onClick={onSearchFolder}
                disabled={!onSearchFolder}
                className="px-2 py-1 bg-blue-100 text-blue-700 rounded text-xs hover:bg-blue-200 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                📁 Find Folder
              </button>
              <button
                onClick={onSearchTag}
                disabled={!onSearchTag}
                className="px-2 py-1 bg-green-100 text-green-700 rounded text-xs hover:bg-green-200 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                🏷️ Find Tag
              </button>
            </div>
          )}
        </div>

        {/* Search Status */}
        {(searchTerm || selectedFolderId) && (
          <div className="flex items-center gap-2 text-xs text-muted-foreground">
            <span>
              {searchTerm && selectedFolderId && `Searching for "${searchTerm}" in folder "${folders?.find(f => f.id === selectedFolderId)?.name || 'Unknown'}"`}
              {searchTerm && !selectedFolderId && `Searching for "${searchTerm}"`}
              {!searchTerm && selectedFolderId && `Showing all content in folder "${folders?.find(f => f.id === selectedFolderId)?.name || 'Unknown'}"`}
            </span>
            <div className="flex items-center gap-2">
              {searchTerm && (
                <button
                  onClick={() => {
                    setLocalSearchTerm("")
                    onSearch("")
                  }}
                  className="text-primary hover:underline"
                >
                  Clear search
                </button>
              )}
              {selectedFolderId && (
                <button
                  onClick={() => {
                    // This would need to be passed as a prop to clear folder selection
                    // For now, just show the instruction
                  }}
                  className="text-muted-foreground hover:text-foreground text-xs"
                  title="Click 'All Notes' in sidebar to clear folder filter"
                >
                  📁 Folder active
                </button>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Notes List */}
      <ScrollArea className="flex-grow">
        <div className="p-3">
        {isLoading ? (
          <div className="space-y-3">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="h-[80px] bg-muted/30 rounded-md animate-pulse" />
            ))}
          </div>
        ) : entries.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full text-center">
            <div className="text-muted-foreground">
              {folders?.find(f => f.id === selectedFolderId) ?
                `This folder is empty. Create a new note to add it to this folder.` :
                `No notes found`}
            </div>
          </div>
        ) : viewMode === "list" ? (
          <div className={cn(
            "space-y-2 px-0 sm:px-1",
            // Enhanced spacing for touch interaction on mobile/tablet
            isMobileOrTablet && "space-y-3 px-1"
          )}>
            {entries.map((entry) => (
              <NoteCard
                key={entry.id}
                entry={{
                  ...entry,
                  is_selected: selectedNoteId === entry.id
                }}
                viewMode="list"
                folders={folders}
                onClick={() => onViewEntry(entry.id)}
              />
            ))}
          </div>
        ) : (
          <div className={getGridClasses()}>
            {entries.map((entry) => (
              <NoteCard
                key={entry.id}
                entry={{
                  ...entry,
                  is_selected: selectedNoteId === entry.id
                }}
                viewMode="grid"
                folders={folders}
                onClick={() => onViewEntry(entry.id)}
              />
            ))}
          </div>
        )}

        {entries.length > 0 && count > limit && (
          <div className="mt-4">
            <Pagination>
              <PaginationContent>
                <PaginationItem>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onPageChange(Math.max(1, page - 1))}
                    disabled={page === 1}
                    className="gap-1 pl-2.5"
                  >
                    <ChevronLeft className="h-4 w-4" />
                    <span>Previous</span>
                  </Button>
                </PaginationItem>

                {Array.from({ length: Math.min(5, totalPages) }).map((_, i) => {
                  const pageNumber = page <= 3
                    ? i + 1
                    : page >= totalPages - 2
                      ? totalPages - 4 + i
                      : page - 2 + i;

                  if (pageNumber <= 0 || pageNumber > totalPages) return null;

                  return (
                    <PaginationItem key={pageNumber}>
                      <PaginationLink
                        isActive={page === pageNumber}
                        onClick={() => onPageChange(pageNumber)}
                      >
                        {pageNumber}
                      </PaginationLink>
                    </PaginationItem>
                  );
                })}

                <PaginationItem>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onPageChange(Math.min(totalPages, page + 1))}
                    disabled={page === totalPages}
                    className="gap-1 pr-2.5"
                  >
                    <span>Next</span>
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          </div>
        )}
        </div>
      </ScrollArea>
    </div>
  )
}
