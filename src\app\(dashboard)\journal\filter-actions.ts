'use server';

import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { format } from 'date-fns';
import { FilterParams, JournalFilterResult } from './types';
import { normalizeString } from './utils';
import { Database } from '@/types/supabase';

// Cache for responses to avoid redundant queries
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
const responseCache = new Map<string, { data: any, timestamp: number }>();

/**
 * Get a cached response if available and not expired
 */
async function getCachedResponse(key: string): Promise<any | null> {
  const cached = responseCache.get(key);
  if (cached) {
    const now = Date.now();
    if (now - cached.timestamp < CACHE_DURATION) {
      console.log(`Using cached response for key: ${key}`);
      return cached.data;
    } else {
      console.log(`Cache expired for key: ${key}`);
      responseCache.delete(key);
    }
  }
  return null;
}

/**
 * Set a response in the cache
 */
async function setCachedResponse(key: string, data: any): Promise<void> {
  responseCache.set(key, { data, timestamp: Date.now() });
  console.log(`Cached response for key: ${key}`);
}

/**
 * Main function to fetch filtered journal data
 */
export async function fetchFilteredJournalData(
  userId: string,
  accountId: string | null,
  searchTerm?: string,
  tags?: string[],
  startDate?: string,
  endDate?: string,
  activeTab: string = 'with-trades',
  page: number = 1,
  pageSize: number = 50,
  loadMore: boolean = false,
  specificDate?: string
): Promise<JournalFilterResult> {
  // Create a params object for logging and caching
  const params = {
    userId,
    accountId,
    searchTerm,
    tags,
    startDate,
    endDate,
    activeTab,
    page,
    pageSize,
    loadMore,
    specificDate
  };

  // Create a cache key based on the parameters
  const cacheKey = `journal-data-${userId}-${accountId}-${searchTerm}-${tags?.join(',')}-${startDate}-${endDate}-${activeTab}-${specificDate}-${page}-${pageSize}`;

  // Clear the cache for tag searches to ensure we always get fresh results
  // This is important because our tag matching logic is more complex now
  if (tags && tags.length > 0) {
    console.log(`Clearing cache for tag search: ${tags.join(', ')}`);
    responseCache.clear();
  }

  // Check if we have a cached response
  const cachedResponse = await getCachedResponse(cacheKey);
  if (cachedResponse) {
    return cachedResponse;
  }

  // Log the filter parameters for debugging
  console.log('Fetching filtered journal data with parameters:', {
    userId,
    accountId,
    searchTerm: searchTerm || 'none',
    tags: tags?.length ? tags.join(', ') : 'none',
    startDate: startDate || 'none',
    endDate: endDate || 'none',
    activeTab,
    page,
    pageSize,
    loadMore,
    specificDate: specificDate || 'none',
  });

  // Initialize Supabase client
  const cookieStore = await cookies();
  const supabase = createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value;
        },
      },
    }
  );

  // Start building the trades query
  // IMPORTANT: For trade-specific journal entries, we need to focus on trades with journal content
  let tradesQuery = supabase
    .from("trades")
    .select("*", { count: 'exact' })
    .eq("user_id", userId)
    .order("time_close", { ascending: false });

  // If we're on the trade-specific tab, only show trades with journal content
  if (activeTab === "with-trades") {
    console.log("Filtering for trades with journal content");
    tradesQuery = tradesQuery.eq("has_journal_content", true);
  }

  // Apply pagination
  tradesQuery = tradesQuery.range((page - 1) * pageSize, page * pageSize - 1);

  // Apply account filter
  if (accountId) {
    tradesQuery = tradesQuery.eq("account_id", accountId);
  }

  // Apply specific date filter if provided (takes precedence over date range)
  if (specificDate) {
    const nextDay = new Date(specificDate);
    nextDay.setDate(nextDay.getDate() + 1);
    const nextDayStr = format(nextDay, 'yyyy-MM-dd');

    tradesQuery = tradesQuery
      .gte('time_close', `${specificDate}T00:00:00`)
      .lt('time_close', `${nextDayStr}T00:00:00`);
  } else {
    // Apply date range filters if provided
    if (startDate) {
      tradesQuery = tradesQuery.gte('time_close', `${startDate}T00:00:00`);
    }

    if (endDate) {
      tradesQuery = tradesQuery.lte('time_close', `${endDate}T23:59:59.999`);
    }
  }

  // IMPORTANT: For tag filtering, we're going to change our approach
  // Instead of trying to filter by tags in the database query,
  // we'll fetch ALL trades and then filter them in post-processing
  // This is more reliable and ensures we don't miss any matches

  // We'll still log that we're applying tag filters for debugging
  if (tags && tags.length > 0) {
    console.log(`Applying tag filters: ${tags.join(', ')} (will be done in post-processing)`);
  }

  // We're intentionally NOT adding any tag filters to the database query
  // This ensures we get all trades and can apply more flexible matching in post-processing

  // Apply search term filter if provided
  if (searchTerm && searchTerm.trim() !== '') {
    const term = normalizeString(searchTerm);
    console.log(`Applying search filter: "${term}"`);

    // IMPROVED SEARCH APPROACH
    // We'll use a more flexible approach for search term filtering
    // We'll search in notes, symbol, and tags

    // Search in notes and symbol - these are safe fields to search
    tradesQuery = tradesQuery.or(`notes.ilike.%${term}%,symbol.ilike.%${term}%`);

    // For tag search, we need to be more careful with the syntax
    // We'll use a different approach that's more reliable
    try {
      // First, get all trades with non-empty tags
      const taggedTradesQuery = supabase
        .from("trades")
        .select("id, tags")
        .eq("user_id", userId)
        .not("tags", "is", null);

      // Execute the query
      const { data: taggedTrades } = await taggedTradesQuery;

      // Filter trades with matching tags in JavaScript
      // This is more reliable than trying to do complex array filtering in SQL
      const matchingTradeIds = taggedTrades
        ?.filter(trade =>
          Array.isArray(trade.tags) &&
          trade.tags.some((tag: string) =>
            typeof tag === 'string' &&
            normalizeString(tag).includes(normalizeString(term))
          )
        )
        .map(trade => trade.id);

      // If we found trades with matching tags, add their IDs to the main query
      if (matchingTradeIds && matchingTradeIds.length > 0) {
        console.log(`Found ${matchingTradeIds.length} trades with tags matching "${term}"`);

        // IMPORTANT: Store these IDs for later use in post-processing
        // This ensures we don't lose matches during post-processing
        const tagMatchingTradeIds = matchingTradeIds;

        // Add these IDs to our main query using the in operator
        if (matchingTradeIds.length === 1) {
          tradesQuery = tradesQuery.or(`id.eq.${matchingTradeIds[0]}`);
        } else if (matchingTradeIds.length > 1) {
          // For multiple IDs, we need to use the in operator
          tradesQuery = tradesQuery.or(`id.in.(${matchingTradeIds.join(',')})`);
        }

        // CRITICAL FIX: Make sure we're also filtering for has_journal_content
        // This ensures we get trades with both matching tags AND journal content
        if (activeTab === "with-trades") {
          // Get the trades with both matching tags AND journal content
          const journalTradesQuery = supabase
            .from("trades")
            .select("id")
            .eq("user_id", userId)
            .eq("has_journal_content", true)
            .in("id", matchingTradeIds);

          // Execute the query
          const { data: journalTrades } = await journalTradesQuery;

          if (journalTrades && journalTrades.length > 0) {
            console.log(`Found ${journalTrades.length} trades with both matching tags AND journal content`);
          } else {
            console.log(`No trades found with both matching tags AND journal content`);
          }
        }
      }
    } catch (error) {
      console.error("Error in tag search:", error);
      // Continue with the search even if tag search fails
    }

    console.log(`Applied search for term: "${term}"`);
  }

  // Execute the query
  const { data: tradesData, error: tradesError, count: totalCount } = await tradesQuery;

  if (tradesError) {
    console.error('Error fetching trades:', tradesError);
    throw new Error(tradesError.message);
  }

  // Create pagination metadata
  const pagination = {
    currentPage: page,
    totalPages: totalCount ? Math.ceil(totalCount / pageSize) : 1,
    pageSize,
    totalCount: totalCount || 0,
    hasMore: page < (totalCount ? Math.ceil(totalCount / pageSize) : 1)
  };

  // Post-process for tag and search filtering
  let filteredTrades = tradesData || [];

  // CRITICAL FIX: If we have no trades but we're searching for tags, we need to fetch them directly
  if (filteredTrades.length === 0 && tags && tags.length > 0) {
    console.log(`No trades found in initial query, but we're searching for tags: ${tags.join(', ')}`);
    console.log(`Attempting direct tag search...`);

    try {
      // Create a direct query for trades with matching tags AND journal content
      const directTagQuery = supabase
        .from("trades")
        .select("*")
        .eq("user_id", userId)
        .eq("has_journal_content", true);

      // Add account filter if provided
      if (accountId) {
        directTagQuery.eq("account_id", accountId);
      }

      // Execute the query
      const { data: directTagResults } = await directTagQuery;

      if (directTagResults && directTagResults.length > 0) {
        console.log(`Found ${directTagResults.length} trades with journal content for direct tag filtering`);

        // Replace our empty results with these trades
        filteredTrades = directTagResults;
      }
    } catch (error) {
      console.error("Error in direct tag search:", error);
    }
  }

  // If we have tags or a search term, we need to filter the trades
  if ((tags && tags.length > 0) || (searchTerm && searchTerm.trim() !== '')) {
    console.log(`Post-processing trades for filtering`);

    // Log the number of trades before filtering
    console.log(`Number of trades before filtering: ${filteredTrades.length}`);

    // Log the tags we're looking for
    if (tags && tags.length > 0) {
      console.log(`Filtering by tags: ${tags.join(', ')}`);
    }

    // Log the search term we're looking for
    if (searchTerm && searchTerm.trim() !== '') {
      console.log(`Filtering by search term: "${searchTerm}"`);
    }

    // Convert tags to lowercase for case-insensitive comparison
    const normalizedTags = tags?.map(tag => normalizeString(tag)) || [];
    const normalizedSearchTerm = searchTerm ? normalizeString(searchTerm) : '';

    // Extract all available tags from the current result set
    // This helps with searching for tags directly
    const availableTags = new Set<string>();
    filteredTrades.forEach(trade => {
      if (Array.isArray(trade.tags)) {
        trade.tags.forEach((tag: any) => {
          if (typeof tag === 'string') {
            availableTags.add(tag);
          }
        });
      }
    });

    // Log available tags for debugging
    console.log(`Available tags in current result set: ${Array.from(availableTags).join(', ')}`);

    // Check if search term matches any available tag
    if (normalizedSearchTerm) {
      const matchingTags = Array.from(availableTags).filter(tag =>
        normalizeString(tag) === normalizedSearchTerm ||
        normalizeString(tag).includes(normalizedSearchTerm) ||
        normalizedSearchTerm.includes(normalizeString(tag))
      );

      if (matchingTags.length > 0) {
        console.log(`Search term "${normalizedSearchTerm}" matches tags: ${matchingTags.join(', ')}`);
      }
    }

    // Filter trades based on tags and search term
    filteredTrades = filteredTrades.filter(trade => {
      // Log each trade's tags for debugging
      if (normalizedTags.length > 0 && Array.isArray(trade.tags) && trade.tags.length > 0) {
        console.log(`Trade ${trade.id} has tags: ${trade.tags.join(', ')}`);
      }

      // Skip trades without tags if we're filtering by tags
      if (normalizedTags.length > 0 && (!Array.isArray(trade.tags) || trade.tags.length === 0)) {
        return false;
      }

      // Convert trade tags to lowercase for case-insensitive comparison
      const normalizedTradeTags = Array.isArray(trade.tags)
        ? trade.tags.map((tag: any) => typeof tag === 'string' ? normalizeString(tag) : '')
        : [];

      // SIMPLIFIED TAG MATCHING LOGIC
      // Based on our database inspection, we know exactly how tags are stored
      // We'll use a more direct approach focused on exact matches

      // First, log the normalized tags we're looking for and the trade's tags
      if (normalizedTags.length > 0) {
        console.log(`Looking for tags: ${normalizedTags.join(', ')} in trade tags: ${normalizedTradeTags.join(', ')}`);
      }

      // Check if any of the normalized tags match the trade's tags
      const hasTagMatch = normalizedTags.length === 0 || normalizedTags.some(searchTag => {
        // Try exact match first (most reliable)
        const exactMatch = normalizedTradeTags.includes(searchTag);

        // Try case-insensitive exact match
        const caseInsensitiveMatch = normalizedTradeTags.some((tradeTag: string) =>
          tradeTag.toLowerCase() === searchTag.toLowerCase()
        );

        // Try partial matches (if a trade tag contains the search tag or vice versa)
        const partialMatches = normalizedTradeTags.some((tradeTag: string) =>
          tradeTag.includes(searchTag) || searchTag.includes(tradeTag)
        );

        // Log the match results for debugging
        if (exactMatch || caseInsensitiveMatch || partialMatches) {
          console.log(`Match found for tag "${searchTag}" in trade ${trade.id}: exact=${exactMatch}, caseInsensitive=${caseInsensitiveMatch}, partial=${partialMatches}`);
        }

        return exactMatch || caseInsensitiveMatch || partialMatches;
      });

      // If we're not searching, just return the tag match result
      if (!normalizedSearchTerm) {
        return hasTagMatch;
      }

      // IMPROVED SEARCH TERM MATCHING
      // We'll use a more flexible approach for search term matching

      // Check if the search term matches the trade's notes, symbol, or tags
      const hasSearchMatch = (
        // Check notes
        (trade.notes && normalizeString(trade.notes).includes(normalizedSearchTerm)) ||

        // Check symbol
        (trade.symbol && normalizeString(trade.symbol).includes(normalizedSearchTerm)) ||

        // Check if any tag contains the search term
        normalizedTradeTags.some((tag: string) => tag.includes(normalizedSearchTerm)) ||

        // Check if the search term contains any tag
        normalizedTradeTags.some((tag: string) => normalizedSearchTerm.includes(tag)) ||

        // Check if the search term is similar to any tag (fuzzy matching)
        normalizedTradeTags.some((tag: string) => {
          // Simple fuzzy match - if the first 3 characters match
          return tag.length >= 3 && normalizedSearchTerm.length >= 3 &&
                 tag.substring(0, 3) === normalizedSearchTerm.substring(0, 3);
        })
      );

      // Log search match results for debugging
      if (hasSearchMatch) {
        console.log(`Search term "${normalizedSearchTerm}" matched trade ${trade.id}`);
      }

      // If we're searching and have tag filters, both must match
      if (normalizedTags.length > 0) {
        return hasTagMatch && hasSearchMatch;
      }

      // If we're just searching (no tag filters), return the search match result
      return hasSearchMatch;
    }) || [];

    console.log(`Filtered to ${filteredTrades.length} trades after post-processing`);

    // Log more details about the filtered trades
    if (filteredTrades.length > 0) {
      console.log(`First matching trade: ${JSON.stringify({
        id: filteredTrades[0].id,
        symbol: filteredTrades[0].symbol,
        tags: filteredTrades[0].tags,
        hasJournalContent: filteredTrades[0].has_journal_content
      })}`);
    } else {
      console.log(`No trades matched the filters. This could be because:
      1. No trades have the specified tags
      2. No trades have journal content
      3. The tag matching logic needs adjustment`);
    }
  }

  // Calculate trading days from filtered trades
  const tradingDaysSet = new Set<string>();
  filteredTrades.forEach(trade => {
    try {
      const tradeDate = new Date(trade.time_close);
      const dateStr = format(tradeDate, 'yyyy-MM-dd');
      tradingDaysSet.add(dateStr);
    } catch (error) {
      console.error('Error processing trade date:', error);
    }
  });

  // Only fetch daily journal entries if we're on the daily journal tab
  if (activeTab === "without-trades") {
    try {
      console.log(`Fetching daily journal entries for tab: ${activeTab}`);

      // Fetch daily journal entries with matching tags
      const { data: dailyJournalEntries } = await supabase.rpc(
        'get_daily_journal_entries',
        {
          p_user_id: userId,
          p_account_id: accountId || null,
          p_start_date: startDate || null,
          p_end_date: endDate || null,
          p_tags: tags && tags.length > 0 ? tags : null
        }
      );

      // Process the daily journal entries
      if (dailyJournalEntries) {
        console.log(`Retrieved ${dailyJournalEntries.length} daily journal entries`);

        // Filter entries if we're searching by term
        let filteredEntries = dailyJournalEntries;

        if (searchTerm && searchTerm.trim() !== '') {
          const term = normalizeString(searchTerm);

          filteredEntries = dailyJournalEntries.filter((entry: any) => {
            // Check if the note contains the search term
            if (entry.note && normalizeString(entry.note).includes(term)) {
              return true;
            }

            // Check if any tag matches the search term
            if (Array.isArray(entry.tags)) {
              return entry.tags.some((tag: string) =>
                normalizeString(tag).includes(term)
              );
            }

            return false;
          });

          console.log(`Filtered to ${filteredEntries.length} daily journal entries matching term "${term}"`);
        }

        // Add dates from matching daily journal entries
        filteredEntries.forEach((entry: any) => {
          const dateStr = format(new Date(entry.date), 'yyyy-MM-dd');
          tradingDaysSet.add(dateStr);
        });
      }
    } catch (error) {
      console.error('Error fetching daily journal entries:', error);
    }
  }

  // Convert to array and sort in descending order (newest first)
  const tradingDays = Array.from(tradingDaysSet).sort((a, b) => {
    return new Date(b).getTime() - new Date(a).getTime();
  });

  // Process trades with journal content
  const tradesWithJournalContent = filteredTrades.filter(trade => {
    const hasNotes = trade.notes && trade.notes.trim().length > 0;
    const hasScreenshots = Array.isArray(trade.screenshots) && trade.screenshots.length > 0;
    const hasJournalFlag = trade.has_journal_content === true;

    // Log details about trades that might have journal content
    if (hasNotes || hasScreenshots || hasJournalFlag) {
      console.log(`Trade ${trade.id} has journal content:`, {
        hasNotes,
        hasScreenshots,
        hasJournalFlag,
        tags: trade.tags
      });
    }

    return hasNotes || hasScreenshots || hasJournalFlag;
  });

  console.log(`Found ${tradesWithJournalContent.length} trades with journal content`);

  // If we're filtering by tags but no trades with journal content match, log this
  if (tags && tags.length > 0 && tradesWithJournalContent.length === 0) {
    console.log(`No trades with journal content match the tags: ${tags.join(', ')}`);
  }

  // Fetch strategies for trades with strategy_id
  const tradesWithStrategy = filteredTrades.filter(trade => trade.strategy_id);
  const strategyIds = [...new Set(tradesWithStrategy.map(trade => trade.strategy_id))];

  let strategies: Array<{ id: string, name: string }> = [];
  let strategyMap: Record<string, string> = {};

  if (strategyIds.length > 0) {
    const { data: strategiesData, error: strategiesError } = await supabase
      .from('strategies')
      .select('id, name')
      .in('id', strategyIds);

    if (strategiesError) {
      console.error('Error fetching strategies:', strategiesError);
    } else {
      strategies = strategiesData || [];

      // Create strategy map
      strategies.forEach(strategy => {
        strategyMap[strategy.id] = strategy.name;
      });
    }
  }

  // Fetch all available tags
  const { data: allTagsData } = await supabase
    .from("trades")
    .select("tags")
    .eq("user_id", userId)
    .not("tags", "is", null);

  // Extract all tags from trades
  const tradeTags = allTagsData?.flatMap(entry =>
    Array.isArray(entry.tags) ? entry.tags : []
  ) || [];

  // Combine and deduplicate all tags
  const availableTags = [...new Set(tradeTags)].filter(tag => tag && tag.trim() !== '');

  console.log(`Found ${availableTags.length} available tags`);

  // Get trades for a specific date if requested
  let tradesForSpecificDate: any[] = [];
  if (specificDate) {
    // This is already filtered by the query if specificDate was provided
    tradesForSpecificDate = filteredTrades;
  }

  // Convert trading days to ISO strings for consistent format
  const tradingDaysISO = tradingDays.map(dateStr => {
    const date = new Date(dateStr);
    return format(date, 'yyyy-MM-dd');
  });

  // Prepare the result with enhanced data
  const result = {
    // If loading more, we're only interested in the new trades
    // Otherwise, return all filtered trades
    trades: filteredTrades,
    tradingDays: tradingDaysISO, // Use ISO format for consistency
    // If loading more, we only want trades with journal content from the current page
    // This avoids duplicating entries when appending to the existing list
    tradesWithJournalContent: loadMore
      ? filteredTrades.filter(trade =>
          (trade.notes && trade.notes.trim().length > 0) ||
          (Array.isArray(trade.screenshots) && trade.screenshots.length > 0) ||
          trade.has_journal_content === true
        )
      : tradesWithJournalContent,
    strategies,
    strategyMap,
    pagination,
    // Add new data for client optimization
    availableTags,
    tradesForSpecificDate,
    // Add metadata for client
    metadata: {
      totalTrades: totalCount || 0,
      tradesWithJournalCount: tradesWithJournalContent.length,
      uniqueDaysCount: tradingDaysISO.length,
      tagsCount: availableTags.length
    }
  };

  // Cache the result for future requests
  // Only cache if not loading more, as we want to cache the complete initial result
  if (!loadMore) {
    await setCachedResponse(cacheKey, result);
  }

  return result;
}
