import { getSupabase<PERSON>rowser } from './supabase'
import { Strategy, Setup, StrategyRule, StrategyPerformance } from '@/types/playbook'
import { Trade } from '@/types/trade'

// Strategy functions
export async function getStrategies(userId: string): Promise<Strategy[]> {
  const supabase = getSupabaseBrowser()

  try {
    const { data, error } = await supabase
      .from('strategies')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Error fetching strategies:', error)
      return []
    }

    return data || []
  } catch (error) {
    console.error('Error in getStrategies:', error)
    return []
  }
}

export async function getStrategyById(userId: string, strategyId: string): Promise<Strategy | null> {
  const supabase = getSupabaseBrowser()

  try {
    const { data, error } = await supabase
      .from('strategies')
      .select('*')
      .eq('id', strategyId)
      .eq('user_id', userId)
      .single()

    if (error) {
      console.error('Error fetching strategy:', error)
      return null
    }

    return data
  } catch (error) {
    console.error('Error in getStrategyById:', error)
    return null
  }
}

export async function createStrategy(userId: string, strategy: Omit<Strategy, 'id' | 'user_id' | 'created_at' | 'updated_at'>): Promise<Strategy | null> {
  const supabase = getSupabaseBrowser()

  try {
    const { data, error } = await supabase
      .from('strategies')
      .insert([
        {
          user_id: userId,
          ...strategy
        }
      ])
      .select()
      .single()

    if (error) {
      console.error('Error creating strategy:', error)
      return null
    }

    return data
  } catch (error) {
    console.error('Error in createStrategy:', error)
    return null
  }
}

export async function updateStrategy(userId: string, strategyId: string, updates: Partial<Omit<Strategy, 'id' | 'user_id' | 'created_at' | 'updated_at'>>): Promise<Strategy | null> {
  const supabase = getSupabaseBrowser()

  try {
    const { data, error } = await supabase
      .from('strategies')
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq('id', strategyId)
      .eq('user_id', userId)
      .select()
      .single()

    if (error) {
      console.error('Error updating strategy:', error)
      return null
    }

    return data
  } catch (error) {
    console.error('Error in updateStrategy:', error)
    return null
  }
}

export async function deleteStrategy(userId: string, strategyId: string): Promise<boolean> {
  const supabase = getSupabaseBrowser()

  try {
    const { error } = await supabase
      .from('strategies')
      .delete()
      .eq('id', strategyId)
      .eq('user_id', userId)

    if (error) {
      console.error('Error deleting strategy:', error)
      return false
    }

    return true
  } catch (error) {
    console.error('Error in deleteStrategy:', error)
    return false
  }
}

// Setup functions
export async function getSetups(userId: string, strategyId?: string): Promise<Setup[]> {
  const supabase = getSupabaseBrowser()

  try {
    let query = supabase
      .from('setups')
      .select('*, strategies(*)')
      .eq('user_id', userId)

    if (strategyId) {
      query = query.eq('strategy_id', strategyId)
    }

    const { data, error } = await query.order('created_at', { ascending: false })

    if (error) {
      console.error('Error fetching setups:', error)
      return []
    }

    return data || []
  } catch (error) {
    console.error('Error in getSetups:', error)
    return []
  }
}

export async function getSetupById(userId: string, setupId: string): Promise<Setup | null> {
  const supabase = getSupabaseBrowser()

  try {
    const { data, error } = await supabase
      .from('setups')
      .select('*, strategies(*)')
      .eq('id', setupId)
      .eq('user_id', userId)
      .single()

    if (error) {
      console.error('Error fetching setup:', error)
      return null
    }

    return data
  } catch (error) {
    console.error('Error in getSetupById:', error)
    return null
  }
}

export async function createSetup(userId: string, setup: Omit<Setup, 'id' | 'user_id' | 'created_at' | 'updated_at'>): Promise<Setup | null> {
  const supabase = getSupabaseBrowser()

  try {
    // Validate required fields
    if (!setup.strategy_id) {
      console.error('Error creating setup: strategy_id is required')
      throw new Error('Strategy ID is required')
    }

    if (!setup.name) {
      console.error('Error creating setup: name is required')
      throw new Error('Setup name is required')
    }

    // Ensure image_urls is an array
    const setupData = {
      ...setup,
      image_urls: Array.isArray(setup.image_urls) ? setup.image_urls : []
    }

    console.log('Creating setup with data:', {
      user_id: userId,
      ...setupData
    })

    const { data, error } = await supabase
      .from('setups')
      .insert([
        {
          user_id: userId,
          ...setupData
        }
      ])
      .select()
      .single()

    if (error) {
      console.error('Error creating setup:', error)

      // Check for unique constraint violation
      if (error.code === '23505' && error.message.includes('setups_strategy_id_name_key')) {
        throw new Error('A setup with this name already exists for this strategy. Please use a different name.')
      } else {
        throw new Error(error.message || 'An error occurred while creating the setup')
      }
    }

    console.log('Setup created successfully:', data)
    return data
  } catch (error) {
    console.error('Error in createSetup:', error)
    throw error
  }
}

export async function updateSetup(userId: string, setupId: string, updates: Partial<Omit<Setup, 'id' | 'user_id' | 'created_at' | 'updated_at'>>): Promise<Setup | null> {
  const supabase = getSupabaseBrowser()

  try {
    // First, get the current setup to compare image URLs
    const { data: currentSetup, error: fetchError } = await supabase
      .from('setups')
      .select('image_urls')
      .eq('id', setupId)
      .eq('user_id', userId)
      .single()

    if (fetchError) {
      console.error('Error fetching current setup for update:', fetchError)
      // Continue with update even if we can't fetch the current setup
    } else if (currentSetup && currentSetup.image_urls && updates.image_urls) {
      // Find images that were removed
      const removedImages = currentSetup.image_urls.filter(
        (url: string) => !updates.image_urls!.includes(url)
      )

      if (removedImages.length > 0) {
        // Import the deleteImageFromStorage function
        const { deleteImageFromStorage } = await import('./image-service')

        // Delete all removed images
        for (const imageUrl of removedImages) {
          try {
            await deleteImageFromStorage(imageUrl)
          } catch (imgError) {
            console.error('Error deleting removed setup image:', imgError)
            // Continue with update even if image deletion fails
          }
        }
      }
    }

    // Now update the setup in the database
    const { data, error } = await supabase
      .from('setups')
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq('id', setupId)
      .eq('user_id', userId)
      .select()
      .single()

    if (error) {
      console.error('Error updating setup:', error)
      return null
    }

    return data
  } catch (error) {
    console.error('Error in updateSetup:', error)
    return null
  }
}

export async function deleteSetup(userId: string, setupId: string): Promise<boolean> {
  const supabase = getSupabaseBrowser()

  try {
    // First, get the setup to retrieve its image URLs
    const { data: setup, error: fetchError } = await supabase
      .from('setups')
      .select('image_urls')
      .eq('id', setupId)
      .eq('user_id', userId)
      .single()

    if (fetchError) {
      console.error('Error fetching setup for deletion:', fetchError)
      // Continue with deletion even if we can't fetch the setup
    } else if (setup && setup.image_urls && setup.image_urls.length > 0) {
      // Import the deleteImageFromStorage function
      const { deleteImageFromStorage } = await import('./image-service')

      // Delete all images associated with this setup
      for (const imageUrl of setup.image_urls) {
        try {
          await deleteImageFromStorage(imageUrl)
        } catch (imgError) {
          console.error('Error deleting setup image:', imgError)
          // Continue with deletion even if image deletion fails
        }
      }
    }

    // Now delete the setup from the database
    const { error } = await supabase
      .from('setups')
      .delete()
      .eq('id', setupId)
      .eq('user_id', userId)

    if (error) {
      console.error('Error deleting setup:', error)
      return false
    }

    return true
  } catch (error) {
    console.error('Error in deleteSetup:', error)
    return false
  }
}

// Strategy Rule functions
export async function getStrategyRules(userId: string, strategyId: string): Promise<StrategyRule[]> {
  const supabase = getSupabaseBrowser()

  try {
    const { data, error } = await supabase
      .from('strategy_rules')
      .select('*')
      .eq('strategy_id', strategyId)
      .eq('user_id', userId)
      .order('priority', { ascending: true })

    if (error) {
      console.error('Error fetching strategy rules:', error)
      return []
    }

    return data || []
  } catch (error) {
    console.error('Error in getStrategyRules:', error)
    return []
  }
}

export async function getStrategyRuleById(userId: string, ruleId: string): Promise<StrategyRule | null> {
  const supabase = getSupabaseBrowser()

  try {
    const { data, error } = await supabase
      .from('strategy_rules')
      .select('*')
      .eq('id', ruleId)
      .eq('user_id', userId)
      .single()

    if (error) {
      console.error('Error fetching strategy rule:', error)
      return null
    }

    return data
  } catch (error) {
    console.error('Error in getStrategyRuleById:', error)
    return null
  }
}

export async function createStrategyRule(userId: string, rule: Omit<StrategyRule, 'id' | 'user_id' | 'created_at' | 'updated_at'>): Promise<StrategyRule | null> {
  const supabase = getSupabaseBrowser()

  try {
    const { data, error } = await supabase
      .from('strategy_rules')
      .insert([
        {
          user_id: userId,
          ...rule
        }
      ])
      .select()
      .single()

    if (error) {
      console.error('Error creating strategy rule:', error)

      // Check for unique constraint violation if there is one
      if (error.code === '23505') {
        throw new Error('A rule with this name already exists for this strategy. Please use a different name.')
      } else {
        throw new Error(error.message || 'An error occurred while creating the rule')
      }
    }

    return data
  } catch (error) {
    console.error('Error in createStrategyRule:', error)
    throw error
  }
}

export async function updateStrategyRule(userId: string, ruleId: string, updates: Partial<Omit<StrategyRule, 'id' | 'user_id' | 'created_at' | 'updated_at'>>): Promise<StrategyRule | null> {
  const supabase = getSupabaseBrowser()

  try {
    const { data, error } = await supabase
      .from('strategy_rules')
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq('id', ruleId)
      .eq('user_id', userId)
      .select()
      .single()

    if (error) {
      console.error('Error updating strategy rule:', error)
      return null
    }

    return data
  } catch (error) {
    console.error('Error in updateStrategyRule:', error)
    return null
  }
}

export async function deleteStrategyRule(userId: string, ruleId: string): Promise<boolean> {
  const supabase = getSupabaseBrowser()

  try {
    const { error } = await supabase
      .from('strategy_rules')
      .delete()
      .eq('id', ruleId)
      .eq('user_id', userId)

    if (error) {
      console.error('Error deleting strategy rule:', error)
      return false
    }

    return true
  } catch (error) {
    console.error('Error in deleteStrategyRule:', error)
    return false
  }
}

// Strategy Performance functions
export async function getStrategyPerformance(userId: string, strategyId: string, accountId?: string | null): Promise<StrategyPerformance[]> {
  const supabase = getSupabaseBrowser()

  try {
    let query = supabase
      .from('strategy_performance')
      .select('*')
      .eq('strategy_id', strategyId)
      .eq('user_id', userId)

    // Filter by account if specified
    if (accountId) {
      console.log(`Filtering performance by account: ${accountId}`)
      query = query.eq('account_id', accountId)
    } else {
      console.log('No account filter applied - showing performance from all accounts')
      query = query.is('account_id', null)
    }

    const { data, error } = await query.order('period_start', { ascending: false })

    if (error) {
      console.error('Error fetching strategy performance:', error)
      return []
    }

    return data || []
  } catch (error) {
    console.error('Error in getStrategyPerformance:', error)
    return []
  }
}

export async function createStrategyPerformance(userId: string, performance: Omit<StrategyPerformance, 'id' | 'user_id' | 'created_at' | 'updated_at'>): Promise<StrategyPerformance | null> {
  const supabase = getSupabaseBrowser()

  try {
    const { data, error } = await supabase
      .from('strategy_performance')
      .insert([
        {
          user_id: userId,
          ...performance
        }
      ])
      .select()
      .single()

    if (error) {
      console.error('Error creating strategy performance:', error)
      return null
    }

    return data
  } catch (error) {
    console.error('Error in createStrategyPerformance:', error)
    return null
  }
}

export async function updateStrategyPerformance(userId: string, performanceId: string, updates: Partial<Omit<StrategyPerformance, 'id' | 'user_id' | 'created_at' | 'updated_at'>>): Promise<StrategyPerformance | null> {
  const supabase = getSupabaseBrowser()

  try {
    const { data, error } = await supabase
      .from('strategy_performance')
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq('id', performanceId)
      .eq('user_id', userId)
      .select()
      .single()

    if (error) {
      console.error('Error updating strategy performance:', error)
      return null
    }

    return data
  } catch (error) {
    console.error('Error in updateStrategyPerformance:', error)
    return null
  }
}

export async function deleteStrategyPerformance(userId: string, performanceId: string): Promise<boolean> {
  const supabase = getSupabaseBrowser()

  try {
    const { error } = await supabase
      .from('strategy_performance')
      .delete()
      .eq('id', performanceId)
      .eq('user_id', userId)

    if (error) {
      console.error('Error deleting strategy performance:', error)
      return false
    }

    return true
  } catch (error) {
    console.error('Error in deleteStrategyPerformance:', error)
    return false
  }
}

// Trade-related functions
export async function getTradesByStrategy(userId: string, strategyId: string, accountId?: string | null): Promise<Trade[]> {
  const supabase = getSupabaseBrowser()

  try {
    console.log(`Fetching trades for strategy ${strategyId} and user ${userId}`)

    // First, check if the strategy exists
    const { data: strategy, error: strategyError } = await supabase
      .from('strategies')
      .select('name')
      .eq('id', strategyId)
      .eq('user_id', userId)
      .single()

    if (strategyError) {
      console.error('Error fetching strategy:', strategyError)
    } else {
      console.log(`Found strategy: ${strategy?.name}`)
    }

    // Get trades for this strategy
    let query = supabase
      .from('trades')
      .select('*')
      .eq('user_id', userId)
      .eq('strategy_id', strategyId)
      .order('time_open', { ascending: false })

    // Filter by account if specified
    if (accountId) {
      console.log(`Filtering trades by account: ${accountId}`)
      query = query.eq('account_id', accountId)
    } else {
      console.log('No account filter applied - showing trades from all accounts')
    }

    const { data, error } = await query

    if (error) {
      console.error('Error fetching trades by strategy:', error)
      return []
    }

    console.log(`Found ${data?.length || 0} trades for strategy ${strategyId}`)

    // If no trades found, try to debug why
    if (!data || data.length === 0) {
      // Check if there are any trades with strategy_id at all
      const { data: allTrades, error: allTradesError } = await supabase
        .from('trades')
        .select('id, strategy_id')
        .eq('user_id', userId)
        .not('strategy_id', 'is', null)
        .limit(10)

      if (allTradesError) {
        console.error('Error checking for any strategy trades:', allTradesError)
      } else {
        console.log(`Found ${allTrades?.length || 0} total trades with any strategy_id`)
        if (allTrades && allTrades.length > 0) {
          console.log('Sample strategy IDs:', allTrades.map(t => t.strategy_id))
        }
      }
    }

    return data || []
  } catch (error) {
    console.error('Error in getTradesByStrategy:', error)
    return []
  }
}

export async function getTradesBySetup(userId: string, setupId: string): Promise<Trade[]> {
  const supabase = getSupabaseBrowser()

  try {
    const { data, error } = await supabase
      .from('trades')
      .select('*')
      .eq('user_id', userId)
      .eq('setup_id', setupId)
      .order('time_open', { ascending: false })

    if (error) {
      console.error('Error fetching trades by setup:', error)
      return []
    }

    return data || []
  } catch (error) {
    console.error('Error in getTradesBySetup:', error)
    return []
  }
}

export async function updateTradeStrategy(userId: string, tradeId: string, strategyId: string | null, setupId: string | null): Promise<boolean> {
  const supabase = getSupabaseBrowser()

  try {
    const { error } = await supabase
      .from('trades')
      .update({
        strategy_id: strategyId,
        setup_id: setupId
      })
      .eq('id', tradeId)
      .eq('user_id', userId)

    if (error) {
      console.error('Error updating trade strategy:', error)
      return false
    }

    return true
  } catch (error) {
    console.error('Error in updateTradeStrategy:', error)
    return false
  }
}

// Calculate strategy performance based on trades
export async function calculateStrategyPerformance(
  userId: string,
  strategyId: string,
  periodStart: string,
  periodEnd: string,
  accountId?: string | null
): Promise<StrategyPerformance | null> {
  try {
    console.log(`Calculating performance for strategy ${strategyId} from ${periodStart} to ${periodEnd}`)

    // Get all trades for this strategy in the given period
    const supabase = getSupabaseBrowser()

    // First, check if the strategy exists
    const { data: strategy, error: strategyError } = await supabase
      .from('strategies')
      .select('name')
      .eq('id', strategyId)
      .eq('user_id', userId)
      .single()

    if (strategyError) {
      console.error('Error fetching strategy:', strategyError)
      return null
    }

    console.log(`Found strategy: ${strategy?.name}`)

    // Get all trades for this strategy
    let query = supabase
      .from('trades')
      .select('*')
      .eq('user_id', userId)
      .eq('strategy_id', strategyId)
      .gte('time_open', periodStart)
      .lte('time_close', periodEnd)

    // Filter by account if specified
    if (accountId) {
      console.log(`Filtering trades by account: ${accountId}`)
      query = query.eq('account_id', accountId)
    } else {
      console.log('No account filter applied - showing trades from all accounts')
    }

    const { data: trades, error } = await query

    if (error) {
      console.error('Error fetching trades for performance calculation:', error)
      return null
    }

    console.log(`Found ${trades?.length || 0} trades for strategy ${strategyId}`)

    // If no trades found, try to debug why
    if (!trades || trades.length === 0) {
      console.warn('No trades found for performance calculation')

      // Check if there are any trades with this strategy_id at all
      const { data: allStrategyTrades, error: allTradesError } = await supabase
        .from('trades')
        .select('id, time_open, time_close, strategy_id')
        .eq('user_id', userId)
        .eq('strategy_id', strategyId)

      if (allTradesError) {
        console.error('Error checking for any strategy trades:', allTradesError)
      } else {
        console.log(`Found ${allStrategyTrades?.length || 0} total trades with strategy_id ${strategyId} (ignoring date range)`)
      }

      // Check if there are any trades in the date range at all
      const { data: dateRangeTrades, error: dateRangeError } = await supabase
        .from('trades')
        .select('id, time_open, time_close, strategy_id')
        .eq('user_id', userId)
        .gte('time_open', periodStart)
        .lte('time_close', periodEnd)

      if (dateRangeError) {
        console.error('Error checking for date range trades:', dateRangeError)
      } else {
        console.log(`Found ${dateRangeTrades?.length || 0} total trades in date range ${periodStart} to ${periodEnd}`)

        // Log the strategy_id values to see if they match
        if (dateRangeTrades && dateRangeTrades.length > 0) {
          const strategyIds = dateRangeTrades.map(t => t.strategy_id).filter(Boolean)
          console.log('Strategy IDs found in trades:', strategyIds)
        }
      }

      return null
    }

    // Calculate performance metrics
    const winningTrades = trades.filter(trade => trade.profit > 0)
    const losingTrades = trades.filter(trade => trade.profit < 0)

    const totalTrades = trades.length
    const winningTradesCount = winningTrades.length
    const losingTradesCount = losingTrades.length

    const winRate = totalTrades > 0 ? winningTradesCount / totalTrades : 0
    const profitLoss = trades.reduce((sum, trade) => sum + trade.profit, 0)

    const averageWin = winningTradesCount > 0
      ? winningTrades.reduce((sum, trade) => sum + trade.profit, 0) / winningTradesCount
      : 0

    const averageLoss = losingTradesCount > 0
      ? Math.abs(losingTrades.reduce((sum, trade) => sum + trade.profit, 0)) / losingTradesCount
      : 0

    const largestWin = winningTradesCount > 0
      ? Math.max(...winningTrades.map(trade => trade.profit))
      : 0

    const largestLoss = losingTradesCount > 0
      ? Math.abs(Math.min(...losingTrades.map(trade => trade.profit)))
      : 0

    const averageRiskReward = averageLoss > 0 ? averageWin / averageLoss : 0
    const expectancy = (winRate * averageWin) - ((1 - winRate) * averageLoss)

    // Calculate profit factor (gross profit / gross loss)
    const grossProfit = winningTrades.reduce((sum, trade) => sum + trade.profit, 0)
    const grossLoss = Math.abs(losingTrades.reduce((sum, trade) => sum + trade.profit, 0))
    // When gross loss is 0 but gross profit is positive, set profit factor to 999 (will display as ∞)
    const profitFactor = grossLoss > 0 ? grossProfit / grossLoss : grossProfit > 0 ? 999 : 0

    // Create or update performance record
    const performanceData = {
      strategy_id: strategyId,
      account_id: accountId || null, // Include account_id if specified
      period_start: periodStart,
      period_end: periodEnd,
      total_trades: totalTrades,
      winning_trades: winningTradesCount,
      losing_trades: losingTradesCount,
      win_rate: winRate,
      profit_loss: profitLoss,
      average_win: averageWin,
      average_loss: averageLoss,
      largest_win: largestWin,
      largest_loss: largestLoss,
      average_risk_reward: averageRiskReward,
      expectancy: expectancy
    }

    // Add profit_factor if the column exists in the database
    try {
      // @ts-ignore - Add profit_factor to the performance data
      performanceData.profit_factor = profitFactor;
    } catch (error) {
      console.warn('Could not add profit_factor to performance data. The column might not exist in the database yet.');
    }

    // Check if a performance record already exists for this period and account
    let lookupQuery = supabase
      .from('strategy_performance')
      .select('id')
      .eq('strategy_id', strategyId)
      .eq('period_start', periodStart)
      .eq('period_end', periodEnd)

    // Include account_id in the uniqueness check if specified
    if (accountId) {
      lookupQuery = lookupQuery.eq('account_id', accountId)
    } else {
      lookupQuery = lookupQuery.is('account_id', null)
    }

    const { data: existingPerformance, error: lookupError } = await lookupQuery.maybeSingle()

    if (lookupError) {
      console.error('Error checking for existing performance record:', JSON.stringify(lookupError))
      // Continue with a fallback approach
      console.log('Falling back to upsert approach due to lookup error')
    }

    // Log what we found to help debug
    console.log('Existing performance record check:', existingPerformance ? `Found ID: ${existingPerformance.id}` : 'Not found')

    let result
    if (existingPerformance) {
      // Update existing record
      const { data, error: updateError } = await supabase
        .from('strategy_performance')
        .update(performanceData)
        .eq('id', existingPerformance.id)
        .eq('user_id', userId)
        .select()
        .single()

      if (updateError) {
        console.error('Error updating performance record:', JSON.stringify(updateError))
        // Continue execution instead of returning null to avoid breaking the UI
        // Just use the calculated data without saving it
        return {
          id: existingPerformance.id,
          user_id: userId,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          profit_factor: 0, // Add missing required field
          ...performanceData
        } as StrategyPerformance
      }

      result = data
    } else {
      // Create new record using upsert to handle potential duplicates
      const { data, error: insertError } = await supabase
        .from('strategy_performance')
        .upsert([{
          user_id: userId,
          ...performanceData
        }], {
          onConflict: 'strategy_id,period_start,period_end,account_id',
          ignoreDuplicates: false // Update if there's a conflict
        })
        .select()
        .single()

      if (insertError) {
        console.error('Error creating performance record:', JSON.stringify(insertError))
        // Continue execution instead of returning null to avoid breaking the UI
        // Just use the calculated data without saving it
        return {
          id: 'temp-' + Date.now(),
          user_id: userId,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          profit_factor: 0, // Add missing required field
          ...performanceData
        } as StrategyPerformance
      }

      result = data
    }

    return result
  } catch (error) {
    console.error('Error in calculateStrategyPerformance:', error)
    return null
  }
}
