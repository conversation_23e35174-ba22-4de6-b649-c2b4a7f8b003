import { getSupabaseBrowser } from "@/lib/supabase";
import { format } from "date-fns";

export interface DailyJournalEntry {
  id?: string;
  user_id?: string;
  account_id?: string;
  date: string;
  note: string;
  screenshots: string[];
  tags: string[];
  created_at?: string;
  updated_at?: string;
}

/**
 * Get daily journal entries for a user
 */
export async function getDailyJournalEntries(
  userId: string,
  options?: {
    accountId?: string;
    startDate?: string;
    endDate?: string;
    tags?: string[];
  }
): Promise<DailyJournalEntry[]> {
  try {
    const supabase = getSupabaseBrowser();

    const { data, error } = await supabase.rpc(
      'get_daily_journal_entries',
      {
        p_user_id: userId,
        p_account_id: options?.accountId || null,
        p_start_date: options?.startDate || null,
        p_end_date: options?.endDate || null,
        p_tags: options?.tags || null
      }
    );

    if (error) {
      console.error('Error fetching daily journal entries:', error);
      throw error;
    }

    return data || [];
  } catch (error) {
    console.error('Error in getDailyJournalEntries:', error);
    throw error;
  }
}

/**
 * Get a daily journal entry for a specific date
 */
export async function getDailyJournalEntry(
  userId: string,
  date: Date,
  accountId?: string
): Promise<DailyJournalEntry | null> {
  try {
    const supabase = getSupabaseBrowser();
    const dateStr = format(date, 'yyyy-MM-dd');

    // Build the query to properly handle the account_id filter
    let query = supabase
      .from('daily_journal_entries')
      .select('*')
      .eq('user_id', userId)
      .eq('date', dateStr);

    // Add account filter if provided
    if (accountId) {
      query = query.eq('account_id', accountId);
    } else {
      query = query.is('account_id', null);
    }

    const { data, error } = await query.maybeSingle();

    if (error) {
      // Only log if it's not a "not found" error
      if (!error.message.includes('not found')) {
        console.error('Error fetching daily journal entry:', error);
      }
      return null;
    }

    return data || null;
  } catch (error) {
    console.error('Error in getDailyJournalEntry:', error);
    return null; // Return null instead of throwing to prevent component crashes
  }
}

/**
 * Batch fetch daily journal entries for multiple dates
 * This optimizes performance by fetching data for multiple dates in a single request
 */
// Cache for batch fetched journal entries
const journalEntriesCache: Record<string, { entries: Record<string, DailyJournalEntry | null>, timestamp: number }> = {};
const JOURNAL_CACHE_TTL = 60000; // 1 minute cache TTL

export async function batchGetDailyJournalEntries(
  userId: string,
  dates: Date[],
  accountId?: string
): Promise<Record<string, DailyJournalEntry | null>> {
  if (!dates || dates.length === 0) {
    return {};
  }

  // Create a cache key based on the parameters
  const dateStrings = dates.map(date => format(date, 'yyyy-MM-dd')).sort().join(',');
  // Include both with and without account ID in the cache key to handle both cases
  const cacheKey = `journal-entries-${userId}-${accountId || 'all'}-${dateStrings}`;

  // Check if we have a valid cached result
  const cachedResult = journalEntriesCache[cacheKey];
  const now = Date.now();

  if (cachedResult && (now - cachedResult.timestamp < JOURNAL_CACHE_TTL)) {
    // Use cached entries if they're still valid
    console.log(`Using cached journal entries for key ${cacheKey}`);
    return cachedResult.entries;
  }

  try {
    const supabase = getSupabaseBrowser();

    // Format dates for the query
    const dateStrings = dates.map(date => format(date, 'yyyy-MM-dd'));

    console.log(`Batch fetching journal entries for dates: ${dateStrings.join(', ')} with accountId: ${accountId || 'none'}`);

    // Build the query to fetch entries for all dates in one request
    let query = supabase
      .from('daily_journal_entries')
      .select('*')
      .eq('user_id', userId)
      .in('date', dateStrings);

    // We'll fetch both account-specific and account-agnostic entries
    // This ensures we get all relevant entries in a single request
    if (accountId) {
      // If accountId is provided, we'll fetch entries for this account
      query = query.eq('account_id', accountId);
    }

    console.log(`Query built for batch fetch with dates: ${dateStrings.length} dates`);
    // If no accountId is provided, we'll get all entries regardless of account

    const { data, error } = await query;

    if (error) {
      console.error('Error batch fetching daily journal entries:', error);
      return {};
    }

    console.log(`Batch fetch returned ${data?.length || 0} journal entries`);

    // Create a map of date strings to entries
    const entriesByDate: Record<string, DailyJournalEntry | null> = {};

    // Initialize all requested dates with null
    dateStrings.forEach(dateStr => {
      entriesByDate[dateStr] = null;
    });

    // Fill in the entries we found
    if (data && data.length > 0) {
      console.log(`Processing ${data.length} journal entries from batch fetch`);
      data.forEach(entry => {
        if (entry.date) {
          console.log(`Found journal entry for date ${entry.date}`);
          entriesByDate[entry.date] = entry;
        }
      });
    } else {
      console.log('No journal entries found in batch fetch');
    }

    // Log the final result
    console.log(`Batch fetch result has entries for dates: ${Object.keys(entriesByDate).filter(date => entriesByDate[date] !== null).join(', ')}`);
    console.log(`Batch fetch result is missing entries for dates: ${Object.keys(entriesByDate).filter(date => entriesByDate[date] === null).join(', ')}`);

    // Cache the result
    journalEntriesCache[cacheKey] = {
      entries: entriesByDate,
      timestamp: now
    };

    return entriesByDate;
  } catch (error) {
    console.error('Error in batchGetDailyJournalEntries:', error);
    return {}; // Return empty object instead of throwing to prevent component crashes
  }
}

/**
 * Save or update a daily journal entry
 */
export async function saveDailyJournalEntry(
  userId: string,
  entry: {
    date: Date;
    note: string;
    screenshots: string[];
    tags: string[];
    accountId?: string;
  }
): Promise<string> {
  try {
    const supabase = getSupabaseBrowser();
    const dateStr = format(entry.date, 'yyyy-MM-dd');

    // Check if entry already exists
    let query = supabase
      .from('daily_journal_entries')
      .select('id')
      .eq('user_id', userId)
      .eq('date', dateStr);

    // Add account filter if provided
    if (entry.accountId) {
      query = query.eq('account_id', entry.accountId);
    } else {
      query = query.is('account_id', null);
    }

    const { data: existingEntry, error: fetchError } = await query.maybeSingle();

    if (fetchError) {
      console.error('Error checking for existing daily journal entry:', fetchError);
      throw fetchError;
    }

    let result;
    const now = new Date().toISOString();

    if (existingEntry) {
      // Update existing entry
      const { data, error } = await supabase
        .from('daily_journal_entries')
        .update({
          note: entry.note,
          screenshots: entry.screenshots,
          tags: entry.tags,
          updated_at: now
        })
        .eq('id', existingEntry.id)
        .select('id')
        .maybeSingle();

      if (error) {
        console.error('Error updating daily journal entry:', error);
        throw error;
      }

      if (!data) {
        throw new Error('No data returned from update operation');
      }

      result = data.id;
    } else {
      // Create new entry
      const { data, error } = await supabase
        .from('daily_journal_entries')
        .insert({
          user_id: userId,
          account_id: entry.accountId || null,
          date: dateStr,
          note: entry.note,
          screenshots: entry.screenshots,
          tags: entry.tags,
          created_at: now,
          updated_at: now
        })
        .select('id')
        .maybeSingle();

      if (error) {
        console.error('Error creating daily journal entry:', error);
        throw error;
      }

      if (!data) {
        throw new Error('No data returned from insert operation');
      }

      result = data.id;
    }

    return result;
  } catch (error) {
    console.error('Error in saveDailyJournalEntry:', error);
    throw error;
  }
}

/**
 * Delete a daily journal entry
 */
export async function deleteDailyJournalEntry(
  userId: string,
  date: Date,
  accountId?: string
): Promise<void> {
  try {
    const supabase = getSupabaseBrowser();
    const dateStr = format(date, 'yyyy-MM-dd');

    // Build the delete query
    let query = supabase
      .from('daily_journal_entries')
      .delete()
      .eq('user_id', userId)
      .eq('date', dateStr);

    // Add account filter if provided
    if (accountId) {
      query = query.eq('account_id', accountId);
    } else {
      query = query.is('account_id', null);
    }

    const { error } = await query;

    if (error) {
      console.error('Error deleting daily journal entry:', error);
      throw error;
    }
  } catch (error) {
    console.error('Error in deleteDailyJournalEntry:', error);
    throw error;
  }
}

/**
 * Migrate localStorage journal entries to Supabase
 * This is a one-time migration function that should only run once
 */
export async function migrateLocalStorageJournalEntries(
  userId: string,
  accountId?: string
): Promise<void> {
  // Check if we've already migrated
  if (localStorage.getItem('journalEntriesMigrated') === 'true') {
    return;
  }

  try {
    const savedEntries = localStorage.getItem('dailyJournalEntries');
    if (!savedEntries) {
      // Mark as migrated even if there's nothing to migrate
      localStorage.setItem('journalEntriesMigrated', 'true');
      return;
    }

    const entries = JSON.parse(savedEntries);
    let migratedCount = 0;

    for (const dateKey in entries) {
      const entry = entries[dateKey];
      const date = new Date(dateKey);

      // Skip invalid dates
      if (isNaN(date.getTime())) continue;

      // Save to Supabase
      await saveDailyJournalEntry(userId, {
        date,
        note: entry.note || '',
        screenshots: Array.isArray(entry.screenshots) ? entry.screenshots : [],
        tags: Array.isArray(entry.tags) ? entry.tags : [],
        accountId
      });

      migratedCount++;
    }

    // After successful migration, clear localStorage and mark as migrated
    localStorage.removeItem('dailyJournalEntries');
    localStorage.setItem('journalEntriesMigrated', 'true');

    if (migratedCount > 0) {
      console.log(`Successfully migrated ${migratedCount} journal entries from localStorage to Supabase`);
    }
  } catch (error) {
    console.error('Error migrating localStorage journal entries:', error);
    // Don't throw the error to prevent component crashes
  }
}
