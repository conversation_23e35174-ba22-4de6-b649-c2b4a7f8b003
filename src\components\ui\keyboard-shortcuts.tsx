"use client"

import React, { useEffect, useState } from 'react'
import { <PERSON><PERSON>, Dialog<PERSON>ontent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { X } from 'lucide-react'

interface ShortcutGroup {
  title: string
  shortcuts: {
    keys: string[]
    description: string
  }[]
}

interface KeyboardShortcutsProps {
  shortcuts: ShortcutGroup[]
  isOpen: boolean
  onClose: () => void
}

export function KeyboardShortcuts({ shortcuts, isOpen, onClose }: KeyboardShortcutsProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <DialogTitle>Keyboard Shortcuts</DialogTitle>
            <Button variant="ghost" size="icon" onClick={onClose} className="h-6 w-6">
              <X className="h-4 w-4" />
            </Button>
          </div>
          <DialogDescription>
            Use these keyboard shortcuts to navigate and interact with TradePivot more efficiently.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-4">
          {shortcuts.map((group, index) => (
            <div key={index} className="space-y-3">
              <h3 className="text-lg font-medium">{group.title}</h3>
              <div className="rounded-md border">
                <div className="divide-y">
                  {group.shortcuts.map((shortcut, idx) => (
                    <div key={idx} className="flex items-center justify-between p-3">
                      <span className="text-sm text-muted-foreground">{shortcut.description}</span>
                      <div className="flex items-center gap-1">
                        {shortcut.keys.map((key, keyIdx) => (
                          <React.Fragment key={keyIdx}>
                            <kbd className="pointer-events-none inline-flex h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium text-muted-foreground opacity-100">
                              {key}
                            </kbd>
                            {keyIdx < shortcut.keys.length - 1 && (
                              <span className="text-xs text-muted-foreground">+</span>
                            )}
                          </React.Fragment>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          ))}
        </div>
      </DialogContent>
    </Dialog>
  )
}

// Hook to handle keyboard shortcuts
export function useKeyboardShortcuts() {
  const [showShortcuts, setShowShortcuts] = useState(false)

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Show keyboard shortcuts dialog when pressing '?'
      if (e.key === '?' && !e.ctrlKey && !e.metaKey) {
        setShowShortcuts(true)
      }

      // Close dialog with Escape key
      if (e.key === 'Escape' && showShortcuts) {
        setShowShortcuts(false)
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [showShortcuts])

  return {
    showShortcuts,
    setShowShortcuts
  }
}

// Default shortcuts for the application
export const defaultShortcuts: ShortcutGroup[] = [
  {
    title: "Navigation",
    shortcuts: [
      { keys: ["g", "d"], description: "Go to Dashboard" },
      { keys: ["g", "t"], description: "Go to Trade Log" },
      { keys: ["g", "s"], description: "Go to Symbols" },
      { keys: ["g", "j"], description: "Go to Journal" },
      { keys: ["g", "n"], description: "Go to Notebook" },
      { keys: ["g", "a"], description: "Go to Analytics" },
      { keys: ["g", "m"], description: "Go to Metrics & Goals" },
      { keys: ["g", "p"], description: "Go to Playbook" },
      { keys: ["g", "c"], description: "Go to Calendar" },
      { keys: ["g", "w"], description: "Go to Accounts" },
    ]
  },
  {
    title: "Actions",
    shortcuts: [
      { keys: ["n"], description: "Add New Trade" },
      { keys: ["f"], description: "Filter Trades" },
      { keys: ["r"], description: "Refresh Data" },
      { keys: ["s"], description: "Save Changes" },
      { keys: ["Alt", "s"], description: "Toggle Sidebar" },
      { keys: ["Esc"], description: "Close Modal/Dialog" },
      { keys: ["?"], description: "Show Keyboard Shortcuts" },
    ]
  },
  {
    title: "Dashboard",
    shortcuts: [
      { keys: ["1"], description: "Switch to Daily P&L Chart" },
      { keys: ["2"], description: "Switch to Cumulative P&L Chart" },
      { keys: ["3"], description: "Switch to Equity Curve" },
      { keys: ["4"], description: "Switch to Win/Loss Distribution" },
    ]
  }
]
