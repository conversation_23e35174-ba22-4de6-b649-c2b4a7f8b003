"use client"

import { useState, useEffect } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { toast } from "sonner"
import { getSupabaseBrowser } from "@/lib/supabase"
import { getStrategies, getSetups } from "@/lib/playbook-service"
import { Strategy, Setup } from "@/types/playbook"
import { ProcessedData } from "@/lib/excel-processor"
import { saveTradeData } from "@/lib/trade-service"

import { Button } from "@/components/ui/button"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { Calendar } from "@/components/ui/calendar"
import { format } from "date-fns"
import { CalendarIcon } from "lucide-react"
import { cn } from "@/lib/utils"

// Define the form schema
const formSchema = z.object({
  symbol: z.string().min(1, "Symbol is required"),
  type: z.enum(["buy", "sell"]),
  volume: z.coerce.number().positive("Volume must be positive"),
  price_open: z.coerce.number().positive("Open price must be positive"),
  price_close: z.coerce.number().positive("Close price must be positive"),
  time_open: z.date(),
  time_close: z.date(),
  profit: z.coerce.number(),
  strategy_id: z.string().optional(),
  setup_id: z.string().optional(),
  comment: z.string().optional(),
})

type FormValues = z.infer<typeof formSchema>

interface ManualTradeFormProps {
  userId: string
  accountId: string
  onTradeAdded: (data: ProcessedData) => void
}

export function ManualTradeForm({ userId, accountId, onTradeAdded }: ManualTradeFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [strategies, setStrategies] = useState<Strategy[]>([])
  const [setups, setSetups] = useState<Setup[]>([])
  const [selectedStrategyId, setSelectedStrategyId] = useState<string | null>(null)

  // Initialize form with default values
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      symbol: "",
      type: "buy",
      volume: 0.01,
      price_open: 0,
      price_close: 0,
      time_open: new Date(),
      time_close: new Date(),
      profit: 0,
      strategy_id: undefined,
      setup_id: undefined,
      comment: "",
    },
  })

  // Fetch strategies and setups
  useEffect(() => {
    const fetchData = async () => {
      if (userId) {
        try {
          const strategiesData = await getStrategies(userId)
          setStrategies(strategiesData)
        } catch (error) {
          console.error("Error fetching strategies:", error)
        }
      }
    }

    fetchData()
  }, [userId])

  // Function to fetch setups for a strategy
  const fetchSetups = async (strategyId?: string | null) => {
    if (!userId) return

    const idToUse = strategyId || selectedStrategyId

    if (idToUse) {
      try {
        console.log(`Fetching setups for strategy ${idToUse}`)
        const setupsData = await getSetups(userId, idToUse)
        console.log(`Found ${setupsData.length} setups for strategy ${idToUse}`)
        setSetups(setupsData)
      } catch (error) {
        console.error("Error fetching setups:", error)
        toast.error("Failed to load setups")
      }
    } else {
      setSetups([])
    }
  }

  // Fetch setups when a strategy is selected
  useEffect(() => {
    if (selectedStrategyId) {
      fetchSetups()
    }
  }, [userId, selectedStrategyId])

  // Handle strategy selection
  const handleStrategyChange = (value: string) => {
    const strategyId = value === "none" ? null : value
    setSelectedStrategyId(strategyId)
    form.setValue("strategy_id", strategyId || undefined)
    form.setValue("setup_id", undefined) // Reset setup when strategy changes
    console.log('Strategy selected:', strategyId)

    // If a strategy is selected, fetch its setups
    if (strategyId) {
      fetchSetups(strategyId)
    } else {
      setSetups([])
    }
  }

  // Calculate profit based on trade type, volume, and prices
  const calculateProfit = (type: string, volume: number, openPrice: number, closePrice: number) => {
    if (type === "buy") {
      return volume * (closePrice - openPrice)
    } else {
      return volume * (openPrice - closePrice)
    }
  }

  // Update profit when relevant fields change
  useEffect(() => {
    const subscription = form.watch((value, { name }) => {
      if (["type", "volume", "price_open", "price_close"].includes(name as string)) {
        const type = value.type as string
        const volume = value.volume as number
        const openPrice = value.price_open as number
        const closePrice = value.price_close as number

        if (volume && openPrice && closePrice) {
          const profit = calculateProfit(type, volume, openPrice, closePrice)
          form.setValue("profit", profit)
        }
      }
    })

    return () => subscription.unsubscribe()
  }, [form])

  // Handle form submission
  const onSubmit = async (values: FormValues) => {
    setIsSubmitting(true)

    try {
      // Create a trade object
      const trade = {
        position_id: Date.now(), // Use timestamp as numeric position_id
        symbol: values.symbol,
        type: values.type,
        volume: values.volume,
        price_open: values.price_open,
        price_close: values.price_close,
        time_open: format(values.time_open, "yyyy-MM-dd HH:mm:ss"),
        time_close: format(values.time_close, "yyyy-MM-dd HH:mm:ss"),
        profit: values.profit,
        commission: 0, // Default commission
        swap: 0, // Default swap
        sl: null, // Default stop loss
        tp: null, // Default take profit
        strategy_id: values.strategy_id && values.strategy_id !== "none" ? values.strategy_id : null,
        setup_id: values.setup_id && values.setup_id !== "none" ? values.setup_id : null,
        // Note: comment field is not included as it doesn't exist in the database
      }

      console.log('Submitting trade with strategy_id:', trade.strategy_id, 'and setup_id:', trade.setup_id)

      // Create a processed data object
      const processedData: ProcessedData = {
        account: {
          name: "Manual Entry",
          account: `Manual-${userId.substring(0, 8)}`, // Make account number unique per user
          company: "Manual Entry",
          date: new Date().toISOString(),
        },
        trades: [trade],
        summary: {
          total_trades: 1,
          profit_trades: trade.profit > 0 ? "100.00%" : "0.00%",
          loss_trades: trade.profit <= 0 ? "100.00%" : "0.00%",
          total_net_profit: trade.profit,
          gross_profit: trade.profit > 0 ? trade.profit : 0,
          gross_loss: trade.profit < 0 ? Math.abs(trade.profit) : 0,
          profit_factor: trade.profit > 0 ? 999 : 0, // If profitable and no losses, set to high value
          expected_payoff: trade.profit,
          balance_drawdown_maximal: "0.00 (0.00%)",
          balance_drawdown_absolute: 0,
          balance_drawdown_relative: "0.00% (0.00)",
          initial_balance: 10000, // Default initial balance
          short_trades_won: "0 (0.00%)",
          long_trades_won: "0 (0.00%)",
          largest_profit_trade: trade.profit > 0 ? trade.profit : 0,
          largest_loss_trade: trade.profit < 0 ? trade.profit : 0,
          average_profit_trade: trade.profit > 0 ? trade.profit : 0,
          average_loss_trade: trade.profit < 0 ? trade.profit : 0,
          maximum_consecutive_wins: "0 (0.00)",
          maximum_consecutive_losses: "0 (0.00)",
          maximal_consecutive_profit: "0.00 (0)",
          maximal_consecutive_loss: "0.00 (0)",
          average_consecutive_wins: 0,
          average_consecutive_losses: 0,
          recovery_factor: 0,
          sharpe_ratio: 0
        },
      }

      // Save the trade to the database with the selected account
      await saveTradeData(userId, processedData, accountId)

      // Notify the parent component
      onTradeAdded(processedData)
      toast.success("Trade added successfully")
      form.reset() // Reset form after successful submission
    } catch (error) {
      // Get a meaningful error message
      const errorMessage = error instanceof Error
        ? error.message
        : typeof error === 'object' && error !== null
          ? JSON.stringify(error)
          : 'Unknown error';

      console.error("Error adding trade:", error)
      toast.error(`Failed to add trade: ${errorMessage}`)
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormField
            control={form.control}
            name="symbol"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Symbol</FormLabel>
                <FormControl>
                  <Input placeholder="EURUSD" {...field} />
                </FormControl>
                <FormDescription>
                  The trading instrument (e.g., EURUSD, AAPL)
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="type"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Trade Type</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select trade type" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="buy">Buy</SelectItem>
                    <SelectItem value="sell">Sell</SelectItem>
                  </SelectContent>
                </Select>
                <FormDescription>
                  Whether you bought or sold the instrument
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <FormField
            control={form.control}
            name="volume"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Volume</FormLabel>
                <FormControl>
                  <Input type="number" step="0.01" min="0.01" {...field} />
                </FormControl>
                <FormDescription>
                  Trade size in lots
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="price_open"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Open Price</FormLabel>
                <FormControl>
                  <Input type="number" step="0.00001" min="0" {...field} />
                </FormControl>
                <FormDescription>
                  Entry price
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="price_close"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Close Price</FormLabel>
                <FormControl>
                  <Input type="number" step="0.00001" min="0" {...field} />
                </FormControl>
                <FormDescription>
                  Exit price
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormField
            control={form.control}
            name="time_open"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>Open Time</FormLabel>
                <Popover>
                  <PopoverTrigger asChild>
                    <FormControl>
                      <Button
                        variant={"outline"}
                        className={cn(
                          "w-full pl-3 text-left font-normal",
                          !field.value && "text-muted-foreground"
                        )}
                      >
                        {field.value ? (
                          format(field.value, "PPP HH:mm:ss")
                        ) : (
                          <span>Pick a date</span>
                        )}
                        <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                      </Button>
                    </FormControl>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={field.value}
                      onSelect={field.onChange}
                      initialFocus
                    />
                    <div className="p-3 border-t">
                      <Input
                        type="time"
                        step="1"
                        value={format(field.value, "HH:mm:ss")}
                        onChange={(e) => {
                          const [hours, minutes, seconds] = e.target.value.split(':').map(Number)
                          const newDate = new Date(field.value)
                          newDate.setHours(hours || 0, minutes || 0, seconds || 0)
                          field.onChange(newDate)
                        }}
                      />
                    </div>
                  </PopoverContent>
                </Popover>
                <FormDescription>
                  When the trade was opened
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="time_close"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>Close Time</FormLabel>
                <Popover>
                  <PopoverTrigger asChild>
                    <FormControl>
                      <Button
                        variant={"outline"}
                        className={cn(
                          "w-full pl-3 text-left font-normal",
                          !field.value && "text-muted-foreground"
                        )}
                      >
                        {field.value ? (
                          format(field.value, "PPP HH:mm:ss")
                        ) : (
                          <span>Pick a date</span>
                        )}
                        <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                      </Button>
                    </FormControl>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={field.value}
                      onSelect={field.onChange}
                      initialFocus
                    />
                    <div className="p-3 border-t">
                      <Input
                        type="time"
                        step="1"
                        value={format(field.value, "HH:mm:ss")}
                        onChange={(e) => {
                          const [hours, minutes, seconds] = e.target.value.split(':').map(Number)
                          const newDate = new Date(field.value)
                          newDate.setHours(hours || 0, minutes || 0, seconds || 0)
                          field.onChange(newDate)
                        }}
                      />
                    </div>
                  </PopoverContent>
                </Popover>
                <FormDescription>
                  When the trade was closed
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <FormField
            control={form.control}
            name="profit"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Profit/Loss</FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    step="0.01"
                    {...field}
                    className={cn(
                      field.value >= 0 ? "text-emerald-500" : "text-rose-500"
                    )}
                    readOnly
                  />
                </FormControl>
                <FormDescription>
                  Calculated automatically
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="strategy_id"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Strategy</FormLabel>
                <Select
                  onValueChange={(value) => handleStrategyChange(value)}
                  value={field.value || "none"}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a strategy (optional)" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="none">None</SelectItem>
                    {strategies.map((strategy) => (
                      <SelectItem key={strategy.id} value={strategy.id}>
                        {strategy.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormDescription>
                  Associate with a strategy
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="setup_id"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Setup</FormLabel>
                <Select
                  onValueChange={(value) => {
                    const setupId = value === "none" ? undefined : value
                    field.onChange(setupId)
                  }}
                  value={field.value || "none"}
                  disabled={!selectedStrategyId}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a setup (optional)" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="none">None</SelectItem>
                    {setups.map((setup) => (
                      <SelectItem key={setup.id} value={setup.id}>
                        {setup.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormDescription>
                  Associate with a setup
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="comment"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Comment</FormLabel>
              <FormControl>
                <Input placeholder="Add notes about this trade" {...field} value={field.value || ""} />
              </FormControl>
              <FormDescription>
                Optional notes or comments about this trade
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <Button type="submit" disabled={isSubmitting}>
          {isSubmitting ? "Adding Trade..." : "Add Trade"}
        </Button>
      </form>
    </Form>
  )
}
