"use client"

import { useState, useEffect } from 'react'
import { getSupabaseBrowser } from '@/lib/supabase'

export function useDashboardLayout(userId: string | null) {
  const [layoutOrder, setLayoutOrder] = useState<string[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const supabase = getSupabaseBrowser()

  // Load saved layout from user preferences
  useEffect(() => {
    async function loadLayout() {
      if (!userId) {
        setIsLoading(false)
        return
      }

      try {
        const { data, error } = await supabase
          .from('user_preferences')
          .select('dashboard_layout')
          .eq('user_id', userId)
          .single()

        if (error) {
          console.error('Error loading dashboard layout:', error)
        } else if (data?.dashboard_layout) {
          setLayoutOrder(data.dashboard_layout)
        }
      } catch (error) {
        console.error('Error accessing user preferences:', error)
      } finally {
        setIsLoading(false)
      }
    }

    loadLayout()
  }, [userId, supabase])

  // Save layout to user preferences
  const saveLayout = async (newOrder: string[]) => {
    if (!userId) return

    try {
      const { error } = await supabase
        .from('user_preferences')
        .update({ dashboard_layout: newOrder })
        .eq('user_id', userId)

      if (error) {
        console.error('Error saving dashboard layout:', error)
      } else {
        setLayoutOrder(newOrder)
      }
    } catch (error) {
      console.error('Error updating user preferences:', error)
    }
  }

  return {
    layoutOrder,
    saveLayout,
    isLoading
  }
}
