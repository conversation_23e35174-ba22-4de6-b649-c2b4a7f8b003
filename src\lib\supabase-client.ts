import { createBrowserClient } from '@supabase/ssr'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'
import type { Database } from '@/types/supabase'

// Get environment variables with non-null assertions
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

// Validate environment variables at runtime
if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables')
}

// Create a single client component client instance
// Use a global variable to ensure we only create one instance
let clientInstance: ReturnType<typeof createBrowserClient<Database>> | null = null

/**
 * Get a Supabase client for browser/client components
 * This function ensures we only create one instance on the client side
 */
export function getSupabaseBrowser() {
  if (typeof window === 'undefined') {
    // Server-side: create a new instance each time with custom headers
    return createBrowserClient<Database>(supabaseUrl, supabaseAnonKey, {
      global: {
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        }
      }
    })
  }

  // Client-side: reuse the same instance
  if (!clientInstance) {
    // Create the client with explicit headers to fix 406 errors
    clientInstance = createBrowserClient<Database>(supabaseUrl, supabaseAnonKey, {
      global: {
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        }
      }
    })
  }
  return clientInstance
}

/**
 * Get a Supabase client for server components
 * This function creates a new instance each time with proper cookie handling
 */
export async function getSupabaseServer() {
  const cookieStore = await cookies()
  return createServerClient<Database>(
    supabaseUrl,
    supabaseAnonKey,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value
        },
        set(_name: string, _value: string, _options: any) {
          // Server components can't set cookies directly
        },
        remove(_name: string, _options: any) {
          // Server components can't remove cookies directly
        }
      },
      global: {
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        }
      }
    }
  )
}

/**
 * Get the authenticated user from Supabase
 * This function can be used in both client and server components
 */
export async function getAuthenticatedUser(useServer = false) {
  const supabase = useServer ? await getSupabaseServer() : getSupabaseBrowser()
  const { data: { user }, error } = await supabase.auth.getUser()
  
  if (error || !user) {
    return { user: null, error }
  }
  
  return { user, error: null }
}
