import { format } from "date-fns";
import { FilterParams } from "./types";
import { DateRange } from "react-day-picker";

/**
 * Normalize a string for case-insensitive comparison
 * @param value String to normalize
 * @returns Normalized string (lowercase, trimmed)
 */
export function normalizeString(value: string): string {
  return value.toLowerCase().trim();
}

/**
 * Check if a tag matches a search term (case-insensitive)
 * @param tag Tag to check
 * @param searchTerm Search term to match against
 * @returns True if the tag matches the search term
 */
export function tagMatchesSearchTerm(tag: string, searchTerm: string): boolean {
  const normalizedTag = normalizeString(tag);
  const normalizedSearchTerm = normalizeString(searchTerm);

  return normalizedTag === normalizedSearchTerm;
}

/**
 * Check if a tag starts with a search term (case-insensitive)
 * @param tag Tag to check
 * @param searchTerm Search term to match against
 * @returns True if the tag starts with the search term
 */
export function tagStartsWithSearchTerm(tag: string, searchTerm: string): boolean {
  const normalizedTag = normalizeString(tag);
  const normalizedSearchTerm = normalizeString(searchTerm);

  return normalizedTag.startsWith(normalizedSearchTerm);
}

/**
 * Check if a tag contains a search term (case-insensitive)
 * @param tag Tag to check
 * @param searchTerm Search term to match against
 * @returns True if the tag contains the search term
 */
export function tagContainsSearchTerm(tag: string, searchTerm: string): boolean {
  const normalizedTag = normalizeString(tag);
  const normalizedSearchTerm = normalizeString(searchTerm);

  return normalizedTag.includes(normalizedSearchTerm);
}

/**
 * Find the best matching tag from a list of tags for a search term
 * @param tags List of available tags
 * @param searchTerm Search term to match against
 * @returns The best matching tag or null if no match is found
 */
export function findBestMatchingTag(tags: string[], searchTerm: string): string | null {
  if (!searchTerm || searchTerm.trim().length === 0) {
    return null;
  }

  // First, try exact match
  const exactMatch = tags.find(tag => tagMatchesSearchTerm(tag, searchTerm));
  if (exactMatch) {
    return exactMatch;
  }

  // Then, try starts with match
  const startsWithMatches = tags.filter(tag => tagStartsWithSearchTerm(tag, searchTerm));
  if (startsWithMatches.length === 1) {
    return startsWithMatches[0];
  }

  // Finally, try contains match
  const containsMatches = tags.filter(tag => tagContainsSearchTerm(tag, searchTerm));
  if (containsMatches.length === 1) {
    return containsMatches[0];
  }

  return null;
}

/**
 * Build URL parameters from filter params
 * @param params Filter parameters
 * @returns URLSearchParams object
 */
export function buildUrlParams(params: FilterParams): URLSearchParams {
  const urlParams = new URLSearchParams();

  if (params.searchTerm) {
    urlParams.set('searchTerm', params.searchTerm);
  }

  if (params.tags && params.tags.length > 0) {
    params.tags.forEach(tag => urlParams.append('tags', tag));
  }

  if (params.startDate) {
    urlParams.set('startDate', params.startDate);
  }

  if (params.endDate) {
    urlParams.set('endDate', params.endDate);
  }

  if (params.activeTab) {
    urlParams.set('tab', params.activeTab);
  }

  return urlParams;
}

/**
 * Format a date range for use in API calls
 * @param dateRange Date range object
 * @returns Object with formatted start and end dates
 */
export function formatDateRange(dateRange?: DateRange): { startDate?: string, endDate?: string } {
  if (!dateRange) {
    return {};
  }

  return {
    startDate: dateRange.from ? format(dateRange.from, "yyyy-MM-dd") : undefined,
    endDate: dateRange.to ? format(dateRange.to, "yyyy-MM-dd") : undefined,
  };
}

/**
 * Log filter parameters for debugging
 * @param params Filter parameters
 */
export function logFilterParams(params: FilterParams): void {
  console.log('Filter parameters:', {
    searchTerm: params.searchTerm || 'none',
    tags: params.tags?.length ? params.tags.join(', ') : 'none',
    startDate: params.startDate || 'none',
    endDate: params.endDate || 'none',
    activeTab: params.activeTab || 'none',
    page: params.page || 1,
    pageSize: params.pageSize || 50,
  });
}
