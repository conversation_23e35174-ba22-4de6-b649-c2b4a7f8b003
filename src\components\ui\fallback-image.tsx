"use client"

import React, { useState, useEffect } from 'react'
import { cn } from '@/lib/utils'
import { checkImageExists } from '@/lib/image-service'

interface FallbackImageProps extends React.ImgHTMLAttributes<HTMLImageElement> {
  fallbackSrc?: string
  fallbackComponent?: React.ReactNode
}

// Default fallback image as a base64 data URL
const DEFAULT_FALLBACK = "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='300' height='200' viewBox='0 0 300 200'%3E%3Crect width='300' height='200' fill='%23f0f0f0'/%3E%3Ctext x='50%25' y='50%25' font-family='Arial' font-size='14' text-anchor='middle' dominant-baseline='middle' fill='%23999999'%3EImage not available%3C/text%3E%3C/svg%3E";

export function FallbackImage({
  src,
  alt,
  className,
  fallbackSrc = DEFAULT_FALLBACK,
  fallbackComponent,
  ...props
}: FallbackImageProps) {
  const [error, setError] = useState(false)
  const [loaded, setLoaded] = useState(false)
  const [cacheBuster, setCacheBuster] = useState(() => Date.now())

  // Reset state when src changes
  useEffect(() => {
    setError(false);
    setLoaded(false);
    setCacheBuster(Date.now());
  }, [src]);

  // Listen for image-deleted, image-cache-cleared, and image-deleted-final events
  useEffect(() => {
    const handleImageDeleted = (event: Event) => {
      const customEvent = event as CustomEvent<{url: string}>;
      if (customEvent.detail && customEvent.detail.url === src) {
        console.log('Detected image deletion event for current image:', src);
        setError(true);
        setLoaded(false);

        // Force a refresh of the component
        setCacheBuster(Date.now());
      }
    };

    const handleImageCacheCleared = (event: Event) => {
      const customEvent = event as CustomEvent<{url: string}>;
      if (customEvent.detail && customEvent.detail.url === src) {
        console.log('Detected cache cleared event for current image:', src);
        setError(true);
        setLoaded(false);

        // Force a refresh of the component
        setCacheBuster(Date.now());

        // Also force a refresh of the image by creating a new Image object
        const img = new Image();
        img.src = `${src}?cache=${Date.now()}&forcereload=true`;
        img.onerror = () => {
          console.log('Image confirmed to be deleted (onerror triggered):', src);
          setError(true);
        };
      }
    };

    const handleImageDeletedFinal = (event: Event) => {
      const customEvent = event as CustomEvent<{url: string}>;
      if (customEvent.detail && customEvent.detail.url === src) {
        console.log('Detected final image deletion event for current image:', src);

        // This is our last chance to update the UI
        setError(true);
        setLoaded(false);

        // Force a complete refresh of the component with a new key
        setCacheBuster(Date.now());

        // Try to force the parent component to re-render
        if (typeof window !== 'undefined') {
          // Create a global event that parent components can listen for
          window.dispatchEvent(new CustomEvent('force-rerender', {
            detail: { url: src }
          }));
        }
      }
    };

    document.addEventListener('image-deleted', handleImageDeleted);
    document.addEventListener('image-cache-cleared', handleImageCacheCleared);
    document.addEventListener('image-deleted-final', handleImageDeletedFinal);

    return () => {
      document.removeEventListener('image-deleted', handleImageDeleted);
      document.removeEventListener('image-cache-cleared', handleImageCacheCleared);
      document.removeEventListener('image-deleted-final', handleImageDeletedFinal);
    };
  }, [src]);

  // Add cache busting to the URL to prevent browser caching
  const getImageUrl = (url: string | undefined) => {
    if (!url) return fallbackSrc;

    // If the URL is a data URL, return it as is
    if (url.startsWith('data:')) return url;

    // If the image has been marked as deleted or doesn't exist, return the fallback
    if (error) return fallbackSrc;

    // Only add cache busting for Supabase storage URLs
    if (url.includes('storage/v1/object/public')) {
      // Check if URL already has query parameters
      const hasParams = url.includes('?');
      // Add both cache busting and no-cache directive
      return `${url}${hasParams ? '&' : '?'}cache=${cacheBuster}&_=${Math.random()}`;
    }

    return url;
  };

  // Simple error handler
  const handleError = () => {
    console.log("Image failed to load:", src)
    setError(true)

    // If this is a Supabase URL, try refreshing with a new cache buster
    if (src && src.includes('storage/v1/object/public') && !error) {
      // Try one more time with a new cache buster
      setCacheBuster(Date.now());
    }
  }

  const handleLoad = () => {
    console.log("Image loaded successfully:", src)
    setLoaded(true)
  }

  // Check if the image exists using our checkImageExists function
  useEffect(() => {
    if (src && src.includes('storage/v1/object/public') && !error) {
      const verifyImage = async () => {
        try {
          // Add a random parameter to force a fresh check
          const exists = await checkImageExists(`${src}${src.includes('?') ? '&' : '?'}_=${Math.random()}`);
          if (!exists) {
            console.log("Image verified to not exist in storage:", src);
            setError(true);
            setLoaded(false);

            // Force a refresh of the component
            setCacheBuster(Date.now());
          } else if (!loaded) {
            console.log("Image verified to exist in storage:", src);
          }
        } catch (err) {
          console.error("Error verifying image existence:", err);
          // Don't set error here as the img tag will handle the error
        }
      };

      verifyImage();

      // Set up a timer to periodically check if the image still exists
      // This helps with cases where the image is deleted but the component hasn't been updated
      const intervalId = setInterval(verifyImage, 5000);

      return () => {
        clearInterval(intervalId);
      };
    }
  }, [src, error, loaded]);

  // If there's an error and a fallback component is provided, show it
  if (error && fallbackComponent) {
    return <>{fallbackComponent}</>
  }

  return (
    <div className={cn("relative", className)}>
      {/* Show a placeholder background until image loads */}
      {!loaded && !error && (
        <div
          className="absolute inset-0 bg-muted/30 animate-pulse"
          role="presentation"
        />
      )}

      {/* The actual image */}
      <img
        src={error ? fallbackSrc : getImageUrl(src)}
        alt={alt || "Image"}
        className={cn("w-full h-full object-contain", className)}
        onError={handleError}
        onLoad={handleLoad}
        crossOrigin="anonymous"
        loading="lazy"
        fetchPriority="high"
        {...props}
      />

      {/* Show a message if there's an error and no fallback component */}
      {error && !fallbackComponent && (
        <div className="absolute inset-0 flex items-center justify-center bg-muted/50">
          <span className="text-xs text-muted-foreground">Image not available</span>
        </div>
      )}
    </div>
  )
}
