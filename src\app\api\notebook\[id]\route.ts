import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { createServerClient } from '@supabase/ssr';
import type { Database } from '@/types/supabase';
import { handleNotebookSync } from '@/lib/sync-utils';

// GET handler for fetching a specific notebook entry
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    // Get cookies for server-side Supabase client
    const cookieStore = await cookies();
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
          set(_name: string, _value: string, _options: any) {
            // API routes can't set cookies directly
          },
          remove(_name: string, _options: any) {
            // API routes can't remove cookies directly
          }
        },
      }
    );

    // Get authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Fetch the notebook entry
    const { data, error } = await supabase
      .from('notebook_entries')
      .select('*')
      .eq('id', id)
      .eq('user_id', user.id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json({ error: 'Notebook entry not found' }, { status: 404 });
      }
      console.error('Error fetching notebook entry:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error('Error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// PATCH handler for updating a notebook entry
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Properly await params in NextJS 15
    const { id } = await params;
    const body = await request.json();
    const { title, content, html_content, category, tags, is_pinned, is_template, account_id, linked_trade_ids, linked_strategy_ids, screenshots } = body;

    console.log('=== NOTEBOOK PATCH API CALLED ===');
    console.log('Notebook ID:', id);
    console.log('Request body keys:', Object.keys(body));
    console.log('Linked trade IDs:', linked_trade_ids);
    console.log('Linked strategy IDs:', linked_strategy_ids);
    console.log('Has screenshots:', screenshots ? screenshots.length : 0);

    // Get cookies for server-side Supabase client
    const cookieStore = await cookies();
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
          set(_name: string, _value: string, _options: any) {
            // API routes can't set cookies directly
          },
          remove(_name: string, _options: any) {
            // API routes can't remove cookies directly
          }
        },
      }
    );

    // Get authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Create the update object
    const updateData: any = {
      updated_at: new Date().toISOString()
    };

    // Only include fields that are provided
    if (title !== undefined) updateData.title = title;
    if (content !== undefined) updateData.content = content;
    if (html_content !== undefined) updateData.html_content = html_content;
    if (category !== undefined) updateData.category = category;
    if (tags !== undefined) updateData.tags = tags;
    if (is_pinned !== undefined) updateData.is_pinned = is_pinned;
    if (is_template !== undefined) updateData.is_template = is_template;
    if (account_id !== undefined) updateData.account_id = account_id;
    if (linked_trade_ids !== undefined) updateData.linked_trade_ids = linked_trade_ids;
    if (linked_strategy_ids !== undefined) updateData.linked_strategy_ids = linked_strategy_ids;
    if (screenshots !== undefined) updateData.screenshots = screenshots;

    // Update the notebook entry
    const { data, error } = await supabase
      .from('notebook_entries')
      .update(updateData)
      .eq('id', id)
      .eq('user_id', user.id)
      .select()
      .single();

    if (error) {
      console.error('Error updating notebook entry:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    // Note: Sync is now handled automatically by Edge Functions via database triggers
    if (data) {
      console.log('=== NOTEBOOK UPDATED ===');
      console.log('Automatic sync will be triggered by database trigger for:', {
        html_content: html_content ? 'present' : 'missing',
        tags: tags ? tags.length : 0,
        screenshots: screenshots ? screenshots.length : 0,
        linked_trade_ids: linked_trade_ids ? linked_trade_ids.length : 0,
        linked_strategy_ids: linked_strategy_ids ? linked_strategy_ids.length : 0
      });
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error('Error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// DELETE handler for deleting a notebook entry
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Properly await params in NextJS 15
    const { id } = await params;

    // Get cookies for server-side Supabase client
    const cookieStore = await cookies();
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
          set(_name: string, _value: string, _options: any) {
            // API routes can't set cookies directly
          },
          remove(_name: string, _options: any) {
            // API routes can't remove cookies directly
          }
        },
      }
    );

    // Get authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Delete the notebook entry
    const { error } = await supabase
      .from('notebook_entries')
      .delete()
      .eq('id', id)
      .eq('user_id', user.id);

    if (error) {
      console.error('Error deleting notebook entry:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
