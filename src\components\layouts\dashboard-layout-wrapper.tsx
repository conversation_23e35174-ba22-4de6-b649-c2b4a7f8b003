'use client'

import { usePathname } from 'next/navigation'
import { DashboardLayout } from './dashboard-layout'

const PAGE_TITLES: Record<string, string> = {
  '/dashboard': 'Performance Dashboard',
  '/trades': 'Trade Log',
  '/symbols': 'Symbols',
  '/journal': 'Daily Journal',
  '/notebook': 'Notebook',
  '/analytics': 'Analytics',
  '/metrics-goals': 'Metrics & Goals',
  '/playbook': 'Playbook',
  '/accounts': 'Accounts',
  '/profile': 'Profile',
  '/add-trade': 'Add Trade'
}

interface DashboardLayoutWrapperProps {
  children: React.ReactNode
}

export function DashboardLayoutWrapper({ children }: DashboardLayoutWrapperProps) {
  const pathname = usePathname()
  
  // Get the page title based on the current pathname
  const pageTitle = pathname ? PAGE_TITLES[pathname] || 'TradePivot' : 'TradePivot'
  
  return (
    <DashboardLayout pageTitle={pageTitle}>
      {children}
    </DashboardLayout>
  )
}
