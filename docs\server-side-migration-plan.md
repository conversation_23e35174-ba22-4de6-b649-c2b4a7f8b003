# Server-Side Migration Plan

This document outlines the plan for migrating client-side components to server-side components in the TradePivot application.

## Pages Requiring Migration

Based on our analysis, the following pages need to be migrated from client-side to server-side rendering:

### 1. Dashboard Page
- **Current Implementation**: Client-side rendering with `"use client"` directive
- **Files to Modify**:
  - `src/app/(dashboard)/dashboard/page.tsx` - Convert to server component
  - Create `src/app/(dashboard)/dashboard/client.tsx` - Move client-side logic here
  - Create `src/app/(dashboard)/dashboard/client-wrapper.tsx` - Create wrapper for dynamic import
- **API Routes to Update**:
  - None (uses client-side services)
- **Migration Priority**: High (core functionality)

### 2. Journal Page
- **Current Implementation**: Client-side rendering with `"use client"` directive
- **Files to Modify**:
  - `src/app/(dashboard)/journal/page.tsx` - Convert to server component
  - Create `src/app/(dashboard)/journal/client.tsx` - Move client-side logic here
  - Create `src/app/(dashboard)/journal/client-wrapper.tsx` - Create wrapper for dynamic import
- **API Routes to Create/Update**:
  - Create `src/app/api/journal-entries/route.ts` - Server-side API for journal entries
  - Create `src/app/api/journal-tags/route.ts` - Server-side API for journal tags
- **Migration Priority**: Medium

### 3. Analytics Page
- **Current Implementation**: Client-side rendering with `"use client"` directive
- **Files to Modify**:
  - `src/app/(dashboard)/analytics/page.tsx` - Convert to server component
  - Create `src/app/(dashboard)/analytics/client.tsx` - Move client-side logic here
  - Create `src/app/(dashboard)/analytics/client-wrapper.tsx` - Create wrapper for dynamic import
- **API Routes to Create/Update**:
  - Create `src/app/api/analytics-data/route.ts` - Server-side API for analytics data
  - Create `src/app/api/analytics-metrics/route.ts` - Server-side API for analytics metrics
- **Migration Priority**: Medium

### 4. Trades Page
- **Current Implementation**: Client-side rendering (assumed)
- **Files to Modify**:
  - `src/app/(dashboard)/trades/page.tsx` - Convert to server component
  - Create `src/app/(dashboard)/trades/client.tsx` - Move client-side logic here
  - Create `src/app/(dashboard)/trades/client-wrapper.tsx` - Create wrapper for dynamic import
- **API Routes to Update**:
  - `src/app/api/trades/route.ts` - Already updated
- **Migration Priority**: Medium

### 5. Profile Page
- **Current Implementation**: Client-side rendering (assumed)
- **Files to Modify**:
  - `src/app/(dashboard)/profile/page.tsx` - Convert to server component
  - Create `src/app/(dashboard)/profile/client.tsx` - Move client-side logic here
  - Create `src/app/(dashboard)/profile/client-wrapper.tsx` - Create wrapper for dynamic import
- **API Routes to Create/Update**:
  - Create `src/app/api/user-profile/route.ts` - Server-side API for user profile
  - Create `src/app/api/user-settings/route.ts` - Server-side API for user settings
- **Migration Priority**: Low

## Migration Steps for Each Page

### Step 1: Create Client Component
1. Create a new file `client.tsx` in the page directory
2. Add `"use client"` directive at the top
3. Move all client-side logic from `page.tsx` to `client.tsx`
4. Define props interface for data that will be passed from server component

### Step 2: Create Client Wrapper Component
1. Create a new file `client-wrapper.tsx` in the page directory
2. Add `"use client"` directive at the top
3. Use dynamic import with `ssr: false` to import the client component
4. Define props interface matching the client component
5. Pass props from server component to client component

### Step 3: Convert Page to Server Component
1. Remove `"use client"` directive from `page.tsx`
2. Implement server-side data fetching using Supabase
3. Use proper cookie handling with `await cookies()`
4. Use `getUser()` for authentication
5. Pass fetched data to client wrapper component

### Step 4: Create/Update API Routes
1. Create new API routes for server-side data operations
2. Implement proper cookie handling with `await cookies()`
3. Use `getUser()` for authentication
4. Implement proper error handling
5. Return appropriate responses

### Step 5: Update Client-Side Services
1. Update client-side services to use new API routes
2. Remove direct Supabase database access from client components
3. Implement proper error handling for API calls

## Common Patterns to Follow

### Server Component Pattern
```tsx
// page.tsx (Server Component)
import { cookies } from 'next/headers';
import { createServerClient } from '@supabase/ssr';
import { redirect } from 'next/navigation';
import type { Database } from '@/types/supabase';
import ClientWrapper from './client-wrapper';

export default async function PageName() {
  // Get cookies for server-side Supabase client
  const cookieStore = await cookies();
  
  // Create a Supabase client with proper cookie handling
  const supabase = createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value;
        },
        set(_name: string, _value: string, _options: any) {
          // Server components can't set cookies directly
        },
        remove(_name: string, _options: any) {
          // Server components can't remove cookies directly
        }
      },
    }
  );

  // Get authenticated user
  const { data: { user }, error: userError } = await supabase.auth.getUser();
  if (userError || !user) {
    redirect('/auth');
  }
  
  const userId = user.id;

  // Fetch initial data
  const { data, error } = await supabase
    .from('your_table')
    .select('*')
    .eq('user_id', userId);

  // Render the client component with the fetched data
  return <ClientWrapper userId={userId} initialData={data || []} />;
}
```

### API Route Pattern
```tsx
// route.ts (API Route)
import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { createServerClient } from '@supabase/ssr';
import type { Database } from '@/types/supabase';

export async function GET(request: Request) {
  try {
    // Get URL parameters
    const url = new URL(request.url);
    const paramId = url.searchParams.get('id');

    // Get cookies for server-side Supabase client
    const cookieStore = await cookies();
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
          set(_name: string, _value: string, _options: any) {
            // API routes can't set cookies directly
          },
          remove(_name: string, _options: any) {
            // API routes can't remove cookies directly
          }
        },
      }
    );

    // Get authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    const userId = user.id;

    // Fetch data
    const { data, error } = await supabase
      .from('your_table')
      .select('*')
      .eq('user_id', userId);

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json(data || []);
  } catch (error) {
    console.error('Error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
```

## Timeline and Priority

1. **Phase 1**: Migrate Playbook Page (Completed)
2. **Phase 2**: Migrate Dashboard Page (High Priority)
3. **Phase 3**: Migrate Journal and Analytics Pages (Medium Priority)
4. **Phase 4**: Migrate Trades Page (Medium Priority)
5. **Phase 5**: Migrate Profile Page (Low Priority)

Each phase should include thorough testing to ensure that the migrated pages work correctly and maintain the same functionality as the original client-side implementation.
