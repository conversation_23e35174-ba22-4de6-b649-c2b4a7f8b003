import { NextRequest, NextResponse } from 'next/server'
import * as XLSX from 'xlsx'

// Maximum file size (9MB)
const MAX_FILE_SIZE = 9 * 1024 * 1024 // 9MB in bytes

interface AccountInfo {
  name: string
  account: string
  company: string
  date: string
}

interface Trade {
  position_id: number
  time_open: string
  time_close: string
  symbol: string
  type: string
  volume: number
  price_open: number
  price_close: number
  commission: number
  swap: number
  profit: number
  sl?: number | null
  tp?: number | null
}

interface TradingSummary {
  initial_balance: number // Add initial balance field
  total_trades: number
  profit_trades: string
  loss_trades: string
  total_net_profit: number
  gross_profit: number
  gross_loss: number
  profit_factor: number
  expected_payoff: number
  recovery_factor: number
  sharpe_ratio: number
  balance_drawdown_absolute: number
  balance_drawdown_maximal: string
  balance_drawdown_relative: string
  short_trades_won: string
  long_trades_won: string
  largest_profit_trade: number
  largest_loss_trade: number
  average_profit_trade: number
  average_loss_trade: number
  maximum_consecutive_wins: string
  maximum_consecutive_losses: string
  maximal_consecutive_profit: string
  maximal_consecutive_loss: string
  average_consecutive_wins: number
  average_consecutive_losses: number
}

interface ProcessedData {
  account: AccountInfo
  trades: Trade[]
  summary: Partial<TradingSummary>
}

export async function POST(req: NextRequest) {
  try {
    console.log('Received file upload request')

    // Check if request is multipart/form-data
    const contentType = req.headers.get('content-type') || ''
    if (!contentType.includes('multipart/form-data')) {
      console.log('Invalid content type:', contentType)
      return NextResponse.json(
        { error: 'Request must be multipart/form-data' },
        { status: 400 }
      )
    }

    // Get the form data
    const formData = await req.formData()
    const file = formData.get('file') as File

    if (!file) {
      console.log('No file provided in request')
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      )
    }

    console.log(`Received file: ${file.name}, size: ${(file.size / 1024).toFixed(2)}KB, type: ${file.type}`)

    // Check file size
    if (file.size > MAX_FILE_SIZE) {
      console.log(`File size exceeds limit: ${(file.size / (1024 * 1024)).toFixed(2)}MB > 9MB`)
      return NextResponse.json(
        { error: `File size exceeds the 9MB limit (${(file.size / (1024 * 1024)).toFixed(2)}MB)` },
        { status: 400 }
      )
    }

    // Check file type
    const fileType = file.type
    const validTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
      'application/vnd.ms-excel', // .xls
      'text/csv', // .csv
    ]

    // Allow files with empty type if they have valid extensions
    const validExtensions = ['.xlsx', '.xls', '.csv']
    const hasValidExtension = validExtensions.some(ext => file.name.toLowerCase().endsWith(ext))

    if (!validTypes.includes(fileType) && !hasValidExtension) {
      console.log(`Invalid file type: ${fileType}`)
      return NextResponse.json(
        { error: 'Invalid file type. Only Excel (.xlsx, .xls) and CSV files are supported' },
        { status: 400 }
      )
    }

    // Process the file
    console.log('Reading file data')
    const arrayBuffer = await file.arrayBuffer()
    const data = new Uint8Array(arrayBuffer)

    console.log('Starting Excel parsing')
    // Parse the Excel file
    const processedData = await processExcelFile(data)

    console.log(`Parsing complete. Found ${processedData.trades.length} trades`)

    // Return the processed data
    return NextResponse.json({ data: processedData })
  } catch (error) {
    console.error('Error processing file:', error)

    // Create a more detailed error response
    const errorMessage = error instanceof Error ? error.message : 'Unknown error'
    const errorStack = error instanceof Error ? error.stack : ''

    console.error('Error details:', errorMessage)
    if (errorStack) console.error('Stack trace:', errorStack)

    return NextResponse.json(
      {
        error: 'Failed to process file',
        details: errorMessage,
        stack: process.env.NODE_ENV === 'development' ? errorStack : undefined
      },
      { status: 500 }
    )
  }
}

async function processExcelFile(data: Uint8Array): Promise<ProcessedData> {
  try {
    console.log('Reading Excel workbook')
    let workbook: XLSX.WorkBook

    try {
      workbook = XLSX.read(data, { type: 'array' })
    } catch (readError) {
      console.error('Error reading Excel file:', readError)
      throw new Error('Could not read the Excel file. The file might be corrupted or in an unsupported format.')
    }

    console.log(`Excel file contains ${workbook.SheetNames.length} sheets: ${workbook.SheetNames.join(', ')}`)

    // Extract account information
    console.log('Extracting account information')
    const accountInfo = extractAccountInfo(workbook)
    console.log('Account info:', accountInfo)

    // Extract trades
    console.log('Extracting trades')
    let trades: Trade[] = []

    try {
      trades = extractTrades(workbook)
      console.log(`Extracted ${trades.length} trades`)

      if (trades.length === 0) {
        console.warn('No trades found in the Excel file')
      }
    } catch (tradeError) {
      console.error('Error extracting trades:', tradeError)
      // Create some dummy trades for testing if in development
      if (process.env.NODE_ENV === 'development') {
        console.log('Creating dummy trades for development testing')
        trades = [
          {
            position_id: 1,
            time_open: '2023-01-01 10:00:00',
            time_close: '2023-01-01 11:00:00',
            symbol: 'EURUSD',
            type: 'buy',
            volume: 0.1,
            price_open: 1.1000,
            price_close: 1.1050,
            commission: -0.5,
            swap: 0,
            profit: 50,
          },
          {
            position_id: 2,
            time_open: '2023-01-02 10:00:00',
            time_close: '2023-01-02 11:00:00',
            symbol: 'GBPUSD',
            type: 'sell',
            volume: 0.1,
            price_open: 1.2500,
            price_close: 1.2450,
            commission: -0.5,
            swap: 0,
            profit: 50,
          },
        ]
      } else {
        throw new Error('Could not extract trades from the Excel file: ' +
          (tradeError instanceof Error ? tradeError.message : 'Unknown error'))
      }
    }

    // Try to extract initial balance
    try {
      const initialBalance = extractInitialBalance(workbook)
      if (initialBalance) {
        console.log(`Found initial balance: ${initialBalance}`)
      }
    } catch (balanceError) {
      console.log('Error extracting initial balance:', balanceError)
    }

    // Extract summary
    console.log('Extracting summary')
    const summary = extractSummary(workbook, trades)
    console.log('Summary extracted')

    return {
      account: accountInfo,
      trades,
      summary,
    }
  } catch (error) {
    console.error('Error in processExcelFile:', error)
    throw new Error('Failed to process Excel file: ' + (error instanceof Error ? error.message : 'Unknown error'))
  }
}

function extractAccountInfo(workbook: XLSX.WorkBook): AccountInfo {
  try {
    // Default values
    let name = 'Trading Account'
    let account = 'Demo Account'
    let company = 'MetaTrader 5'
    let date = new Date().toISOString()

    // Try to find account info in any sheet
    for (const sheetName of workbook.SheetNames) {
      try {
        const worksheet = workbook.Sheets[sheetName]

        // Convert to JSON to easily access the data
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 }) as any[][]

        // Look for account information in the first few rows
        for (let i = 0; i < Math.min(20, jsonData.length); i++) {
          const row = jsonData[i]
          if (!row || row.length === 0) continue

          // Convert row to lowercase strings for easier matching
          const rowLabels = row.map((cell: any) => String(cell || '').toLowerCase().trim())
          const rowText = rowLabels.join(' ')

          // Look for patterns that might indicate account information
          if (rowText.includes('name') || rowText.includes('trader')) {
            // Find a value that looks like a name
            for (let j = 0; j < row.length; j++) {
              const cell = row[j]
              if (cell === undefined || cell === null) continue

              const cellStr = String(cell).trim()
              const prevCell = j > 0 ? String(row[j-1] || '').toLowerCase().trim() : ''

              // If the previous cell contains 'name' and this cell has content, use it
              if (prevCell.includes('name') && cellStr.length > 0) {
                name = cellStr
                break
              }

              // If this cell looks like a name (not a number, date, etc.)
              if (cellStr.length > 3 && !/^\d/.test(cellStr) &&
                  !['name', 'account', 'company', 'date'].some(k => cellStr.toLowerCase().includes(k))) {
                name = cellStr
              }
            }
          }

          if (rowText.includes('account')) {
            // Find a value that looks like an account number
            for (let j = 0; j < row.length; j++) {
              const cell = row[j]
              if (cell === undefined || cell === null) continue

              const cellStr = String(cell).trim()
              const prevCell = j > 0 ? String(row[j-1] || '').toLowerCase().trim() : ''

              // If the previous cell contains 'account' and this cell has content, use it
              if (prevCell.includes('account') && cellStr.length > 0) {
                account = cellStr
                break
              }

              // If this cell looks like an account number (contains digits)
              if (/\d/.test(cellStr) && cellStr.length >= 5) {
                account = cellStr
              }
            }
          }

          if (rowText.includes('company') || rowText.includes('broker')) {
            // Find a value that looks like a company name
            for (let j = 0; j < row.length; j++) {
              const cell = row[j]
              if (cell === undefined || cell === null) continue

              const cellStr = String(cell).trim()
              const prevCell = j > 0 ? String(row[j-1] || '').toLowerCase().trim() : ''

              // If the previous cell contains 'company' or 'broker' and this cell has content, use it
              if ((prevCell.includes('company') || prevCell.includes('broker')) && cellStr.length > 0) {
                company = cellStr
                break
              }
            }
          }

          if (rowText.includes('date') || rowText.includes('generated')) {
            // Find a value that looks like a date
            for (let j = 0; j < row.length; j++) {
              const cell = row[j]
              if (cell === undefined || cell === null) continue

              const cellStr = String(cell).trim()
              const prevCell = j > 0 ? String(row[j-1] || '').toLowerCase().trim() : ''

              // If the previous cell contains 'date' and this cell has content, use it
              if (prevCell.includes('date') && cellStr.length > 0) {
                date = cellStr
                break
              }

              // If this cell looks like a date (contains digits and separators)
              if (/\d[\/.\-]\d/.test(cellStr)) {
                date = cellStr
              }
            }
          }

          // MT5 specific format
          const firstCell = String(row[0] || '').trim()

          if (firstCell === 'Name:' && row.length >= 2) {
            for (let j = 1; j < row.length; j++) {
              const cellStr = String(row[j] || '').trim()
              if (cellStr.length > 0) {
                name = cellStr
                break
              }
            }
          } else if (firstCell === 'Account:' && row.length >= 2) {
            for (let j = 1; j < row.length; j++) {
              const cellStr = String(row[j] || '').trim()
              if (cellStr.length > 0) {
                account = cellStr
                break
              }
            }
          } else if (firstCell === 'Company:' && row.length >= 2) {
            for (let j = 1; j < row.length; j++) {
              const cellStr = String(row[j] || '').trim()
              if (cellStr.length > 0) {
                company = cellStr
                break
              }
            }
          } else if (firstCell === 'Date:' && row.length >= 2) {
            for (let j = 1; j < row.length; j++) {
              const cellStr = String(row[j] || '').trim()
              if (cellStr.length > 0) {
                date = cellStr
                break
              }
            }
          }
        }

        // If we found all the information, break out of the sheet loop
        if (name !== 'Trading Account' && account !== 'Demo Account') {
          break
        }
      } catch (sheetError) {
        console.log(`Error extracting account info from sheet ${sheetName}:`, sheetError)
        // Continue to next sheet
      }
    }

    return { name, account, company, date }
  } catch (error) {
    console.error('Error extracting account info:', error)
    return {
      name: 'Trading Account',
      account: 'Demo Account',
      company: 'MetaTrader 5',
      date: new Date().toISOString(),
    }
  }
}

function extractTrades(workbook: XLSX.WorkBook): Trade[] {
  try {
    // Get all sheets and try to find trades in any of them
    let allTrades: Trade[] = []
    let positionTrades: Trade[] = []

    // First, try to find position trades specifically
    for (const sheetName of workbook.SheetNames) {
      try {
        const worksheet = workbook.Sheets[sheetName]

        // Look specifically for the Positions section
        const trades = tryParsePositionsFromSheet(worksheet)
        if (trades.length > 0) {
          console.log(`Successfully extracted ${trades.length} position trades from sheet: ${sheetName}`)
          positionTrades = trades
          break // We found position trades, no need to look further
        }
      } catch (sheetError) {
        console.log(`Error parsing positions from sheet ${sheetName}:`, sheetError)
        // Continue to next sheet
      }
    }

    // If we found position trades, return them
    if (positionTrades.length > 0) {
      return positionTrades
    }

    // If we didn't find position trades, try other approaches
    for (const sheetName of workbook.SheetNames) {
      try {
        const worksheet = workbook.Sheets[sheetName]

        // Try different parsing approaches
        const trades = tryParseTradesFromSheet(worksheet)
        if (trades.length > 0) {
          console.log(`Successfully extracted ${trades.length} trades from sheet: ${sheetName}`)
          allTrades = trades
          break
        }
      } catch (sheetError) {
        console.log(`Error parsing trades from sheet ${sheetName}:`, sheetError)
        // Continue to next sheet
      }
    }

    if (allTrades.length > 0) {
      return allTrades
    }

    // If we get here, we couldn't find trades in any sheet
    // Try a more generic approach as a last resort
    return tryGenericParsing(workbook)
  } catch (error) {
    console.error('Error extracting trades:', error)
    throw new Error('Failed to extract trades: ' + (error instanceof Error ? error.message : 'Unknown error'))
  }
}

function tryParsePositionsFromSheet(worksheet: XLSX.WorkSheet): Trade[] {
  // Convert to JSON with headers
  const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 }) as any[][]

  // Look specifically for the "Positions" section
  let positionsStartRow = -1
  let positionsHeaderRow = -1

  // Find the "Positions" section and its header row
  for (let i = 0; i < jsonData.length; i++) {
    const row = jsonData[i]
    if (!row || row.length === 0) continue

    const firstCell = String(row[0] || '').trim()

    if (firstCell === 'Positions') {
      positionsStartRow = i
    } else if (positionsStartRow !== -1 && firstCell === 'Time') {
      positionsHeaderRow = i
      break
    }
  }

  if (positionsStartRow === -1 || positionsHeaderRow === -1) {
    // No positions section found
    return []
  }

  // Extract header columns
  const headers = jsonData[positionsHeaderRow].map((h: any) => String(h || '').trim())

  // Find column indices
  const columnMap: Record<string, number> = {}

  // Define possible column name variations
  const columnVariations: Record<string, string[]> = {
    time_open: ['time', 'open time', 'time open'],
    position_id: ['position', 'ticket', 'order', 'id', 'deal'],
    symbol: ['symbol', 'instrument', 'pair', 'asset'],
    type: ['type', 'direction', 'action', 'side', 'buy/sell'],
    volume: ['volume', 'size', 'amount', 'quantity', 'lots'],
    price_open: ['price', 'open price', 'price open'],
    sl: ['s / l', 'stop loss', 'sl'],
    tp: ['t / p', 'take profit', 'tp'],
    time_close: ['time', 'close time', 'time close'],
    price_close: ['price', 'close price', 'price close'],
    commission: ['commission', 'fee', 'fees'],
    swap: ['swap', 'rollover', 'overnight fee'],
    profit: ['profit', 'p/l', 'pnl', 'net profit', 'result']
  }

  // Debug the headers
  console.log('Position section headers:', headers)

  // First pass: Find exact matches for open columns
  for (let i = 0; i < headers.length; i++) {
    const headerLabel = headers[i].toLowerCase()

    // First time column is always open time
    if (headerLabel === 'time' && !('time_open' in columnMap)) {
      columnMap.time_open = i
      console.log('Found time_open at index', i)
      continue
    }

    // First price column is always open price
    if (headerLabel === 'price' && !('price_open' in columnMap)) {
      columnMap.price_open = i
      console.log('Found price_open at index', i)
      continue
    }

    // Other exact matches
    if (headerLabel === 'position') {
      columnMap.position_id = i
    } else if (headerLabel === 'symbol') {
      columnMap.symbol = i
    } else if (headerLabel === 'type') {
      columnMap.type = i
    } else if (headerLabel === 'volume') {
      columnMap.volume = i
    } else if (headerLabel === 's / l') {
      columnMap.sl = i
    } else if (headerLabel === 't / p') {
      columnMap.tp = i
    } else if (headerLabel === 'commission') {
      columnMap.commission = i
    } else if (headerLabel === 'swap') {
      columnMap.swap = i
    } else if (headerLabel === 'profit') {
      columnMap.profit = i
    }
  }

  // Second pass: Find the second occurrence of time and price for close values
  let timeCount = 0
  let priceCount = 0

  for (let i = 0; i < headers.length; i++) {
    const headerLabel = headers[i].toLowerCase()

    if (headerLabel === 'time') {
      timeCount++
      if (timeCount === 2) {
        columnMap.time_close = i
        console.log('Found time_close at index', i)
      }
    }

    if (headerLabel === 'price') {
      priceCount++
      if (priceCount === 2) {
        columnMap.price_close = i
        console.log('Found price_close at index', i)
      }
    }
  }

  // Fallback to variations if exact matches not found
  for (const [key, variations] of Object.entries(columnVariations)) {
    if (!(key in columnMap)) {
      for (let i = 0; i < headers.length; i++) {
        const headerLabel = headers[i].toLowerCase()
        if (variations.some(v => headerLabel.includes(v))) {
          columnMap[key] = i
          console.log(`Found ${key} at index ${i} using variation match`)
          break
        }
      }
    }
  }

  // Check if we have the minimum required columns
  const requiredColumns = ['position_id', 'symbol', 'type', 'volume', 'profit']
  const missingColumns = requiredColumns.filter(col => !(col in columnMap))

  if (missingColumns.length > 0) {
    console.log(`Missing required columns in positions section: ${missingColumns.join(', ')}`)
    return []
  }

  const trades: Trade[] = []

  // Extract trades data
  for (let i = positionsHeaderRow + 1; i < jsonData.length; i++) {
    const row = jsonData[i]
    if (!row || row.length === 0) continue

    // Check if we've reached the end of the Positions section
    const firstCell = String(row[0] || '').trim()
    if (firstCell === 'Orders') break

    // Skip empty rows
    if (!row[columnMap.position_id]) continue

    const trade: Trade = {
      position_id: parseInt(String(row[columnMap.position_id] || '0'), 10),
      time_open: String(row[columnMap.time_open] || ''),
      time_close: 'time_close' in columnMap ? String(row[columnMap.time_close] || '') : '',
      symbol: String(row[columnMap.symbol] || ''),
      type: String(row[columnMap.type] || '').toLowerCase().includes('buy') ? 'buy' : 'sell',
      volume: parseFloat(String(row[columnMap.volume] || '0')),
      price_open: 'price_open' in columnMap ? parseFloat(String(row[columnMap.price_open] || '0').replace(/\s+/g, '')) : 0,
      price_close: 'price_close' in columnMap ? parseFloat(String(row[columnMap.price_close] || '0').replace(/\s+/g, '')) : 0,
      commission: 'commission' in columnMap ? parseFloat(String(row[columnMap.commission] || '0')) : 0,
      swap: 'swap' in columnMap ? parseFloat(String(row[columnMap.swap] || '0')) : 0,
      profit: parseFloat(String(row[columnMap.profit] || '0').replace(/\s+/g, '')),
    }

    // Add optional fields if they exist
    if ('sl' in columnMap && row[columnMap.sl]) {
      trade.sl = parseFloat(String(row[columnMap.sl]).replace(/\s+/g, ''))
    }

    if ('tp' in columnMap && row[columnMap.tp]) {
      trade.tp = parseFloat(String(row[columnMap.tp]).replace(/\s+/g, ''))
    }

    // Only add trades with valid position IDs and symbols
    if (trade.position_id > 0 && trade.symbol) {
      trades.push(trade)
    }
  }

  return trades
}

function tryParseTradesFromSheet(worksheet: XLSX.WorkSheet): Trade[] {
  // Convert to JSON with headers
  const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 }) as any[][]

  // Try to find the positions/trades section
  let headerRow = -1
  let headerLabels: string[] = []

  // Look for potential header rows with common trade column names
  const tradeHeaderKeywords = ['time', 'position', 'symbol', 'type', 'volume', 'price', 'profit']

  for (let i = 0; i < Math.min(20, jsonData.length); i++) {
    const row = jsonData[i]
    if (!row || row.length < 5) continue // Skip rows with too few columns

    // Convert row to lowercase strings for easier matching
    const rowLabels = row.map((cell: any) => String(cell || '').toLowerCase().trim())

    // Count how many trade-related keywords are in this row
    const keywordMatches = tradeHeaderKeywords.filter(keyword =>
      rowLabels.some(label => label.includes(keyword))
    ).length

    // If we find at least 4 keywords, consider this a header row
    if (keywordMatches >= 4) {
      headerRow = i
      headerLabels = row.map((cell: any) => String(cell || '').trim())
      break
    }
  }

  if (headerRow === -1) {
    throw new Error('Could not find a header row with trade data')
  }

  // Map common column names to their indices
  const columnMap: Record<string, number> = {}

  // Define possible column name variations
  const columnVariations: Record<string, string[]> = {
    time_open: ['time open', 'open time', 'time', 'open', 'entry time', 'entry date'],
    position_id: ['position id', 'position', 'ticket', 'order', 'id', 'deal'],
    symbol: ['symbol', 'instrument', 'pair', 'asset'],
    type: ['type', 'direction', 'action', 'side', 'buy/sell'],
    volume: ['volume', 'size', 'amount', 'quantity', 'lots'],
    price_open: ['price open', 'open price', 'entry price', 'price', 'open'],
    time_close: ['time close', 'close time', 'exit time', 'exit date', 'close'],
    price_close: ['price close', 'close price', 'exit price'],
    commission: ['commission', 'fee', 'fees'],
    swap: ['swap', 'rollover', 'overnight fee'],
    profit: ['profit', 'p/l', 'pnl', 'net profit', 'result']
  }

  // Find column indices based on variations
  for (const [key, variations] of Object.entries(columnVariations)) {
    for (let i = 0; i < headerLabels.length; i++) {
      const headerLabel = headerLabels[i].toLowerCase()
      if (variations.some(v => headerLabel.includes(v))) {
        columnMap[key] = i
        break
      }
    }
  }

  // Special handling for time_close and price_close if not found
  if (!('time_close' in columnMap) && 'time_open' in columnMap) {
    // Look for a second occurrence of time-related column
    for (let i = 0; i < headerLabels.length; i++) {
      const headerLabel = headerLabels[i].toLowerCase()
      if (i !== columnMap.time_open &&
          (headerLabel.includes('time') || headerLabel.includes('date'))) {
        columnMap.time_close = i
        break
      }
    }
  }

  if (!('price_close' in columnMap) && 'price_open' in columnMap) {
    // Look for a second occurrence of price-related column
    for (let i = 0; i < headerLabels.length; i++) {
      const headerLabel = headerLabels[i].toLowerCase()
      if (i !== columnMap.price_open && headerLabel.includes('price')) {
        columnMap.price_close = i
        break
      }
    }
  }

  // Check if we have the minimum required columns
  const requiredColumns = ['position_id', 'symbol', 'type', 'volume', 'profit']
  const missingColumns = requiredColumns.filter(col => !(col in columnMap))

  if (missingColumns.length > 0) {
    throw new Error(`Missing required columns: ${missingColumns.join(', ')}`)
  }

  const trades: Trade[] = []

  // Extract trades data
  for (let i = headerRow + 1; i < jsonData.length; i++) {
    const row = jsonData[i]
    if (!row || row.length === 0) continue

    // Skip rows that don't have a position ID or are section headers
    const firstCell = String(row[0] || '').trim()
    if (!row[columnMap.position_id] || ['positions', 'orders', 'deals'].includes(firstCell.toLowerCase())) {
      continue
    }

    // Create trade object with default values
    const trade: Trade = {
      position_id: 0,
      time_open: new Date().toISOString(),
      time_close: new Date().toISOString(),
      symbol: '',
      type: 'buy',
      volume: 0,
      price_open: 0,
      price_close: 0,
      commission: 0,
      swap: 0,
      profit: 0
    }

    // Fill in values from the row based on column map
    for (const [key, index] of Object.entries(columnMap)) {
      if (row[index] !== undefined) {
        const value = row[index]

        switch (key) {
          case 'position_id':
            trade.position_id = parseInt(String(value).replace(/\D/g, ''), 10) || 0
            break
          case 'time_open':
            trade.time_open = String(value)
            break
          case 'time_close':
            trade.time_close = String(value)
            break
          case 'symbol':
            trade.symbol = String(value)
            break
          case 'type':
            trade.type = String(value).toLowerCase().includes('buy') ? 'buy' : 'sell'
            break
          case 'volume':
            trade.volume = parseFloat(String(value).replace(/\s+/g, '')) || 0
            break
          case 'price_open':
            trade.price_open = parseFloat(String(value).replace(/\s+/g, '')) || 0
            break
          case 'price_close':
            trade.price_close = parseFloat(String(value).replace(/\s+/g, '')) || 0
            break
          case 'commission':
            trade.commission = parseFloat(String(value).replace(/\s+/g, '')) || 0
            break
          case 'swap':
            trade.swap = parseFloat(String(value).replace(/\s+/g, '')) || 0
            break
          case 'profit':
            trade.profit = parseFloat(String(value).replace(/\s+/g, '')) || 0
            break
        }
      }
    }

    // Only add trades with valid position IDs and symbols
    if (trade.position_id > 0 && trade.symbol) {
      trades.push(trade)
    }
  }

  return trades
}

function tryGenericParsing(workbook: XLSX.WorkBook): Trade[] {
  // Last resort: try to parse any table-like data as trades
  const trades: Trade[] = []

  for (const sheetName of workbook.SheetNames) {
    const worksheet = workbook.Sheets[sheetName]

    // Parse as objects with headers
    try {
      const rows = XLSX.utils.sheet_to_json(worksheet) as any[]

      if (rows.length === 0) continue

      // Check if this looks like trade data
      const sampleRow = rows[0]
      const hasTradeData = Object.keys(sampleRow).some(key =>
        key.toLowerCase().includes('symbol') ||
        key.toLowerCase().includes('position') ||
        key.toLowerCase().includes('profit')
      )

      if (!hasTradeData) continue

      // Try to map columns to trade properties
      for (const row of rows) {
        const trade: Trade = {
          position_id: 0,
          time_open: new Date().toISOString(),
          time_close: new Date().toISOString(),
          symbol: '',
          type: 'buy',
          volume: 0,
          price_open: 0,
          price_close: 0,
          commission: 0,
          swap: 0,
          profit: 0
        }

        // Try to find values in the row
        for (const [key, value] of Object.entries(row)) {
          const keyLower = String(key).toLowerCase()

          if (value === null || value === undefined) continue

          // Map row values to trade properties based on column names
          if (keyLower.includes('ticket') || keyLower.includes('position') || keyLower.includes('order')) {
            trade.position_id = parseInt(String(value).replace(/\D/g, ''), 10) || 0
          } else if (keyLower.includes('symbol') || keyLower.includes('instrument')) {
            trade.symbol = String(value)
          } else if (keyLower.includes('type') || keyLower.includes('direction')) {
            trade.type = String(value).toLowerCase().includes('buy') ? 'buy' : 'sell'
          } else if (keyLower.includes('volume') || keyLower.includes('lot')) {
            trade.volume = parseFloat(String(value)) || 0
          } else if ((keyLower.includes('open') && keyLower.includes('price')) || keyLower.includes('entry price')) {
            trade.price_open = parseFloat(String(value)) || 0
          } else if ((keyLower.includes('close') && keyLower.includes('price')) || keyLower.includes('exit price')) {
            trade.price_close = parseFloat(String(value)) || 0
          } else if (keyLower.includes('profit') || keyLower.includes('p/l') || keyLower.includes('pnl')) {
            trade.profit = parseFloat(String(value)) || 0
          } else if (keyLower.includes('commission') || keyLower.includes('fee')) {
            trade.commission = parseFloat(String(value)) || 0
          } else if (keyLower.includes('swap') || keyLower.includes('rollover')) {
            trade.swap = parseFloat(String(value)) || 0
          } else if ((keyLower.includes('open') && keyLower.includes('time')) || keyLower.includes('entry time')) {
            trade.time_open = String(value)
          } else if ((keyLower.includes('close') && keyLower.includes('time')) || keyLower.includes('exit time')) {
            trade.time_close = String(value)
          }
        }

        // Only add trades with valid position IDs and symbols
        if (trade.position_id > 0 && trade.symbol) {
          trades.push(trade)
        }
      }

      if (trades.length > 0) {
        console.log(`Found ${trades.length} trades using generic parsing in sheet: ${sheetName}`)
        return trades
      }
    } catch (error) {
      console.log(`Error in generic parsing for sheet ${sheetName}:`, error)
      // Continue to next sheet
    }
  }

  // If we still couldn't find any trades, return an empty array
  return []
}

function extractSummary(workbook: XLSX.WorkBook, trades: Trade[]): Partial<TradingSummary> {
  try {
    // Try to find a summary section in any sheet
    for (const sheetName of workbook.SheetNames) {
      try {
        const worksheet = workbook.Sheets[sheetName]
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 }) as any[][]

        // Look for keywords that indicate a summary section
        const summaryKeywords = ['results', 'summary', 'statistics', 'performance', 'report']
        let summaryStartRow = -1

        // Find a potential summary section
        for (let i = 0; i < jsonData.length; i++) {
          const row = jsonData[i]
          if (!row || row.length === 0) continue

          const firstCell = String(row[0] || '').trim().toLowerCase()

          if (summaryKeywords.some(keyword => firstCell.includes(keyword))) {
            summaryStartRow = i
            break
          }
        }

        if (summaryStartRow !== -1) {
          // Try to extract summary data
          const summary = extractSummaryFromRows(jsonData, summaryStartRow)

          // If we found some summary data, return it
          if (Object.keys(summary).length > 3) {
            console.log(`Found summary data in sheet: ${sheetName}`)
            return summary
          }
        }
      } catch (error) {
        console.log(`Error extracting summary from sheet ${sheetName}:`, error)
        // Continue to next sheet
      }
    }

    // If we couldn't find a summary section, calculate it from trades
    console.log('Calculating summary from trades')
    return calculateBasicSummary(trades, workbook)
  } catch (error) {
    console.error('Error extracting summary:', error)
    // Fallback to calculating basic summary from trades
    return calculateBasicSummary(trades, workbook)
  }
}

function extractSummaryFromRows(jsonData: any[][], startRow: number): Partial<TradingSummary> {
  const summary: Partial<TradingSummary> = {}

  // Define patterns to look for in the rows
  const patterns = [
    { key: 'total_net_profit', keywords: ['total net profit', 'net profit', 'total profit'] },
    { key: 'gross_profit', keywords: ['gross profit'] },
    { key: 'gross_loss', keywords: ['gross loss'] },
    { key: 'profit_factor', keywords: ['profit factor'] },
    { key: 'expected_payoff', keywords: ['expected payoff'] },
    { key: 'recovery_factor', keywords: ['recovery factor'] },
    { key: 'sharpe_ratio', keywords: ['sharpe ratio'] },
    { key: 'balance_drawdown_absolute', keywords: ['balance drawdown absolute', 'absolute drawdown'] },
    { key: 'balance_drawdown_maximal', keywords: ['balance drawdown maximal', 'maximal drawdown'] },
    { key: 'balance_drawdown_relative', keywords: ['balance drawdown relative', 'relative drawdown'] },
    { key: 'total_trades', keywords: ['total trades'] },
    { key: 'short_trades_won', keywords: ['short trades won', 'short trades (won'] },
    { key: 'long_trades_won', keywords: ['long trades won', 'long trades (won'] },
    { key: 'profit_trades', keywords: ['profit trades', 'winning trades'] },
    { key: 'loss_trades', keywords: ['loss trades', 'losing trades'] },
    { key: 'largest_profit_trade', keywords: ['largest profit trade'] },
    { key: 'largest_loss_trade', keywords: ['largest loss trade'] },
    { key: 'average_profit_trade', keywords: ['average profit trade'] },
    { key: 'average_loss_trade', keywords: ['average loss trade'] },
    { key: 'maximum_consecutive_wins', keywords: ['maximum consecutive wins'] },
    { key: 'maximum_consecutive_losses', keywords: ['maximum consecutive losses'] },
    { key: 'maximal_consecutive_profit', keywords: ['maximal consecutive profit'] },
    { key: 'maximal_consecutive_loss', keywords: ['maximal consecutive loss'] },
    { key: 'average_consecutive_wins', keywords: ['average consecutive wins'] },
    { key: 'average_consecutive_losses', keywords: ['average consecutive losses'] },
  ]

  // Scan the rows for summary data
  for (let i = startRow; i < Math.min(startRow + 30, jsonData.length); i++) {
    const row = jsonData[i]
    if (!row || row.length === 0) continue

    // Check if this row contains any of our patterns
    const rowText = row.map((cell: any) => String(cell || '').toLowerCase().trim()).join(' ')

    for (const pattern of patterns) {
      if (pattern.keywords.some(keyword => rowText.includes(keyword))) {
        // Found a match, now extract the value
        // Look for numeric values in the row
        for (let j = 0; j < row.length; j++) {
          const cell = row[j]
          if (cell === undefined || cell === null) continue

          const cellStr = String(cell).trim()

          // Check if this cell contains a number or percentage
          if (/^-?\d+(\.\d+)?%?$/.test(cellStr.replace(/[,\s]/g, ''))) {
            // This looks like a numeric value
            const numValue = parseFloat(cellStr.replace(/[%,\s]/g, ''))

            if (!isNaN(numValue)) {
              // For numeric fields
              if (['total_net_profit', 'gross_profit', 'gross_loss', 'profit_factor', 'expected_payoff',
                   'recovery_factor', 'sharpe_ratio', 'balance_drawdown_absolute', 'largest_profit_trade',
                   'largest_loss_trade', 'average_profit_trade', 'average_loss_trade', 'total_trades',
                   'average_consecutive_wins', 'average_consecutive_losses'].includes(pattern.key)) {
                summary[pattern.key as keyof TradingSummary] = numValue as any
                break
              } else {
                // For string fields (percentages, etc.)
                summary[pattern.key as keyof TradingSummary] = cellStr as any
                break
              }
            }
          } else if (cellStr.includes('/') && pattern.key.includes('trades')) {
            // This might be a ratio like "5/10 (50.00%)" for win/loss trades
            summary[pattern.key as keyof TradingSummary] = cellStr as any
            break
          }
        }
      }
    }
  }

  return summary
}

function calculateBasicSummary(trades: Trade[], workbook?: XLSX.WorkBook): Partial<TradingSummary> {
  // Filter out trades that are not from the positions section
  // We identify position trades by checking if they have both open and close times
  const positionTrades = trades.filter(trade =>
    trade.time_open && trade.time_close &&
    trade.position_id > 0 &&
    trade.symbol &&
    (trade.price_open > 0 || trade.price_close > 0)
  )

  // Use position trades count for total_trades
  const totalTrades = positionTrades.length

  // For other calculations, we can still use all trades to get a complete picture
  const profitTrades = trades.filter(trade => trade.profit > 0)
  const lossTrades = trades.filter(trade => trade.profit < 0)
  const totalProfit = trades.reduce((sum, trade) => sum + trade.profit, 0)

  // Try to find initial balance from the Deals section
  let initialBalance = 10000 // Default initial balance if not found

  if (workbook) {
    try {
      initialBalance = extractInitialBalance(workbook) || initialBalance
      console.log(`Found initial balance: ${initialBalance}`)
    } catch (error) {
      console.log('Error extracting initial balance:', error)
    }
  }

  // Calculate max drawdown with initial balance
  let balance = initialBalance
  let peak = initialBalance
  let maxDrawdown = 0
  let maxDrawdownAmount = 0

  // Sort trades by time to calculate drawdown chronologically
  const sortedTrades = [...trades].sort((a, b) => {
    const timeA = new Date(a.time_close || a.time_open).getTime()
    const timeB = new Date(b.time_close || b.time_open).getTime()
    return timeA - timeB
  })

  sortedTrades.forEach(trade => {
    balance += trade.profit
    peak = Math.max(peak, balance)
    const currentDrawdown = peak - balance
    if (currentDrawdown > maxDrawdownAmount) {
      maxDrawdownAmount = currentDrawdown
      maxDrawdown = peak > 0 ? (currentDrawdown / peak) * 100 : 0
    }
  })

  // Calculate win/loss percentages based on position trades
  const profitPositions = positionTrades.filter(trade => trade.profit > 0)
  const lossPositions = positionTrades.filter(trade => trade.profit < 0)

  const profitPercentage = totalTrades > 0 ?
    ((profitPositions.length / totalTrades) * 100).toFixed(2) + '%' : '0.00%'

  const lossPercentage = totalTrades > 0 ?
    ((lossPositions.length / totalTrades) * 100).toFixed(2) + '%' : '0.00%'

  // Calculate additional metrics
  const grossProfit = profitTrades.reduce((sum, trade) => sum + trade.profit, 0)
  const grossLoss = Math.abs(lossTrades.reduce((sum, trade) => sum + trade.profit, 0))
  const profitFactor = grossLoss > 0 ? grossProfit / grossLoss : grossProfit > 0 ? 999 : 0
  const expectedPayoff = totalTrades > 0 ? totalProfit / totalTrades : 0

  return {
    initial_balance: initialBalance, // Add initial balance to the summary
    total_trades: totalTrades,
    profit_trades: profitPercentage,
    loss_trades: lossPercentage,
    total_net_profit: totalProfit,
    balance_drawdown_absolute: maxDrawdownAmount,
    balance_drawdown_maximal: `${maxDrawdownAmount.toFixed(2)} (${maxDrawdown.toFixed(2)}%)`,
    balance_drawdown_relative: `${maxDrawdown.toFixed(2)}% (${maxDrawdownAmount.toFixed(2)})`,
    gross_profit: grossProfit,
    gross_loss: grossLoss,
    profit_factor: profitFactor,
    expected_payoff: expectedPayoff,
    largest_profit_trade: profitTrades.length > 0 ?
      Math.max(...profitTrades.map(trade => trade.profit)) : 0,
    largest_loss_trade: lossTrades.length > 0 ?
      Math.min(...lossTrades.map(trade => trade.profit)) : 0,
    average_profit_trade: profitTrades.length > 0 ?
      grossProfit / profitTrades.length : 0,
    average_loss_trade: lossTrades.length > 0 ?
      -grossLoss / lossTrades.length : 0,
  }
}

function extractInitialBalance(workbook: XLSX.WorkBook): number | null {
  try {
    // Look for the Deals section in any sheet
    for (const sheetName of workbook.SheetNames) {
      const worksheet = workbook.Sheets[sheetName]
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 }) as any[][]

      let dealsStartRow = -1
      let dealsHeaderRow = -1

      // Find the "Deals" section and its header row
      for (let i = 0; i < jsonData.length; i++) {
        const row = jsonData[i]
        if (!row || row.length === 0) continue

        const firstCell = String(row[0] || '').trim()

        if (firstCell === 'Deals') {
          dealsStartRow = i
        } else if (dealsStartRow !== -1 && firstCell === 'Time') {
          dealsHeaderRow = i
          break
        }
      }

      if (dealsStartRow === -1 || dealsHeaderRow === -1) {
        continue // Try next sheet
      }

      // Extract header columns
      const headers = jsonData[dealsHeaderRow].map((h: any) => String(h || '').trim().toLowerCase())

      // Find column indices
      const typeIndex = headers.indexOf('type')
      const balanceIndex = headers.indexOf('balance')

      if (typeIndex === -1 || balanceIndex === -1) {
        continue // Try next sheet
      }

      // Look for the initial balance entry (usually the first row after the header)
      for (let i = dealsHeaderRow + 1; i < jsonData.length; i++) {
        const row = jsonData[i]
        if (!row || row.length === 0) continue

        const type = String(row[typeIndex] || '').trim().toLowerCase()

        if (type === 'balance') {
          // Found a balance entry, extract the balance value
          const balanceValue = parseFloat(String(row[balanceIndex] || '0').replace(/\s+/g, ''))
          if (!isNaN(balanceValue) && balanceValue > 0) {
            return balanceValue
          }
        }
      }
    }

    return null // No initial balance found
  } catch (error) {
    console.error('Error extracting initial balance:', error)
    return null
  }
}
