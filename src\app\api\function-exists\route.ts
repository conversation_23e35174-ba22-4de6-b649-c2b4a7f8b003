import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

export async function GET(request: Request) {
  try {
    // Get the function name from the query parameters
    const url = new URL(request.url);
    const functionName = url.searchParams.get('name');
    
    if (!functionName) {
      return NextResponse.json({ error: 'Function name is required' }, { status: 400 });
    }
    
    // Create a Supabase client with admin privileges
    const supabaseAdmin = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL || '',
      process.env.SUPABASE_SERVICE_ROLE_KEY || ''
    );
    
    // Check if the function exists
    const { data, error } = await supabaseAdmin.from('pg_proc')
      .select('proname')
      .eq('proname', functionName)
      .limit(1);
    
    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }
    
    return NextResponse.json({
      exists: data && data.length > 0,
      functionName
    });
  } catch (error) {
    console.error('Error in function-exists route:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
