import { createServerClient } from '@supabase/ssr'
import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import type { Database } from '@/types/supabase'

export async function middleware(req: NextRequest) {
  try {
    const pathname = req.nextUrl.pathname

    // Skip middleware for static files and Next.js internals
    if (
      pathname.startsWith('/_next') ||
      pathname.startsWith('/favicon') ||
      pathname === '/robots.txt' ||
      pathname === '/sitemap.xml' ||
      pathname.includes('.')
    ) {
      return NextResponse.next()
    }

    // Define routes
    const isAuthRoute = pathname === '/auth' || pathname.startsWith('/auth/')
    const isApiRoute = pathname.startsWith('/api/')
    const isHomePage = pathname === '/' || pathname === '/home'

    // Public routes that don't require authentication
    const isPublicRoute =
      isHomePage ||
      isAuthRoute ||
      isApiRoute ||
      pathname.startsWith('/test') ||
      pathname.startsWith('/simple') ||
      pathname.startsWith('/editor-test') ||
      pathname.startsWith('/quill') ||
      pathname.startsWith('/tiptap') ||
      pathname.startsWith('/advanced-quill')

    // Protected routes that require authentication
    const isProtectedRoute =
      pathname.startsWith('/dashboard') ||
      pathname.startsWith('/trades') ||
      pathname.startsWith('/symbols') ||
      pathname.startsWith('/journal') ||
      pathname.startsWith('/analytics') ||
      pathname.startsWith('/metrics-goals') ||
      pathname.startsWith('/accounts') ||
      pathname.startsWith('/reports') ||
      pathname.startsWith('/calendar') ||
      pathname.startsWith('/profile') ||
      pathname.startsWith('/settings') ||
      pathname.startsWith('/playbook') ||
      pathname.startsWith('/notebook')

    // Allow public routes to pass through without authentication checks
    if (isPublicRoute) {
      return NextResponse.next()
    }

    // Only check authentication for protected routes
    if (isProtectedRoute) {
      const res = NextResponse.next()
      // Create a Supabase client for the middleware
      const supabase = createServerClient<Database>(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
        {
          cookies: {
            get: (name) => req.cookies.get(name)?.value,
            set: (name, value, options) => {
              req.cookies.set({ name, value, ...options })
              res.cookies.set({ name, value, ...options })
            },
            remove: (name, options) => {
              req.cookies.set({ name, value: '', ...options })
              res.cookies.set({ name, value: '', ...options })
            },
          },
        }
      )

      // Get authenticated user using getUser() for better security
      const { data: { user }, error: userError } = await supabase.auth.getUser()

      // If no user or error, redirect to auth page
      if (userError || !user) {
        console.log(`No authenticated user found for ${req.nextUrl.pathname}, redirecting to auth`)
        const redirectUrl = new URL('/auth', req.url)
        redirectUrl.searchParams.set('redirect', req.nextUrl.pathname)
        return NextResponse.redirect(redirectUrl)
      }

      // Add user ID to request headers for debugging
      res.headers.set('x-user-id', user.id)

      return res
    }

    // For any other routes (like static files, etc.), just proceed
    return NextResponse.next()
  } catch (error) {
    console.error('Middleware error:', error)
    // Don't redirect to auth for non-protected routes during errors
    const pathname = req.nextUrl.pathname
    if (pathname === '/' || pathname.startsWith('/auth') || pathname.startsWith('/api/')) {
      return NextResponse.next()
    }
    return NextResponse.redirect(new URL('/auth', req.url))
  }
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - _next/webpack-hmr (webpack hot reload)
     * - favicon.ico (favicon file)
     * - robots.txt, sitemap.xml (SEO files)
     * - any file with extension (images, css, js, etc.)
     */
    '/((?!_next/static|_next/image|_next/webpack-hmr|favicon.ico|robots.txt|sitemap.xml|.*\\.).*)',
  ],
}