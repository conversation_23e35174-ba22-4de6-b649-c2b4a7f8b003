import { cookies } from 'next/headers';
import { createServerClient } from '@supabase/ssr';
import type { Database } from '@/types/supabase';
import JournalClient from './new-client';
import { AuthCheck } from '@/components/auth-check';
import { format } from 'date-fns';
import { fetchFilteredJournalData } from './filter-actions';

// Helper function to compare dates (ignoring time)
const areDatesEqual = (date1: Date, date2: Date): boolean => {
  // Set both dates to UTC midnight to ensure proper comparison
  const date1UTC = new Date(Date.UTC(
    date1.getFullYear(),
    date1.getMonth(),
    date1.getDate()
  ));

  const date2UTC = new Date(Date.UTC(
    date2.getFullYear(),
    date2.getMonth(),
    date2.getDate()
  ));

  // Compare the UTC dates
  return date1UTC.getTime() === date2UTC.getTime();
}

export default async function JournalPage({
  searchParams
}: {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>
}) {
  // In Next.js 15, searchParams is a Promise that needs to be awaited
  const resolvedParams = await searchParams;

  // Extract search parameters with proper type checking
  const searchTerm = typeof resolvedParams.searchTerm === 'string' ? resolvedParams.searchTerm : '';

  // Handle tags which could be a string, array, or undefined
  let tags: string[] = [];
  if (Array.isArray(resolvedParams.tags)) {
    tags = resolvedParams.tags;
  } else if (typeof resolvedParams.tags === 'string') {
    tags = [resolvedParams.tags];
  }

  // Handle date parameters
  const startDate = typeof resolvedParams.startDate === 'string' ? resolvedParams.startDate : undefined;
  const endDate = typeof resolvedParams.endDate === 'string' ? resolvedParams.endDate : undefined;

  // Handle tab parameter with default
  const activeTab = typeof resolvedParams.tab === 'string' ? resolvedParams.tab : 'all';

  // Create a Supabase client for server-side operations
  // Use await with cookies() as it's now an async function in Next.js 15
  const cookieStore = await cookies();

  // Create a Supabase client for server-side operations using the updated API
  const supabase = createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name) {
          return cookieStore.get(name)?.value;
        },
        set(_name, _value, _options) {
          // Server components can't set cookies directly
        },
        remove(_name, _options) {
          // Server components can't remove cookies directly
        }
      }
    }
  );

  // Get authenticated user using getUser() for better security
  const { data: { user }, error: userError } = await supabase.auth.getUser();

  // If authentication error, log it
  if (userError) {
    console.error('Error authenticating user:', userError);
    // We'll let the client-side AuthCheck handle the redirect
  }

  // Use a default value for userId in case there's no user
  const userId = user?.id || '';

  // Fetch user accounts
  const { data: accounts, error: accountsError } = await supabase
    .from("trading_accounts")
    .select("*")
    .eq("user_id", userId)
    .order("updated_at", { ascending: false });

  if (accountsError) {
    console.error('Error fetching trading accounts:', accountsError);
    // Continue with empty accounts array
  }

  // Get the selected account ID from the first account (if any)
  const selectedAccountId = accounts && accounts.length > 0 ? accounts[0].id : null;

  // Fetch data in parallel for better performance
  // This reduces the total wait time by executing queries concurrently
  const [
    journalEntriesResult,
    tagsDataResult,
    dailyJournalEntriesResult
  ] = await Promise.all([
    // Fetch journal entries
    supabase
      .from("journal_entries")
      .select("*")
      .eq("user_id", userId)
      .order("entry_date", { ascending: false }),

    // Fetch all tags
    supabase
      .from("journal_entries")
      .select("tags")
      .eq("user_id", userId),

    // Fetch daily journal entries
    supabase.rpc(
      'get_daily_journal_entries',
      {
        p_user_id: userId,
        p_account_id: selectedAccountId || null,
        p_start_date: startDate || null,
        p_end_date: endDate || null,
        p_tags: tags.length > 0 ? tags : null
      }
    )
  ]);

  // Extract results and handle errors
  const { data: journalEntries, error: journalError } = journalEntriesResult;
  if (journalError) {
    console.error('Error fetching journal entries:', journalError);
  }

  const { data: tagsData, error: tagsError } = tagsDataResult;
  if (tagsError) {
    console.error('Error fetching tags:', tagsError);
  }

  const { data: dailyJournalEntries, error: dailyJournalError } = dailyJournalEntriesResult;
  if (dailyJournalError) {
    console.error('Error fetching daily journal entries:', dailyJournalError);
  }

  // Extract all tags and remove duplicates
  const allTags = tagsData?.flatMap(entry => entry.tags || []) || [];
  const uniqueTags = [...new Set(allTags)];

  // Extract tags from daily journal entries
  const dailyJournalTags = dailyJournalEntries?.flatMap((entry: any) => entry.tags || []) || [];
  const uniqueDailyJournalTags = [...new Set(dailyJournalTags)];

  // Combine and deduplicate all tags
  const combinedTags = [...new Set([...uniqueTags, ...uniqueDailyJournalTags])];

  // Define pagination parameters
  const PAGE_SIZE = 50; // Limit initial load to 50 trades for better performance
  const page = 1; // Start with first page

  // Start building the trades query with filters and pagination
  let tradesQuery = supabase
    .from("trades")
    .select("*", { count: 'exact' }) // Get total count for pagination
    .eq("user_id", userId)
    .order("time_close", { ascending: false })
    .range((page - 1) * PAGE_SIZE, page * PAGE_SIZE - 1); // Apply pagination

  // Apply account filter
  if (selectedAccountId) {
    tradesQuery = tradesQuery.eq("account_id", selectedAccountId);
  }

  // Apply date filters if provided
  if (startDate) {
    tradesQuery = tradesQuery.gte('time_close', `${startDate}T00:00:00`);
  }

  if (endDate) {
    tradesQuery = tradesQuery.lte('time_close', `${endDate}T23:59:59.999`);
  }

  // Apply tag filters if provided
  if (tags && tags.length > 0) {
    tradesQuery = tradesQuery.contains('tags', tags);
  }

  // Apply search term filter if provided
  if (searchTerm && searchTerm.trim() !== '') {
    const term = searchTerm.toLowerCase().trim();
    tradesQuery = tradesQuery.or(`notes.ilike.%${term}%,symbol.ilike.%${term}%`);
  }

  // Execute the query
  const { data: tradesData, error: tradesError, count: totalTradeCount } = await tradesQuery;

  if (tradesError) {
    console.error('Error fetching trades:', tradesError);
  }

  // Add pagination metadata to be passed to the client
  const paginationMeta = {
    currentPage: page,
    totalPages: totalTradeCount ? Math.ceil(totalTradeCount / PAGE_SIZE) : 1,
    pageSize: PAGE_SIZE,
    totalCount: totalTradeCount || 0
  };

  // Post-process for tag search if needed
  let filteredTrades = tradesData || [];

  // If we have a search term, we need to also check tags since we can't do that fully in the query
  if (searchTerm && searchTerm.trim() !== '') {
    const term = searchTerm.toLowerCase().trim();

    // Filter for tag matches that weren't caught by the OR condition
    const tagMatches = (tradesData || []).filter(trade =>
      Array.isArray(trade.tags) &&
      trade.tags.some((tag: string) => tag.toLowerCase().includes(term))
    );

    // Combine and deduplicate results
    const allMatches = [...filteredTrades, ...tagMatches];
    const uniqueIds = new Set();
    filteredTrades = allMatches.filter(trade => {
      if (uniqueIds.has(trade.id)) return false;
      uniqueIds.add(trade.id);
      return true;
    });
  }

  // Calculate trading days from trades
  const tradingDays: string[] = [];
  const tradingDaysSet = new Set<string>();

  filteredTrades.forEach(trade => {
    try {
      const tradeDate = new Date(trade.time_close);
      const dateStr = tradeDate.toISOString().split('T')[0]; // YYYY-MM-DD format
      tradingDaysSet.add(dateStr);
    } catch (error) {
      console.error('Error processing trade date:', error);
    }
  });

  // Convert to array and sort in descending order (newest first)
  tradingDays.push(...Array.from(tradingDaysSet).sort((a, b) => {
    return new Date(b).getTime() - new Date(a).getTime();
  }));

  // Create the trades object with both trades and trading days
  const trades = {
    trades: filteredTrades,
    tradingDays
  };

  // Fetch strategies for trades with strategy_id
  const tradesWithStrategy = filteredTrades.filter(trade => trade.strategy_id);
  const strategyIds = [...new Set(tradesWithStrategy.map(trade => trade.strategy_id))];

  let strategies: Array<{ id: string, name: string }> = [];
  if (strategyIds.length > 0) {
    const { data: strategiesData, error: strategiesError } = await supabase
      .from('strategies')
      .select('id, name')
      .in('id', strategyIds);

    if (strategiesError) {
      console.error('Error fetching strategies:', strategiesError);
    } else {
      strategies = strategiesData || [];
    }
  }

  // Create strategy map on the server
  const strategyMap: Record<string, string> = {};
  strategies.forEach(strategy => {
    strategyMap[strategy.id] = strategy.name;
  });

  // Process trades with journal content on the server
  const tradesWithJournalContent = filteredTrades.filter(trade =>
    (trade.notes && trade.notes.trim().length > 0) ||
    (Array.isArray(trade.screenshots) && trade.screenshots.length > 0) ||
    trade.has_journal_content === true
  );

  // Get filtered trading days for daily journals
  // This is a complex operation that was previously done on the client
  let filteredDays = tradingDays;

  // If we're filtering by tags, include days that have daily journal entries with matching tags
  if (tags.length > 0 && activeTab !== "with-trades") {
    try {
      // Fetch all daily journal entries that have any of the selected tags
      const params = new URLSearchParams();
      tags.forEach(tag => params.append('tags', tag));
      if (selectedAccountId) params.append('accountId', selectedAccountId);

      // Use the already fetched daily journal entries
      const entriesWithMatchingTags = dailyJournalEntries?.filter((entry: any) =>
        Array.isArray(entry.tags) &&
        entry.tags.some((tag: string) => tags.includes(tag))
      ) || [];

      // For each matching entry, add its date to our unique dates set
      entriesWithMatchingTags.forEach((entry: any) => {
        const dateStr = format(new Date(entry.date), 'yyyy-MM-dd');
        tradingDaysSet.add(dateStr);
      });

      // Update the filtered days
      filteredDays = Array.from(tradingDaysSet).sort((a, b) => {
        return new Date(b).getTime() - new Date(a).getTime();
      });
    } catch (error) {
      console.error('Error processing daily journal entries with matching tags:', error);
    }
  }

  // Fetch initial data using our new implementation
  try {
    console.log('Fetching initial journal data with parameters:', {
      userId,
      searchTerm,
      tags,
      startDate,
      endDate,
      activeTab,
    });

    const initialData = await fetchFilteredJournalData(
      userId,
      selectedAccountId,
      searchTerm,
      tags,
      startDate,
      endDate,
      activeTab
    );

    // Pass the fetched and processed data to the client component with type assertions
    // Wrap with AuthCheck to ensure client-side authentication
    return (
      <AuthCheck>
        <JournalClient
          userId={userId}
          initialData={initialData}
          initialSearchTerm={searchTerm}
          initialTags={tags}
          initialStartDate={startDate}
          initialEndDate={endDate}
          initialTab={activeTab}
        />
      </AuthCheck>
    );
  } catch (error) {
    console.error('Error fetching initial journal data:', error);

    // Return error state
    return (
      <AuthCheck>
        <div className="container mx-auto py-6">
          <div className="bg-destructive/10 border border-destructive rounded-lg p-4 text-center">
            <h2 className="text-lg font-medium text-destructive">Error Loading Journal</h2>
            <p className="mt-2">
              There was an error loading your journal data. Please try refreshing the page.
            </p>
          </div>
        </div>
      </AuthCheck>
    );
  }
}
