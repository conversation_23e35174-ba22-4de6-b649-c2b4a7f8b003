"use client"

import { useState, useEffect } from 'react'
import { useSearchParams } from 'next/navigation'
import { getSupabaseBrowser } from '@/lib/supabase'
import { toast } from 'sonner'
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { AlertCircle, Loader2 } from 'lucide-react'
import { Alert, AlertDescription } from "@/components/ui/alert"

export default function AuthPage() {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')
  const [successMessage, setSuccessMessage] = useState('')
  const searchParams = useSearchParams()
  const redirectPath = searchParams?.get('redirect') || '/dashboard'
  const defaultTab = searchParams?.get('tab') === 'register' ? 'register' : 'login'
  const [activeTab, setActiveTab] = useState(defaultTab)
  const supabase = getSupabaseBrowser()

  // Check if user was redirected after successful registration
  useEffect(() => {
    const isRegistrationSuccess = searchParams?.get('registration') === 'success'
    if (isRegistrationSuccess) {
      setSuccessMessage('Registration successful! Please sign in with your new account.')
      setActiveTab('login')
    }
  }, [searchParams])

  // Login form state
  const [loginEmail, setLoginEmail] = useState('')
  const [loginPassword, setLoginPassword] = useState('')

  // Register form state
  const [registerEmail, setRegisterEmail] = useState('')
  const [registerPassword, setRegisterPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')

  // We no longer automatically redirect users with a session
  // This allows users to explicitly sign in or register
  // The middleware will handle redirecting authenticated users who try to access the auth page directly

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError('')

    try {
      // Validate inputs
      if (!loginEmail || !loginPassword) {
        setError('Please enter both email and password')
        toast.error('Missing credentials')
        return
      }

      // Sign in with password - wrap in try/catch to prevent console errors
      let data;
      try {
        const result = await supabase.auth.signInWithPassword({
          email: loginEmail,
          password: loginPassword,
        })

        // Handle authentication errors
        if (result.error) {
          // Handle specific error types with user-friendly messages
          if (result.error.message === 'Invalid login credentials') {
            setError('The email or password you entered is incorrect. Please try again.')
          } else if (result.error.message.includes('Email not confirmed')) {
            setError('Please check your email to confirm your account before signing in.')
          } else {
            setError('Unable to sign in at this time. Please try again later.')
          }
          toast.error('Sign in failed')
          return
        }

        data = result.data;
      } catch (authError) {
        setError('The email or password you entered is incorrect. Please try again.')
        toast.error('Sign in failed')
        return
      }

      if (!data || !data.session) {
        setError('Unable to create a session. Please try again.')
        toast.error('Sign in failed')
        return
      }

      // Success! Redirect to dashboard
      toast.success('Signed in successfully!')

      // Force a hard navigation to the dashboard
      window.location.href = redirectPath
    } catch (err: any) {
      // Generic error handler as fallback
      setError('An unexpected error occurred. Please try again later.')
      toast.error('Sign in failed')
    } finally {
      setIsLoading(false)
    }
  }

  const handleRegister = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError('')

    // Validate inputs
    if (!registerEmail) {
      setError('Please enter an email address')
      setIsLoading(false)
      return
    }

    // Validate passwords match
    if (registerPassword !== confirmPassword) {
      setError('Passwords do not match')
      setIsLoading(false)
      return
    }

    try {
      // Validate password strength
      if (registerPassword.length < 6) {
        setError('Password must be at least 6 characters long')
        toast.error('Password too short')
        return
      }

      // Sign up with email and password - wrap in try/catch to prevent console errors
      try {
        const result = await supabase.auth.signUp({
          email: registerEmail,
          password: registerPassword,
          options: {
            emailRedirectTo: `${window.location.origin}/auth/callback`,
          },
        })

        if (result.error) {
          // Handle specific error types with user-friendly messages
          if (result.error.message.includes('email')) {
            setError('This email address is invalid or already in use. Please try another.')
          } else if (result.error.message.includes('password')) {
            setError('Please choose a stronger password. It should be at least 6 characters long.')
          } else {
            setError('Unable to create your account at this time. Please try again later.')
          }
          toast.error('Registration failed')
          return
        }
      } catch (signUpError) {
        setError('Unable to create your account at this time. Please try again later.')
        toast.error('Registration failed')
        return
      }

      // Show success message and switch to login tab
      toast.success('Registration successful! Please check your email to confirm your account.')

      // Clear the registration form
      setRegisterEmail('')
      setRegisterPassword('')
      setConfirmPassword('')

      // Switch to login tab
      setActiveTab('login')

      // No automatic redirection - user must explicitly log in
    } catch (err: any) {
      // Generic error handler as fallback
      setError('An unexpected error occurred. Please try again later.')
      toast.error('Registration failed')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="flex min-h-screen items-center justify-center bg-background p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1">
          <CardTitle className="text-2xl font-bold text-center">TradePivot</CardTitle>
          <CardDescription className="text-center">
            Enter your credentials to access your account
          </CardDescription>
        </CardHeader>
        <CardContent>
          {error && (
            <Alert variant="destructive" className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {successMessage && (
            <Alert className="mb-4 bg-green-50 text-green-800 border-green-200">
              <AlertDescription>{successMessage}</AlertDescription>
            </Alert>
          )}

          <Tabs defaultValue={defaultTab} value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-2 mb-4">
              <TabsTrigger value="login">Login</TabsTrigger>
              <TabsTrigger value="register">Register</TabsTrigger>
            </TabsList>

            <TabsContent value="login">
              <form onSubmit={handleLogin} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={loginEmail}
                    onChange={(e) => setLoginEmail(e.target.value)}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="password">Password</Label>
                  </div>
                  <Input
                    id="password"
                    type="password"
                    value={loginPassword}
                    onChange={(e) => setLoginPassword(e.target.value)}
                    required
                  />
                </div>
                <Button type="submit" className="w-full" disabled={isLoading}>
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Signing in...
                    </>
                  ) : (
                    'Sign In'
                  )}
                </Button>
              </form>
            </TabsContent>

            <TabsContent value="register">
              <form onSubmit={handleRegister} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="register-email">Email</Label>
                  <Input
                    id="register-email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={registerEmail}
                    onChange={(e) => setRegisterEmail(e.target.value)}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="register-password">Password</Label>
                  <Input
                    id="register-password"
                    type="password"
                    value={registerPassword}
                    onChange={(e) => setRegisterPassword(e.target.value)}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="confirm-password">Confirm Password</Label>
                  <Input
                    id="confirm-password"
                    type="password"
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    required
                  />
                </div>
                <Button type="submit" className="w-full" disabled={isLoading}>
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Creating account...
                    </>
                  ) : (
                    'Create Account'
                  )}
                </Button>
              </form>
            </TabsContent>
          </Tabs>
        </CardContent>
        <CardFooter className="flex flex-col">
          <p className="text-xs text-center text-muted-foreground mt-4">
            By continuing, you agree to our Terms of Service and Privacy Policy.
          </p>
        </CardFooter>
      </Card>
    </div>
  )
}
