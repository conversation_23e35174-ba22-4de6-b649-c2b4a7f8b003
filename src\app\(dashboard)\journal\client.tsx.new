"use client"

import { useState, useEffect } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { format } from "date-fns"
import { fetchFilteredJournalData, fetchTradesForDate, batchFetchTradesForDates } from "./actions"

// Helper function to compare dates (ignoring time)
const areDatesEqual = (date1: Date, date2: Date): boolean => {
  // Set both dates to UTC midnight to ensure proper comparison
  const date1UTC = new Date(Date.UTC(
    date1.getFullYear(),
    date1.getMonth(),
    date1.getDate()
  ));

  const date2UTC = new Date(Date.UTC(
    date2.getFullYear(),
    date2.getMonth(),
    date2.getDate()
  ));

  // Compare the UTC dates
  return date1UTC.getTime() === date2UTC.getTime();
}
import { DateRange } from "react-day-picker"
import { TableDateRangePicker } from "@/components/ui/table-date-range-picker"
import { Plus, Search, Filter, Tag, Calendar, X, <PERSON><PERSON><PERSON>, <PERSON><PERSON>hart3, <PERSON>, Loader2, <PERSON><PERSON>ronLeft, ChevronRight } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { JournalEntryForm } from "@/components/journal-entry-form"
import { AutomatedDailyJournal } from "@/components/automated-daily-journal"
import { TradeJournalCard } from "@/components/trade-journal-card"
import { TradeDetailsEditDialog } from "@/components/trade-details-edit-dialog"
import { type JournalEntry, type JournalEntryInsert } from "@/lib/journal-service"
import { useAccount } from "@/contexts/account-context"
import { toast } from "sonner"

// Define the props interface for the client component
interface JournalClientProps {
  userId: string;
  initialEntries: JournalEntry[];
  initialTags: string[];
  initialTrades: {
    trades: any[];
    tradingDays: string[];
  };
  initialDailyJournalEntries: any[];
  initialStrategies: any[];
  selectedAccountId: string | null;
  initialTradesWithJournalContent: any[];
  initialStrategyMap: Record<string, string>;
  initialFilteredDays: string[];
  pagination?: {
    currentPage: number;
    totalPages: number;
    pageSize: number;
    totalCount: number;
  };
  searchParams: {
    searchTerm: string;
    tags: string[];
    startDate?: string;
    endDate?: string;
    activeTab: string;
  };
}

export default function JournalClient({
  userId,
  initialEntries,
  initialTags,
  initialTrades,
  initialDailyJournalEntries,
  initialStrategies,
  selectedAccountId: initialAccountId,
  initialTradesWithJournalContent,
  initialStrategyMap,
  initialFilteredDays,
  pagination,
  searchParams
}: JournalClientProps) {
  const router = useRouter()
  // Note: We no longer use entries since we've removed the Journal Entries section
  // But we keep the state for API compatibility
  const [, setEntries] = useState<JournalEntry[]>(initialEntries)

  const [isNewEntryDialogOpen, setIsNewEntryDialogOpen] = useState(false)
  const [isEditEntryDialogOpen, setIsEditEntryDialogOpen] = useState(false)
  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] = useState(false)
  const [currentEntry, setCurrentEntry] = useState<JournalEntry | null>(null)
  const [isTradeEditDialogOpen, setIsTradeEditDialogOpen] = useState(false)
  const [currentTrade, setCurrentTrade] = useState<any>(null)

  // Initialize state with server-provided search params
  const [searchTerm, setSearchTerm] = useState(searchParams.searchTerm || "")
  const [selectedTags, setSelectedTags] = useState<string[]>(searchParams.tags || [])
  const [availableTags, setAvailableTags] = useState<string[]>(initialTags)
  const [dateRange, setDateRange] = useState<DateRange | undefined>(() => {
    if (!searchParams.startDate && !searchParams.endDate) return undefined;

    return {
      from: searchParams.startDate ? new Date(searchParams.startDate) : undefined,
      to: searchParams.endDate ? new Date(searchParams.endDate) : undefined
    };
  })
  const [isFilterOpen, setIsFilterOpen] = useState(false)
  const [activeTab, setActiveTab] = useState(searchParams.activeTab || "with-trades")
  const [batchTradeData, setBatchTradeData] = useState<Record<string, any>>({})
  const [batchJournalData, setBatchJournalData] = useState<Record<string, any>>({})
  const [isBatchLoading, setIsBatchLoading] = useState(false)

  // Journal cards pagination state
  const [journalPagination, setJournalPagination] = useState({
    currentPage: 1,
    itemsPerPage: 5,
    totalPages: 1
  })

  // Use server-processed filtered days
  const [, setTradingDays] = useState<Date[]>(
    initialTrades.tradingDays?.map((dateStr: string) => new Date(dateStr)) || []
  )

  // Use server-processed filtered days
  const [filteredDays, setFilteredDays] = useState<Date[]>(
    initialFilteredDays?.map((dateStr: string) => new Date(dateStr)) || []
  )

  const { selectedAccountId, isInitializing } = useAccount()

  // Use server-processed trades with journal content
  const [tradesWithJournalContent, setTradesWithJournalContent] = useState<any[]>(
    initialTradesWithJournalContent || []
  )

  // Add pagination state
  const [paginationState, setPaginationState] = useState({
    currentPage: pagination?.currentPage || 1,
    totalPages: pagination?.totalPages || 1,
    pageSize: pagination?.pageSize || 50,
    totalCount: pagination?.totalCount || 0,
    isLoading: false,
    hasMore: pagination?.totalPages ? pagination.currentPage < pagination.totalPages : false
  })

  // Use server-created strategy map
  const [strategyMap, setStrategyMap] = useState<Map<string, string>>(() => {
    const map = new Map<string, string>();
    Object.entries(initialStrategyMap).forEach(([key, value]) => {
      map.set(key, value);
    });
    return map;
  })

  // Batch fetch trades and journal entries for multiple dates at once
  const batchFetchData = async (dates: Date[]) => {
    if (!dates || dates.length === 0) return;

    try {
      setIsBatchLoading(true);
      console.log(`Starting batch fetch for ${dates.length} dates with account ID: ${selectedAccountId || 'none'}`);

      // Format dates for the server action
      const dateStrings = dates.map(date => format(date, "yyyy-MM-dd"));
      console.log(`Formatted dates for batch fetch: ${dateStrings.join(', ')}`);

      // When we're showing daily journals that matched our tag filter,
      // we want to show ALL trades for those dates, not just the ones that match the filter
      const shouldApplyFilters = !(selectedTags.length > 0);

      // Use the batch server action to get trades for all dates at once
      console.log(`Fetching batch trade data with filters: ${shouldApplyFilters}`);
      const batchTradeResults = await batchFetchTradesForDates(
        userId,
        selectedAccountId,
        dateStrings,
        shouldApplyFilters,
        shouldApplyFilters ? searchTerm : undefined,
        shouldApplyFilters ? selectedTags : undefined
      );

      // Update the batch trade data state
      setBatchTradeData(batchTradeResults);
      console.log(`Batch trade data fetched with ${Object.keys(batchTradeResults).length} date entries`);

      // Batch fetch journal entries for the same dates
      console.log(`Fetching batch journal entries for user ${userId} with account ${selectedAccountId || 'none'}`);
      const { batchGetDailyJournalEntries } = await import("@/lib/daily-journal-service");
      const batchJournalResults = await batchGetDailyJournalEntries(
        userId,
        dates,
        selectedAccountId || undefined
      );

      console.log(`Batch journal entries fetched with ${Object.keys(batchJournalResults).length} entries`);

      // Update the batch journal data state with a delay to ensure React state updates properly
      setTimeout(() => {
        setBatchJournalData(batchJournalResults);
        console.log(`Batch journal data state updated`);
      }, 0);

      // Log the keys in the batch journal data for debugging
      console.log(`Batch journal data keys: ${Object.keys(batchJournalResults).join(', ')}`);

      // Log a sample entry to see its structure
      const sampleKey = Object.keys(batchJournalResults)[0];
      if (sampleKey) {
        console.log(`Sample journal entry for ${sampleKey}:`, batchJournalResults[sampleKey]);
      } else {
        console.log(`No journal entries found in batch results`);
      }
    } catch (error) {
      console.error('Error batch fetching data:', error);
      toast.error("Failed to load data");
    } finally {
      setIsBatchLoading(false);
    }
  };

  // Get filtered trading days based on trades that match filters
  // This now uses the server action for better performance
  const getFilteredTradingDays = async () => {
    try {
      // Wait for account context to be fully initialized
      if (isInitializing) {
        console.log('Account context is still initializing, waiting...');
        // Wait for initialization to complete
        await new Promise<void>((resolve) => {
          const checkInitialization = () => {
            if (!isInitializing) {
              resolve();
            } else {
              setTimeout(checkInitialization, 100); // Check every 100ms
            }
          };
          checkInitialization();
        });
        console.log('Account context initialization complete, proceeding with data fetch');
      }

      console.log(`Fetching filtered journal data with account ID: ${selectedAccountId || 'none'}`);

      // Use the server action to get filtered data
      const filteredData = await fetchFilteredJournalData(
        userId,
        selectedAccountId,
        searchTerm,
        selectedTags,
        dateRange?.from ? format(dateRange.from, "yyyy-MM-dd") : undefined,
        dateRange?.to ? format(dateRange.to, "yyyy-MM-dd") : undefined,
        activeTab
      );

      // Convert the trading days to Date objects
      const days = filteredData.tradingDays.map((dateStr: string) => new Date(dateStr))
        .sort((a: Date, b: Date) => b.getTime() - a.getTime()); // newest first

      console.log(`Found ${days.length} trading days, batch fetching data for first 10`);

      // Batch fetch trades and journal entries for these days
      if (days.length > 0) {
        await batchFetchData(days.slice(0, 10)); // Limit to first 10 days for performance
      } else {
        console.log('No trading days found, skipping batch fetch');
      }

      return days;
    } catch (error) {
      console.error('Error fetching filtered trading days:', error);
      toast.error("Failed to load trading days");
      return [];
    }
  };

  // Functions to handle journal pagination
  const goToNextPage = () => {
    setJournalPagination(prev => ({
      ...prev,
      currentPage: Math.min(prev.currentPage + 1, prev.totalPages)
    }));
  };

  const goToPreviousPage = () => {
    setJournalPagination(prev => ({
      ...prev,
      currentPage: Math.max(prev.currentPage - 1, 1)
    }));
  };

  // Fetch journal entries and trades using server action
  const fetchEntries = async (options?: {
    searchTerm?: string;
    tags?: string[];
    startDate?: string;
    endDate?: string;
  }) => {
    try {
      // Use server action to fetch filtered data
      const filteredData = await fetchFilteredJournalData(
        userId,
        selectedAccountId,
        options?.searchTerm,
        options?.tags,
        options?.startDate,
        options?.endDate,
        activeTab
      );

      // No need to update allTrades anymore
      setTradingDays(filteredData.tradingDays.map((dateStr: string) => new Date(dateStr)));
      setFilteredDays(filteredData.tradingDays.map((dateStr: string) => new Date(dateStr)));
      setTradesWithJournalContent(filteredData.tradesWithJournalContent);

      // Update strategy map
      const newStrategyMap = new Map<string, string>();
      Object.entries(filteredData.strategyMap).forEach(([key, value]) => {
        newStrategyMap.set(key, value as string);
      });
      setStrategyMap(newStrategyMap);

      // Fetch journal entries for backward compatibility
      const params = new URLSearchParams();
      if (options?.searchTerm) params.append('searchTerm', options.searchTerm);
      if (options?.tags?.length) options.tags.forEach(tag => params.append('tags', tag));
      if (options?.startDate) params.append('startDate', options.startDate);
      if (options?.endDate) params.append('endDate', options.endDate);
      if (selectedAccountId) params.append('accountId', selectedAccountId);

      const response = await fetch(`/api/journal-entries?${params.toString()}`);
      if (!response.ok) throw new Error('Failed to fetch journal entries');
      const data = await response.json();
      setEntries(data);
    } catch (error) {
      console.error("Error fetching journal entries:", error);
      toast.error("Failed to load journal entries");
    }
  }

  // Function to load more trades
  const loadMoreTrades = async () => {
    if (paginationState.isLoading || !paginationState.hasMore) return;

    try {
      setPaginationState(prev => ({ ...prev, isLoading: true }));

      // Use server action to fetch more trades
      const nextPage = paginationState.currentPage + 1;
      const filteredData = await fetchFilteredJournalData(
        userId,
        selectedAccountId,
        searchTerm,
        selectedTags,
        dateRange?.from ? format(dateRange.from, "yyyy-MM-dd") : undefined,
        dateRange?.to ? format(dateRange.to, "yyyy-MM-dd") : undefined,
        activeTab,
        nextPage,
        paginationState.pageSize,
        true // loadMore flag
      );

      // Update pagination state
      setPaginationState(prev => ({
        ...prev,
        currentPage: nextPage,
        isLoading: false,
        hasMore: filteredData.pagination.hasMore
      }));

      // Update other state as needed
      setTradesWithJournalContent(prev => {
        const newTradesWithContent = filteredData.tradesWithJournalContent || [];
        return [...prev, ...newTradesWithContent];
      });

    } catch (error) {
      console.error("Error loading more trades:", error);
      setPaginationState(prev => ({ ...prev, isLoading: false }));
      toast.error("Failed to load more trades");
    }
  }

  // Fetch tags from API
  const fetchTags = async () => {
    try {
      const params = new URLSearchParams()
      if (selectedAccountId) params.append('accountId', selectedAccountId)

      const response = await fetch(`/api/journal-tags?${params.toString()}`)
      if (!response.ok) throw new Error('Failed to fetch tags')
      const data = await response.json()

      setAvailableTags(data)
    } catch (error) {
      console.error("Error fetching tags:", error)
    }
  }

  // Set the current trade for editing
  const handleEditTrade = (trade: any) => {
    setCurrentTrade(trade)
    setIsTradeEditDialogOpen(true)
  }

  // Create a new journal entry
  const handleCreateEntry = async (entry: Omit<JournalEntryInsert, "user_id">) => {
    try {
      const response = await fetch('/api/journal-entries', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(entry)
      })

      if (!response.ok) throw new Error('Failed to create journal entry')

      toast.success("Journal entry created successfully")
      setIsNewEntryDialogOpen(false)
      fetchEntries()
      fetchTags()
    } catch (error) {
      console.error("Error creating journal entry:", error)
      toast.error("Failed to create journal entry")
    }
  }

  // Update an existing journal entry
  const handleUpdateEntry = async (entry: Omit<JournalEntryInsert, "user_id">) => {
    if (!currentEntry) return

    try {
      const response = await fetch(`/api/journal-entries/${currentEntry.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(entry)
      })

      if (!response.ok) throw new Error('Failed to update journal entry')

      toast.success("Journal entry updated successfully")
      setIsEditEntryDialogOpen(false)
      fetchEntries()
      fetchTags()
    } catch (error) {
      console.error("Error updating journal entry:", error)
      toast.error("Failed to update journal entry")
    }
  }

  // Delete a journal entry
  const handleDeleteEntry = async () => {
    if (!currentEntry) return

    try {
      const response = await fetch(`/api/journal-entries/${currentEntry.id}`, {
        method: 'DELETE'
      })

      if (!response.ok) throw new Error('Failed to delete journal entry')

      toast.success("Journal entry deleted successfully")
      setIsDeleteConfirmOpen(false)
      fetchEntries()
    } catch (error) {
      console.error("Error deleting journal entry:", error)
      toast.error("Failed to delete journal entry")
    }
  }

  // Refresh data after a trade is updated using server action
  const handleTradeUpdate = async () => {
    try {
      // Use server action to fetch updated data
      const filteredData = await fetchFilteredJournalData(
        userId,
        selectedAccountId,
        searchTerm,
        selectedTags,
        dateRange?.from ? format(dateRange.from, "yyyy-MM-dd") : undefined,
        dateRange?.to ? format(dateRange.to, "yyyy-MM-dd") : undefined,
        activeTab
      );

      // No need to update allTrades anymore
      setTradingDays(filteredData.tradingDays.map((dateStr: string) => new Date(dateStr)));
      setFilteredDays(filteredData.tradingDays.map((dateStr: string) => new Date(dateStr)));
      setTradesWithJournalContent(filteredData.tradesWithJournalContent);

      // Update strategy map
      const newStrategyMap = new Map<string, string>();
      Object.entries(filteredData.strategyMap).forEach(([key, value]) => {
        newStrategyMap.set(key, value as string);
      });
      setStrategyMap(newStrategyMap);
    } catch (error) {
      console.error("Error refreshing data after trade update:", error);
    }
  }

  // Apply filters to the journal entries using server action
  const applyFilters = async () => {
    // If there's a search term that might be a tag, check if it matches any available tags
    if (searchTerm && searchTerm.trim() !== '' && selectedTags.length === 0) {
      const term = searchTerm.trim().toLowerCase();

      // Check if the search term exactly matches any available tag
      const matchingTag = availableTags.find(tag => tag.toLowerCase() === term);

      if (matchingTag) {
        // If it matches a tag, add it to selected tags
        console.log(`Search term "${term}" matches tag "${matchingTag}", adding to selected tags`);
        setSelectedTags([matchingTag]);
      }
    }

    // Build URL parameters for the new URL
    const params = new URLSearchParams();
    if (searchTerm) params.set('searchTerm', searchTerm);
    selectedTags.forEach(tag => params.append('tags', tag));
    if (dateRange?.from) params.set('startDate', format(dateRange.from, "yyyy-MM-dd"));
    if (dateRange?.to) params.set('endDate', format(dateRange.to, "yyyy-MM-dd"));
    params.set('tab', activeTab);

    // Update the URL with the new search parameters
    router.push(`/journal?${params.toString()}`);

    // Use server action to fetch filtered data
    try {
      const filteredData = await fetchFilteredJournalData(
        userId,
        selectedAccountId,
        searchTerm,
        selectedTags,
        dateRange?.from ? format(dateRange.from, "yyyy-MM-dd") : undefined,
        dateRange?.to ? format(dateRange.to, "yyyy-MM-dd") : undefined,
        activeTab
      );

      // No need to update allTrades anymore
      setTradingDays(filteredData.tradingDays.map((dateStr: string) => new Date(dateStr)));
      setFilteredDays(filteredData.tradingDays.map((dateStr: string) => new Date(dateStr)));
      setTradesWithJournalContent(filteredData.tradesWithJournalContent);

      // Update strategy map
      const newStrategyMap = new Map<string, string>();
      Object.entries(filteredData.strategyMap).forEach(([key, value]) => {
        newStrategyMap.set(key, value as string);
      });
      setStrategyMap(newStrategyMap);
    } catch (error) {
      console.error("Error applying filters:", error);
      toast.error("Failed to apply filters");
    }

    setIsFilterOpen(false);
  }

  // Reset all filters using server action
  const resetFilters = async () => {
    setSearchTerm("")
    setSelectedTags([])
    setDateRange(undefined)

    // Update the URL to remove all search parameters
    router.push('/journal');

    // Use server action to fetch unfiltered data
    try {
      const filteredData = await fetchFilteredJournalData(
        userId,
        selectedAccountId
      );

      // No need to update allTrades anymore
      setTradingDays(filteredData.tradingDays.map((dateStr: string) => new Date(dateStr)));
      setFilteredDays(filteredData.tradingDays.map((dateStr: string) => new Date(dateStr)));
      setTradesWithJournalContent(filteredData.tradesWithJournalContent);

      // Update strategy map
      const newStrategyMap = new Map<string, string>();
      Object.entries(filteredData.strategyMap).forEach(([key, value]) => {
        newStrategyMap.set(key, value as string);
      });
      setStrategyMap(newStrategyMap);
    } catch (error) {
      console.error("Error resetting filters:", error);
      toast.error("Failed to reset filters");
    }

    setIsFilterOpen(false)
  }

  // Toggle a tag selection
  const toggleTag = (tag: string) => {
    if (selectedTags.includes(tag)) {
      setSelectedTags(selectedTags.filter(t => t !== tag))
    } else {
      setSelectedTags([...selectedTags, tag])
    }
  }

  // Update filtered days when filters change
  useEffect(() => {
    const updateFilteredDays = async () => {
      try {
        // Get the days that match our filter criteria
        // This will also fetch trades for daily journal entries with matching tags
        const days = await getFilteredTradingDays();

        // Set the filtered days
        setFilteredDays(days);

        // Update pagination total pages
        setJournalPagination(prev => ({
          ...prev,
          currentPage: 1, // Reset to first page on filter change
          totalPages: Math.ceil(days.length / prev.itemsPerPage)
        }));
      } catch (error) {
        console.error("Error updating filtered days:", error);
      }
    };

    updateFilteredDays();
  }, [selectedAccountId, selectedTags, dateRange, searchTerm, activeTab]);

  return (
    <div className="space-y-6">
      {selectedAccountId === null ? (
        <Card className="p-8 text-center">
          <div className="flex flex-col items-center justify-center space-y-4">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="h-12 w-12 text-muted-foreground mb-2"
            >
              <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
              <circle cx="9" cy="7" r="4" />
              <path d="M22 21v-2a4 4 0 0 0-3-3.87" />
              <path d="M16 3.13a4 4 0 0 1 0 7.75" />
            </svg>
            <h2 className="text-xl font-semibold">No Account Selected</h2>
            <p className="text-muted-foreground max-w-md">
              Please select an account from the dashboard to view your journal entries. No data will be displayed until an account is selected.
            </p>
            <Button
              variant="outline"
              onClick={() => router.push('/dashboard')}
              className="mt-4"
            >
              Go to Dashboard
            </Button>
          </div>
        </Card>
      ) : (
        <>
          {/* Header with search and filters */}
          <div className="flex flex-col space-y-4 border-b pb-4">
            <div className="flex items-center justify-between">
              <h1 className="text-2xl font-semibold">Trading Journal</h1>
              <Button onClick={() => setIsNewEntryDialogOpen(true)}>
                <Plus className="mr-2 h-4 w-4" />
                New Entry
              </Button>
            </div>

            <div className="flex items-center space-x-4">
              <div className="relative flex-1">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search journal entries..."
                  value={searchTerm}
                  onChange={(e) => {
                    const newSearchTerm = e.target.value;
                    setSearchTerm(newSearchTerm);

                    // Apply search after a short delay to avoid too many requests
                    const delayDebounceFn = setTimeout(async () => {
                      // Check if the search term exactly matches any available tag
                      const term = newSearchTerm.trim().toLowerCase();
                      let updatedTags = [...selectedTags];

                      if (term && selectedTags.length === 0) {
                        const matchingTag = availableTags.find(tag => tag.toLowerCase() === term);
                        if (matchingTag) {
                          // If it matches a tag, add it to selected tags
                          console.log(`Search term "${term}" matches tag "${matchingTag}", adding to selected tags`);
                          updatedTags = [matchingTag];
                          setSelectedTags(updatedTags);
                        }
                      }

                      // Build URL parameters
                      const params = new URLSearchParams();
                      if (newSearchTerm) params.set('searchTerm', newSearchTerm);
                      updatedTags.forEach(tag => params.append('tags', tag));
                      if (dateRange?.from) params.set('startDate', format(dateRange.from, "yyyy-MM-dd"));
                      if (dateRange?.to) params.set('endDate', format(dateRange.to, "yyyy-MM-dd"));
                      params.set('tab', activeTab);

                      // Update URL without full navigation
                      window.history.replaceState({}, '', `/journal?${params.toString()}`);

                      // Use server action to fetch filtered data
                      try {
                        const filteredData = await fetchFilteredJournalData(
                          userId,
                          selectedAccountId,
                          newSearchTerm,
                          updatedTags,
                          dateRange?.from ? format(dateRange.from, "yyyy-MM-dd") : undefined,
                          dateRange?.to ? format(dateRange.to, "yyyy-MM-dd") : undefined,
                          activeTab
                        );

                        // No need to update allTrades anymore
                        setTradingDays(filteredData.tradingDays.map((dateStr: string) => new Date(dateStr)));
                        setFilteredDays(filteredData.tradingDays.map((dateStr: string) => new Date(dateStr)));
                        setTradesWithJournalContent(filteredData.tradesWithJournalContent);
                      } catch (error) {
                        console.error("Error applying search filter:", error);
                      }
                    }, 500); // Increased delay to give user time to type

                    return () => clearTimeout(delayDebounceFn);
                  }}
                  className="pl-8"
                />
              </div>

              <Popover open={isFilterOpen} onOpenChange={setIsFilterOpen}>
                <PopoverTrigger asChild>
                  <Button variant="outline">
                    <Filter className="mr-2 h-4 w-4" />
                    Filters
                    {(selectedTags.length > 0 || dateRange?.from || dateRange?.to) && (
                      <Badge variant="secondary" className="ml-2 px-1 py-0">
                        {selectedTags.length + (dateRange?.from ? 1 : 0) + (dateRange?.to ? 1 : 0)}
                      </Badge>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-80">
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <h4 className="font-medium">Date Range</h4>
                      <TableDateRangePicker
                        dateRange={dateRange}
                        onDateRangeChange={setDateRange}
                        align="center"
                      />
                    </div>

                    <div className="space-y-2">
                      <h4 className="font-medium">Tags</h4>
                      <div className="flex flex-wrap gap-2 max-h-[150px] overflow-y-auto p-2 border rounded-md">
                        {availableTags.length > 0 ? (
                          availableTags.map((tag) => (
                            <Badge
                              key={tag}
                              variant={selectedTags.includes(tag) ? "default" : "outline"}
                              className="cursor-pointer"
                              onClick={() => toggleTag(tag)}
                            >
                              {tag}
                            </Badge>
                          ))
                        ) : (
                          <div className="text-sm text-muted-foreground">No tags available</div>
                        )}
                      </div>
                    </div>

                    <div className="flex justify-between">
                      <Button variant="outline" size="sm" onClick={resetFilters}>
                        Reset
                      </Button>
                      <Button size="sm" onClick={applyFilters}>
                        Apply Filters
                      </Button>
                    </div>
                  </div>
                </PopoverContent>
              </Popover>

              <Button
                variant="outline"
                onClick={() => {
                  if (searchTerm || selectedTags.length > 0 || dateRange?.from || dateRange?.to) {
                    resetFilters();
                  }
                }}
                disabled={!searchTerm && selectedTags.length === 0 && !dateRange?.from && !dateRange?.to}
              >
                <X className="mr-2 h-4 w-4" />
                Clear
              </Button>
            </div>
          </div>

          {/* Tabs for filtering entries */}
          <Tabs
            defaultValue="with-trades"
            value={activeTab}
            onValueChange={async (value) => {
              setActiveTab(value);

              // Build URL parameters
              const params = new URLSearchParams();
              if (searchTerm) params.set('searchTerm', searchTerm);
              selectedTags.forEach(tag => params.append('tags', tag));
              if (dateRange?.from) params.set('startDate', format(dateRange.from, "yyyy-MM-dd"));
              if (dateRange?.to) params.set('endDate', format(dateRange.to, "yyyy-MM-dd"));
              params.set('tab', value);

              // Update URL
              router.push(`/journal?${params.toString()}`);

              // Use server action to fetch filtered data
              try {
                const filteredData = await fetchFilteredJournalData(
                  userId,
                  selectedAccountId,
                  searchTerm,
                  selectedTags,
                  dateRange?.from ? format(dateRange.from, "yyyy-MM-dd") : undefined,
                  dateRange?.to ? format(dateRange.to, "yyyy-MM-dd") : undefined,
                  value
                );

                // No need to update allTrades anymore
                setTradingDays(filteredData.tradingDays.map((dateStr: string) => new Date(dateStr)));
                setFilteredDays(filteredData.tradingDays.map((dateStr: string) => new Date(dateStr)));
                setTradesWithJournalContent(filteredData.tradesWithJournalContent);
              } catch (error) {
                console.error("Error changing tab:", error);
              }
            }}>
            <TabsList className="grid w-full grid-cols-2 h-10 items-stretch">
              <TabsTrigger
                value="with-trades"
                className="flex items-center justify-center gap-2 data-[state=active]:bg-background data-[state=active]:shadow-none data-[state=active]:border-b-2 data-[state=active]:border-primary rounded-none"
              >
                <BarChart3 className="h-4 w-4" />
                <span>Trade-Specific</span>
              </TabsTrigger>
              <TabsTrigger
                value="without-trades"
                className="flex items-center justify-center gap-2 data-[state=active]:bg-background data-[state=active]:shadow-none data-[state=active]:border-b-2 data-[state=active]:border-primary rounded-none"
              >
                <Calendar className="h-4 w-4" />
                <span>Daily Journal</span>
              </TabsTrigger>
            </TabsList>
          </Tabs>

          {/* Active filters display */}
          {(selectedTags.length > 0 || dateRange?.from || dateRange?.to) && (
            <div className="flex flex-wrap items-center gap-2 text-sm">
              <span className="text-muted-foreground">Active filters:</span>

              {dateRange?.from && dateRange?.to && (
                <Badge variant="secondary" className="flex items-center gap-1">
                  <Calendar className="h-3 w-3" />
                  {format(dateRange.from, "MMM d, yyyy")} - {format(dateRange.to, "MMM d, yyyy")}
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setDateRange(undefined)}
                    className="h-4 w-4 p-0 ml-1 text-muted-foreground hover:text-foreground"
                  >
                    <X className="h-3 w-3" />
                    <span className="sr-only">Remove date filter</span>
                  </Button>
                </Badge>
              )}

              {dateRange?.from && !dateRange?.to && (
                <Badge variant="secondary" className="flex items-center gap-1">
                  <Calendar className="h-3 w-3" />
                  From: {format(dateRange.from, "MMM d, yyyy")}
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setDateRange(undefined)}
                    className="h-4 w-4 p-0 ml-1 text-muted-foreground hover:text-foreground"
                  >
                    <X className="h-3 w-3" />
                    <span className="sr-only">Remove date filter</span>
                  </Button>
                </Badge>
              )}

              {selectedTags.map(tag => (
                <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                  <Tag className="h-3 w-3" />
                  {tag}
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setSelectedTags(selectedTags.filter(t => t !== tag))}
                    className="h-4 w-4 p-0 ml-1 text-muted-foreground hover:text-foreground"
                  >
                    <X className="h-3 w-3" />
                    <span className="sr-only">Remove tag filter</span>
                  </Button>
                </Badge>
              ))}
            </div>
          )}

          {/* Journals Section - Show different content based on active tab */}
          <div className="space-y-4 mb-8">
            <div className="flex flex-col space-y-2">
              <h2 className="text-lg font-medium">
                {activeTab === "with-trades" ? "Trade-Specific Journals" : "Daily Trading Journals"}
              </h2>
              <p className="text-sm text-muted-foreground">
                {activeTab === "with-trades"
                  ? "Journals created from individual trade details pages with notes and screenshots"
                  : "Daily journals summarizing all trading activity for each day"}
              </p>

              {/* Display active tag filters */}
              {selectedTags.length > 0 && (
                <div className="flex flex-wrap gap-2 mt-2">
                  <span className="text-xs text-muted-foreground flex items-center">
                    <Filter className="h-3 w-3 mr-1" />
                    Filtered by tags:
                  </span>
                  {selectedTags.map(tag => (
                    <Badge
                      key={tag}
                      variant="outline"
                      className="text-xs bg-purple-50 text-purple-700 border-purple-200 flex items-center gap-1"
                    >
                      <Check className="h-3 w-3" />
                      {tag}
                    </Badge>
                  ))}
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-5 text-xs px-2 text-muted-foreground hover:text-foreground"
                    onClick={resetFilters}
                  >
                    <X className="h-3 w-3 mr-1" />
                    Clear
                  </Button>
                </div>
              )}
            </div>

            {activeTab === "with-trades" ? (
              // Trade Journals Tab - Only show individual trades with notes or screenshots
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-2">Trade-Specific Journals</h3>
                {tradesWithJournalContent.length > 0 ? (
                  <div className="space-y-4">
                    {/* Sort trades by date, newest first */}
                    {tradesWithJournalContent
                      .sort((a, b) => new Date(b.time_close).getTime() - new Date(a.time_close).getTime())
                      .map((trade) => (
                        <TradeJournalCard
                          key={trade.id}
                          trade={trade}
                          strategyName={trade.strategy_id ? strategyMap.get(trade.strategy_id) : undefined}
                          onEdit={handleEditTrade}
                        />
                      ))
                    }

                    {/* Load more button */}
                    {paginationState.hasMore && (
                      <div className="flex justify-center mt-6">
                        <Button
                          variant="outline"
                          onClick={loadMoreTrades}
                          disabled={paginationState.isLoading}
                          className="w-full max-w-xs"
                        >
                          {paginationState.isLoading ? (
                            <>
                              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                              Loading...
                            </>
                          ) : (
                            <>Load More Trades</>
                          )}
                        </Button>
                      </div>
                    )}
                  </div>
                ) : (
                  <Card className="p-6 text-center">
                    <div className="flex flex-col items-center justify-center space-y-2">
                      <BarChart3 className="h-8 w-8 text-muted-foreground mb-2" />
                      <h3 className="text-lg font-medium">No Trade Journals Found</h3>
                      <p className="text-sm text-muted-foreground max-w-md">
                        You haven't created any trade-specific journal entries yet. Add notes or screenshots to your trades to see them here.
                      </p>
                    </div>
                  </Card>
                )}
              </div>
            ) : activeTab === "without-trades" ? (
              // Daily Journal Tab - Show automated daily journals
              <div>
                <div className="flex items-center justify-between mb-2">
                  <h3 className="text-sm font-medium text-muted-foreground">Daily Trading Journals</h3>
                  {isBatchLoading && (
                    <div className="flex items-center text-xs text-muted-foreground">
                      <div className="mr-2 h-3 w-3 animate-spin rounded-full border-2 border-current border-t-transparent"></div>
                      Loading trade data...
                    </div>
                  )}
                </div>
                {filteredDays.length > 0 ? (
                  <div className="space-y-6">
                    {/* Show only 5 cards per page */}
                    {filteredDays
                      .slice(
                        (journalPagination.currentPage - 1) * journalPagination.itemsPerPage,
                        journalPagination.currentPage * journalPagination.itemsPerPage
                      )
                      .map((day) => (
                        <AutomatedDailyJournal
                          key={format(day, "yyyy-MM-dd")}
                          date={day}
                          trades={batchTradeData[format(day, "yyyy-MM-dd")] || []} // Use batch trade data if available
                          tradeDate={format(day, "yyyy-MM-dd")} // Pass the date string instead
                          userId={userId}
                          accountId={selectedAccountId}
                          onUpdate={() => fetchEntries()}
                          initialJournalEntry={batchJournalData[format(day, "yyyy-MM-dd")] || null} // Pass batch journal data if available
                        />
                      ))}

                    {/* Pagination controls */}
                    {journalPagination.totalPages > 1 && (
                      <div className="flex items-center justify-between mt-6 pt-4 border-t">
                        <div className="text-sm text-muted-foreground">
                          Page {journalPagination.currentPage} of {journalPagination.totalPages}
                        </div>
                        <div className="flex space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={goToPreviousPage}
                            disabled={journalPagination.currentPage === 1}
                          >
                            <ChevronLeft className="h-4 w-4 mr-1" />
                            Previous
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={goToNextPage}
                            disabled={journalPagination.currentPage === journalPagination.totalPages}
                          >
                            Next
                            <ChevronRight className="h-4 w-4 ml-1" />
                          </Button>
                        </div>
                      </div>
                    )}
                  </div>
                ) : (
                  <Card className="p-6 text-center">
                    <div className="flex flex-col items-center justify-center space-y-2">
                      <Calendar className="h-8 w-8 text-muted-foreground mb-2" />
                      <h3 className="text-lg font-medium">No Trading Days Found</h3>
                      <p className="text-sm text-muted-foreground max-w-md">
                        No trading days match your current filters. Try adjusting your filters or adding trades to see daily journals.
                      </p>
                    </div>
                  </Card>
                )}
              </div>
            )}
          </div>

          {/* New Entry Dialog */}
          <Dialog open={isNewEntryDialogOpen} onOpenChange={setIsNewEntryDialogOpen}>
            <DialogContent className="sm:max-w-[600px]">
              <DialogHeader>
                <DialogTitle>Create New Journal Entry</DialogTitle>
              </DialogHeader>
              <JournalEntryForm
                onSubmit={handleCreateEntry}
                userId={userId}
              />
            </DialogContent>
          </Dialog>

          {/* Edit Entry Dialog */}
          <Dialog open={isEditEntryDialogOpen} onOpenChange={setIsEditEntryDialogOpen}>
            <DialogContent className="sm:max-w-[600px]">
              <DialogHeader>
                <DialogTitle>Edit Journal Entry</DialogTitle>
              </DialogHeader>
              {currentEntry && (
                <JournalEntryForm
                  entry={currentEntry}
                  onSubmit={handleUpdateEntry}
                  userId={userId}
                />
              )}
            </DialogContent>
          </Dialog>

          {/* Delete Confirmation Dialog */}
          <Dialog open={isDeleteConfirmOpen} onOpenChange={setIsDeleteConfirmOpen}>
            <DialogContent className="sm:max-w-[425px]">
              <DialogHeader>
                <DialogTitle>Delete Journal Entry</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <p>Are you sure you want to delete this journal entry? This action cannot be undone.</p>
                <div className="flex justify-end space-x-2">
                  <Button variant="outline" onClick={() => setIsDeleteConfirmOpen(false)}>
                    Cancel
                  </Button>
                  <Button variant="destructive" onClick={handleDeleteEntry}>
                    Delete
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>

          {/* Trade Edit Dialog */}
          <TradeDetailsEditDialog
            isOpen={isTradeEditDialogOpen}
            onOpenChange={setIsTradeEditDialogOpen}
            trade={currentTrade}
            userId={userId}
            onUpdate={handleTradeUpdate}
          />
        </>
      )}
    </div>
  );