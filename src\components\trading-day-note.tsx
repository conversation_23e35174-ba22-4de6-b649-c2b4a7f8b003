"use client"

import { useState } from "react"
import { format } from "date-fns"
import { Pencil, Save, X } from "lucide-react"

import { Button } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { cn } from "@/lib/utils"

interface TradingDayNoteProps {
  date: Date
  initialNote?: string
  onSave: (date: Date, note: string) => void
  className?: string
}

export function TradingDayNote({
  date,
  initialNote = "",
  onSave,
  className
}: TradingDayNoteProps) {
  const [isEditing, setIsEditing] = useState(false)
  const [note, setNote] = useState(initialNote)

  const handleSave = () => {
    onSave(date, note)
    setIsEditing(false)
  }

  const handleCancel = () => {
    setNote(initialNote)
    setIsEditing(false)
  }

  return (
    <div className={cn("space-y-2", className)}>
      <div className="flex items-center justify-between">
        <h4 className="text-sm font-medium">Notes for {format(date, "MMMM d, yyyy")}</h4>
        {!isEditing ? (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsEditing(true)}
            className="h-8 w-8 p-0"
          >
            <Pencil className="h-4 w-4" />
            <span className="sr-only">Edit note</span>
          </Button>
        ) : (
          <div className="flex space-x-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleSave}
              className="h-8 w-8 p-0 text-emerald-500"
            >
              <Save className="h-4 w-4" />
              <span className="sr-only">Save note</span>
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleCancel}
              className="h-8 w-8 p-0 text-rose-500"
            >
              <X className="h-4 w-4" />
              <span className="sr-only">Cancel</span>
            </Button>
          </div>
        )}
      </div>
      
      {isEditing ? (
        <Textarea
          value={note}
          onChange={(e) => setNote(e.target.value)}
          placeholder="Add your trading notes here..."
          className="min-h-[100px]"
        />
      ) : (
        <div className="rounded-md bg-muted/50 p-3 text-sm">
          {note ? note : <span className="text-muted-foreground italic">No notes for this day</span>}
        </div>
      )}
    </div>
  )
}
