/* Quill Editor Custom Styles */

.quill-editor-wrapper,
.advanced-quill-editor-wrapper,
.quill-editor-direct {
  display: flex;
  flex-direction: column;
  height: 100%;
  border-radius: 0.5rem;
  overflow: hidden;
}

/* Make the editor take up the full height */
.quill-editor-wrapper .quill,
.advanced-quill-editor-wrapper .quill,
.quill-editor-direct .quill {
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* Style the toolbar */
.quill-editor-wrapper .ql-toolbar,
.advanced-quill-editor-wrapper .ql-toolbar,
.quill-editor-direct .ql-toolbar {
  border-top-left-radius: 0.5rem;
  border-top-right-radius: 0.5rem;
  background-color: var(--background);
  border-color: var(--border);
  position: sticky;
  top: 0;
  z-index: 10;
}

/* Special styling for advanced editor toolbar */
.advanced-quill-editor-wrapper .ql-toolbar,
.quill-editor-direct .ql-toolbar {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Style the editor container */
.quill-editor-wrapper .ql-container,
.advanced-quill-editor-wrapper .ql-container,
.quill-editor-direct .ql-container {
  flex: 1;
  overflow: auto;
  font-size: 1rem;
  border-bottom-left-radius: 0.5rem;
  border-bottom-right-radius: 0.5rem;
  background-color: var(--background);
  border-color: var(--border);
}

/* Style the editor content area */
.quill-editor-wrapper .ql-editor,
.advanced-quill-editor-wrapper .ql-editor,
.quill-editor-direct .ql-editor {
  min-height: 200px;
  height: 100%;
  overflow-y: auto;
  color: var(--foreground);
}

/* Style the placeholder */
.quill-editor-wrapper .ql-editor.ql-blank::before,
.advanced-quill-editor-wrapper .ql-editor.ql-blank::before,
.quill-editor-direct .ql-editor.ql-blank::before {
  color: var(--muted-foreground);
  font-style: italic;
}

/* Style toolbar buttons */
.quill-editor-wrapper .ql-toolbar button,
.advanced-quill-editor-wrapper .ql-toolbar button,
.quill-editor-direct .ql-toolbar button {
  color: var(--foreground);
}

.quill-editor-wrapper .ql-toolbar button:hover,
.advanced-quill-editor-wrapper .ql-toolbar button:hover,
.quill-editor-direct .ql-toolbar button:hover {
  color: var(--primary);
}

.quill-editor-wrapper .ql-toolbar button.ql-active,
.advanced-quill-editor-wrapper .ql-toolbar button.ql-active,
.quill-editor-direct .ql-toolbar button.ql-active {
  color: var(--primary);
}

/* Style dropdown menus */
.quill-editor-wrapper .ql-toolbar .ql-picker,
.advanced-quill-editor-wrapper .ql-toolbar .ql-picker,
.quill-editor-direct .ql-toolbar .ql-picker {
  color: var(--foreground);
}

.quill-editor-wrapper .ql-toolbar .ql-picker-options,
.advanced-quill-editor-wrapper .ql-toolbar .ql-picker-options,
.quill-editor-direct .ql-toolbar .ql-picker-options {
  background-color: var(--background);
  border-color: var(--border);
}

.quill-editor-wrapper .ql-toolbar .ql-picker-item:hover,
.advanced-quill-editor-wrapper .ql-toolbar .ql-picker-item:hover,
.quill-editor-direct .ql-toolbar .ql-picker-item:hover {
  color: var(--primary);
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
  .quill-editor-wrapper .ql-snow .ql-stroke,
  .advanced-quill-editor-wrapper .ql-snow .ql-stroke,
  .quill-editor-direct .ql-snow .ql-stroke {
    stroke: var(--foreground);
  }

  .quill-editor-wrapper .ql-snow .ql-fill,
  .advanced-quill-editor-wrapper .ql-snow .ql-fill,
  .quill-editor-direct .ql-snow .ql-fill {
    fill: var(--foreground);
  }

  .quill-editor-wrapper .ql-snow.ql-toolbar button:hover .ql-stroke,
  .quill-editor-wrapper .ql-snow.ql-toolbar button.ql-active .ql-stroke,
  .advanced-quill-editor-wrapper .ql-snow.ql-toolbar button:hover .ql-stroke,
  .advanced-quill-editor-wrapper .ql-snow.ql-toolbar button.ql-active .ql-stroke,
  .quill-editor-direct .ql-snow.ql-toolbar button:hover .ql-stroke,
  .quill-editor-direct .ql-snow.ql-toolbar button.ql-active .ql-stroke {
    stroke: var(--primary);
  }

  .quill-editor-wrapper .ql-snow.ql-toolbar button:hover .ql-fill,
  .quill-editor-wrapper .ql-snow.ql-toolbar button.ql-active .ql-fill,
  .advanced-quill-editor-wrapper .ql-snow.ql-toolbar button:hover .ql-fill,
  .advanced-quill-editor-wrapper .ql-snow.ql-toolbar button.ql-active .ql-fill,
  .quill-editor-direct .ql-snow.ql-toolbar button:hover .ql-fill,
  .quill-editor-direct .ql-snow.ql-toolbar button.ql-active .ql-fill {
    fill: var(--primary);
  }
}

/* Ensure proper spacing for lists */
.quill-editor-wrapper .ql-editor ul,
.quill-editor-wrapper .ql-editor ol,
.advanced-quill-editor-wrapper .ql-editor ul,
.advanced-quill-editor-wrapper .ql-editor ol,
.quill-editor-direct .ql-editor ul,
.quill-editor-direct .ql-editor ol {
  padding-left: 1.5em;
}

/* Style blockquotes */
.quill-editor-wrapper .ql-editor blockquote,
.advanced-quill-editor-wrapper .ql-editor blockquote,
.quill-editor-direct .ql-editor blockquote {
  border-left: 4px solid var(--border);
  padding-left: 1em;
  margin-left: 0;
  color: var(--muted-foreground);
}

/* Style code blocks */
.quill-editor-wrapper .ql-editor pre.ql-syntax,
.advanced-quill-editor-wrapper .ql-editor pre.ql-syntax,
.quill-editor-direct .ql-editor pre.ql-syntax {
  background-color: var(--muted);
  color: var(--foreground);
  border-radius: 0.25rem;
  padding: 0.5em;
  overflow: auto;
}
