"use client"

import { useMemo } from "react"
import { format } from "date-fns"
import {
  LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, ReferenceLine, Legend
} from "recharts"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

interface Trade {
  id: string
  symbol: string
  type: string
  volume: number
  price_open: number
  price_close: number
  time_open: string
  time_close: string
  profit: number
  commission: number
  swap: number
}

interface DailyCumulativePnLBySymbolProps {
  trades: Trade[]
  selectedSymbols: string[]
}

export function DailyCumulativePnLBySymbol({ trades, selectedSymbols }: DailyCumulativePnLBySymbolProps) {
  // Define colors for the selected symbols
  const SYMBOL_COLORS = ['#3B82F6', '#10B981', '#F97316', '#EC4899']

  // Prepare data for the cumulative P&L chart
  const chartData = useMemo(() => {
    if (!trades.length || !selectedSymbols.length) return []

    // Filter trades to only include selected symbols
    const filteredTrades = trades.filter(trade => selectedSymbols.includes(trade.symbol))

    // Group trades by date and symbol
    const tradesByDate = new Map<string, Map<string, number>>()

    // Initialize with all dates and symbols
    const allDates = new Set<string>()

    filteredTrades.forEach(trade => {
      const date = format(new Date(trade.time_close), "yyyy-MM-dd")
      allDates.add(date)
    })

    // Sort dates chronologically
    const sortedDates = Array.from(allDates).sort()

    // Initialize all dates with 0 for each symbol
    sortedDates.forEach(date => {
      const symbolMap = new Map<string, number>()
      selectedSymbols.forEach(symbol => {
        symbolMap.set(symbol, 0)
      })
      tradesByDate.set(date, symbolMap)
    })

    // Add profit for each trade
    filteredTrades.forEach(trade => {
      const date = format(new Date(trade.time_close), "yyyy-MM-dd")
      const symbolMap = tradesByDate.get(date)!
      const currentProfit = symbolMap.get(trade.symbol) || 0
      symbolMap.set(trade.symbol, currentProfit + trade.profit)
    })

    // Calculate cumulative P&L for each symbol
    const cumulativePnL = new Map<string, number>()
    selectedSymbols.forEach(symbol => {
      cumulativePnL.set(symbol, 0)
    })

    // Create chart data with cumulative P&L
    return sortedDates.map(date => {
      const symbolMap = tradesByDate.get(date)!
      const dataPoint: any = { date }

      selectedSymbols.forEach(symbol => {
        const dailyProfit = symbolMap.get(symbol) || 0
        const currentCumulative = cumulativePnL.get(symbol) || 0
        const newCumulative = currentCumulative + dailyProfit
        cumulativePnL.set(symbol, newCumulative)
        dataPoint[symbol] = newCumulative
      })

      return dataPoint
    })
  }, [trades, selectedSymbols])

  return (
    <Card className="bg-card/50 hover:bg-card/80 transition-colors">
      <CardHeader className="p-4 pb-2">
        <CardTitle className="text-base font-medium">Daily Cumulative P&L by Symbol</CardTitle>
      </CardHeader>
      <CardContent className="p-4 pt-0">
        {selectedSymbols.length === 0 ? (
          <div className="h-[450px] flex items-center justify-center text-muted-foreground">
            Select symbols to compare
          </div>
        ) : (
          <div className="h-[450px]">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart
                data={chartData}
                margin={{ top: 40, right: 30, left: 40, bottom: 40 }}
              >
                <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" opacity={0.3} />
                <XAxis
                  dataKey="date"
                  tick={{
                    fill: 'hsl(var(--muted-foreground))',
                    fontSize: 11,
                    opacity: 0.7
                  }}
                  tickFormatter={(value) => format(new Date(value), "MMM d")}
                  height={60}
                  stroke="hsl(var(--border))"
                  strokeOpacity={0.5}
                />
                <ReferenceLine y={0} stroke="hsl(var(--border))" strokeOpacity={0.5} />
                <YAxis
                  tickFormatter={(value) => `$${value.toFixed(0)}`}
                  tick={{ fill: 'hsl(var(--muted-foreground))', fontSize: 11, opacity: 0.7 }}
                  stroke="hsl(var(--border))"
                  strokeOpacity={0.5}
                />
                <Tooltip
                  content={({ active, payload, label }) => {
                    if (!active || !payload?.length) return null

                    return (
                      <div className="rounded-lg border bg-card/90 p-2 shadow-sm text-xs">
                        <div className="font-medium mb-1">{format(new Date(label), "MMM d, yyyy")}</div>
                        <div className="space-y-1">
                          {payload.map((entry, index) => {
                            // Safely convert entry.value to number
                            const value = typeof entry.value === 'number' ? entry.value : 0;
                            return (
                              <div key={`item-${index}`} className="flex justify-between items-center gap-2">
                                <div className="flex items-center">
                                  <div
                                    className="w-2 h-2 rounded-full mr-1"
                                    style={{ backgroundColor: entry.stroke }}
                                  />
                                  <span>{entry.name}:</span>
                                </div>
                                <span style={{ color: value >= 0 ? "#10b981" : "#ef4444" }}>
                                  ${value.toFixed(2)}
                                </span>
                              </div>
                            );
                          })}
                        </div>
                      </div>
                    )
                  }}
                />
                <Legend
                  layout="horizontal"
                  verticalAlign="bottom"
                  align="center"
                  wrapperStyle={{ paddingTop: "20px" }}
                />
                {selectedSymbols.map((symbol, index) => {
                  // Use the same index-based color mapping as in SymbolComparison
                  const colorIndex = selectedSymbols.indexOf(symbol);
                  return (
                    <Line
                      key={symbol}
                      type="monotone"
                      dataKey={symbol}
                      name={symbol}
                      stroke={SYMBOL_COLORS[colorIndex % SYMBOL_COLORS.length]}
                      strokeWidth={2}
                      dot={false}
                      activeDot={{ r: 6, strokeWidth: 0 }}
                    />
                  );
                })}
              </LineChart>
            </ResponsiveContainer>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
