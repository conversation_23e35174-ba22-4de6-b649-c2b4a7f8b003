"use client"

import { useEffect } from "react"
import { Card } from "@/components/ui/card"

interface ClientWrapperProps {
  isLoading?: boolean
  children?: React.ReactNode
}

export default function ClientWrapper({ isLoading = false, children }: ClientWrapperProps) {
  // Add an effect to ensure the loading indicator stops after component mount
  useEffect(() => {
    // Function to stop the loading indicator
    const stopLoadingIndicator = () => {
      // Method 1: Push state to history
      window.history.pushState(null, '', window.location.href);
      
      // Method 2: Dispatch navigation end event
      window.dispatchEvent(new Event('routeChangeComplete'));
      
      // Method 3: Force any NProgress instances to complete
      if (typeof window !== 'undefined') {
        // Try to access NProgress directly
        const anyWindow = window as any;
        if (anyWindow.NProgress) {
          anyWindow.NProgress.done();
        }
        
        // Try to find and manipulate the loading bar element directly
        const loadingBar = document.getElementById('nprogress');
        if (loadingBar) {
          loadingBar.style.display = 'none';
        }
      }
    };
    
    // Execute immediately
    stopLoadingIndicator();
    
    // Then try multiple times with increasing delays
    const timers = [
      setTimeout(stopLoadingIndicator, 100),
      setTimeout(stopLoadingIndicator, 300),
      setTimeout(stopLoadingIndicator, 500),
      setTimeout(stopLoadingIndicator, 1000)
    ];
    
    return () => {
      timers.forEach(timer => clearTimeout(timer));
    };
  }, []);

  if (isLoading) {
    return null // Let the main client handle loading states
  }

  return <>{children}</>
}
