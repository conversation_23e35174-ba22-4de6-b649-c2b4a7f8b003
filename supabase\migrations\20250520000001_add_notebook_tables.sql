-- Create notebook_entries table
CREATE TABLE IF NOT EXISTS public.notebook_entries (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  account_id UUID REFERENCES public.trading_accounts(id) ON DELETE SET NULL,
  title TEXT NOT NULL,
  content JSONB NOT NULL DEFAULT '{}'::jsonb,
  html_content TEXT,
  category TEXT,
  tags TEXT[] DEFAULT '{}',
  is_pinned BOOLEAN DEFAULT false,
  is_template BOOLEAN DEFAULT false,
  linked_trade_ids UUID[] DEFAULT '{}',
  linked_strategy_ids UUID[] DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Add RLS policies
ALTER TABLE public.notebook_entries ENABLE ROW LEVEL SECURITY;

-- Policy for selecting entries (users can only see their own entries)
CREATE POLICY "Users can view their own notebook entries"
  ON public.notebook_entries
  FOR SELECT
  USING (auth.uid() = user_id);

-- Policy for inserting entries
CREATE POLICY "Users can insert their own notebook entries"
  ON public.notebook_entries
  FOR INSERT
  WITH CHECK (auth.uid() = user_id);

-- Policy for updating entries
CREATE POLICY "Users can update their own notebook entries"
  ON public.notebook_entries
  FOR UPDATE
  USING (auth.uid() = user_id);

-- Policy for deleting entries
CREATE POLICY "Users can delete their own notebook entries"
  ON public.notebook_entries
  FOR DELETE
  USING (auth.uid() = user_id);

-- Create indexes
CREATE INDEX IF NOT EXISTS notebook_entries_user_id_idx ON public.notebook_entries(user_id);
CREATE INDEX IF NOT EXISTS notebook_entries_account_id_idx ON public.notebook_entries(account_id);
CREATE INDEX IF NOT EXISTS notebook_entries_tags_idx ON public.notebook_entries USING GIN(tags);
CREATE INDEX IF NOT EXISTS notebook_entries_created_at_idx ON public.notebook_entries(created_at);
CREATE INDEX IF NOT EXISTS notebook_entries_updated_at_idx ON public.notebook_entries(updated_at);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION public.update_notebook_entry_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to update updated_at timestamp
CREATE TRIGGER update_notebook_entry_updated_at
BEFORE UPDATE ON public.notebook_entries
FOR EACH ROW
EXECUTE FUNCTION public.update_notebook_entry_updated_at();

-- Create function to search notebook entries
CREATE OR REPLACE FUNCTION public.search_notebook_entries(
  p_user_id UUID,
  p_account_id UUID DEFAULT NULL,
  p_search_term TEXT DEFAULT NULL,
  p_tags TEXT[] DEFAULT NULL,
  p_category TEXT DEFAULT NULL,
  p_is_template BOOLEAN DEFAULT NULL
)
RETURNS SETOF notebook_entries
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  RETURN QUERY
  SELECT *
  FROM public.notebook_entries
  WHERE user_id = p_user_id
  AND (p_account_id IS NULL OR account_id = p_account_id)
  AND (p_search_term IS NULL OR 
       title ILIKE '%' || p_search_term || '%' OR
       html_content ILIKE '%' || p_search_term || '%')
  AND (p_tags IS NULL OR tags && p_tags)
  AND (p_category IS NULL OR category = p_category)
  AND (p_is_template IS NULL OR is_template = p_is_template)
  ORDER BY is_pinned DESC, updated_at DESC;
END;
$$;
