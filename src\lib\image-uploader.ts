import { getSup<PERSON><PERSON><PERSON><PERSON>er } from './supabase';
import { toast } from 'sonner';

const BUCKET_NAME = 'strategyimages';

/**
 * Uploads an image to Supabase storage
 * @param file The file to upload
 * @returns The URL of the uploaded image or null if upload failed
 */
export async function uploadImage(file: File): Promise<string | null> {
  const supabase = getSupabaseBrowser();

  try {
    // Generate a unique filename
    const timestamp = Date.now();
    const randomString = Math.random().toString(36).substring(2, 10);
    const fileExt = file.name.split('.').pop();
    const fileName = `image_${timestamp}_${randomString}.${fileExt}`;

    console.log('Uploading image with filename:', fileName);

    // Upload the file
    const { data, error } = await supabase.storage
      .from(BUCKET_NAME)
      .upload(fileName, file, {
        cacheControl: '3600',
        upsert: false
      });

    if (error) {
      console.error('Error uploading image:', error);
      toast.error('Failed to upload image');
      return null;
    }

    console.log('Image uploaded successfully, data:', data);

    // Get the public URL directly from Supabase
    const { data: publicUrlData } = supabase.storage
      .from(BUCKET_NAME)
      .getPublicUrl(fileName);

    if (!publicUrlData || !publicUrlData.publicUrl) {
      console.error('Failed to get public URL from Supabase');
      toast.error('Failed to get public URL for uploaded image');
      return null;
    }

    // Log the public URL for debugging
    console.log('Got public URL from Supabase:', publicUrlData.publicUrl);

    // Return the full public URL directly from Supabase
    // This will be used with the DirectImageDisplay component
    return publicUrlData.publicUrl;
  } catch (error) {
    console.error('Exception during image upload:', error);
    toast.error('An unexpected error occurred');
    return null;
  }
}

/**
 * Creates a data URL from a file (for preview)
 * @param file The file to convert to a data URL
 * @returns A promise that resolves to the data URL
 */
export function createImagePreview(file: File): Promise<string> {
  return new Promise((resolve) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      if (e.target && typeof e.target.result === 'string') {
        resolve(e.target.result);
      } else {
        resolve('');
      }
    };
    reader.readAsDataURL(file);
  });
}
