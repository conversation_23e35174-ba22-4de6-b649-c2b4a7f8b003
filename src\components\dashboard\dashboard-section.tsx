"use client"

import { ReactNode } from "react"
import { Button } from "@/components/ui/button"

interface DashboardSectionProps {
  title: string
  children: ReactNode
  viewAllLink?: string
  onViewAll?: () => void
  className?: string
  id?: string
}

export function DashboardSection({ title, children, viewAllLink, onViewAll, className, id }: DashboardSectionProps) {
  return (
    <div className={`grid gap-4 ${className || ''}`} id={id}>
      <div className="flex justify-between items-center mb-2">
        <h2 className="text-xl font-semibold">{title}</h2>
        {(viewAllLink || onViewAll) && (
          <Button
            variant="outline"
            size="sm"
            onClick={onViewAll}
            {...(viewAllLink ? { asChild: true } : {})}
          >
            {viewAllLink ? <a href={viewAllLink}>View All</a> : "View All"}
          </Button>
        )}
      </div>
      {children}
    </div>
  )
}
