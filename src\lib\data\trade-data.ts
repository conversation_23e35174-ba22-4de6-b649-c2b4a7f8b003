import { getSupabaseBrowser } from '@/lib/supabase-browser'
import { getSupabaseServer } from '@/lib/supabase-server'
import { handleError, NotFoundError } from '@/lib/error-handler'
import type { Trade } from '@/types/trade'
import type { ProcessedData } from '@/lib/excel-processor'

/**
 * Get trades for a user with optional filtering
 */
export async function getUserTrades(
  userId: string,
  options?: {
    accountId?: string | null,
    startDate?: string | Date | null,
    endDate?: string | Date | null,
    symbol?: string | null,
    tradeType?: string | null,
    minProfit?: number | null,
    maxProfit?: number | null,
    tags?: string[] | null,
    useServer?: boolean
  }
): Promise<Trade[]> {
  try {
    // If accountId is explicitly null (user has unselected all accounts), return empty array
    if (options?.accountId === null) {
      console.log('No account selected, returning empty trades array')
      return []
    }

    const supabase = options?.useServer
      ? await getSupabaseServer()
      : getSupabaseBrowser()

    // Start building the query
    let query = supabase
      .from("trades")
      .select("*")
      .eq("user_id", userId)

    // Add account filter
    if (options?.accountId) {
      query = query.eq("account_id", options.accountId)
    }

    // Add date filters if provided
    if (options?.startDate) {
      const formattedStartDate = typeof options.startDate === 'string'
        ? options.startDate
        : options.startDate instanceof Date
          ? options.startDate.toISOString()
          : null

      if (formattedStartDate) {
        query = query.gte("time_close", formattedStartDate)
      }
    }

    if (options?.endDate) {
      const formattedEndDate = typeof options.endDate === 'string'
        ? options.endDate
        : options.endDate instanceof Date
          ? options.endDate.toISOString()
          : null

      if (formattedEndDate) {
        query = query.lte("time_close", formattedEndDate)
      }
    }

    // Add symbol filter if provided
    if (options?.symbol) {
      query = query.eq("symbol", options.symbol)
    }

    // Add trade type filter if provided
    if (options?.tradeType) {
      query = query.eq("type", options.tradeType)
    }

    // Add profit range filters if provided
    if (options?.minProfit !== undefined && options?.minProfit !== null) {
      query = query.gte("profit", options.minProfit)
    }

    if (options?.maxProfit !== undefined && options?.maxProfit !== null) {
      query = query.lte("profit", options.maxProfit)
    }

    // Add tag filter if provided
    if (options?.tags && options.tags.length > 0) {
      query = query.overlaps("tags", options.tags)
    }

    // Add ordering
    query = query.order("time_close", { ascending: false })

    // Execute the query
    const { data: trades, error } = await query

    if (error) {
      throw error
    }

    return trades || []
  } catch (error) {
    return handleError(error, [], 'getUserTrades')
  }
}

/**
 * Get a single trade by ID
 */
export async function getTradeById(
  userId: string,
  tradeId: string,
  options?: { useServer?: boolean }
): Promise<Trade | null> {
  try {
    const supabase = options?.useServer
      ? await getSupabaseServer()
      : getSupabaseBrowser()

    const { data, error } = await supabase
      .from("trades")
      .select("*")
      .eq("id", tradeId)
      .eq("user_id", userId)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        throw new NotFoundError(`Trade with ID ${tradeId} not found`)
      }
      throw error
    }

    return data
  } catch (error) {
    return handleError(error, null, 'getTradeById')
  }
}

/**
 * Save trade data from imported file or manual entry
 */
export async function saveTradeData(
  userId: string,
  data: ProcessedData,
  selectedAccountId?: string
): Promise<void> {
  try {
    const supabase = getSupabaseBrowser()

    // Get or create account
    let accountId = selectedAccountId
    if (!accountId) {
      const { data: accountData, error: accountError } = await supabase
        .from("trading_accounts")
        .upsert({
          user_id: userId,
          name: data.account.name,
          account_number: data.account.account,
          broker: data.account.company,
          updated_at: new Date().toISOString(),
        }, { onConflict: 'account_number' })
        .select('id')
        .single()

      if (accountError) {
        throw accountError
      }

      if (!accountData) {
        throw new Error('Failed to retrieve account ID after saving')
      }

      accountId = accountData.id
    }

    // Prepare trades data with account_id
    const tradesData = data.trades.map((trade) => ({
      user_id: userId,
      account_id: accountId,
      position_id: trade.position_id,
      symbol: trade.symbol,
      type: trade.type,
      volume: trade.volume,
      price_open: trade.price_open,
      price_close: trade.price_close,
      sl: trade.sl || null,
      tp: trade.tp || null,
      time_open: new Date(trade.time_open).toISOString(),
      time_close: new Date(trade.time_close).toISOString(),
      commission: trade.commission,
      swap: trade.swap,
      profit: trade.profit,
      strategy_id: trade.strategy_id || null,
      setup_id: trade.setup_id || null,
    }))

    // Save trades in batches to avoid hitting size limits
    const batchSize = 100
    for (let i = 0; i < tradesData.length; i += batchSize) {
      const batch = tradesData.slice(i, i + batchSize)
      const { error: tradesError } = await supabase.from("trades").upsert(batch)

      if (tradesError) {
        throw tradesError
      }
    }
  } catch (error) {
    throw error
  }
}
