-- Update the get_journal_entries function to include tag filtering for trades
CREATE OR REPLACE FUNCTION public.get_journal_entries(
  p_user_id UUID,
  p_search_term TEXT DEFAULT NULL,
  p_tags TEXT[] DEFAULT NULL,
  p_start_date DATE DEFAULT NULL,
  p_end_date DATE DEFAULT NULL,
  p_account_id UUID DEFAULT NULL
)
RETURNS SETOF public.journal_entries
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  RETURN QUERY
  SELECT je.*
  FROM public.journal_entries je
  LEFT JOIN public.trades t ON je.trade_id = t.id
  WHERE je.user_id = p_user_id
    AND (
      -- Account filter (only applies to entries with trades)
      p_account_id IS NULL 
      OR je.trade_id IS NULL 
      OR t.account_id = p_account_id
    )
    AND (
      -- Search term filter
      p_search_term IS NULL
      OR je.title ILIKE '%' || p_search_term || '%'
      OR je.content ILIKE '%' || p_search_term || '%'
    )
    AND (
      -- Tag filter for journal entries
      p_tags IS NULL
      OR p_tags = '{}'
      OR je.tags && p_tags
      -- Also include trades with matching tags
      OR (je.trade_id IS NOT NULL AND t.tags && p_tags)
    )
    AND (
      -- Date range filter
      (p_start_date IS NULL OR je.date >= p_start_date)
      AND (p_end_date IS NULL OR je.date <= p_end_date)
    )
  ORDER BY je.date DESC, je.created_at DESC;
END;
$$;

-- Update the get_user_trades function to include tag filtering
CREATE OR REPLACE FUNCTION public.get_user_trades(
  p_user_id UUID,
  p_account_id UUID DEFAULT NULL,
  p_start_date TIMESTAMP DEFAULT NULL,
  p_end_date TIMESTAMP DEFAULT NULL,
  p_symbol TEXT DEFAULT NULL,
  p_trade_type TEXT DEFAULT NULL,
  p_min_profit NUMERIC DEFAULT NULL,
  p_max_profit NUMERIC DEFAULT NULL,
  p_tags TEXT[] DEFAULT NULL
)
RETURNS SETOF public.trades
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  RETURN QUERY
  SELECT t.*
  FROM public.trades t
  WHERE t.user_id = p_user_id
    AND (p_account_id IS NULL OR t.account_id = p_account_id)
    AND (p_start_date IS NULL OR t.time_close >= p_start_date)
    AND (p_end_date IS NULL OR t.time_close <= p_end_date)
    AND (p_symbol IS NULL OR t.symbol = p_symbol)
    AND (p_trade_type IS NULL OR t.type = p_trade_type)
    AND (p_min_profit IS NULL OR t.profit >= p_min_profit)
    AND (p_max_profit IS NULL OR t.profit <= p_max_profit)
    AND (p_tags IS NULL OR p_tags = '{}' OR t.tags && p_tags)
  ORDER BY t.time_close DESC;
END;
$$;
