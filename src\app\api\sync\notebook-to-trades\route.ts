import { NextRequest, NextResponse } from "next/server"
import { getSupabaseServer } from "@/lib/supabase-server"

export async function POST(request: NextRequest) {
  try {
    console.log('=== MANUAL NOTEBOOK TO TRADES SYNC API CALLED ===')
    
    const { notebookEntryId } = await request.json()
    
    if (!notebookEntryId) {
      return NextResponse.json(
        { error: "Notebook entry ID is required" },
        { status: 400 }
      )
    }

    // Get authenticated user
    const supabase = await getSupabaseServer()
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    
    if (userError || !user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    // Fetch the notebook entry to get current and previous data
    const { data: notebookEntry, error: fetchError } = await supabase
      .from("notebook_entries")
      .select("*")
      .eq("id", notebookEntryId)
      .eq("user_id", user.id)
      .single()

    if (fetchError || !notebookEntry) {
      return NextResponse.json(
        { error: "Notebook entry not found" },
        { status: 404 }
      )
    }

    // Call the sync Edge Function manually
    const edgeFunctionUrl = `${process.env.NEXT_PUBLIC_SUPABASE_URL}/functions/v1/sync-notebook-changes`
    const serviceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

    if (!serviceKey) {
      return NextResponse.json(
        { error: "Service key not configured" },
        { status: 500 }
      )
    }

    const syncResponse = await fetch(edgeFunctionUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${serviceKey}`,
      },
      body: JSON.stringify({
        type: 'UPDATE',
        record: notebookEntry,
        old_record: null // We don't have old record for manual sync, but the function handles this
      })
    })

    if (!syncResponse.ok) {
      const errorText = await syncResponse.text()
      console.error('Edge function sync failed:', errorText)
      return NextResponse.json(
        { error: "Sync failed", details: errorText },
        { status: 500 }
      )
    }

    const syncResult = await syncResponse.json()
    console.log('Manual sync completed successfully:', syncResult)

    return NextResponse.json({ 
      success: true, 
      message: "Notebook entry synced to trades successfully",
      syncResult 
    })

  } catch (error) {
    console.error('Error in manual notebook sync:', error)
    return NextResponse.json(
      { error: "Internal server error", details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}
