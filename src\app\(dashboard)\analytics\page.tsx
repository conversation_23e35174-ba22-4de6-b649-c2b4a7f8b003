import { cookies } from 'next/headers';
import { createServerClient } from '@supabase/ssr';
import { redirect } from 'next/navigation';
import type { Database } from '@/types/supabase';
import ClientWrapper from './client-wrapper';

export default async function AnalyticsPage() {
  // Get cookies for server-side Supabase client
  const cookieStore = await cookies();
  const supabase = createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value;
        },
        set(_name: string, _value: string, _options: any) {
          // Server components can't set cookies directly
        },
        remove(_name: string, _options: any) {
          // Server components can't remove cookies directly
        }
      },
    }
  );

  // Get authenticated user
  const { data: { user }, error: userError } = await supabase.auth.getUser();
  if (userError || !user) {
    redirect('/login');
  }

  const userId = user.id;

  // Fetch user accounts
  const { data: accounts, error: accountsError } = await supabase
    .from("trading_accounts")
    .select("*")
    .eq("user_id", userId)
    .order("updated_at", { ascending: false });

  if (accountsError) {
    console.error('Error fetching trading accounts:', accountsError);
    // Continue with empty accounts array
  }

  // Get the selected account ID from the first account (if any)
  const selectedAccountId = accounts && accounts.length > 0 ? accounts[0].id : null;

  // Fetch trades for the selected account
  let trades = [];
  if (selectedAccountId) {
    const { data: tradesData, error: tradesError } = await supabase
      .from("trades")
      .select("*")
      .eq("account_id", selectedAccountId)
      .order("time_close", { ascending: false });

    if (tradesError) {
      console.error('Error fetching trades:', tradesError);
    } else {
      trades = tradesData || [];
    }
  }

  // Fetch trading summary for the selected account
  let summary = null;
  if (selectedAccountId) {
    const { data: summaryData, error: summaryError } = await supabase
      .from("trading_summaries")
      .select("*")
      .eq("account_id", selectedAccountId)
      .maybeSingle();

    if (summaryError) {
      console.error('Error fetching trading summary:', summaryError);
    } else {
      summary = summaryData;
    }
  }

  // Pass the fetched data to the client component
  return (
    <ClientWrapper
      userId={userId}
      initialTrades={trades || []}
      initialSummary={summary}
      selectedAccountId={selectedAccountId}
    />
  );
}

