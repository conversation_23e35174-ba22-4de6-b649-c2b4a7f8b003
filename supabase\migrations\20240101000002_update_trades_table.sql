-- Add or ensure columns exist for strategy assignments and trade details
DO $$
BEGIN
    -- Check if strategy_id column exists, if not add it
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_schema = 'public'
        AND table_name = 'trades'
        AND column_name = 'strategy_id'
    ) THEN
        ALTER TABLE public.trades ADD COLUMN strategy_id UUID REFERENCES public.strategies(id) NULL;
    END IF;

    -- Check if setup_id column exists, if not add it
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_schema = 'public'
        AND table_name = 'trades'
        AND column_name = 'setup_id'
    ) THEN
        ALTER TABLE public.trades ADD COLUMN setup_id UUID REFERENCES public.setups(id) NULL;
    END IF;

    -- Check if notes column exists, if not add it
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_schema = 'public'
        AND table_name = 'trades'
        AND column_name = 'notes'
    ) THEN
        ALTER TABLE public.trades ADD COLUMN notes TEXT NULL;
    END IF;

    -- Check if screenshots column exists, if not add it
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_schema = 'public'
        AND table_name = 'trades'
        AND column_name = 'screenshots'
    ) THEN
        ALTER TABLE public.trades ADD COLUMN screenshots TEXT[] NULL DEFAULT '{}';
    END IF;

    -- Check if followed_rules column exists, if not add it
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_schema = 'public'
        AND table_name = 'trades'
        AND column_name = 'followed_rules'
    ) THEN
        ALTER TABLE public.trades ADD COLUMN followed_rules TEXT[] NULL DEFAULT '{}';
    END IF;

    -- Check if followed_setup_criteria column exists, if not add it
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_schema = 'public'
        AND table_name = 'trades'
        AND column_name = 'followed_setup_criteria'
    ) THEN
        ALTER TABLE public.trades ADD COLUMN followed_setup_criteria TEXT[] NULL DEFAULT '{}';
    END IF;

    -- Check if updated_at column exists, if not add it
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_schema = 'public'
        AND table_name = 'trades'
        AND column_name = 'updated_at'
    ) THEN
        ALTER TABLE public.trades ADD COLUMN updated_at TIMESTAMPTZ NULL DEFAULT now();
    END IF;

    -- Add trigger to update the updated_at column, but only if the function exists
    IF NOT EXISTS (
        SELECT 1
        FROM pg_trigger
        WHERE tgname = 'set_trades_updated_at'
    ) AND EXISTS (
        SELECT 1
        FROM pg_proc
        JOIN pg_namespace ON pg_proc.pronamespace = pg_namespace.oid
        WHERE pg_namespace.nspname = 'public'
        AND pg_proc.proname = 'set_updated_at'
    ) THEN
        CREATE TRIGGER set_trades_updated_at
        BEFORE UPDATE ON public.trades
        FOR EACH ROW
        EXECUTE FUNCTION public.set_updated_at();
    END IF;
END
$$;

-- Create or replace function to update trade details
CREATE OR REPLACE FUNCTION public.update_trade_details(
    p_trade_id UUID,
    p_strategy_id UUID,
    p_setup_id UUID,
    p_notes TEXT,
    p_screenshots TEXT[],
    p_followed_rules TEXT[],
    p_followed_setup_criteria TEXT[]
) RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    -- Update the trade with the provided details
    UPDATE public.trades
    SET
        strategy_id = p_strategy_id,
        setup_id = p_setup_id,
        notes = p_notes,
        screenshots = p_screenshots,
        followed_rules = p_followed_rules,
        followed_setup_criteria = p_followed_setup_criteria,
        updated_at = NOW()
    WHERE id = p_trade_id;

    -- Return true if the update was successful
    RETURN FOUND;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.update_trade_details TO authenticated;

-- Add comment to the function
COMMENT ON FUNCTION public.update_trade_details IS 'Updates trade details with proper permissions';

-- Create or replace function to get trades by strategy
CREATE OR REPLACE FUNCTION public.get_trades_by_strategy(
    p_user_id UUID,
    p_strategy_id UUID,
    p_account_id UUID DEFAULT NULL
) RETURNS SETOF public.trades
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    IF p_account_id IS NOT NULL THEN
        RETURN QUERY
        SELECT *
        FROM public.trades
        WHERE user_id = p_user_id
        AND strategy_id = p_strategy_id
        AND account_id = p_account_id
        ORDER BY time_close DESC;
    ELSE
        RETURN QUERY
        SELECT *
        FROM public.trades
        WHERE user_id = p_user_id
        AND strategy_id = p_strategy_id
        ORDER BY time_close DESC;
    END IF;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.get_trades_by_strategy TO authenticated;

-- Add comment to the function
COMMENT ON FUNCTION public.get_trades_by_strategy IS 'Gets trades by strategy with proper permissions';
