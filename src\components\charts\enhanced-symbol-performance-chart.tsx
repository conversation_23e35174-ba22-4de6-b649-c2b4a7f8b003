"use client"

import { useMemo } from "react"
import { format } from "date-fns"
import {
  LineChart,
  Line,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  ComposedChart,
  Legend,
  Cell,
  ReferenceLine,
} from "recharts"
import { ChartContainer } from "@/components/ui/chart-container"
import { ChartTooltip } from "@/components/ui/chart-tooltip"

interface Trade {
  time_close: string
  profit: number
}

interface EnhancedSymbolPerformanceChartProps {
  trades: Trade[]
}

export function EnhancedSymbolPerformanceChart({ trades }: EnhancedSymbolPerformanceChartProps) {
  const chartData = useMemo(() => {
    // Sort trades by date
    const sortedTrades = [...trades].sort(
      (a, b) => new Date(a.time_close).getTime() - new Date(b.time_close).getTime()
    )

    // Group trades by day
    const tradesByDay: Record<string, { profit: number, trades: number, cumulative: number }> = {}
    let cumulative = 0

    sortedTrades.forEach(trade => {
      const day = format(new Date(trade.time_close), "yyyy-MM-dd")

      if (!tradesByDay[day]) {
        tradesByDay[day] = {
          profit: 0,
          trades: 0,
          cumulative: 0
        }
      }

      tradesByDay[day].profit += trade.profit
      tradesByDay[day].trades += 1
      cumulative += trade.profit
      tradesByDay[day].cumulative = cumulative
    })

    // Convert to array and ensure dates are in order
    return Object.entries(tradesByDay)
      .map(([date, data]) => ({
        date,
        profit: parseFloat(data.profit.toFixed(2)),
        trades: data.trades,
        cumulative: parseFloat(data.cumulative.toFixed(2))
      }))
      .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
  }, [trades])

  return (
    <ChartContainer>
      <ResponsiveContainer width="100%" height="100%">
        <ComposedChart
          data={chartData}
          margin={{ top: 20, right: 30, left: 20, bottom: 60 }}
        >
          <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" opacity={0.3} />
          <XAxis
            dataKey="date"
            angle={-45}
            textAnchor="end"
            height={60}
            tickFormatter={(value) => format(new Date(value), "MMM d")}
            tick={{ fill: 'hsl(var(--muted-foreground))', fontSize: 11, opacity: 0.7 }}
            stroke="hsl(var(--muted-foreground))"
            strokeOpacity={0.5}
          />
          <YAxis
            yAxisId="left"
            orientation="left"
            tickFormatter={(value) => `$${value}`}
            tick={{ fill: 'hsl(var(--muted-foreground))', fontSize: 11, opacity: 0.7 }}
            stroke="hsl(var(--muted-foreground))"
            strokeOpacity={0.5}
          />
          <YAxis
            yAxisId="right"
            orientation="right"
            tickFormatter={(value) => `$${value}`}
            tick={{ fill: 'hsl(var(--muted-foreground))', fontSize: 11, opacity: 0.7 }}
            stroke="hsl(var(--muted-foreground))"
            strokeOpacity={0.5}
          />
          <Tooltip
            content={({ active, payload }) => {
              if (!active || !payload?.length) return null
              const data = payload[0].payload
              return (
                <ChartTooltip
                  active={active}
                  payload={payload}
                  label={format(new Date(data.date), "MMM d, yyyy")}
                  formatter={(value, name) => {
                    if (name === "profit") return [`$${value}`, "Daily P&L"]
                    if (name === "cumulative") return [`$${value}`, "Cumulative P&L"]
                    if (name === "trades") return [value, "Trades"]
                    return [value, name]
                  }}
                />
              )
            }}
          />
          <ReferenceLine y={0} yAxisId="left" stroke="hsl(var(--border))" strokeWidth={2} strokeOpacity={0.8} />
          <Legend
            layout="horizontal"
            verticalAlign="bottom"
            align="center"
            wrapperStyle={{ paddingTop: "20px" }}
          />
          <Bar
            yAxisId="left"
            dataKey="profit"
            name="Daily P&L"
            fill="currentColor"
            className="fill-emerald-500 data-[value<0]:fill-rose-500"
            barSize={20}
          >
            {chartData.map((entry, index) => (
              <Cell
                key={`cell-${index}`}
                fill={entry.profit >= 0 ? "hsl(var(--emerald-500))" : "hsl(var(--rose-500))"}
              />
            ))}
          </Bar>
          <Line
            yAxisId="right"
            type="monotone"
            dataKey="cumulative"
            name="Cumulative P&L"
            stroke="hsl(var(--blue-500))"
            strokeWidth={2}
            dot={{ r: 3 }}
            activeDot={{ r: 6, strokeWidth: 0 }}
          />
        </ComposedChart>
      </ResponsiveContainer>
    </ChartContainer>
  )
}
