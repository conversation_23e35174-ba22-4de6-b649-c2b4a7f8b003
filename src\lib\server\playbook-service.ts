import { Strategy, Setup, StrategyRule, StrategyPerformance } from '@/types/playbook';

// Strategy functions
export async function getStrategies(): Promise<Strategy[]> {
  try {
    const response = await fetch('/api/strategies', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const error = await response.json();
      console.error('Error fetching strategies:', error);
      return [];
    }

    return await response.json();
  } catch (error) {
    console.error('Error in getStrategies:', error);
    return [];
  }
}

export async function getStrategyById(strategyId: string): Promise<Strategy | null> {
  try {
    const response = await fetch(`/api/strategies?id=${strategyId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const error = await response.json();
      console.error('Error fetching strategy:', error);
      return null;
    }

    return await response.json();
  } catch (error) {
    console.error('Error in getStrategyById:', error);
    return null;
  }
}

export async function createStrategy(strategy: Omit<Strategy, 'id' | 'user_id' | 'created_at' | 'updated_at'>): Promise<Strategy | null> {
  try {
    const response = await fetch('/api/strategies', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(strategy),
    });

    if (!response.ok) {
      const error = await response.json();
      console.error('Error creating strategy:', error);
      return null;
    }

    return await response.json();
  } catch (error) {
    console.error('Error in createStrategy:', error);
    return null;
  }
}

export async function updateStrategy(strategyId: string, updates: Partial<Omit<Strategy, 'id' | 'user_id' | 'created_at' | 'updated_at'>>): Promise<Strategy | null> {
  try {
    const response = await fetch('/api/strategies', {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        id: strategyId,
        ...updates,
      }),
    });

    if (!response.ok) {
      const error = await response.json();
      console.error('Error updating strategy:', error);
      return null;
    }

    return await response.json();
  } catch (error) {
    console.error('Error in updateStrategy:', error);
    return null;
  }
}

export async function deleteStrategy(strategyId: string): Promise<boolean> {
  try {
    const response = await fetch(`/api/strategies?id=${strategyId}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const error = await response.json();
      console.error('Error deleting strategy:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error in deleteStrategy:', error);
    return false;
  }
}

// Setup functions
export async function getSetups(strategyId?: string): Promise<Setup[]> {
  try {
    const url = strategyId 
      ? `/api/strategy-setups?strategyId=${strategyId}`
      : '/api/strategy-setups';
      
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const error = await response.json();
      console.error('Error fetching setups:', error);
      return [];
    }

    return await response.json();
  } catch (error) {
    console.error('Error in getSetups:', error);
    return [];
  }
}

export async function getSetupById(setupId: string): Promise<Setup | null> {
  try {
    const response = await fetch(`/api/strategy-setups?id=${setupId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const error = await response.json();
      console.error('Error fetching setup:', error);
      return null;
    }

    return await response.json();
  } catch (error) {
    console.error('Error in getSetupById:', error);
    return null;
  }
}

export async function createSetup(setup: Omit<Setup, 'id' | 'user_id' | 'created_at' | 'updated_at'>): Promise<Setup | null> {
  try {
    const response = await fetch('/api/strategy-setups', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(setup),
    });

    if (!response.ok) {
      const error = await response.json();
      console.error('Error creating setup:', error);
      return null;
    }

    return await response.json();
  } catch (error) {
    console.error('Error in createSetup:', error);
    return null;
  }
}

export async function updateSetup(setupId: string, updates: Partial<Omit<Setup, 'id' | 'user_id' | 'created_at' | 'updated_at'>>): Promise<Setup | null> {
  try {
    const response = await fetch('/api/strategy-setups', {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        id: setupId,
        ...updates,
      }),
    });

    if (!response.ok) {
      const error = await response.json();
      console.error('Error updating setup:', error);
      return null;
    }

    return await response.json();
  } catch (error) {
    console.error('Error in updateSetup:', error);
    return null;
  }
}

export async function deleteSetup(setupId: string): Promise<{ success: boolean, imageUrls: string[] }> {
  try {
    const response = await fetch(`/api/strategy-setups?id=${setupId}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const error = await response.json();
      console.error('Error deleting setup:', error);
      return { success: false, imageUrls: [] };
    }

    const result = await response.json();
    return { 
      success: result.success, 
      imageUrls: result.imageUrls || [] 
    };
  } catch (error) {
    console.error('Error in deleteSetup:', error);
    return { success: false, imageUrls: [] };
  }
}

// Strategy Rule functions
export async function getStrategyRules(strategyId: string): Promise<StrategyRule[]> {
  try {
    const response = await fetch(`/api/strategy-rules?strategyId=${strategyId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const error = await response.json();
      console.error('Error fetching strategy rules:', error);
      return [];
    }

    return await response.json();
  } catch (error) {
    console.error('Error in getStrategyRules:', error);
    return [];
  }
}

export async function getStrategyRuleById(ruleId: string): Promise<StrategyRule | null> {
  try {
    const response = await fetch(`/api/strategy-rules?id=${ruleId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const error = await response.json();
      console.error('Error fetching strategy rule:', error);
      return null;
    }

    return await response.json();
  } catch (error) {
    console.error('Error in getStrategyRuleById:', error);
    return null;
  }
}

export async function createStrategyRule(rule: Omit<StrategyRule, 'id' | 'user_id' | 'created_at' | 'updated_at'>): Promise<StrategyRule | null> {
  try {
    const response = await fetch('/api/strategy-rules', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(rule),
    });

    if (!response.ok) {
      const error = await response.json();
      console.error('Error creating strategy rule:', error);
      return null;
    }

    return await response.json();
  } catch (error) {
    console.error('Error in createStrategyRule:', error);
    return null;
  }
}

export async function updateStrategyRule(ruleId: string, updates: Partial<Omit<StrategyRule, 'id' | 'user_id' | 'created_at' | 'updated_at'>>): Promise<StrategyRule | null> {
  try {
    const response = await fetch('/api/strategy-rules', {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        id: ruleId,
        ...updates,
      }),
    });

    if (!response.ok) {
      const error = await response.json();
      console.error('Error updating strategy rule:', error);
      return null;
    }

    return await response.json();
  } catch (error) {
    console.error('Error in updateStrategyRule:', error);
    return null;
  }
}

export async function deleteStrategyRule(ruleId: string): Promise<boolean> {
  try {
    const response = await fetch(`/api/strategy-rules?id=${ruleId}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const error = await response.json();
      console.error('Error deleting strategy rule:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error in deleteStrategyRule:', error);
    return false;
  }
}

// Strategy Performance functions
export async function getStrategyPerformance(strategyId: string, accountId?: string | null): Promise<StrategyPerformance[]> {
  try {
    const url = accountId 
      ? `/api/strategy-performance?strategyId=${strategyId}&accountId=${accountId}`
      : `/api/strategy-performance?strategyId=${strategyId}`;
      
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const error = await response.json();
      console.error('Error fetching strategy performance:', error);
      return [];
    }

    return await response.json();
  } catch (error) {
    console.error('Error in getStrategyPerformance:', error);
    return [];
  }
}

export async function calculateStrategyPerformance(
  strategyId: string,
  periodStart: string,
  periodEnd: string,
  accountId?: string | null
): Promise<StrategyPerformance | null> {
  try {
    const response = await fetch('/api/strategy-performance', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        strategyId,
        periodStart,
        periodEnd,
        accountId
      }),
    });

    if (!response.ok) {
      const error = await response.json();
      console.error('Error calculating strategy performance:', error);
      return null;
    }

    return await response.json();
  } catch (error) {
    console.error('Error in calculateStrategyPerformance:', error);
    return null;
  }
}
