-- Create trading_notes table
CREATE TABLE IF NOT EXISTS public.trading_notes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    account_id UUID REFERENCES public.trading_accounts(id) ON DELETE CASCADE,
    date TEXT NOT NULL,
    content TEXT NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    UNIQUE (user_id, account_id, date)
);

-- Add RLS policies
ALTER TABLE public.trading_notes ENABLE ROW LEVEL SECURITY;

-- Policy for select
CREATE POLICY "Users can view their own trading notes"
ON public.trading_notes
FOR SELECT
USING (auth.uid() = user_id);

-- Policy for insert
CREATE POLICY "Users can insert their own trading notes"
ON public.trading_notes
FOR INSERT
WITH CHECK (auth.uid() = user_id);

-- Policy for update
CREATE POLICY "Users can update their own trading notes"
ON public.trading_notes
FOR UPDATE
USING (auth.uid() = user_id);

-- Policy for delete
CREATE POLICY "Users can delete their own trading notes"
ON public.trading_notes
FOR DELETE
USING (auth.uid() = user_id);

-- Add indexes
CREATE INDEX IF NOT EXISTS trading_notes_user_id_idx ON public.trading_notes (user_id);
CREATE INDEX IF NOT EXISTS trading_notes_account_id_idx ON public.trading_notes (account_id);
CREATE INDEX IF NOT EXISTS trading_notes_date_idx ON public.trading_notes (date);
