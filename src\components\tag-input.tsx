"use client"

import { useState, useRef, useEffect } from "react"
import { X, Plus } from "lucide-react"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"

interface TagInputProps {
  value: string[]
  onChange: (value: string[]) => void
  placeholder?: string
  className?: string
  suggestions?: string[]
}

export function TagInput({
  value = [],
  onChange,
  placeholder = "Add tags...",
  className,
  suggestions = []
}: TagInputProps) {
  const [inputValue, setInputValue] = useState("")
  const [showSuggestions, setShowSuggestions] = useState(false)
  const [filteredSuggestions, setFilteredSuggestions] = useState<string[]>([])
  const inputRef = useRef<HTMLInputElement>(null)
  const suggestionsRef = useRef<HTMLDivElement>(null)

  // Store suggestions in a ref to avoid dependency changes
  const suggestionsRef2 = useRef(suggestions)

  // Update the ref when suggestions prop changes
  useEffect(() => {
    suggestionsRef2.current = suggestions;
  }, [suggestions]);

  useEffect(() => {
    // Filter suggestions based on input value
    if (inputValue.trim()) {
      const filtered = suggestionsRef2.current.filter(
        (suggestion) =>
          suggestion.toLowerCase().includes(inputValue.toLowerCase()) &&
          !value.includes(suggestion)
      )
      setFilteredSuggestions(filtered)
    } else {
      setFilteredSuggestions([])
    }
  }, [inputValue, value])

  useEffect(() => {
    // Close suggestions dropdown when clicking outside
    const handleClickOutside = (event: MouseEvent) => {
      if (
        suggestionsRef.current &&
        !suggestionsRef.current.contains(event.target as Node) &&
        inputRef.current &&
        !inputRef.current.contains(event.target as Node)
      ) {
        setShowSuggestions(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value)
    setShowSuggestions(true)
  }

  const handleInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter" && inputValue.trim()) {
      e.preventDefault()
      addTag(inputValue.trim())
    } else if (e.key === "Backspace" && !inputValue && value.length > 0) {
      // Remove the last tag when backspace is pressed and input is empty
      const newTags = [...value]
      newTags.pop()
      onChange(newTags)
    }
  }

  const addTag = (tag: string) => {
    if (tag && !value.includes(tag)) {
      const newTags = [...value, tag]
      onChange(newTags)
      setInputValue("")
      setShowSuggestions(false)
    }
  }

  const removeTag = (tagToRemove: string) => {
    const newTags = value.filter((tag) => tag !== tagToRemove)
    onChange(newTags)
  }

  const selectSuggestion = (suggestion: string) => {
    addTag(suggestion)
  }

  return (
    <div className={cn("space-y-2", className)}>
      <div className="flex flex-wrap gap-2 p-2 bg-muted/50 rounded-md min-h-[42px]">
        {Array.isArray(value) && value.map((tag) => (
          <Badge key={tag} variant="secondary" className="px-2 py-1 text-sm">
            {tag}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => removeTag(tag)}
              className="h-4 w-4 p-0 ml-1 text-muted-foreground hover:text-foreground"
            >
              <X className="h-3 w-3" />
              <span className="sr-only">Remove {tag}</span>
            </Button>
          </Badge>
        ))}
        <div className="relative flex-1 min-w-[120px]">
          <div className="flex items-center">
            <Input
              ref={inputRef}
              type="text"
              value={inputValue}
              onChange={handleInputChange}
              onKeyDown={handleInputKeyDown}
              onFocus={() => setShowSuggestions(true)}
              placeholder={value.length === 0 ? placeholder : ""}
              className="border-0 bg-transparent p-0 h-auto focus-visible:ring-0 focus-visible:ring-offset-0"
            />
            {inputValue && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => addTag(inputValue.trim())}
                className="h-6 w-6 p-0 ml-1"
              >
                <Plus className="h-4 w-4" />
                <span className="sr-only">Add tag</span>
              </Button>
            )}
          </div>
          {showSuggestions && filteredSuggestions.length > 0 && (
            <div
              ref={suggestionsRef}
              className="absolute z-10 mt-1 w-full bg-popover rounded-md shadow-md overflow-hidden"
            >
              <div className="py-1 max-h-[200px] overflow-y-auto">
                {filteredSuggestions.map((suggestion) => (
                  <div
                    key={suggestion}
                    className="px-3 py-2 text-sm cursor-pointer hover:bg-muted"
                    onClick={() => selectSuggestion(suggestion)}
                  >
                    {suggestion}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default TagInput
