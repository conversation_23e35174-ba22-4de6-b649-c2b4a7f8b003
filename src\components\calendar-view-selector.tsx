"use client"

import { But<PERSON> } from "@/components/ui/button"
import { cn } from "@/lib/utils"

interface CalendarViewSelectorProps {
  view: "month" | "week" | "day"
  onChange: (view: "month" | "week" | "day") => void
  className?: string
}

export function CalendarViewSelector({
  view,
  onChange,
  className
}: CalendarViewSelectorProps) {
  return (
    <div className={cn("flex items-center space-x-1", className)}>
      <Button
        variant="ghost"
        size="sm"
        className={cn(
          "hover:bg-accent text-xs px-2",
          view === "month" && "bg-accent"
        )}
        onClick={() => onChange("month")}
      >
        Month
      </Button>
      <Button
        variant="ghost"
        size="sm"
        className={cn(
          "hover:bg-accent text-xs px-2",
          view === "week" && "bg-accent"
        )}
        onClick={() => onChange("week")}
      >
        Week
      </Button>
      <Button
        variant="ghost"
        size="sm"
        className={cn(
          "hover:bg-accent text-xs px-2",
          view === "day" && "bg-accent"
        )}
        onClick={() => onChange("day")}
      >
        Day
      </Button>
    </div>
  )
}
