"use client"

import { Inter } from "next/font/google"
import { Providers } from "@/components/providers"
import "./globals.css"
import { useEffect } from "react"
import { initFetchInterceptor } from "@/lib/fetch-interceptor"
import { initFocusTrapFix } from "@/lib/focus-trap-fix"
import EditorNav from "@/components/navigation/editor-nav"

const inter = Inter({ subsets: ["latin"] })

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  // Initialize the fetch interceptor to fix 406 errors and focus trap fix
  useEffect(() => {
    // Initialize the interceptor and get the cleanup function
    const restoreOriginalFetch = initFetchInterceptor();

    // Initialize the focus trap fix to prevent aria-hidden issues
    const cleanupFocusTrapFix = initFocusTrapFix();

    // Return the cleanup functions to restore the original state when unmounting
    return () => {
      restoreOriginalFetch();
      if (cleanupFocusTrapFix) cleanupFocusTrapFix();
    };
  }, []);
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={`${inter.className} antialiased`} suppressHydrationWarning>
        <Providers>
          <main className="relative flex min-h-screen flex-col">
            {children}
            <EditorNav />
          </main>
        </Providers>
      </body>
    </html>
  )
}
