# Dependencies
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Production builds
.next/
out/
dist/
build/

# Environment files (keep .env.local for production)
.env
.env.development.local
.env.test.local

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git
.gitignore

# Documentation and development files
README.md
ROADMAP.md
*.md.bak
docs/
.github/
implementation_roadmap.md
notebook-*.md
previous*.md
requirements.md
sample-code.md
testing-guide.md
trade-list-with-pagination.txt
mcp-config.json
mcp_setup.md

# Test files
coverage/
.nyc_output
test/
tests/
__tests__/
test-sync.js

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# TypeScript build info
tsconfig.tsbuildinfo

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# next.js build output
.next

# nuxt.js build output
.nuxt

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# PNPM files (we're using npm)
pnpm-lock.yaml
.pnpm-debug.log*
