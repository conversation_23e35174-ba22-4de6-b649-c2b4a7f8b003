import { METRIC_TEMPLATES, MetricTemplate } from './metric-templates';

export interface GoalTemplate {
  id: string;
  title: string;
  description: string;
  category: 'daily' | 'weekly' | 'monthly' | 'quarterly';
  metricTemplateId: string;
  targetValue: number;
  timeframe: number; // days
  priority: 'low' | 'medium' | 'high';
  explanation: string;
  tips: string[];
}

export const GOAL_TEMPLATES: GoalTemplate[] = [
  // Daily Goals
  {
    id: 'daily_win_rate_60',
    title: 'Achieve 60% Daily Win Rate',
    description: 'Maintain a 60% win rate for daily trading sessions',
    category: 'daily',
    metricTemplateId: 'win_rate',
    targetValue: 60.00,
    timeframe: 1,
    priority: 'medium',
    explanation: 'A 60% daily win rate indicates good trade selection and timing.',
    tips: [
      'Focus on high-probability setups',
      'Wait for clear entry signals',
      'Avoid overtrading'
    ]
  },
  {
    id: 'daily_profit_target_100',
    title: 'Daily Profit Target: $100',
    description: 'Achieve $100 profit per trading day',
    category: 'daily',
    metricTemplateId: 'average_trade_return',
    targetValue: 100.00,
    timeframe: 1,
    priority: 'high',
    explanation: 'Setting daily profit targets helps maintain consistent performance.',
    tips: [
      'Stop trading once target is reached',
      'Focus on quality over quantity',
      'Manage risk per trade'
    ]
  },

  // Weekly Goals
  {
    id: 'weekly_win_rate_65',
    title: 'Weekly Win Rate: 65%',
    description: 'Maintain 65% win rate over weekly periods',
    category: 'weekly',
    metricTemplateId: 'win_rate',
    targetValue: 65.00,
    timeframe: 7,
    priority: 'medium',
    explanation: 'Weekly win rate smooths out daily variations and shows consistent performance.',
    tips: [
      'Review weekly performance every Sunday',
      'Identify patterns in losing trades',
      'Adjust strategy based on market conditions'
    ]
  },
  {
    id: 'weekly_profit_factor_15',
    title: 'Weekly Profit Factor: 1.5',
    description: 'Achieve profit factor of 1.5 or higher weekly',
    category: 'weekly',
    metricTemplateId: 'profit_factor',
    targetValue: 1.50,
    timeframe: 7,
    priority: 'high',
    explanation: 'A profit factor above 1.5 indicates strong profitability.',
    tips: [
      'Focus on risk/reward ratios',
      'Cut losses quickly',
      'Let winners run'
    ]
  },
  {
    id: 'weekly_max_drawdown_5',
    title: 'Limit Weekly Drawdown to 5%',
    description: 'Keep weekly drawdown under 5%',
    category: 'weekly',
    metricTemplateId: 'max_consecutive_losses',
    targetValue: 5.00,
    timeframe: 7,
    priority: 'high',
    explanation: 'Controlling drawdown is crucial for long-term success.',
    tips: [
      'Use proper position sizing',
      'Set stop losses on all trades',
      'Take breaks after consecutive losses'
    ]
  },

  // Monthly Goals
  {
    id: 'monthly_win_rate_70',
    title: 'Monthly Win Rate: 70%',
    description: 'Achieve 70% win rate over monthly periods',
    category: 'monthly',
    metricTemplateId: 'win_rate',
    targetValue: 70.00,
    timeframe: 30,
    priority: 'high',
    explanation: 'A 70% monthly win rate indicates excellent trading skills.',
    tips: [
      'Focus on your best setups only',
      'Continuously refine your strategy',
      'Keep detailed trading records'
    ]
  },
  {
    id: 'monthly_profit_target_2000',
    title: 'Monthly Profit: $2,000',
    description: 'Generate $2,000 profit per month',
    category: 'monthly',
    metricTemplateId: 'net_profit_margin',
    targetValue: 2000.00,
    timeframe: 30,
    priority: 'high',
    explanation: 'Monthly profit targets help track long-term performance.',
    tips: [
      'Break down into weekly targets',
      'Track progress daily',
      'Adjust position sizes based on performance'
    ]
  },
  {
    id: 'monthly_consistency_80',
    title: 'Monthly Consistency: 80%',
    description: 'Achieve 80% consistency in monthly performance',
    category: 'monthly',
    metricTemplateId: 'consistency_ratio',
    targetValue: 0.80,
    timeframe: 30,
    priority: 'medium',
    explanation: 'Consistency is more important than occasional big wins.',
    tips: [
      'Stick to your trading plan',
      'Avoid emotional trading',
      'Review and adjust regularly'
    ]
  },

  // Quarterly Goals
  {
    id: 'quarterly_profit_factor_20',
    title: 'Quarterly Profit Factor: 2.0',
    description: 'Achieve profit factor of 2.0 over quarterly periods',
    category: 'quarterly',
    metricTemplateId: 'profit_factor',
    targetValue: 2.00,
    timeframe: 90,
    priority: 'high',
    explanation: 'A quarterly profit factor of 2.0 indicates strong long-term profitability.',
    tips: [
      'Focus on long-term strategy development',
      'Continuously educate yourself',
      'Adapt to changing market conditions'
    ]
  },
  {
    id: 'quarterly_max_drawdown_10',
    title: 'Quarterly Max Drawdown: 10%',
    description: 'Keep quarterly drawdown under 10%',
    category: 'quarterly',
    metricTemplateId: 'max_consecutive_losses',
    targetValue: 10.00,
    timeframe: 90,
    priority: 'high',
    explanation: 'Long-term drawdown control is essential for sustainable trading.',
    tips: [
      'Diversify your trading strategies',
      'Use proper risk management',
      'Take extended breaks when needed'
    ]
  },
  {
    id: 'quarterly_avg_trade_50',
    title: 'Quarterly Average Trade: $50',
    description: 'Achieve $50 average profit per trade quarterly',
    category: 'quarterly',
    metricTemplateId: 'average_trade_return',
    targetValue: 50.00,
    timeframe: 90,
    priority: 'medium',
    explanation: 'Consistent average trade profits indicate sustainable trading.',
    tips: [
      'Focus on trade quality over quantity',
      'Continuously improve your edge',
      'Track and analyze all trades'
    ]
  }
];

export const GOAL_CATEGORIES = [
  {
    id: 'daily',
    name: 'Daily Goals',
    description: 'Short-term goals for daily trading sessions',
    icon: '📅',
    color: 'bg-blue-500'
  },
  {
    id: 'weekly',
    name: 'Weekly Goals',
    description: 'Medium-term goals for weekly performance',
    icon: '📊',
    color: 'bg-green-500'
  },
  {
    id: 'monthly',
    name: 'Monthly Goals',
    description: 'Long-term goals for monthly achievements',
    icon: '🎯',
    color: 'bg-purple-500'
  },
  {
    id: 'quarterly',
    name: 'Quarterly Goals',
    description: 'Strategic goals for quarterly milestones',
    icon: '🏆',
    color: 'bg-orange-500'
  }
] as const;

export function getGoalTemplatesByCategory(category: string): GoalTemplate[] {
  return GOAL_TEMPLATES.filter(template => template.category === category);
}

export function getGoalTemplateById(id: string): GoalTemplate | undefined {
  return GOAL_TEMPLATES.find(template => template.id === id);
}

export function getLinkedMetricTemplate(goalTemplate: GoalTemplate): MetricTemplate | undefined {
  return METRIC_TEMPLATES.find(metric => metric.id === goalTemplate.metricTemplateId);
}

export function getGoalTemplatesForMetric(metricTemplateId: string): GoalTemplate[] {
  return GOAL_TEMPLATES.filter(template => template.metricTemplateId === metricTemplateId);
}

export function generateSmartGoalSuggestions(currentMetrics: Record<string, number>): GoalTemplate[] {
  const suggestions: GoalTemplate[] = [];
  
  // Suggest goals based on current performance
  Object.entries(currentMetrics).forEach(([metricId, value]) => {
    const relatedGoals = getGoalTemplatesForMetric(metricId);
    
    relatedGoals.forEach(goal => {
      // Suggest goals that are challenging but achievable (10-20% improvement)
      const improvementTarget = value * 1.15; // 15% improvement
      if (Math.abs(goal.targetValue - improvementTarget) < goal.targetValue * 0.3) {
        suggestions.push(goal);
      }
    });
  });
  
  // If no current metrics, suggest beginner-friendly goals
  if (Object.keys(currentMetrics).length === 0) {
    suggestions.push(
      ...GOAL_TEMPLATES.filter(goal => 
        goal.priority === 'medium' && 
        ['daily_win_rate_60', 'weekly_profit_factor_15', 'monthly_win_rate_70'].includes(goal.id)
      )
    );
  }
  
  return suggestions.slice(0, 6); // Return top 6 suggestions
}
