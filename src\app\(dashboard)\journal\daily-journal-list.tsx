'use client';

import { useState, useEffect, useRef } from 'react';
import Image from 'next/image';
import { format, parseISO } from 'date-fns';
import { ChevronDown, ChevronUp, Tag, Calendar, Edit, X, Save, Plus, Trash2, ChevronLeft, ChevronRight } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { Skeleton } from '@/components/ui/skeleton';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { ImageDisplay } from '@/components/ui/image-display';
import { DailyJournalEntry } from './types';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';
import { fetchDailyJournalEntry, saveDailyJournalEntry } from './daily-journal-actions';

interface DailyJournalListProps {
  tradingDays: string[];
  userId: string;
  accountId: string | null;
}

export function DailyJournalList({ tradingDays, userId, accountId }: DailyJournalListProps) {
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 7; // Show 7 cards per page as requested

  // Calculate pagination
  const totalPages = Math.ceil(tradingDays.length / pageSize);

  // Get current page items
  const indexOfLastItem = currentPage * pageSize;
  const indexOfFirstItem = indexOfLastItem - pageSize;
  const currentItems = tradingDays.slice(indexOfFirstItem, indexOfLastItem);

  // Change page
  const goToPage = (pageNumber: number) => {
    setCurrentPage(pageNumber);
    // Scroll to top of the list
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  // Generate page numbers for pagination
  const getPageNumbers = () => {
    const pageNumbers = [];
    const maxPagesToShow = 5; // Show at most 5 page numbers

    if (totalPages <= maxPagesToShow) {
      // If we have 5 or fewer pages, show all page numbers
      for (let i = 1; i <= totalPages; i++) {
        pageNumbers.push(i);
      }
    } else {
      // Always include first page
      pageNumbers.push(1);

      // Calculate start and end of page numbers to show
      let startPage = Math.max(2, currentPage - 1);
      let endPage = Math.min(totalPages - 1, currentPage + 1);

      // Adjust if we're at the beginning or end
      if (currentPage <= 2) {
        endPage = 4;
      } else if (currentPage >= totalPages - 1) {
        startPage = totalPages - 3;
      }

      // Add ellipsis if needed
      if (startPage > 2) {
        pageNumbers.push('...');
      }

      // Add middle pages
      for (let i = startPage; i <= endPage; i++) {
        pageNumbers.push(i);
      }

      // Add ellipsis if needed
      if (endPage < totalPages - 1) {
        pageNumbers.push('...');
      }

      // Always include last page
      if (totalPages > 1) {
        pageNumbers.push(totalPages);
      }
    }

    return pageNumbers;
  };

  if (tradingDays.length === 0) {
    return (
      <div className="text-center py-10 border rounded-lg">
        <h3 className="text-lg font-medium">No Daily Journals Found</h3>
        <p className="text-muted-foreground mt-2">
          No daily journal entries match your current filters.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Journal Cards */}
      {currentItems.map((dateStr) => (
        <DailyJournalCard
          key={dateStr}
          date={dateStr}
          userId={userId}
          accountId={accountId}
        />
      ))}

      {/* Pagination Controls */}
      {totalPages > 1 && (
        <div className="flex justify-center items-center gap-1 mt-6 pt-4 border-t">
          {/* Previous Page Button */}
          <Button
            variant="outline"
            size="sm"
            onClick={() => goToPage(currentPage - 1)}
            disabled={currentPage === 1}
            className="h-8 w-8 p-0"
          >
            <ChevronLeft className="h-4 w-4" />
            <span className="sr-only">Previous Page</span>
          </Button>

          {/* Page Numbers */}
          <div className="flex items-center gap-1">
            {getPageNumbers().map((page, index) => (
              typeof page === 'number' ? (
                <Button
                  key={index}
                  variant={currentPage === page ? "default" : "outline"}
                  size="sm"
                  onClick={() => goToPage(page)}
                  className={cn(
                    "h-8 w-8 p-0",
                    currentPage === page && "bg-purple-600 hover:bg-purple-700"
                  )}
                >
                  {page}
                </Button>
              ) : (
                <span key={index} className="px-1 text-muted-foreground">
                  {page}
                </span>
              )
            ))}
          </div>

          {/* Next Page Button */}
          <Button
            variant="outline"
            size="sm"
            onClick={() => goToPage(currentPage + 1)}
            disabled={currentPage === totalPages}
            className="h-8 w-8 p-0"
          >
            <ChevronRight className="h-4 w-4" />
            <span className="sr-only">Next Page</span>
          </Button>
        </div>
      )}

      {/* Page indicator */}
      {totalPages > 1 && (
        <div className="text-center text-xs text-muted-foreground">
          Page {currentPage} of {totalPages} • Showing {indexOfFirstItem + 1}-{Math.min(indexOfLastItem, tradingDays.length)} of {tradingDays.length} entries
        </div>
      )}
    </div>
  );
}

interface DailyJournalCardProps {
  date: string;
  userId: string;
  accountId: string | null;
}

function DailyJournalCard({ date, userId, accountId }: DailyJournalCardProps) {
  const [isOpen, setIsOpen] = useState(false);
  // We don't need selectedImageIndex anymore as we're using ImageDisplay component
  const [entry, setEntry] = useState<DailyJournalEntry | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [trades, setTrades] = useState<any[]>([]);
  const [stats, setStats] = useState<any>(null);

  // Edit mode state
  const [isEditing, setIsEditing] = useState(false);
  const [editedNote, setEditedNote] = useState('');
  const [editedTags, setEditedTags] = useState<string[]>([]);
  const [newTag, setNewTag] = useState('');
  const [editedScreenshots, setEditedScreenshots] = useState<string[]>([]);
  const [isSaving, setIsSaving] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);

  // Debounced save function to reduce database calls
  const debouncedSave = useRef<NodeJS.Timeout | null>(null);

  // Cleanup effect to cancel any pending debounced saves when the component unmounts
  useEffect(() => {
    return () => {
      if (debouncedSave.current) {
        clearTimeout(debouncedSave.current);
      }
    };
  }, []);

  // We don't need image viewer state anymore as we're using ImageDisplay component

  // Fetch the journal entry data for this date
  const fetchJournalEntry = async () => {
    if (!userId || !date) return;

    try {
      setIsLoading(true);

      // Use the server action to fetch the journal entry
      const data = await fetchDailyJournalEntry(userId, date, accountId);

      console.log(`Fetched journal entry for ${date}:`, data);
      setEntry(data.entry || null);

      // Process trades to add strategy_name property for backward compatibility
      const processedTrades = (data.trades || []).map((trade: {
        strategies?: { name: string };
        strategy_name?: string;
        [key: string]: any;
      }) => {
        // If the trade has a strategies relation, add the strategy_name property
        if (trade.strategies?.name && !trade.strategy_name) {
          return {
            ...trade,
            strategy_name: trade.strategies.name
          };
        }
        return trade;
      });

      setTrades(processedTrades);
      setStats(data.stats || null);
      setIsLoading(false);

      // Initialize edit form with current values
      if (data.entry) {
        setEditedNote(data.entry.note || '');
        setEditedTags(Array.isArray(data.entry.tags) ? [...data.entry.tags] : []);
        setEditedScreenshots(Array.isArray(data.entry.screenshots) ? [...data.entry.screenshots] : []);
      } else {
        setEditedNote('');
        setEditedTags([]);
        setEditedScreenshots([]);
      }
    } catch (error) {
      console.error(`Error fetching journal entry for ${date}:`, error);
      setEntry(null);
      setTrades([]);
      setStats(null);
      setIsLoading(false);
      toast.error('Failed to load journal entry');
    }
  };

  useEffect(() => {
    const loadData = async () => {
      await fetchJournalEntry();
    };

    loadData();
  }, [userId, date, accountId]);

  const formattedDate = format(parseISO(date), 'EEEE, MMMM d, yyyy');

  // Check content availability
  const hasScreenshots = entry && Array.isArray(entry.screenshots) && entry.screenshots.length > 0;
  const hasNotes = entry && entry.note && entry.note.trim().length > 0;
  const hasTags = entry && Array.isArray(entry.tags) && entry.tags.length > 0;
  const hasTrades = trades && trades.length > 0;
  const hasStats = stats && stats.totalTrades > 0;

  // Check if we have any content to display
  const hasAnyContent = hasNotes || hasScreenshots || hasTags || hasTrades;

  // Handle edit mode
  const startEditing = () => {
    setIsEditing(true);
    setIsOpen(true); // Always expand when editing

    // Initialize form with current values
    if (entry) {
      setEditedNote(entry.note || '');
      setEditedTags(Array.isArray(entry.tags) ? [...entry.tags] : []);
      setEditedScreenshots(Array.isArray(entry.screenshots) ? [...entry.screenshots] : []);
    }
  };

  const cancelEditing = () => {
    setIsEditing(false);

    // Reset form values
    if (entry) {
      setEditedNote(entry.note || '');
      setEditedTags(Array.isArray(entry.tags) ? [...entry.tags] : []);
      setEditedScreenshots(Array.isArray(entry.screenshots) ? [...entry.screenshots] : []);
    }
  };

  // Helper function to save changes to the database
  const saveChangesToDatabase = async (note: string, tags: string[], screenshots: string[]) => {
    if (!userId || !date) return false;

    try {
      // Use the server action to save the journal entry
      const savedEntry = await saveDailyJournalEntry(
        userId,
        date,
        accountId,
        note,
        tags,
        screenshots
      );

      // Update local state with saved entry
      setEntry(savedEntry);

      return true;
    } catch (error) {
      console.error('Error saving journal entry:', error);
      toast.error('Failed to save journal entry');
      return false;
    }
  };

  // Debounced save function - DISABLED for manual save behavior
  const debouncedSaveToDatabase = (note: string, tags: string[], screenshots: string[]) => {
    // Manual save only - no auto-save
    console.log('Auto-save disabled - manual save required');
  };

  const saveJournalEntry = async () => {
    if (!userId || !date) return;

    try {
      setIsSaving(true);

      const success = await saveChangesToDatabase(editedNote, editedTags, editedScreenshots);

      if (success) {
        setIsEditing(false);
        toast.success('Journal entry saved successfully');

        // Refresh data to ensure we have the latest information
        await fetchJournalEntry();

        // Trigger auto-migration and sync processing
        if (entry?.id) {
          try {
            // Process any pending migrations first
            const queueResponse = await fetch('/api/migrate/process-queue', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' }
            });

            if (queueResponse.ok) {
              console.log('Auto-migration queue processed successfully');
            } else {
              console.warn('Auto-migration queue processing failed');
            }

            // Then trigger sync to linked notebook entries
            const syncResponse = await fetch('/api/sync/daily-journal-to-notebooks', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ dailyJournalId: entry.id })
            });

            if (syncResponse.ok) {
              console.log('Daily journal synced to notebooks successfully');
            } else {
              console.warn('Daily journal sync failed, but entry was saved');
            }
          } catch (error) {
            console.warn('Auto-migration or sync error:', error);
          }
        }
      }
    } catch (error) {
      console.error('Error saving journal entry:', error);
      toast.error('Failed to save journal entry');
    } finally {
      setIsSaving(false);
    }
  };

  // Tag management
  const addTag = () => {
    if (newTag.trim() && !editedTags.includes(newTag.trim())) {
      setEditedTags([...editedTags, newTag.trim()]);
      setNewTag('');
    }
  };

  const removeTag = (tagToRemove: string) => {
    setEditedTags(editedTags.filter(tag => tag !== tagToRemove));
  };

  // Screenshot management
  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    // Set uploading state
    setIsUploading(true);
    setUploadProgress(0);

    try {
      const formData = new FormData();
      formData.append('file', files[0]);

      // Simulate progress for better UX
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          const newProgress = prev + Math.random() * 15;
          return newProgress > 90 ? 90 : newProgress;
        });
      }, 150);

      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      });

      // Clear progress interval
      clearInterval(progressInterval);

      if (!response.ok) {
        throw new Error('Failed to upload image');
      }

      // Set progress to 100% when upload is complete
      setUploadProgress(100);

      const { url } = await response.json();

      // Update local state immediately for responsive UI
      const updatedScreenshots = [...editedScreenshots, url];
      setEditedScreenshots(updatedScreenshots);

      // Show success message
      toast.success('Screenshot uploaded successfully (will be saved when you save the journal)');
    } catch (error) {
      console.error('Error uploading screenshot:', error);
      toast.error('Failed to upload screenshot');
    } finally {
      // Reset uploading state
      setIsUploading(false);
      setUploadProgress(0);

      // Clear the file input if needed
      if (event.target) {
        event.target.value = '';
      }
    }
  };

  const removeScreenshot = (index: number) => {
    try {
      // Update the local state immediately for a responsive UI
      const updatedScreenshots = [...editedScreenshots];
      updatedScreenshots.splice(index, 1);
      setEditedScreenshots(updatedScreenshots);

      // Show success message
      toast.success("Screenshot removed (will be saved when you save the journal)");
    } catch (error) {
      console.error("Error removing screenshot:", error);
      toast.error("Failed to remove screenshot");
    }
  };

  // We don't need image viewer functions anymore as we're using ImageDisplay component

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <Skeleton className="h-6 w-1/3" />
          <Skeleton className="h-4 w-1/4 mt-2" />
        </CardHeader>
        <CardContent>
          <Skeleton className="h-20 w-full" />
        </CardContent>
      </Card>
    );
  }

  // If there's no entry and no trades, don't render anything
  if (!entry && !hasTrades) {
    return null;
  }

  // If there's no entry but there are trades, show a card with trading stats and an "Add Journal" button
  if (!entry && hasTrades) {
    return (
      <Card className="overflow-hidden">
        <CardHeader className="pb-3">
          <div className="flex justify-between items-start">
            <div>
              <CardTitle className="text-lg flex items-center">
                <Calendar className="h-4 w-4 mr-2" />
                {formattedDate}
              </CardTitle>
              <CardDescription>
                Trading activity found
                {hasStats && stats.totalTrades > 0 && ` • ${stats.totalTrades} trades`}
              </CardDescription>
            </div>

            {/* Add Journal Button */}
            <Button
              variant="outline"
              size="sm"
              className="gap-1 border-purple-500/50 text-purple-600 hover:bg-purple-50 dark:border-purple-400/30 dark:text-purple-400 dark:hover:bg-purple-900/20"
              onClick={startEditing}
            >
              <Plus className="h-3.5 w-3.5" />
              Add Journal
            </Button>
          </div>
        </CardHeader>

        {/* Show trading statistics even without a journal entry */}
        <CardContent className="pb-4 pt-0">
          {/* Trading Statistics Summary */}
          {hasStats ? (
            <div className="grid grid-cols-3 md:grid-cols-6 gap-2 mb-4">
              {/* Total Trades */}
              <div className="bg-muted/50 p-2 rounded-md transition-all hover:bg-muted hover:shadow-sm cursor-default">
                <div className="text-xs text-muted-foreground">Trades</div>
                <div className="text-sm font-semibold">
                  {stats && typeof stats.totalTrades === 'number' ? stats.totalTrades : 0}
                </div>
              </div>

              {/* Win Rate */}
              <div className="bg-muted/50 p-2 rounded-md transition-all hover:bg-muted hover:shadow-sm cursor-default">
                <div className="text-xs text-muted-foreground">Win Rate</div>
                <div className="text-sm font-semibold">
                  {stats && typeof stats.winRate === 'number' ? `${stats.winRate.toFixed(1)}%` : '0.0%'}
                </div>
              </div>

              {/* Total P&L */}
              <div className="bg-muted/50 p-2 rounded-md transition-all hover:bg-muted hover:shadow-sm cursor-default">
                <div className="text-xs text-muted-foreground">P&L</div>
                <div className={`text-sm font-semibold ${stats && stats.totalProfit >= 0 ? 'text-green-500' : 'text-red-500'}`}>
                  ${stats && typeof stats.totalProfit === 'number' ? stats.totalProfit.toFixed(2) : '0.00'}
                </div>
              </div>

              {/* Win/Loss Ratio */}
              <div className="bg-muted/50 p-2 rounded-md transition-all hover:bg-muted hover:shadow-sm cursor-default">
                <div className="text-xs text-muted-foreground">W/L Ratio</div>
                <div className="text-sm font-semibold">
                  {stats && stats.avgWinLossRatio === Infinity ? '∞' :
                   stats && typeof stats.avgWinLossRatio === 'number' ? stats.avgWinLossRatio.toFixed(2) : '0.00'}
                </div>
              </div>

              {/* Total Volume */}
              <div className="bg-muted/50 p-2 rounded-md transition-all hover:bg-muted hover:shadow-sm cursor-default">
                <div className="text-xs text-muted-foreground">Volume</div>
                <div className="text-sm font-semibold">
                  {stats && typeof stats.totalVolume === 'number' ? stats.totalVolume.toFixed(2) : '0.00'}
                </div>
              </div>

              {/* Profit Factor */}
              <div className="bg-muted/50 p-2 rounded-md transition-all hover:bg-muted hover:shadow-sm cursor-default">
                <div className="text-xs text-muted-foreground">Profit Factor</div>
                <div className="text-sm font-semibold">
                  {stats && stats.profitFactor === Infinity ? '∞' :
                   stats && typeof stats.profitFactor === 'number' ? stats.profitFactor.toFixed(2) : '0.00'}
                </div>
              </div>
            </div>
          ) : (
            <div className="text-sm text-muted-foreground text-center py-2">
              Loading trading statistics...
            </div>
          )}

          {/* Mini Chart - Trading Activity Visualization */}
          {hasTrades && (
            <div className="h-16 mb-4 bg-muted/20 rounded-md overflow-hidden relative group">
              {/* Chart title that appears on hover */}
              <div className="absolute inset-0 bg-black/60 text-white flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity z-10 text-xs font-medium">
                {trades.length} Trade{trades.length !== 1 ? 's' : ''} • ${stats?.totalProfit.toFixed(2) || '0.00'}
              </div>

              {/* Simple bar chart visualization of trades */}
              <div className="flex h-full items-end justify-between px-1">
                {trades.map((trade, index) => {
                  const profit = Number(trade.profit);
                  const isPositive = profit >= 0;
                  const height = Math.min(Math.abs(profit) / 100 * 100, 100); // Scale height based on profit

                  return (
                    <div
                      key={index}
                      className={`w-1 ${isPositive ? 'bg-green-500' : 'bg-red-500'} relative group/bar transition-all hover:w-2 hover:opacity-100`}
                      style={{
                        height: `${Math.max(height, 10)}%`,
                        opacity: 0.7
                      }}
                    >
                      {/* Tooltip that appears on hover over each bar */}
                      <div className="absolute bottom-full mb-1 left-1/2 -translate-x-1/2 bg-black/80 text-white text-[10px] px-1.5 py-0.5 rounded whitespace-nowrap opacity-0 group-hover/bar:opacity-100 pointer-events-none z-20">
                        {trade.symbol}: ${profit.toFixed(2)}
                        <div className="absolute top-full left-1/2 -translate-x-1/2 border-4 border-transparent border-t-black/80"></div>
                      </div>
                    </div>
                  );
                })}
              </div>

              {/* Horizontal zero line */}
              <div className="absolute left-0 right-0 top-1/2 border-t border-dashed border-white/20 pointer-events-none"></div>
            </div>
          )}

          {/* Prompt to add journal entry */}
          <div className="text-center py-3 border border-dashed rounded-md border-purple-200 dark:border-purple-900/50 bg-purple-50/50 dark:bg-purple-900/10">
            <p className="text-sm text-purple-700 dark:text-purple-400">
              Add notes, tags, and screenshots to document your trading day
            </p>
          </div>
        </CardContent>

        {/* Handle edit mode dialog */}
        {isEditing && (
          <Dialog open={true} onOpenChange={(open) => !open && setIsEditing(false)}>
            <DialogContent className="sm:max-w-[600px]">
              <DialogHeader>
                <DialogTitle>Add Journal Entry for {formattedDate}</DialogTitle>
              </DialogHeader>

              <div className="space-y-6 py-4">
                {/* Journal Notes Editor */}
                <div>
                  <h4 className="text-sm font-medium mb-2">Notes</h4>
                  <Textarea
                    value={editedNote}
                    onChange={(e) => setEditedNote(e.target.value)}
                    placeholder="Add your trading notes for this day..."
                    className="min-h-[120px] resize-y"
                  />
                </div>

                {/* Tags Editor */}
                <div>
                  <h4 className="text-sm font-medium mb-2">Tags</h4>
                  <div className="flex flex-wrap gap-2 mb-2">
                    {editedTags.map((tag) => (
                      <Badge key={tag} variant="outline" className="flex items-center gap-1 group">
                        <Tag className="h-3 w-3" />
                        {tag}
                        <X
                          className="h-3 w-3 ml-1 opacity-0 group-hover:opacity-100 cursor-pointer"
                          onClick={() => removeTag(tag)}
                        />
                      </Badge>
                    ))}
                  </div>
                  <div className="flex gap-2">
                    <Input
                      value={newTag}
                      onChange={(e) => setNewTag(e.target.value)}
                      placeholder="Add a tag..."
                      className="flex-1"
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          e.preventDefault();
                          addTag();
                        }
                      }}
                    />
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={addTag}
                      disabled={!newTag.trim()}
                    >
                      <Plus className="h-4 w-4 mr-1" />
                      Add
                    </Button>
                  </div>
                </div>

                {/* Screenshots Editor */}
                <div>
                  <h4 className="text-sm font-medium mb-2">Screenshots</h4>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-2 mb-3">
                    {editedScreenshots.map((screenshot, index) => (
                      <div key={index} className="relative group">
                        <ImageDisplay
                          src={screenshot}
                          alt={`Screenshot ${index + 1}`}
                          aspectRatio="video"
                          lightboxGroup={editedScreenshots}
                          lightboxIndex={index}
                          className="rounded-md border hover:border-purple-500/50 dark:hover:border-purple-400/50 transition-colors"
                        />
                        <Button
                          variant="destructive"
                          size="icon"
                          className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity h-7 w-7 z-10"
                          onClick={(e) => {
                            e.stopPropagation();
                            removeScreenshot(index);
                          }}
                        >
                          <Trash2 className="h-3.5 w-3.5" />
                        </Button>
                      </div>
                    ))}

                    {/* Upload button */}
                    <label
                      className={cn(
                        "relative aspect-video rounded-md border border-dashed flex flex-col items-center justify-center cursor-pointer bg-muted/30 hover:bg-muted/50 transition-colors",
                        isUploading && "pointer-events-none"
                      )}
                    >
                      {isUploading ? (
                        <>
                          <div className="absolute inset-0 bg-background/80 flex flex-col items-center justify-center z-10">
                            <div className="w-full max-w-[80%] h-2 bg-muted rounded-full overflow-hidden mb-2">
                              <div
                                className="h-full bg-primary transition-all duration-300 ease-out"
                                style={{ width: `${uploadProgress}%` }}
                              />
                            </div>
                            <span className="text-xs text-muted-foreground">Uploading... {Math.round(uploadProgress)}%</span>
                          </div>
                          <Plus className="h-6 w-6 mb-1 text-muted-foreground opacity-50" />
                          <span className="text-xs text-muted-foreground opacity-50">Uploading...</span>
                        </>
                      ) : (
                        <>
                          <Plus className="h-6 w-6 mb-1 text-muted-foreground" />
                          <span className="text-xs text-muted-foreground">Add Screenshot</span>
                        </>
                      )}
                      <input
                        type="file"
                        className="sr-only"
                        accept="image/*"
                        onChange={handleFileUpload}
                        disabled={isUploading}
                      />
                    </label>
                  </div>
                </div>
              </div>

              <DialogFooter>
                <Button
                  variant="outline"
                  onClick={() => setIsEditing(false)}
                  disabled={isSaving}
                >
                  Cancel
                </Button>
                <Button
                  onClick={saveJournalEntry}
                  disabled={isSaving}
                >
                  {isSaving ? 'Saving...' : 'Save Journal Entry'}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        )}
      </Card>
    );
  }

  return (
    <Card className="overflow-hidden">
      <Collapsible
        open={isOpen}
        onOpenChange={setIsOpen}>
        <CardHeader className="pb-3">
          <div className="flex justify-between items-start">
            <div>
              <CardTitle className="text-lg flex items-center">
                <Calendar className="h-4 w-4 mr-2" />
                {formattedDate}
              </CardTitle>
              <CardDescription>
                Daily trading journal
                {hasStats && stats.totalTrades > 0 && ` • ${stats.totalTrades} trades`}
              </CardDescription>
            </div>

            <div className="flex items-center gap-1">
              {/* Edit Button */}
              {!isEditing && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0 transition-colors hover:bg-purple-100 dark:hover:bg-purple-900/20"
                  onClick={startEditing}
                  title="Edit journal entry"
                >
                  <Edit className="h-4 w-4 text-purple-500" />
                </Button>
              )}

              {/* Save/Cancel Buttons (only shown in edit mode) */}
              {isEditing && (
                <>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0 text-red-500 hover:text-red-600 transition-all hover:bg-red-50 dark:hover:bg-red-900/20"
                    onClick={cancelEditing}
                    title="Cancel editing"
                    disabled={isSaving}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0 text-green-500 hover:text-green-600 transition-all hover:bg-green-50 dark:hover:bg-green-900/20"
                    onClick={saveJournalEntry}
                    title="Save changes"
                    disabled={isSaving}
                  >
                    <Save className="h-4 w-4" />
                    {isSaving && <span className="sr-only">Saving...</span>}
                  </Button>
                </>
              )}

              {/* Expand/Collapse Button (hidden in edit mode) */}
              {!isEditing && (
                <CollapsibleTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0 transition-colors hover:bg-purple-100 dark:hover:bg-purple-900/20"
                  >
                    {isOpen ?
                      <ChevronUp className="h-4 w-4 text-purple-500" /> :
                      <ChevronDown className="h-4 w-4 text-purple-500" />
                    }
                  </Button>
                </CollapsibleTrigger>
              )}
            </div>
          </div>
        </CardHeader>
        {/* COLLAPSED STATE - Always visible */}
        <CardContent className="pb-4 pt-0">
          {/* Trading Statistics Summary */}
          {hasStats ? (
            <div className="grid grid-cols-3 md:grid-cols-6 gap-2 mb-4">
              {/* Total Trades */}
              <div className="bg-muted/50 p-2 rounded-md border border-border">
                <div className="text-xs text-muted-foreground">Trades</div>
                <div className="text-sm font-semibold">
                  {stats && typeof stats.totalTrades === 'number' ? stats.totalTrades : 0}
                </div>
              </div>

              {/* Win Rate */}
              <div className="bg-muted/50 p-2 rounded-md border border-border">
                <div className="text-xs text-muted-foreground">Win Rate</div>
                <div className="text-sm font-semibold">
                  {stats && typeof stats.winRate === 'number' ? `${stats.winRate.toFixed(1)}%` : '0.0%'}
                </div>
              </div>

              {/* Total P&L */}
              <div className="bg-muted/50 p-2 rounded-md border border-border">
                <div className="text-xs text-muted-foreground">P&L</div>
                <div className={`text-sm font-semibold ${stats && stats.totalProfit >= 0 ? 'text-green-500' : 'text-red-500'}`}>
                  ${stats && typeof stats.totalProfit === 'number' ? stats.totalProfit.toFixed(2) : '0.00'}
                </div>
              </div>

              {/* Win/Loss Ratio */}
              <div className="bg-muted/50 p-2 rounded-md border border-border">
                <div className="text-xs text-muted-foreground">W/L Ratio</div>
                <div className="text-sm font-semibold">
                  {stats && stats.avgWinLossRatio === Infinity ? '∞' :
                   stats && typeof stats.avgWinLossRatio === 'number' ? stats.avgWinLossRatio.toFixed(2) : '0.00'}
                </div>
              </div>

              {/* Total Volume */}
              <div className="bg-muted/50 p-2 rounded-md border border-border">
                <div className="text-xs text-muted-foreground">Volume</div>
                <div className="text-sm font-semibold">
                  {stats && typeof stats.totalVolume === 'number' ? stats.totalVolume.toFixed(2) : '0.00'}
                </div>
              </div>

              {/* Profit Factor */}
              <div className="bg-muted/50 p-2 rounded-md border border-border">
                <div className="text-xs text-muted-foreground">Profit Factor</div>
                <div className="text-sm font-semibold">
                  {stats && stats.profitFactor === Infinity ? '∞' :
                   stats && typeof stats.profitFactor === 'number' ? stats.profitFactor.toFixed(2) : '0.00'}
                </div>
              </div>
            </div>
          ) : hasAnyContent ? (
            <div className="text-sm text-muted-foreground text-center py-2">
              No trading activity recorded, but journal content available
            </div>
          ) : (
            <div className="text-sm text-muted-foreground text-center py-2">
              No trading activity or journal content for this day
            </div>
          )}

          {/* Mini Chart - Trading Activity Visualization */}
          {hasTrades && (
            <div className="h-16 mb-4 bg-muted/20 rounded-md overflow-hidden relative group border border-border">
              {/* Chart title that appears on hover */}
              <div className="absolute inset-0 bg-black/60 text-white flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity z-10 text-xs font-medium">
                {trades.length} Trade{trades.length !== 1 ? 's' : ''} • ${stats?.totalProfit.toFixed(2) || '0.00'}
              </div>

              {/* Simple bar chart visualization of trades */}
              <div className="flex h-full items-end justify-between px-1">
                {trades.map((trade, index) => {
                  const profit = Number(trade.profit);
                  const isPositive = profit >= 0;
                  const height = Math.min(Math.abs(profit) / 100 * 100, 100); // Scale height based on profit

                  return (
                    <div
                      key={index}
                      className={`w-1 ${isPositive ? 'bg-green-500' : 'bg-red-500'} relative group/bar`}
                      style={{
                        height: `${Math.max(height, 10)}%`,
                        opacity: 0.8
                      }}
                    >
                      {/* Tooltip that appears on hover over each bar */}
                      <div className="absolute bottom-full mb-1 left-1/2 -translate-x-1/2 bg-black/80 text-white text-[10px] px-1.5 py-0.5 rounded whitespace-nowrap opacity-0 group-hover/bar:opacity-100 pointer-events-none z-20">
                        {trade.symbol}: ${profit.toFixed(2)}
                        <div className="absolute top-full left-1/2 -translate-x-1/2 border-4 border-transparent border-t-black/80"></div>
                      </div>
                    </div>
                  );
                })}
              </div>

              {/* Horizontal zero line */}
              <div className="absolute left-0 right-0 top-1/2 border-t border-dashed border-white/20 pointer-events-none"></div>
            </div>
          )}
        </CardContent>

        {/* EXPANDED STATE - Only visible when expanded */}
        <CollapsibleContent>
          <CardContent className="pt-0 pb-4">
            {isEditing ? (
              /* EDIT MODE */
              <div className="space-y-6">
                {/* Journal Notes Editor */}
                <div>
                  <h4 className="text-sm font-medium mb-2">Notes</h4>
                  <Textarea
                    value={editedNote}
                    onChange={(e) => setEditedNote(e.target.value)}
                    placeholder="Add your trading notes for this day..."
                    className="min-h-[120px] resize-y"
                  />
                </div>

                {/* Tags Editor */}
                <div>
                  <h4 className="text-sm font-medium mb-2">Tags</h4>
                  <div className="flex flex-wrap gap-2 mb-2">
                    {editedTags.map((tag) => (
                      <Badge key={tag} variant="outline" className="flex items-center gap-1 group">
                        <Tag className="h-3 w-3" />
                        {tag}
                        <X
                          className="h-3 w-3 ml-1 opacity-0 group-hover:opacity-100 cursor-pointer"
                          onClick={() => removeTag(tag)}
                        />
                      </Badge>
                    ))}
                  </div>
                  <div className="flex gap-2">
                    <Input
                      value={newTag}
                      onChange={(e) => setNewTag(e.target.value)}
                      placeholder="Add a tag..."
                      className="flex-1"
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          e.preventDefault();
                          addTag();
                        }
                      }}
                    />
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={addTag}
                      disabled={!newTag.trim()}
                    >
                      <Plus className="h-4 w-4 mr-1" />
                      Add
                    </Button>
                  </div>
                </div>

                {/* Screenshots Editor */}
                <div>
                  <h4 className="text-sm font-medium mb-2">Screenshots</h4>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-2 mb-3">
                    {editedScreenshots.map((screenshot, index) => (
                      <div
                        key={index}
                        className="relative aspect-video rounded-md overflow-hidden border group"
                      >
                        <Image
                          src={screenshot}
                          alt={`Screenshot ${index + 1}`}
                          fill
                          sizes="(max-width: 768px) 50vw, 33vw"
                          className="object-cover"
                        />
                        <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                          <Button
                            variant="destructive"
                            size="sm"
                            className="h-8 w-8 p-0 rounded-full"
                            onClick={() => removeScreenshot(index)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ))}

                    {/* Upload button */}
                    <label
                      className={cn(
                        "relative aspect-video rounded-md border border-dashed flex flex-col items-center justify-center cursor-pointer bg-muted/30 hover:bg-muted/50 transition-colors",
                        isUploading && "pointer-events-none"
                      )}
                    >
                      {isUploading ? (
                        <>
                          <div className="absolute inset-0 bg-background/80 flex flex-col items-center justify-center z-10">
                            <div className="w-full max-w-[80%] h-2 bg-muted rounded-full overflow-hidden mb-2">
                              <div
                                className="h-full bg-primary transition-all duration-300 ease-out"
                                style={{ width: `${uploadProgress}%` }}
                              />
                            </div>
                            <span className="text-xs text-muted-foreground">Uploading... {Math.round(uploadProgress)}%</span>
                          </div>
                          <Plus className="h-6 w-6 mb-1 text-muted-foreground opacity-50" />
                          <span className="text-xs text-muted-foreground opacity-50">Uploading...</span>
                        </>
                      ) : (
                        <>
                          <Plus className="h-6 w-6 mb-1 text-muted-foreground" />
                          <span className="text-xs text-muted-foreground">Add Screenshot</span>
                        </>
                      )}
                      <input
                        type="file"
                        className="sr-only"
                        accept="image/*"
                        onChange={handleFileUpload}
                        disabled={isUploading}
                      />
                    </label>
                  </div>
                </div>
              </div>
            ) : (
              /* VIEW MODE */
              <>
                {/* Journal Notes */}
                {hasNotes && (
                  <div className="mb-6">
                    <h4 className="text-sm font-medium mb-2">Notes</h4>
                    <div className="bg-muted/30 p-3 rounded-md border border-border">
                      <p className="whitespace-pre-wrap text-sm">{entry?.note}</p>
                    </div>
                  </div>
                )}

                {/* Tags */}
                {hasTags && (
                  <div className="mb-6">
                    <h4 className="text-sm font-medium mb-2">Tags</h4>
                    <div className="flex flex-wrap gap-2">
                      {entry?.tags?.map((tag) => (
                        <Badge key={tag} variant="outline" className="flex items-center gap-1">
                          <Tag className="h-3 w-3" />
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                {/* Screenshots Gallery */}
                {hasScreenshots && (
                  <div className="mb-6">
                    <h4 className="text-sm font-medium mb-2">Screenshots</h4>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                      {entry?.screenshots?.map((screenshot, index) => (
                        <div key={index} className="relative group">
                          <ImageDisplay
                            src={screenshot}
                            alt={`Screenshot ${index + 1} for ${formattedDate}`}
                            aspectRatio="video"
                            lightboxGroup={entry.screenshots}
                            lightboxIndex={index}
                            className="rounded-md border border-border"
                          />
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Trade List - Formatted Table */}
                {hasTrades && (
                  <div className="mb-4">
                    <h4 className="text-sm font-medium mb-2">Trades</h4>
                    <div className="border rounded-md overflow-hidden border-border">
                      <div className="overflow-x-auto">
                        <table className="w-full min-w-full">
                          <thead>
                            <tr className="bg-muted/50">
                              <th className="px-2 sm:px-4 py-2 text-left text-xs font-medium text-muted-foreground">Symbol</th>
                              <th className="px-2 sm:px-4 py-2 text-left text-xs font-medium text-muted-foreground">Type</th>
                              <th className="px-2 sm:px-4 py-2 text-left text-xs font-medium text-muted-foreground hidden xs:table-cell">Entry</th>
                              <th className="px-2 sm:px-4 py-2 text-left text-xs font-medium text-muted-foreground hidden xs:table-cell">Exit</th>
                              <th className="px-2 sm:px-4 py-2 text-left text-xs font-medium text-muted-foreground hidden sm:table-cell">Duration</th>
                              <th className="px-2 sm:px-4 py-2 text-left text-xs font-medium text-muted-foreground hidden md:table-cell">Strategy</th>
                              <th className="px-2 sm:px-4 py-2 text-right text-xs font-medium text-muted-foreground">P&L</th>
                            </tr>
                          </thead>
                          <tbody>
                            {trades.map((trade, i) => {
                              const entryTime = new Date(trade.time_open);
                              const exitTime = new Date(trade.time_close);
                              const durationMs = exitTime.getTime() - entryTime.getTime();
                              const durationMinutes = Math.floor(durationMs / (1000 * 60));
                              const profit = Number(trade.profit);

                              return (
                                <tr key={i} className="border-t hover:bg-muted/50">
                                  <td className="px-2 sm:px-4 py-2 text-xs sm:text-sm">{trade.symbol}</td>
                                  <td className="px-2 sm:px-4 py-2 text-xs sm:text-sm">
                                    <span className={trade.type === 0 || trade.type === "buy" ? "text-emerald-500" : "text-rose-500"}>
                                      {trade.type === 0 ? "BUY" : trade.type === 1 ? "SELL" : trade.type.toUpperCase()}
                                    </span>
                                  </td>
                                  <td className="px-2 sm:px-4 py-2 text-xs sm:text-sm hidden xs:table-cell">{format(entryTime, "HH:mm:ss")}</td>
                                  <td className="px-2 sm:px-4 py-2 text-xs sm:text-sm hidden xs:table-cell">{format(exitTime, "HH:mm:ss")}</td>
                                  <td className="px-2 sm:px-4 py-2 text-xs sm:text-sm hidden sm:table-cell">{durationMinutes}m</td>
                                  <td className="px-2 sm:px-4 py-2 text-xs sm:text-sm hidden md:table-cell">
                                    {trade.strategies?.name || trade.strategy_name ? (
                                      <span className="px-2 py-0.5 bg-muted text-muted-foreground rounded-full text-xs">
                                        {trade.strategies?.name || trade.strategy_name}
                                      </span>
                                    ) : (
                                      <span className="text-muted-foreground text-xs">—</span>
                                    )}
                                  </td>
                                  <td className={cn(
                                    "px-2 sm:px-4 py-2 text-xs sm:text-sm text-right font-medium",
                                    profit >= 0 ? "text-emerald-500" : "text-rose-500"
                                  )}>
                                    ${profit.toFixed(2)}
                                  </td>
                                </tr>
                              );
                            })}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  </div>
                )}
              </>
            )}
          </CardContent>
        </CollapsibleContent>
      </Collapsible>

      {/* We're using the ImageDisplay component instead of a custom modal */}
    </Card>
  );
}
