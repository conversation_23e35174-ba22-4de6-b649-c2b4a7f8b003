"use client"

import dynamic from 'next/dynamic';
import { Strategy } from '@/types/playbook';

// Dynamically import the client component with no SSR
const PlaybookClient = dynamic(() => import('./client'), {
  ssr: false
});

interface ClientWrapperProps {
  userId: string;
  initialStrategies: Strategy[];
  prefetchedData?: any;
}

export default function ClientWrapper({ userId, initialStrategies, prefetchedData }: ClientWrapperProps) {
  return <PlaybookClient
    userId={userId}
    initialStrategies={initialStrategies}
    prefetchedData={prefetchedData}
  />;
}
