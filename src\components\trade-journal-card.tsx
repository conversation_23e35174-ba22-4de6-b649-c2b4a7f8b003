"use client"

import { useState, useRef, useEffect } from "react"
import { format } from "date-fns"
import {
  ChevronDown,
  ChevronUp,
  Calendar,
  FileText,
  ImageIcon,
  BarChart3,
  DollarSign,
  TrendingUp,
  BookOpen,
  Edit,
  Tag
} from "lucide-react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { Button } from "@/components/ui/button"
import { ImageDisplay } from "@/components/ui/image-display"
import { JournalPnLChart } from "@/components/journal-pnl-chart"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"

interface TradeJournalCardProps {
  trade: any
  strategyName?: string
  className?: string
  onEdit?: (trade: any) => void
}

export function TradeJournalCard({ trade, strategyName, className, onEdit }: TradeJournalCardProps) {
  const [isOpen, setIsOpen] = useState(false)

  // Format currency values
  const formatCurrency = (value: number) => {
    if (value === 0) return "$0.00"
    return value > 0
      ? `$${value.toFixed(2)}`
      : `$-${Math.abs(value).toFixed(2)}`
  }

  // Check if the trade has notes, screenshots, or tags
  const hasNotes = trade.notes && trade.notes.trim().length > 0
  const hasScreenshots = Array.isArray(trade.screenshots) && trade.screenshots.length > 0
  const hasTags = Array.isArray(trade.tags) && trade.tags.length > 0

  return (
    <Card className={cn(
      "w-full relative overflow-hidden rounded-lg border-l-[3px]",
      "border-l-purple-600/80 dark:border-l-white/20",
      className
    )}>
      <Collapsible open={isOpen} onOpenChange={setIsOpen}>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center space-x-2">
              <CollapsibleTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0 text-muted-foreground hover:text-foreground transition-colors"
                >
                  {isOpen ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                </Button>
              </CollapsibleTrigger>
              <div className="flex items-center space-x-2">
                <Calendar className="h-4 w-4 text-purple-500 dark:text-muted-foreground" />
                <div>
                  <CardTitle className="text-base">
                    Trade Journal: {trade.symbol} {format(new Date(trade.time_close), "MMM d, yyyy")}
                  </CardTitle>
                  <div className="text-xs text-muted-foreground mt-0.5 flex items-center">
                    {!isOpen && (
                      <span className="flex items-center">
                        <ChevronDown className="h-3 w-3 mr-1 inline" />
                        Expand to view trade details
                      </span>
                    )}
                    {isOpen && (
                      <span>
                        {[
                          hasNotes ? "notes" : null,
                          hasScreenshots ? `${trade.screenshots.length} screenshot${trade.screenshots.length > 1 ? 's' : ''}` : null,
                          hasTags ? `${trade.tags.length} tag${trade.tags.length > 1 ? 's' : ''}` : null
                        ].filter(Boolean).join(", ")}
                      </span>
                    )}
                  </div>
                </div>
              </div>
              {onEdit && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    onEdit(trade);
                  }}
                  className="h-8 w-8 p-0 text-muted-foreground hover:text-foreground transition-colors"
                >
                  <Edit className="h-4 w-4" />
                  <span className="sr-only">Edit trade details</span>
                </Button>
              )}
            </div>
          </div>
        </CardHeader>

        <CardContent className="pb-3">
          {/* Trade Summary with Chart */}
          <div className="flex flex-col md:flex-row gap-4">
            {/* P&L Chart on the left */}
            <div className="md:w-1/3 h-[120px] bg-muted/20 rounded-md overflow-hidden">
              <JournalPnLChart trades={[trade]} />
            </div>

            {/* Metrics on the right */}
            <div className="md:w-2/3 grid grid-cols-2 md:grid-cols-3 gap-3">
              <div className="dark:bg-muted/30 bg-background p-3 rounded-md">
                <div className="flex items-center justify-between">
                  <div className="text-xs text-muted-foreground">Symbol</div>
                  <BarChart3 className="h-3.5 w-3.5 text-muted-foreground" />
                </div>
                <div className="text-lg font-medium">{trade.symbol}</div>
              </div>

              <div className="dark:bg-muted/30 bg-background p-3 rounded-md">
                <div className="flex items-center justify-between">
                  <div className="text-xs text-muted-foreground">Type</div>
                  <TrendingUp className="h-3.5 w-3.5 text-muted-foreground" />
                </div>
                <div className="text-lg font-medium">{trade.type.toUpperCase()}</div>
              </div>

              <div className="dark:bg-muted/30 bg-background p-3 rounded-md">
                <div className="flex items-center justify-between">
                  <div className="text-xs text-muted-foreground">P&L</div>
                  <DollarSign className="h-3.5 w-3.5 text-muted-foreground" />
                </div>
                <div className={cn(
                  "text-lg font-medium",
                  trade.profit > 0 ? "text-emerald-500" :
                  trade.profit < 0 ? "text-rose-500" : ""
                )}>
                  {formatCurrency(trade.profit)}
                </div>
              </div>

              <div className="dark:bg-muted/30 bg-background p-3 rounded-md">
                <div className="flex items-center justify-between">
                  <div className="text-xs text-muted-foreground">Volume</div>
                  <BarChart3 className="h-3.5 w-3.5 text-muted-foreground" />
                </div>
                <div className="text-lg font-medium">{trade.volume}</div>
              </div>

              <div className="dark:bg-muted/30 bg-background p-3 rounded-md">
                <div className="flex items-center justify-between">
                  <div className="text-xs text-muted-foreground">Strategy</div>
                  <BookOpen className="h-3.5 w-3.5 text-muted-foreground" />
                </div>
                <div className="text-lg font-medium truncate">
                  {strategyName || (trade.strategy_id ? "Loading..." : "None")}
                </div>
              </div>

              <div className="dark:bg-muted/30 bg-background p-3 rounded-md">
                <div className="flex items-center justify-between">
                  <div className="text-xs text-muted-foreground">Date</div>
                  <Calendar className="h-3.5 w-3.5 text-muted-foreground" />
                </div>
                <div className="text-lg font-medium">
                  {format(new Date(trade.time_close), "MMM d")}
                </div>
              </div>
            </div>
          </div>
        </CardContent>

        <CollapsibleContent>
          <div className="border-t px-4 py-3">
            {/* Trade Notes */}
            {hasNotes ? (
              <div className="space-y-2 mb-6">
                <div className="flex items-center justify-between">
                  <div className="text-sm font-medium">Trade Notes</div>
                </div>
                <div className="text-sm bg-muted/30 p-3 rounded-md whitespace-pre-wrap">
                  {trade.notes}
                </div>
              </div>
            ) : null}

            {/* Tags */}
            {hasTags ? (
              <div className="space-y-2 mb-6">
                <div className="text-sm font-medium mb-2">Tags</div>
                <div className="flex flex-wrap gap-2">
                  {trade.tags.map((tag: string, index: number) => (
                    <Badge
                      key={index}
                      variant="outline"
                      className="bg-purple-50 text-purple-700 border-purple-200 hover:bg-purple-100 dark:bg-purple-900/20 dark:text-purple-300 dark:border-purple-800/50"
                    >
                      {tag}
                    </Badge>
                  ))}
                </div>
              </div>
            ) : null}

            {/* Screenshots */}
            {hasScreenshots ? (
              <div className="space-y-2 mb-6">
                <div className="text-sm font-medium mb-2">Screenshots</div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {trade.screenshots.map((screenshot: string, index: number) => (
                    <div key={index} className="relative group">
                      <ImageDisplay
                        src={screenshot}
                        alt={`Trade screenshot ${index + 1}`}
                        aspectRatio="video"
                        lightboxGroup={trade.screenshots}
                        lightboxIndex={index}
                      />
                    </div>
                  ))}
                </div>
              </div>
            ) : null}
          </div>
        </CollapsibleContent>
      </Collapsible>
    </Card>
  )
}
