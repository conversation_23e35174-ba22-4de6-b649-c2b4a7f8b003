import { cookies } from 'next/headers';
import { createServerClient } from '@supabase/ssr';
import { redirect } from 'next/navigation';
import type { Database } from '@/types/supabase';
import { Strategy } from '@/types/playbook';
import ClientWrapper from './client-wrapper';

export default async function PlaybookPage() {
  // Get cookies for server-side Supabase client
  const cookieStore = await cookies();

  // Create a Supabase client with proper cookie handling
  const supabase = createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value;
        },
        set(_name: string, _value: string, _options: any) {
          // Server components can't set cookies
        },
        remove(_name: string, _options: any) {
          // Server components can't remove cookies
        }
      },
    }
  );

  // Get authenticated user
  const { data: { user }, error: userError } = await supabase.auth.getUser();
  if (userError || !user) {
    console.error('Authentication error:', userError);
    redirect('/auth');
  }

  const userId = user.id;

  // Fetch strategies from the database
  const { data: strategiesData, error } = await supabase
    .from('strategies')
    .select('*')
    .eq('user_id', userId)
    .order('created_at', { ascending: false });

  if (error) {
    console.error('Error fetching strategies:', error);
    // We'll still render the page, but with empty strategies
  }

  // Convert to Strategy type
  const strategies = (strategiesData || []) as Strategy[];

  // Prefetch additional data if we have strategies
  let prefetchedData: any = {};

  if (strategies && strategies.length > 0) {
    try {
      const firstStrategy = strategies[0];

      // Prefetch setups for the first strategy
      const { data: setups } = await supabase
        .from('setups')
        .select('*')
        .eq('strategy_id', firstStrategy.id)
        .eq('user_id', userId);

      // Prefetch rules for the first strategy
      const { data: rules } = await supabase
        .from('strategy_rules')
        .select('*')
        .eq('strategy_id', firstStrategy.id)
        .eq('user_id', userId);

      // Prefetch performance data for the first strategy
      const { data: performance } = await supabase
        .from('strategy_performance')
        .select('*')
        .eq('strategy_id', firstStrategy.id)
        .eq('user_id', userId)
        .order('period_start', { ascending: false })
        .limit(1);

      prefetchedData = {
        setups: setups || [],
        rules: rules || [],
        performance: performance && performance.length > 0 ? performance[0] : null
      };
    } catch (prefetchError) {
      console.error('Error prefetching additional data:', prefetchError);
      // Continue without the prefetched data
    }
  }

  // Render the client component with the fetched data
  return <ClientWrapper
    userId={userId}
    initialStrategies={strategies}
    prefetchedData={prefetchedData}
  />;
}
