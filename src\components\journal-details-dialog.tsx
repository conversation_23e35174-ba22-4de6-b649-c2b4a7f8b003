"use client"

import { format } from "date-fns"
import { useState, useEffect } from "react"
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
  DialogDescription
} from "@/components/ui/dialog"
import { Card, CardContent } from "@/components/ui/card"
import { JournalPnLChart } from "@/components/journal-pnl-chart"
import { ImageDisplay } from "@/components/ui/image-display"
import { getJournalEntryForDate } from "@/lib/journal-utils"
import { cn } from "@/lib/utils"
import { BarChart3, DollarSign, TrendingUp, TrendingDown } from "lucide-react"
import { useUser } from "@/hooks/use-user"

interface JournalDetailsDialogProps {
  isOpen: boolean
  onOpenChange: (open: boolean) => void
  selectedDate: Date | undefined
  selectedTrades: any[]
}

export function JournalDetailsDialog({
  isOpen,
  onOpenChange,
  selectedDate,
  selectedTrades
}: JournalDetailsDialogProps) {
  const { user } = useUser()
  const [journalEntry, setJournalEntry] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(false)

  // Fetch journal entry when dialog opens or date changes
  useEffect(() => {
    if (!selectedDate || !isOpen || !user?.id) return

    const fetchJournalEntry = async () => {
      setIsLoading(true)
      try {
        // Get the selected account ID from localStorage
        const selectedAccountId = localStorage.getItem('selectedAccountId')

        // Fetch the journal entry
        const entry = await getJournalEntryForDate(selectedDate, user.id, selectedAccountId || undefined)
        setJournalEntry(entry)
      } catch (error) {
        console.error("Error fetching journal entry:", error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchJournalEntry()
  }, [selectedDate, isOpen, user?.id])

  if (!selectedDate) return null

  // Calculate metrics
  const calculateMetrics = () => {
    if (!selectedTrades || selectedTrades.length === 0) {
      return {
        totalTrades: 0,
        winRate: 0,
        totalProfit: 0,
        averageWin: 0,
        averageLoss: 0,
        profitFactor: 0,
        volume: 0
      }
    }

    const winningTrades = selectedTrades.filter(trade => trade.profit > 0)
    const losingTrades = selectedTrades.filter(trade => trade.profit < 0)

    const totalProfit = selectedTrades.reduce((sum, trade) => sum + trade.profit, 0)
    const totalWinnings = winningTrades.reduce((sum, trade) => sum + trade.profit, 0)
    const totalLosses = Math.abs(losingTrades.reduce((sum, trade) => sum + trade.profit, 0))

    const averageWin = winningTrades.length > 0
      ? totalWinnings / winningTrades.length
      : 0

    const averageLoss = losingTrades.length > 0
      ? totalLosses / losingTrades.length
      : 0

    const profitFactor = totalLosses > 0
      ? totalWinnings / totalLosses
      : totalWinnings > 0 ? Infinity : 0

    const volume = selectedTrades.reduce((sum, trade) => sum + trade.volume, 0)

    return {
      totalTrades: selectedTrades.length,
      winRate: selectedTrades.length > 0 ? (winningTrades.length / selectedTrades.length) * 100 : 0,
      totalProfit,
      averageWin,
      averageLoss,
      profitFactor,
      volume
    }
  }

  const metrics = calculateMetrics()

  // Format currency values
  const formatCurrency = (value: number) => {
    if (value === 0) return "$0.00"

    return value > 0
      ? `$${value.toFixed(2)}`
      : `$-${Math.abs(value).toFixed(2)}`
  }

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            Trading Journal: {selectedDate ? format(selectedDate, "MMMM d, yyyy") : ""}
          </DialogTitle>
          <DialogDescription>
            Your trading journal entry and performance metrics for this day.
          </DialogDescription>
        </DialogHeader>

        {/* P&L Chart */}
        <div className="h-[180px] bg-muted/20 rounded-md overflow-hidden mb-4">
          <JournalPnLChart trades={selectedTrades} />
        </div>

        {/* Metrics */}
        <div className="grid grid-cols-2 md:grid-cols-3 gap-3 mb-6">
          <div className="dashboard-card bg-card p-3 rounded-md">
            <div className="flex items-center justify-between">
              <div className="text-xs text-muted-foreground">Total Trades</div>
              <BarChart3 className="h-3.5 w-3.5 text-foreground dark:text-foreground" />
            </div>
            <div className="text-lg font-medium">{metrics.totalTrades}</div>
          </div>

          <div className="dashboard-card bg-card p-3 rounded-md">
            <div className="flex items-center justify-between">
              <div className="text-xs text-muted-foreground">Win Rate</div>
              <TrendingUp className="h-3.5 w-3.5 text-foreground dark:text-foreground" />
            </div>
            <div className="text-lg font-medium">{metrics.winRate.toFixed(1)}%</div>
          </div>

          <div className="dashboard-card bg-card p-3 rounded-md">
            <div className="flex items-center justify-between">
              <div className="text-xs text-muted-foreground">Total P&L</div>
              <DollarSign className="h-3.5 w-3.5 text-foreground dark:text-foreground" />
            </div>
            <div className={cn(
              "text-lg font-medium",
              metrics.totalProfit > 0 ? "text-emerald-500" :
              metrics.totalProfit < 0 ? "text-rose-500" : ""
            )}>
              {formatCurrency(metrics.totalProfit)}
            </div>
          </div>

          <div className="dashboard-card bg-card p-3 rounded-md">
            <div className="flex items-center justify-between">
              <div className="text-xs text-muted-foreground">Avg Win/Loss</div>
              <TrendingDown className="h-3.5 w-3.5 text-foreground dark:text-foreground" />
            </div>
            <div className="text-lg font-medium">
              {metrics.averageLoss > 0
                ? (metrics.averageWin / metrics.averageLoss).toFixed(2)
                : metrics.averageWin > 0 ? "∞" : "0.00"}
            </div>
          </div>

          <div className="dashboard-card bg-card p-3 rounded-md">
            <div className="flex items-center justify-between">
              <div className="text-xs text-muted-foreground">Volume</div>
              <BarChart3 className="h-3.5 w-3.5 text-foreground dark:text-foreground" />
            </div>
            <div className="text-lg font-medium">
              {metrics.volume.toFixed(2)}
            </div>
          </div>

          <div className="dashboard-card bg-card p-3 rounded-md">
            <div className="flex items-center justify-between">
              <div className="text-xs text-muted-foreground">Profit Factor</div>
              <TrendingUp className="h-3.5 w-3.5 text-foreground dark:text-foreground" />
            </div>
            <div className="text-lg font-medium">
              {metrics.profitFactor === Infinity ? "∞" : metrics.profitFactor.toFixed(2)}
            </div>
          </div>
        </div>

        {/* Journal Notes */}
        {isLoading ? (
          <div className="mb-6">
            <h3 className="text-sm font-medium mb-2">Trading Notes</h3>
            <div className="text-sm bg-muted/30 p-3 rounded-md h-20 animate-pulse"></div>
          </div>
        ) : journalEntry?.note ? (
          <div className="mb-6">
            <h3 className="text-sm font-medium mb-2">Trading Notes</h3>
            <div className="text-sm bg-muted/30 p-3 rounded-md whitespace-pre-wrap">
              {journalEntry.note}
            </div>
          </div>
        ) : null}

        {/* Screenshots */}
        {isLoading ? (
          <div className="mb-6">
            <h3 className="text-sm font-medium mb-2">Screenshots</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-muted/30 rounded-md h-40 animate-pulse"></div>
              <div className="bg-muted/30 rounded-md h-40 animate-pulse"></div>
            </div>
          </div>
        ) : journalEntry?.screenshots && journalEntry.screenshots.length > 0 ? (
          <div className="mb-6">
            <h3 className="text-sm font-medium mb-2">Screenshots</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {journalEntry.screenshots.map((screenshot: string, index: number) => (
                <div key={index} className="relative">
                  <ImageDisplay
                    src={screenshot}
                    alt={`Trading screenshot ${index + 1}`}
                    aspectRatio="video"
                    lightboxGroup={journalEntry.screenshots}
                    lightboxIndex={index}
                  />
                </div>
              ))}
            </div>
          </div>
        ) : null}

        {/* Trades Table */}
        <div>
          <h3 className="text-sm font-medium mb-2">Trades</h3>
          <div className="border rounded-md overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="bg-muted/50">
                  <th className="px-2 py-2 text-left text-xs font-medium text-muted-foreground">Symbol</th>
                  <th className="px-2 py-2 text-left text-xs font-medium text-muted-foreground">Type</th>
                  <th className="px-2 py-2 text-left text-xs font-medium text-muted-foreground">Entry</th>
                  <th className="px-2 py-2 text-left text-xs font-medium text-muted-foreground">Exit</th>
                  <th className="px-2 py-2 text-right text-xs font-medium text-muted-foreground">Size</th>
                  <th className="px-2 py-2 text-right text-xs font-medium text-muted-foreground">P&L</th>
                </tr>
              </thead>
              <tbody>
                {selectedTrades.map((trade, i) => (
                  <tr key={i} className="border-t hover:bg-muted/50">
                    <td className="px-2 py-2 text-xs">{trade.symbol}</td>
                    <td className="px-2 py-2 text-xs">
                      <span className={cn(
                        "px-2 py-0.5 rounded-full text-xs",
                        trade.type === "buy" ? "bg-emerald-100 text-emerald-700 dark:bg-emerald-900/30 dark:text-emerald-400" :
                        "bg-rose-100 text-rose-700 dark:bg-rose-900/30 dark:text-rose-400"
                      )}>
                        {trade.type === "buy" ? "Long" : "Short"}
                      </span>
                    </td>
                    <td className="px-2 py-2 text-xs">{format(new Date(trade.time_open), "HH:mm:ss")}</td>
                    <td className="px-2 py-2 text-xs">{format(new Date(trade.time_close), "HH:mm:ss")}</td>
                    <td className="px-2 py-2 text-xs text-right">{parseFloat(trade.volume).toFixed(2)}</td>
                    <td className={cn(
                      "px-2 py-2 text-xs text-right font-medium",
                      trade.profit > 0 ? "text-emerald-500" :
                      trade.profit < 0 ? "text-rose-500" : ""
                    )}>
                      {formatCurrency(trade.profit)}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
