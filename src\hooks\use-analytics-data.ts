"use client"

import { useQuery } from '@tanstack/react-query'
import { Trade } from '@/types/trade'

interface AnalyticsData {
  trades: Trade[]
  summary: {
    initial_balance: number
    [key: string]: any
  } | null
}

// Generic fetch function with error handling
async function fetchApi<T>(url: string, options?: RequestInit): Promise<T> {
  const response = await fetch(url, options)
  
  if (!response.ok) {
    throw new Error(`API error: ${response.status} ${response.statusText}`)
  }
  
  return response.json()
}

// Hook for fetching analytics data
export function useAnalyticsData(accountId?: string | null) {
  return useQuery<AnalyticsData>({
    queryKey: ['analytics', 'data', accountId],
    queryFn: () => fetchApi<AnalyticsData>(`/api/analytics-data?accountId=${accountId}`),
    enabled: !!accountId, // Only run query if accountId is provided
    staleTime: 60 * 1000, // 1 minute
    refetchOnWindowFocus: true,
    refetchOnMount: true,
    refetchOnReconnect: true,
  })
}
