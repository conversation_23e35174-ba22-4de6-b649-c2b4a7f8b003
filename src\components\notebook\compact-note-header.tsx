"use client"

import { useState } from "react"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import {
  MoreHorizontal,
  Plus,
  X,
  Tag,
  Folder,
  Save,
  ChevronDown,
  ChevronUp,
  Settings
} from "lucide-react"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { cn } from "@/lib/utils"

interface CompactNoteHeaderProps {
  title: string
  onTitleChange: (title: string) => void
  category: string
  onCategoryChange: (category: string) => void
  folderId: string | null
  onFolderChange: (folderId: string | null) => void
  tags: string[]
  tagInput: string
  onTagInputChange: (input: string) => void
  onAddTag: () => void
  onRemoveTag: (tag: string) => void
  isTemplate: boolean
  onTemplateChange: (isTemplate: boolean) => void
  onSave: () => void
  onCancel: () => void
  isCreating?: boolean
  folders: Array<{ id: string; name: string }>
  categoryOptions?: string[]
  tagOptions?: string[]
  className?: string
}

export function CompactNoteHeader({
  title,
  onTitleChange,
  category,
  onCategoryChange,
  folderId,
  onFolderChange,
  tags,
  tagInput,
  onTagInputChange,
  onAddTag,
  onRemoveTag,
  isTemplate,
  onTemplateChange,
  onSave,
  onCancel,
  isCreating = false,
  folders = [],
  categoryOptions = [],
  tagOptions = [],
  className
}: CompactNoteHeaderProps) {
  const [showAdvancedOptions, setShowAdvancedOptions] = useState(false)

  const selectedFolder = folders.find(f => f.id === folderId)

  return (
    <div className={cn("space-y-1", className)}>
      <div className="flex items-center justify-between mb-1">
        <h2 className="text-sm font-medium text-muted-foreground">
          {isCreating ? "New Note" : "Edit Note"}
        </h2>
        <div className="flex items-center gap-1">
          <Button
            variant="outline"
            size="sm"
            className="h-7 text-xs"
            onClick={onCancel}
          >
            Cancel
          </Button>
          <Button
            variant="default"
            size="sm"
            className="h-7 text-xs"
            onClick={onSave}
          >
            Save
          </Button>
        </div>
      </div>

      <div className="flex flex-col gap-1">
        <Input
          value={title}
          onChange={(e) => onTitleChange(e.target.value)}
          placeholder="Note title"
          className="text-sm font-medium h-8"
        />

        <div className="flex items-center gap-2">
          {/* Quick folder selector */}
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                size="sm"
                className="h-8 text-xs flex items-center gap-1"
              >
                <Folder className="h-3.5 w-3.5" />
                {selectedFolder ? selectedFolder.name : "No folder"}
                <ChevronDown className="h-3 w-3 ml-1" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-56 p-2">
              <div className="space-y-1">
                <div
                  className={cn(
                    "px-2 py-1 rounded-md text-sm cursor-pointer hover:bg-accent",
                    !folderId && "bg-accent"
                  )}
                  onClick={() => onFolderChange(null)}
                >
                  No folder
                </div>
                {folders.map(folder => (
                  <div
                    key={folder.id}
                    className={cn(
                      "px-2 py-1 rounded-md text-sm cursor-pointer hover:bg-accent",
                      folderId === folder.id && "bg-accent"
                    )}
                    onClick={() => onFolderChange(folder.id)}
                  >
                    {folder.name}
                  </div>
                ))}
              </div>
            </PopoverContent>
          </Popover>

          {/* Quick template toggle */}
          <Button
            variant={isTemplate ? "secondary" : "outline"}
            size="sm"
            className="h-8 text-xs"
            onClick={() => onTemplateChange(!isTemplate)}
          >
            Template
          </Button>

          {/* Advanced options */}
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                size="sm"
                className="h-8 w-8 p-0"
              >
                <Settings className="h-3.5 w-3.5" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-80 p-4">
              <div className="space-y-4">
                <div>
                  <Label htmlFor="category" className="text-xs">Category</Label>
                  <Input
                    id="category"
                    value={category}
                    onChange={(e) => onCategoryChange(e.target.value)}
                    placeholder="Category (optional)"
                    className="h-8 text-sm mt-1"
                    list="categories"
                  />
                  <datalist id="categories">
                    {categoryOptions.map(cat => (
                      <option key={cat} value={cat} />
                    ))}
                  </datalist>
                </div>

                <div>
                  <Label htmlFor="tags" className="text-xs">Tags</Label>
                  <div className="flex gap-1 mt-1">
                    <Input
                      id="tags"
                      value={tagInput}
                      onChange={(e) => onTagInputChange(e.target.value)}
                      placeholder="Add tags"
                      className="h-8 text-sm"
                      list="tags"
                      onKeyDown={(e) => {
                        if (e.key === "Enter") {
                          e.preventDefault();
                          onAddTag();
                        }
                      }}
                    />
                    <Button
                      type="button"
                      onClick={onAddTag}
                      size="sm"
                      className="h-8 w-8 p-0"
                    >
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                  <datalist id="tags">
                    {tagOptions.map(tag => (
                      <option key={tag} value={tag} />
                    ))}
                  </datalist>
                </div>

                {tags.length > 0 && (
                  <div className="flex flex-wrap gap-1">
                    {tags.map(tag => (
                      <Badge
                        key={tag}
                        variant="outline"
                        className="gap-1 text-xs rounded-md"
                      >
                        <Tag className="h-3 w-3" />
                        {tag}
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-4 w-4 p-0 ml-1"
                          onClick={() => onRemoveTag(tag)}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </Badge>
                    ))}
                  </div>
                )}
              </div>
            </PopoverContent>
          </Popover>
        </div>

        {/* Display selected tags */}
        {tags.length > 0 && (
          <div className="flex flex-wrap gap-1">
            {tags.map(tag => (
              <Badge
                key={tag}
                variant="secondary"
                className="gap-1 text-xs"
              >
                {tag}
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-4 w-4 p-0 ml-1 opacity-70 hover:opacity-100"
                  onClick={() => onRemoveTag(tag)}
                >
                  <X className="h-3 w-3" />
                </Button>
              </Badge>
            ))}
          </div>
        )}
      </div>

      <Separator className="my-0.5" />
    </div>
  )
}
