import { format } from "date-fns"
import { getSupabaseBrowser } from "@/lib/supabase"

export interface JournalEntry {
  note: string
  screenshots: string[]
  tags?: string[]
}

// Cache for journal entries to avoid excessive database queries
const journalEntryCache: Record<string, { entry: JournalEntry | null, timestamp: number }> = {}

/**
 * Get a journal entry from Supabase for a specific date
 * This is a client-side function that uses the browser Supabase client
 */
export async function getJournalEntryForDate(date: Date, userId?: string, accountId?: string): Promise<JournalEntry | null> {
  if (!userId) return null;

  const dateKey = format(date, "yyyy-MM-dd");
  const cacheKey = `${userId}-${accountId || 'default'}-${dateKey}`;

  // Check cache first (valid for 5 minutes)
  const now = Date.now();
  const cachedEntry = journalEntryCache[cacheKey];
  if (cachedEntry && (now - cachedEntry.timestamp < 5 * 60 * 1000)) {
    return cachedEntry.entry;
  }

  try {
    const supabase = getSupabaseBrowser();

    // Query for the journal entry
    let query = supabase
      .from('daily_journal_entries')
      .select('*')
      .eq('user_id', userId)
      .eq('date', dateKey);

    // Add account filter if provided
    if (accountId) {
      query = query.eq('account_id', accountId);
    } else {
      query = query.is('account_id', null);
    }

    // Use maybeSingle instead of single to avoid 406 errors
    // maybeSingle returns null instead of throwing an error when no rows are found
    const { data, error } = await query.maybeSingle();

    if (error) {
      // PGRST116 is "no rows returned" which is expected
      if (error.code === 'PGRST116') {
        // This is normal, just return null
        journalEntryCache[cacheKey] = { entry: null, timestamp: now };
        return null;
      }

      // Log other errors but don't throw
      console.error('Error fetching journal entry:',
        error.message || error.code || JSON.stringify(error));

      // Cache the null result to avoid repeated failed requests
      journalEntryCache[cacheKey] = { entry: null, timestamp: now };
      return null;
    }

    if (!data) {
      // Cache the null result
      journalEntryCache[cacheKey] = { entry: null, timestamp: now };
      return null;
    }

    const entry: JournalEntry = {
      note: data.note || '',
      screenshots: data.screenshots || [],
      tags: data.tags || []
    };

    // Cache the result
    journalEntryCache[cacheKey] = { entry, timestamp: now };
    return entry;
  } catch (error) {
    console.error('Error in getJournalEntryForDate:', error);
    return null;
  }
}

/**
 * Check if a date has a journal entry with notes or screenshots
 * This is a client-side function that uses memory cache to avoid excessive database queries
 */
export function hasJournalEntry(date: Date): boolean {
  const dateKey = format(date, "yyyy-MM-dd");

  // We'll always return false initially and then check asynchronously
  // This ensures we're always getting the latest data from Supabase
  setTimeout(async () => {
    try {
      // Get the current user from Supabase
      const supabase = getSupabaseBrowser();
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      const userId = user.id;

      // Get the selected account ID from Supabase user preferences
      const { data: userPrefs } = await supabase
        .from('user_preferences')
        .select('selected_account_id')
        .eq('user_id', userId)
        .maybeSingle();

      const selectedAccountId = userPrefs?.selected_account_id;

      // Check if there's an entry
      try {
        const entry = await getJournalEntryForDate(date, userId, selectedAccountId || undefined);
        const hasEntry = !!(entry?.note || (entry?.screenshots && entry?.screenshots.length > 0));

        // If we found an entry, trigger a UI update
        if (hasEntry) {
          // Dispatch a custom event to notify components to update
          const event = new CustomEvent('journal-entry-found', {
            detail: { date: dateKey }
          });
          document.dispatchEvent(event);
        }
      } catch (entryError) {
        console.error('Error getting journal entry in hasJournalEntry:', entryError);
      }
    } catch (error) {
      console.error('Error checking for journal entry:', error);
    }
  }, 0);

  return false;
}
