"use client"

import { useState, use<PERSON><PERSON>back, useEffect, useRef } from "react"
import { NotebookCategory, NotebookTag, NotebookFolderWithMeta } from "@/types/notebook"
import { Button } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import {
  AlertCircle,
  Bookmark,
  ChevronDown,
  ChevronRight,
  FileText,
  Folder,
  FolderOpen,
  MoreHorizontal,
  Pencil,
  Plus,
  Search,
  Tag,
  Trash2,
  X
} from "lucide-react"
import { cn } from "@/lib/utils"
import {
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from "@/components/ui/dialog"
import { DialogFix as Dialog } from "@/components/ui/dialog-fix"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu-fix"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog-fix"
import { FocusTrap } from "@/components/ui/focus-trap"

interface NotebookSidebarProps {
  categories: NotebookCategory[]
  tags: NotebookTag[]
  folders: NotebookFolderWithMeta[]
  selectedCategory?: string
  selectedFolderId?: string
  selectedTags: string[]
  showTemplates: boolean
  onSelectCategory: (category: string | undefined) => void
  onSelectFolder: (folderId: string | undefined) => void
  onSelectTag: (tag: string) => void
  onToggleTemplates: () => void
  onCreateCategory: (category: string) => void
  onCreateFolder: (name: string, color?: string, icon?: string, parentCategory?: string, isCategory?: boolean) => void
  onDeleteFolder: (id: string) => Promise<void>
  onCreateNote?: (folderId: string) => void
  onImportJournalEntries?: () => void
  onRenameFolder?: (id: string, newName: string) => void
  onMoveFolder?: (id: string, categoryName: string | null) => void
  isImporting?: boolean
  isFolderLoading?: boolean
  className?: string
  onExpandCategory?: (expandFn: (categoryName: string) => void) => void
}

export function NotebookSidebar({
  categories,
  tags,
  folders,
  selectedCategory,
  selectedFolderId,
  selectedTags,
  showTemplates,
  onSelectCategory,
  onSelectFolder,
  onSelectTag,
  onToggleTemplates,
  onCreateCategory,
  onCreateFolder,
  onDeleteFolder,
  onCreateNote,
  onImportJournalEntries,
  onRenameFolder,
  onMoveFolder,
  isImporting = false,
  isFolderLoading = false,
  className,
  onExpandCategory
}: NotebookSidebarProps) {
  const [showCategories, setShowCategories] = useState(true)
  const [showFolders, setShowFolders] = useState(true)
  const [showTags, setShowTags] = useState(true)
  const [newCategory, setNewCategory] = useState("")
  const [newFolderName, setNewFolderName] = useState("")
  const [newFolderColor, setNewFolderColor] = useState("#725ac1")

  // State for expanded category folders
  const [expandedCategoryFolders, setExpandedCategoryFolders] = useState<Record<string, boolean>>({})

  // Toggle category folder expansion
  const toggleCategoryFolder = (categoryName: string) => {
    setExpandedCategoryFolders(prev => ({
      ...prev,
      [categoryName]: !prev[categoryName]
    }))
  }

  // Expand a specific category folder
  const expandCategoryFolder = useCallback((categoryName: string) => {
    setExpandedCategoryFolders(prev => ({
      ...prev,
      [categoryName]: true
    }));
  }, []);

  // Expose the expand category function to parent component
  useEffect(() => {
    if (onExpandCategory) {
      onExpandCategory(expandCategoryFolder);
    }
  }, [onExpandCategory, expandCategoryFolder]);
  const [isAddingCategory, setIsAddingCategory] = useState(false)
  const [isAddingFolder, setIsAddingFolder] = useState(false)
  const [folderToRename, setFolderToRename] = useState<string | null>(null)
  const [newFolderNameForRename, setNewFolderNameForRename] = useState("")
  const [folderToMove, setFolderToMove] = useState<string | null>(null)
  const [categoryForMove, setCategoryForMove] = useState<string | null>(null)
  const [tagFilter, setTagFilter] = useState("")

  // State for delete confirmation dialog
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [folderToDelete, setFolderToDelete] = useState<{id: string, name: string} | null>(null)

  // Filter tags based on search input
  const filteredTags = tags.filter(tag =>
    tag.name.toLowerCase().includes(tagFilter.toLowerCase())
  )

  // Handle category creation
  const handleCreateCategory = () => {
    if (newCategory.trim()) {
      onCreateCategory(newCategory.trim())
      setNewCategory("")
      setIsAddingCategory(false)
    }
  }

  // State for folder creation
  const [selectedCategoryForFolder, setSelectedCategoryForFolder] = useState<string | null>(null);
  const [folderType, setFolderType] = useState<'regular' | 'category'>('regular');

  // State for folder creation validation and loading
  const [folderNameError, setFolderNameError] = useState<string | null>(null);
  const [isCreatingFolder, setIsCreatingFolder] = useState(false);

  // Handle folder creation
  const handleCreateFolder = async () => {
    // Reset error state
    setFolderNameError(null);

    // Validate folder name
    if (!newFolderName.trim()) {
      setFolderNameError("Folder name is required");
      return;
    }

    // Check for duplicate names in the current folders
    const isDuplicate = folders.some(folder =>
      folder.name.toLowerCase() === newFolderName.trim().toLowerCase()
    );

    if (isDuplicate) {
      setFolderNameError("A folder with this name already exists");
      return;
    }

    try {
      setIsCreatingFolder(true);

      if (folderType === 'category') {
        // Create a category folder
        onCreateFolder(
          newFolderName.trim(),
          newFolderColor,
          "category", // Use "category" as the icon for category folders
          undefined,
          true // isCategory flag
        );
      } else {
        // Create a regular folder
        onCreateFolder(
          newFolderName.trim(),
          newFolderColor,
          "folder", // Use "folder" as the icon for regular folders
          selectedCategoryForFolder || undefined,
          false // Not a category folder
        );
      }

      // Reset form
      setNewFolderName("");
      setNewFolderColor("#725ac1");
      setSelectedCategoryForFolder(null);
      setFolderType('regular');
      setIsAddingFolder(false);
    } catch (error) {
      console.error("Error creating folder:", error);
      // Error is handled by the mutation hook
    } finally {
      setIsCreatingFolder(false);
    }
  }

  // We're now handling deletion directly in the dropdown menu items

  // Handle folder renaming
  const handleRenameFolder = () => {
    if (folderToRename && newFolderNameForRename.trim() && onRenameFolder) {
      onRenameFolder(folderToRename, newFolderNameForRename.trim())
      setFolderToRename(null)
      setNewFolderNameForRename("")
    }
  }

  // Handle moving folder to category
  const handleMoveFolder = () => {
    if (folderToMove && onMoveFolder) {
      onMoveFolder(folderToMove, categoryForMove)
      setFolderToMove(null)
      setCategoryForMove(null)
    }
  }

  // Handle folder deletion confirmation
  const handleDeleteFolder = () => {
    if (folderToDelete) {
      onDeleteFolder(folderToDelete.id)
      setFolderToDelete(null)
      setIsDeleteDialogOpen(false)
    }
  }

  return (
    <div className={cn("w-64 border-r h-full flex flex-col bg-background", className)}>
      <div className="p-4 border-b">
        {/* Search bar removed as it's redundant with the one in the note list */}

        <div className="flex items-center gap-2 mb-2">
          <Dialog open={isAddingFolder} onOpenChange={setIsAddingFolder}>
            <DialogTrigger asChild>
              <Button
                variant="outline"
                size="sm"
                className="text-xs h-8 w-full"
              >
                <Plus className="h-3.5 w-3.5 mr-1" />
                Add folder
              </Button>
            </DialogTrigger>
            <FocusTrap isActive={isAddingFolder} onEscapeKey={() => setIsAddingFolder(false)}>
              <DialogContent className="sm:max-w-[425px]" onClick={(e) => e.stopPropagation()}>
              <DialogHeader>
                <DialogTitle>Create New Folder</DialogTitle>
                <DialogDescription>
                  Enter a name for your new folder.
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4" onClick={(e) => e.stopPropagation()}>
                <div className="grid grid-cols-4 items-center gap-4">
                  <label htmlFor="folderType" className="text-right text-sm font-medium">
                    Folder Type
                  </label>
                  <div className="col-span-3 flex gap-4">
                    <div className="flex items-center">
                      <input
                        type="radio"
                        id="regularFolder"
                        name="folderType"
                        value="regular"
                        checked={folderType === 'regular'}
                        onChange={() => setFolderType('regular')}
                        className="mr-2"
                      />
                      <label htmlFor="regularFolder" className="text-sm">Regular Folder</label>
                    </div>
                    <div className="flex items-center">
                      <input
                        type="radio"
                        id="categoryFolder"
                        name="folderType"
                        value="category"
                        checked={folderType === 'category'}
                        onChange={() => setFolderType('category')}
                        className="mr-2"
                      />
                      <label htmlFor="categoryFolder" className="text-sm">Category Folder</label>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-4 items-center gap-4">
                  <label htmlFor="folderName" className="text-right text-sm font-medium">
                    Name
                  </label>
                  <div className="col-span-3">
                    <Input
                      id="folderName"
                      value={newFolderName}
                      onChange={(e) => {
                        setNewFolderName(e.target.value);
                        // Clear error when user types
                        if (folderNameError) setFolderNameError(null);
                      }}
                      className={cn(folderNameError ? "border-red-500" : "")}
                      placeholder="My Folder"
                      onClick={(e) => e.stopPropagation()}
                    />
                    {folderNameError && (
                      <p className="text-red-500 text-xs mt-1">{folderNameError}</p>
                    )}
                  </div>
                </div>

                <div className="grid grid-cols-4 items-center gap-4">
                  <label htmlFor="folderColor" className="text-right text-sm font-medium">
                    Color
                  </label>
                  <Input
                    id="folderColor"
                    type="color"
                    value={newFolderColor}
                    onChange={(e) => setNewFolderColor(e.target.value)}
                    className="col-span-3 h-10 w-full"
                    onClick={(e) => e.stopPropagation()}
                  />
                </div>

                {folderType === 'regular' && (
                  <div className="grid grid-cols-4 items-center gap-4">
                    <label htmlFor="folderCategory" className="text-right text-sm font-medium">
                      Category
                    </label>
                    <select
                      id="folderCategory"
                      value={selectedCategoryForFolder || ""}
                      onChange={(e) => setSelectedCategoryForFolder(e.target.value || null)}
                      className="col-span-3 h-10 w-full rounded-md border border-input bg-background px-3 py-1"
                      onClick={(e) => e.stopPropagation()}
                    >
                      <option value="">No category</option>
                      {/* Include both categories from props and category folders from the database */}
                      {categories.map(category => (
                        <option key={category.name} value={category.name}>
                          {category.name}
                        </option>
                      ))}
                      {folders
                        .filter(folder => folder.description?.includes('IsCategory: true') || folder.icon === 'category')
                        .map(folder => (
                          <option key={folder.id} value={folder.name}>
                            {folder.name}
                          </option>
                        ))}
                    </select>
                  </div>
                )}
              </div>
              <DialogFooter onClick={(e) => e.stopPropagation()}>
                <Button
                  variant="outline"
                  onClick={(e) => {
                    e.stopPropagation();
                    setIsAddingFolder(false);
                  }}
                >
                  Cancel
                </Button>
                <Button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleCreateFolder();
                  }}
                  disabled={!newFolderName.trim() || isCreatingFolder}
                >
                  {isCreatingFolder ? (
                    <>
                      <span className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-background border-t-foreground"></span>
                      Creating...
                    </>
                  ) : (
                    "Create Folder"
                  )}
                </Button>
              </DialogFooter>
            </DialogContent>
            </FocusTrap>
          </Dialog>
        </div>

        {/* Add Category Dialog */}
        <Dialog open={isAddingCategory} onOpenChange={setIsAddingCategory}>
          <FocusTrap isActive={isAddingCategory} onEscapeKey={() => setIsAddingCategory(false)}>
            <DialogContent className="sm:max-w-[425px]" onClick={(e) => e.stopPropagation()}>
            <DialogHeader>
              <DialogTitle>Create New Category</DialogTitle>
              <DialogDescription>
                Enter a name for your new category.
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4" onClick={(e) => e.stopPropagation()}>
              <div className="grid grid-cols-4 items-center gap-4">
                <label htmlFor="categoryName" className="text-right text-sm font-medium">
                  Name
                </label>
                <Input
                  id="categoryName"
                  value={newCategory}
                  onChange={(e) => setNewCategory(e.target.value)}
                  className="col-span-3"
                  placeholder="My Category"
                  onClick={(e) => e.stopPropagation()}
                />
              </div>
            </div>
            <DialogFooter onClick={(e) => e.stopPropagation()}>
              <Button
                variant="outline"
                onClick={(e) => {
                  e.stopPropagation();
                  setIsAddingCategory(false);
                }}
              >
                Cancel
              </Button>
              <Button
                onClick={(e) => {
                  e.stopPropagation();
                  handleCreateCategory();
                }}
                disabled={!newCategory.trim()}
              >
                Create Category
              </Button>
            </DialogFooter>
          </DialogContent>
          </FocusTrap>
        </Dialog>

        {/* Rename Folder Dialog */}
        <Dialog open={folderToRename !== null} onOpenChange={(open) => !open && setFolderToRename(null)}>
          <FocusTrap isActive={folderToRename !== null} onEscapeKey={() => setFolderToRename(null)}>
            <DialogContent className="sm:max-w-[425px]" onClick={(e) => e.stopPropagation()}>
            <DialogHeader>
              <DialogTitle>Rename Folder</DialogTitle>
              <DialogDescription>
                Enter a new name for the folder.
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4" onClick={(e) => e.stopPropagation()}>
              <div className="grid grid-cols-4 items-center gap-4">
                <label htmlFor="newFolderName" className="text-right text-sm font-medium">
                  Name
                </label>
                <Input
                  id="newFolderName"
                  value={newFolderNameForRename}
                  onChange={(e) => setNewFolderNameForRename(e.target.value)}
                  className="col-span-3"
                  placeholder="New folder name"
                  onClick={(e) => e.stopPropagation()}
                />
              </div>
            </div>
            <DialogFooter onClick={(e) => e.stopPropagation()}>
              <Button
                variant="outline"
                onClick={(e) => {
                  e.stopPropagation();
                  setFolderToRename(null);
                }}
              >
                Cancel
              </Button>
              <Button
                onClick={(e) => {
                  e.stopPropagation();
                  handleRenameFolder();
                }}
                disabled={!newFolderNameForRename.trim()}
              >
                Rename Folder
              </Button>
            </DialogFooter>
          </DialogContent>
          </FocusTrap>
        </Dialog>

        {/* Move Folder Dialog */}
        <Dialog open={folderToMove !== null} onOpenChange={(open) => !open && setFolderToMove(null)}>
          <FocusTrap isActive={folderToMove !== null} onEscapeKey={() => setFolderToMove(null)}>
            <DialogContent className="sm:max-w-[425px]" onClick={(e) => e.stopPropagation()}>
            <DialogHeader>
              <DialogTitle>Move Folder to Category</DialogTitle>
              <DialogDescription>
                Select a category to move this folder to.
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4" onClick={(e) => e.stopPropagation()}>
              <div className="grid grid-cols-4 items-center gap-4">
                <label htmlFor="categoryForMove" className="text-right text-sm font-medium">
                  Category
                </label>
                <select
                  id="categoryForMove"
                  value={categoryForMove || ""}
                  onChange={(e) => setCategoryForMove(e.target.value || null)}
                  className="col-span-3 h-10 w-full rounded-md border border-input bg-background px-3 py-1"
                  onClick={(e) => e.stopPropagation()}
                >
                  <option value="">No category (remove from current category)</option>
                  {/* Include both categories from props and category folders from the database */}
                  {categories.map(category => (
                    <option key={category.name} value={category.name}>
                      {category.name}
                    </option>
                  ))}
                  {folders
                    .filter(folder => folder.description?.includes('IsCategory: true') || folder.icon === 'category')
                    .map(folder => (
                      <option key={folder.id} value={folder.name}>
                        {folder.name}
                      </option>
                    ))}
                </select>
              </div>
            </div>
            <DialogFooter onClick={(e) => e.stopPropagation()}>
              <Button
                variant="outline"
                onClick={(e) => {
                  e.stopPropagation();
                  setFolderToMove(null);
                }}
              >
                Cancel
              </Button>
              <Button
                onClick={(e) => {
                  e.stopPropagation();
                  handleMoveFolder();
                }}
              >
                Move Folder
              </Button>
            </DialogFooter>
          </DialogContent>
          </FocusTrap>
        </Dialog>

        {onImportJournalEntries && (
          <Button
            variant="ghost"
            size="sm"
            className="text-xs h-8 w-full justify-start"
            onClick={onImportJournalEntries}
            disabled={isImporting}
          >
            <FileText className="h-3.5 w-3.5 mr-2" />
            {isImporting ? "Importing..." : "Import Journal Entries"}
          </Button>
        )}
      </div>

      <ScrollArea className="flex-grow">
        <div className="p-4">
          {/* Folders Section */}
          <div className="mb-6">
            <div
              className="flex items-center justify-between mb-2 cursor-pointer"
              onClick={() => setShowFolders(!showFolders)}
            >
              <div className="font-medium text-sm flex items-center">
                {showFolders ? (
                  <ChevronDown className="h-4 w-4 mr-1" />
                ) : (
                  <ChevronRight className="h-4 w-4 mr-1" />
                )}
                Folders
                {isFolderLoading && (
                  <div className="ml-2 animate-spin h-3 w-3 border border-primary border-t-transparent rounded-full opacity-60" />
                )}
              </div>
            </div>

            {showFolders && (
              <div className="space-y-1 ml-2">
                <Button
                  variant="ghost"
                  className={cn(
                    "w-full justify-start text-xs h-8",
                    !selectedFolderId && !selectedCategory && "bg-accent"
                  )}
                  onClick={() => {
                    onSelectFolder(undefined)
                    onSelectCategory(undefined)
                  }}
                >
                  <FolderOpen className="h-4 w-4 mr-2" />
                  All notes
                </Button>

                {isFolderLoading && folders.length === 0 ? (
                  <div className="flex items-center justify-center py-2">
                    <div className="animate-spin h-4 w-4 border-2 border-primary border-t-transparent rounded-full" />
                  </div>
                ) : folders.length > 0 ? (
                  <>

                    {/* Uncategorized folders */}
                    {folders
                      .filter(folder => !folder.is_system && !folder.description?.includes('Category:'))
                      .map(folder => (
                        <div key={folder.id} className="relative group">
                          <Button
                            variant="ghost"
                            className={cn(
                              "w-full justify-start text-xs h-8 pr-8",
                              selectedFolderId === folder.id && "bg-accent"
                            )}
                            onClick={() => {
                              // Clear all filters when selecting a folder
                              onSelectFolder(folder.id)
                              onSelectCategory(undefined)
                            }}
                          >
                            {folder.description?.includes('IsCategory') ? (
                              <FolderOpen className="h-4 w-4 mr-2 text-muted-foreground" />
                            ) : (
                              <div
                                className="h-3 w-3 mr-2 rounded-sm"
                                style={{ backgroundColor: folder.color || '#725ac1' }}
                              />
                            )}
                            <span className="truncate flex-grow text-left">
                              {folder.name}
                            </span>
                            {folder.count !== undefined && folder.count > 0 && (
                              <Badge variant="outline" className="ml-auto text-xs">
                                {folder.count}
                              </Badge>
                            )}
                            {folder.count === undefined && (
                              <div className="ml-auto animate-pulse bg-muted rounded w-6 h-4" />
                            )}
                          </Button>

                          {!folder.is_system && (
                            <div className="absolute right-1 top-1 opacity-0 group-hover:opacity-100 transition-opacity">
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button
                                    variant="ghost"
                                    size="icon"
                                    className="h-6 w-6"
                                    onClick={(e) => e.stopPropagation()}
                                  >
                                    <MoreHorizontal className="h-3 w-3 text-muted-foreground" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end" className="w-40">
                                  {onCreateNote && (
                                    <DropdownMenuItem
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        e.preventDefault();
                                        // First close the dropdown menu
                                        const closeEvent = new Event('keydown');
                                        Object.defineProperty(closeEvent, 'key', { value: 'Escape' });
                                        document.dispatchEvent(closeEvent);

                                        // Then call the onCreateNote function after a short delay
                                        setTimeout(() => {
                                          onCreateNote(folder.id);
                                        }, 100);
                                      }}
                                      className="text-xs"
                                    >
                                      <FileText className="h-3 w-3 mr-2" />
                                      Add Note
                                    </DropdownMenuItem>
                                  )}

                                  {onRenameFolder && (
                                    <DropdownMenuItem
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        e.preventDefault();
                                        // First close the dropdown menu
                                        const closeEvent = new Event('keydown');
                                        Object.defineProperty(closeEvent, 'key', { value: 'Escape' });
                                        document.dispatchEvent(closeEvent);

                                        // Then set the folder to rename after a short delay
                                        setTimeout(() => {
                                          setFolderToRename(folder.id);
                                          // Pre-fill with current name
                                          setNewFolderNameForRename(folder.name);
                                        }, 100);
                                      }}
                                      className="text-xs"
                                    >
                                      <Pencil className="h-3 w-3 mr-2" />
                                      Rename
                                    </DropdownMenuItem>
                                  )}

                                  {onMoveFolder && (
                                    <DropdownMenuItem
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        e.preventDefault();
                                        // First close the dropdown menu
                                        const closeEvent = new Event('keydown');
                                        Object.defineProperty(closeEvent, 'key', { value: 'Escape' });
                                        document.dispatchEvent(closeEvent);

                                        // Then set the folder to move after a short delay
                                        setTimeout(() => {
                                          setFolderToMove(folder.id);
                                          // Extract current category if any
                                          const currentCategory = folder.description?.match(/Category: (.+)/)?.[1] || null;
                                          setCategoryForMove(currentCategory);
                                        }, 100);
                                      }}
                                      className="text-xs"
                                    >
                                      <Folder className="h-3 w-3 mr-2" />
                                      Move to Category
                                    </DropdownMenuItem>
                                  )}

                                  <DropdownMenuSeparator />

                                  <DropdownMenuItem
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      e.preventDefault();

                                      // First close the dropdown menu
                                      const closeEvent = new Event('keydown');
                                      Object.defineProperty(closeEvent, 'key', { value: 'Escape' });
                                      document.dispatchEvent(closeEvent);

                                      // Then open the delete confirmation dialog after a short delay
                                      setTimeout(() => {
                                        setFolderToDelete({
                                          id: folder.id,
                                          name: folder.name
                                        });
                                        setIsDeleteDialogOpen(true);
                                      }, 100);
                                    }}
                                    className="text-xs text-red-500"
                                  >
                                    <Trash2 className="h-3 w-3 mr-2" />
                                    Delete
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </div>
                          )}
                        </div>
                      ))}
                  </>
                ) : (
                  <div className="flex flex-col items-center justify-center p-4 text-center">
                    <Folder className="h-8 w-8 text-muted-foreground mb-2 opacity-50" />
                    <p className="text-xs text-muted-foreground">No folders found</p>
                    <p className="text-xs text-muted-foreground mt-1">Click "Add folder" to create one</p>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Categories Section */}
          <div className="mb-6">
            <div
              className="flex items-center justify-between mb-2 cursor-pointer"
              onClick={() => setShowCategories(!showCategories)}
            >
              <div className="font-medium text-sm flex items-center">
                {showCategories ? (
                  <ChevronDown className="h-4 w-4 mr-1" />
                ) : (
                  <ChevronRight className="h-4 w-4 mr-1" />
                )}
                Categories
              </div>
              <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6"
                onClick={(e) => {
                  e.stopPropagation();
                  setIsAddingCategory(true);
                }}
                title="Add category"
              >
                <Plus className="h-3 w-3" />
              </Button>
            </div>

            {showCategories && (
              <div className="space-y-1 ml-2">
                {/* Category for imported folders */}
                <div className="mb-2">
                  <div
                    className="relative group cursor-pointer"
                    onClick={() => toggleCategoryFolder('Imported Journals')}
                  >
                    <div className="flex items-center justify-between w-full px-2 py-1.5 rounded-md hover:bg-accent/50">
                      <div className="flex items-center">
                        {expandedCategoryFolders['Imported Journals'] ? (
                          <ChevronDown className="h-3.5 w-3.5 mr-1.5 text-muted-foreground" />
                        ) : (
                          <ChevronRight className="h-3.5 w-3.5 mr-1.5 text-muted-foreground" />
                        )}
                        <span className="text-xs font-medium">Imported Journals</span>
                      </div>
                      <div className="opacity-0 group-hover:opacity-100 transition-opacity">
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-5 w-5"
                          onClick={(e) => {
                            e.stopPropagation();
                            setSelectedCategoryForFolder('Imported Journals');
                            setIsAddingFolder(true);
                          }}
                          title="Create folder in Imported Journals"
                        >
                          <Plus className="h-3 w-3 text-muted-foreground" />
                        </Button>
                      </div>
                    </div>
                  </div>

                  {/* Show imported system folders */}
                  {expandedCategoryFolders['Imported Journals'] && (
                    <div className="ml-4 mt-1 space-y-1">
                      {folders
                        .filter(folder => folder.is_system)
                        .map(folder => (
                          <div key={folder.id} className="relative group">
                            <div className="flex flex-col">
                              <Button
                                variant="ghost"
                                className={cn(
                                  "w-full justify-start text-xs h-7 pr-8",
                                  selectedFolderId === folder.id && "bg-accent"
                                )}
                                onClick={() => {
                                  // Clear all filters when selecting a folder
                                  onSelectFolder(folder.id)
                                  onSelectCategory(undefined)
                                }}
                              >
                                <div
                                  className="h-2.5 w-2.5 mr-2 rounded-sm"
                                  style={{ backgroundColor: folder.color || '#725ac1' }}
                                />
                                <span className="truncate flex-grow text-left">
                                  {folder.name}
                                </span>
                                {folder.count !== undefined && folder.count > 0 && (
                                  <Badge variant="outline" className="ml-auto text-xs">
                                    {folder.count}
                                  </Badge>
                                )}
                                {folder.count === undefined && (
                                  <div className="ml-auto animate-pulse bg-muted rounded w-6 h-4" />
                                )}
                              </Button>
                              <div className="absolute right-1 top-1 opacity-0 group-hover:opacity-100 transition-opacity flex gap-1">
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  className="h-5 w-5"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    // Call the onCreateNote function with this folder
                                    if (onCreateNote) {
                                      onCreateNote(folder.id);
                                    }
                                  }}
                                  title={`Create note in ${folder.name}`}
                                >
                                  <FileText className="h-2.5 w-2.5 text-muted-foreground" />
                                </Button>
                              </div>
                            </div>
                          </div>
                        ))}
                    </div>
                  )}
                </div>

                {/* Get all category folders from the database */}
                {folders
                  .filter(folder => folder.is_category || folder.description?.includes('IsCategory: true') || folder.icon === 'category')
                  .map(folder => (
                    <div key={folder.id} className="mb-2">
                      <div
                        className="relative group cursor-pointer"
                        onClick={() => toggleCategoryFolder(folder.name)}
                      >
                        <div className="flex items-center justify-between w-full px-2 py-1.5 rounded-md hover:bg-accent/50">
                          <div className="flex items-center">
                            {expandedCategoryFolders[folder.name] ? (
                              <ChevronDown className="h-3.5 w-3.5 mr-1.5 text-muted-foreground" />
                            ) : (
                              <ChevronRight className="h-3.5 w-3.5 mr-1.5 text-muted-foreground" />
                            )}
                            <span
                              className="text-xs font-medium cursor-pointer"
                              onClick={(e) => {
                                e.stopPropagation();
                                onSelectCategory(folder.name);
                                onSelectFolder(undefined);
                              }}
                            >
                              {folder.name}
                            </span>
                            {folder.count !== undefined && folder.count > 0 && (
                              <Badge variant="outline" className="ml-2 text-[10px]">
                                {folder.count}
                              </Badge>
                            )}
                            {folder.count === undefined && (
                              <div className="ml-2 animate-pulse bg-muted rounded w-4 h-3" />
                            )}
                          </div>
                          <div className="opacity-0 group-hover:opacity-100 transition-opacity">
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-5 w-5"
                              onClick={(e) => {
                                e.stopPropagation();
                                setSelectedCategoryForFolder(folder.name);
                                setIsAddingFolder(true);
                              }}
                              title={`Create folder in ${folder.name}`}
                            >
                              <Plus className="h-3 w-3 text-muted-foreground" />
                            </Button>
                          </div>
                        </div>
                      </div>

                      {/* Show folders associated with this category */}
                      {expandedCategoryFolders[folder.name] && (
                        <div className="ml-4 mt-1 space-y-1">
                          {/* Show regular folders in this category */}
                          {folders
                            .filter(f =>
                              f.description?.includes(`Category: ${folder.name}`) &&
                              !f.is_category &&
                              !f.description?.includes('IsCategory: true')
                            )
                            .map(categoryFolder => (
                              <div key={categoryFolder.id} className="relative group">
                                <Button
                                  variant="ghost"
                                  className={cn(
                                    "w-full justify-start text-xs h-7 pr-8",
                                    selectedFolderId === categoryFolder.id && "bg-accent"
                                  )}
                                  onClick={() => {
                                    // Clear all filters when selecting a folder
                                    onSelectFolder(categoryFolder.id)
                                    onSelectCategory(undefined)
                                  }}
                                >
                                  <div
                                    className="h-2.5 w-2.5 mr-2 rounded-sm"
                                    style={{ backgroundColor: categoryFolder.color || '#725ac1' }}
                                  />
                                  <span className="truncate flex-grow text-left">
                                    {categoryFolder.name}
                                  </span>
                                  {categoryFolder.count !== undefined && categoryFolder.count > 0 && (
                                    <Badge variant="outline" className="ml-auto text-xs">
                                      {categoryFolder.count}
                                    </Badge>
                                  )}
                                  {categoryFolder.count === undefined && (
                                    <div className="ml-auto animate-pulse bg-muted rounded w-6 h-4" />
                                  )}
                                </Button>

                                {!categoryFolder.is_system && (
                                  <div className="absolute right-1 top-1 opacity-0 group-hover:opacity-100 transition-opacity">
                                    <DropdownMenu>
                                      <DropdownMenuTrigger asChild>
                                        <Button
                                          variant="ghost"
                                          size="icon"
                                          className="h-5 w-5"
                                          onClick={(e) => e.stopPropagation()}
                                        >
                                          <MoreHorizontal className="h-2.5 w-2.5 text-muted-foreground" />
                                        </Button>
                                      </DropdownMenuTrigger>
                                      <DropdownMenuContent align="end" className="w-40">
                                        {onCreateNote && (
                                          <DropdownMenuItem
                                            onClick={(e) => {
                                              e.stopPropagation();
                                              e.preventDefault();
                                              // First close the dropdown menu
                                              const closeEvent = new Event('keydown');
                                              Object.defineProperty(closeEvent, 'key', { value: 'Escape' });
                                              document.dispatchEvent(closeEvent);

                                              // Then call the onCreateNote function after a short delay
                                              setTimeout(() => {
                                                onCreateNote(categoryFolder.id);
                                              }, 100);
                                            }}
                                            className="text-xs"
                                          >
                                            <FileText className="h-3 w-3 mr-2" />
                                            Add Note
                                          </DropdownMenuItem>
                                        )}

                                        {onRenameFolder && (
                                          <DropdownMenuItem
                                            onClick={(e) => {
                                              e.stopPropagation();
                                              e.preventDefault();
                                              // First close the dropdown menu
                                              const closeEvent = new Event('keydown');
                                              Object.defineProperty(closeEvent, 'key', { value: 'Escape' });
                                              document.dispatchEvent(closeEvent);

                                              // Then set the folder to rename after a short delay
                                              setTimeout(() => {
                                                setFolderToRename(categoryFolder.id);
                                                // Pre-fill with current name
                                                setNewFolderNameForRename(categoryFolder.name);
                                              }, 100);
                                            }}
                                            className="text-xs"
                                          >
                                            <Pencil className="h-3 w-3 mr-2" />
                                            Rename
                                          </DropdownMenuItem>
                                        )}

                                        {onMoveFolder && (
                                          <DropdownMenuItem
                                            onClick={(e) => {
                                              e.stopPropagation();
                                              e.preventDefault();
                                              // First close the dropdown menu
                                              const closeEvent = new Event('keydown');
                                              Object.defineProperty(closeEvent, 'key', { value: 'Escape' });
                                              document.dispatchEvent(closeEvent);

                                              // Then set the folder to move after a short delay
                                              setTimeout(() => {
                                                setFolderToMove(categoryFolder.id);
                                                // Extract current category if any
                                                const currentCategory = categoryFolder.description?.match(/Category: (.+)/)?.[1] || null;
                                                setCategoryForMove(currentCategory);
                                              }, 100);
                                            }}
                                            className="text-xs"
                                          >
                                            <Folder className="h-3 w-3 mr-2" />
                                            Move to Category
                                          </DropdownMenuItem>
                                        )}

                                        <DropdownMenuSeparator />

                                        <DropdownMenuItem
                                          onClick={(e) => {
                                            e.stopPropagation();
                                            e.preventDefault();

                                            // First close the dropdown menu
                                            const closeEvent = new Event('keydown');
                                            Object.defineProperty(closeEvent, 'key', { value: 'Escape' });
                                            document.dispatchEvent(closeEvent);

                                            // Then open the delete confirmation dialog after a short delay
                                            setTimeout(() => {
                                              setFolderToDelete({
                                                id: categoryFolder.id,
                                                name: categoryFolder.name
                                              });
                                              setIsDeleteDialogOpen(true);
                                            }, 100);
                                          }}
                                          className="text-xs text-red-500"
                                        >
                                          <Trash2 className="h-3 w-3 mr-2" />
                                          Delete
                                        </DropdownMenuItem>
                                      </DropdownMenuContent>
                                    </DropdownMenu>
                                  </div>
                                )}
                              </div>
                            ))}
                        </div>
                      )}
                    </div>
                  ))}

                {folders.filter(folder => folder.is_category || folder.description?.includes('IsCategory: true') || folder.icon === 'category').length === 0 && (
                  <div className="flex flex-col items-center justify-center p-4 text-center">
                    <Folder className="h-8 w-8 text-muted-foreground mb-2 opacity-50" />
                    <p className="text-xs text-muted-foreground">No categories found</p>
                    <p className="text-xs text-muted-foreground mt-1">Click the "+" button to create one</p>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Tags Section */}
          <div>
            <div
              className="flex items-center justify-between mb-2 cursor-pointer"
              onClick={() => setShowTags(!showTags)}
            >
              <div className="font-medium text-sm flex items-center">
                {showTags ? (
                  <ChevronDown className="h-4 w-4 mr-1" />
                ) : (
                  <ChevronRight className="h-4 w-4 mr-1" />
                )}
                Tags
              </div>
            </div>

            {showTags && (
              <div className="space-y-2 ml-2">
                <div className="relative mb-2">
                  <Search className="absolute left-2 top-2 h-3 w-3 text-muted-foreground" />
                  <Input
                    placeholder="Filter tags"
                    className="pl-7 h-7 text-xs"
                    value={tagFilter}
                    onChange={(e) => setTagFilter(e.target.value)}
                  />
                </div>
                <div className="flex flex-col gap-1 pt-1">
                  {filteredTags.map(tag => (
                    <Button
                      key={tag.name}
                      variant="ghost"
                      className={cn(
                        "w-full justify-start text-xs h-8",
                        selectedTags.includes(tag.name) && "bg-accent"
                      )}
                      onClick={() => {
                        onSelectTag(tag.name)
                        // Don't clear folder selection - allow filtering within selected folder
                      }}
                    >
                      <Tag className="h-3 w-3 mr-2" />
                      {tag.name}
                      <Badge variant="outline" className="ml-auto text-xs">
                        {tag.count}
                      </Badge>
                    </Button>
                  ))}

                  {filteredTags.length === 0 && (
                    <div className="flex flex-col items-center justify-center p-4 text-center">
                      <Tag className="h-8 w-8 text-muted-foreground mb-2 opacity-50" />
                      <p className="text-xs text-muted-foreground">
                        {tags.length === 0 ? "No tags found" : "No matching tags"}
                      </p>
                      {tags.length === 0 ? (
                        <p className="text-xs text-muted-foreground mt-1">Tags will appear when you add them to notes</p>
                      ) : (
                        <p className="text-xs text-muted-foreground mt-1">Try a different search term</p>
                      )}
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Templates */}
          <div className="mt-6">
            <Button
              variant="ghost"
              className={cn(
                "w-full justify-start text-xs h-8",
                showTemplates && "bg-accent"
              )}
              onClick={onToggleTemplates}
            >
              <Bookmark className="h-4 w-4 mr-2" />
              Templates
            </Button>
          </div>

          {/* Recently Deleted */}
          <div className="mt-2">
            <Button
              variant="ghost"
              className="w-full justify-start text-xs h-8"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Recently Deleted
            </Button>
          </div>
        </div>
      </ScrollArea>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <FocusTrap isActive={isDeleteDialogOpen} onEscapeKey={() => setIsDeleteDialogOpen(false)}>
          <AlertDialogContent onClick={(e) => e.stopPropagation()} inert={!isDeleteDialogOpen}>
grae            <AlertDialogHeader>
              <AlertDialogTitle>Delete Folder</AlertDialogTitle>
              <AlertDialogDescription>
                Are you sure you want to delete the folder "{folderToDelete?.name}"?
                This will not delete the notes inside, but they will be moved to the general notes area.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter onClick={(e) => e.stopPropagation()}>
              <AlertDialogCancel onClick={(e) => {
                e.stopPropagation();
                setIsDeleteDialogOpen(false);
              }}>
                Cancel
              </AlertDialogCancel>
              <AlertDialogAction
                onClick={(e) => {
                  e.stopPropagation();
                  handleDeleteFolder();
                }}
                className="bg-destructive text-destructive-foreground"
              >
                Delete
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </FocusTrap>
      </AlertDialog>
    </div>
  )
}
