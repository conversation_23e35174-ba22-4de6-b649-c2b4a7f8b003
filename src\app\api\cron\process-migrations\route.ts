import { NextRequest, NextResponse } from "next/server"

export async function GET(request: NextRequest) {
  try {
    console.log('=== CRON: PROCESSING AUTO-MIGRATION QUEUE ===')

    // Verify this is a legitimate cron call (optional security)
    const authHeader = request.headers.get('authorization')
    const cronSecret = process.env.CRON_SECRET || 'default-secret'
    
    if (authHeader !== `Bearer ${cronSecret}`) {
      console.log('Unauthorized cron call')
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Call the queue processor
    const processUrl = `${request.nextUrl.origin}/api/migrate/process-queue`
    
    const response = await fetch(processUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      }
    })

    if (!response.ok) {
      const errorText = await response.text()
      console.error('Queue processing failed:', errorText)
      return NextResponse.json(
        { error: 'Queue processing failed', details: errorText },
        { status: 500 }
      )
    }

    const result = await response.json()
    console.log('Cron queue processing completed:', result)

    return NextResponse.json({
      success: true,
      message: 'Cron migration processing completed',
      result
    })

  } catch (error) {
    console.error('Error in cron migration processing:', error)
    return NextResponse.json(
      { error: "Internal server error", details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}

// Also support POST for manual triggering
export async function POST(request: NextRequest) {
  return GET(request)
}
