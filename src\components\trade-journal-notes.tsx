"use client"

import { useState, useEffect } from "react"
import { format } from "date-fns"
import { Plus, Edit, Trash2, Loader2 } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog"
import { JournalEntryForm } from "@/components/journal-entry-form"
import { JournalEntryCard } from "@/components/journal-entry-card"
import { getSupabaseClient } from "@/lib/supabase-singleton"
import {
  getJournalEntries,
  createJournalEntry,
  updateJournalEntry,
  deleteJournalEntry,
  type JournalEntry,
  type JournalEntryInsert
} from "@/lib/journal-service"
import { toast } from "sonner"

interface TradeJournalNotesProps {
  tradeId: string
  className?: string
}

export function TradeJournalNotes({
  tradeId,
  className
}: TradeJournalNotesProps) {
  const [entries, setEntries] = useState<JournalEntry[]>([])
  const [loading, setLoading] = useState(true)
  const [userId, setUserId] = useState<string | null>(null)
  const [isNewEntryDialogOpen, setIsNewEntryDialogOpen] = useState(false)
  const [isEditEntryDialogOpen, setIsEditEntryDialogOpen] = useState(false)
  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] = useState(false)
  const [currentEntry, setCurrentEntry] = useState<JournalEntry | null>(null)
  const supabase = getSupabaseClient()

  useEffect(() => {
    const fetchUser = async () => {
      const { data: { session } } = await supabase.auth.getSession()
      // Only use authenticated user ID
      if (!session?.user?.id) {
        console.log('No authenticated user found')
        return
      }
      const currentUserId = session.user.id
      setUserId(currentUserId)

      await fetchEntries(currentUserId)
    }

    fetchUser()
  }, [supabase.auth, tradeId])

  const fetchEntries = async (userId: string) => {
    try {
      setLoading(true)
      const entries = await getJournalEntries(userId, {
        tradeId
      })
      setEntries(entries)
    } catch (error) {
      console.error("Error fetching journal entries:", error)
      toast.error("Failed to load journal notes for this trade")
    } finally {
      setLoading(false)
    }
  }

  const handleCreateEntry = async (entry: Omit<JournalEntryInsert, "user_id">) => {
    if (!userId) return

    try {
      await createJournalEntry(userId, {
        ...entry,
        trade_id: tradeId
      })
      toast.success("Journal note created successfully")
      setIsNewEntryDialogOpen(false)
      fetchEntries(userId)
    } catch (error) {
      console.error("Error creating journal note:", error)
      toast.error("Failed to create journal note")
    }
  }

  const handleUpdateEntry = async (entry: Omit<JournalEntryInsert, "user_id">) => {
    if (!userId || !currentEntry) return

    try {
      await updateJournalEntry(userId, currentEntry.id, {
        ...entry,
        trade_id: tradeId
      })
      toast.success("Journal note updated successfully")
      setIsEditEntryDialogOpen(false)
      fetchEntries(userId)
    } catch (error) {
      console.error("Error updating journal note:", error)
      toast.error("Failed to update journal note")
    }
  }

  const handleDeleteEntry = async () => {
    if (!userId || !currentEntry) return

    try {
      await deleteJournalEntry(userId, currentEntry.id)
      toast.success("Journal note deleted successfully")
      setIsDeleteConfirmOpen(false)
      fetchEntries(userId)
    } catch (error) {
      console.error("Error deleting journal note:", error)
      toast.error("Failed to delete journal note")
    }
  }

  const handleEditEntry = (entry: JournalEntry) => {
    setCurrentEntry(entry)
    setIsEditEntryDialogOpen(true)
  }

  const handleDeleteConfirm = (entry: JournalEntry) => {
    setCurrentEntry(entry)
    setIsDeleteConfirmOpen(true)
  }

  return (
    <div className={className}>
      <div className="bg-background rounded-md border">
        <div className="flex flex-row items-center justify-between p-2 border-b">
          <div className="text-sm font-medium">Trade Notes</div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsNewEntryDialogOpen(true)}
            className="h-7 px-2"
          >
            <Plus className="h-3.5 w-3.5 mr-1" />
            Add
          </Button>
        </div>
        <div className="p-2">
          {loading ? (
            <div className="flex justify-center items-center py-4">
              <Loader2 className="h-5 w-5 animate-spin text-muted-foreground" />
            </div>
          ) : entries.length > 0 ? (
            <div className="space-y-3">
              {entries.map((entry) => (
                <JournalEntryCard
                  key={entry.id}
                  entry={entry}
                  onEdit={() => handleEditEntry(entry)}
                  onDelete={() => handleDeleteConfirm(entry)}
                  className="border-0 shadow-none p-0"
                />
              ))}
            </div>
          ) : (
            <div className="text-center py-4 text-muted-foreground text-sm">
              <p>No notes for this trade yet</p>
              <Button
                variant="link"
                onClick={() => setIsNewEntryDialogOpen(true)}
                className="mt-1 text-xs h-auto p-0"
              >
                Add your first note
              </Button>
            </div>
          )}
        </div>
      </div>

      {/* New Entry Dialog */}
      <Dialog open={isNewEntryDialogOpen} onOpenChange={setIsNewEntryDialogOpen}>
        <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>New Trade Note</DialogTitle>
            <DialogDescription>
              Add a new note to your trade journal.
            </DialogDescription>
          </DialogHeader>
          {userId && (
            <JournalEntryForm
              onSubmit={handleCreateEntry}
              onCancel={() => setIsNewEntryDialogOpen(false)}
              userId={userId}
              tradeId={tradeId}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Edit Entry Dialog */}
      <Dialog open={isEditEntryDialogOpen} onOpenChange={setIsEditEntryDialogOpen}>
        <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Trade Note</DialogTitle>
            <DialogDescription>
              Update your existing trade journal note.
            </DialogDescription>
          </DialogHeader>
          {userId && currentEntry && (
            <JournalEntryForm
              entry={currentEntry}
              onSubmit={handleUpdateEntry}
              onCancel={() => setIsEditEntryDialogOpen(false)}
              userId={userId}
              tradeId={tradeId}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteConfirmOpen} onOpenChange={setIsDeleteConfirmOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Trade Note</DialogTitle>
            <DialogDescription>
              This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <p>Are you sure you want to delete this note? This action cannot be undone.</p>
          </div>
          <div className="flex justify-end space-x-2">
            <Button variant="outline" onClick={() => setIsDeleteConfirmOpen(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleDeleteEntry}>
              Delete
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}

export default TradeJournalNotes

