"use client"

import Head from 'next/head'
import { useEffect, useState } from 'react'

export function HeadOptimizer() {
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
  }, [])

  if (!isClient) return null

  return (
    <Head>
      {/* Optimize CSS preloading */}
      <link
        rel="preload"
        href="/_next/static/css/app/layout.css"
        as="style"
        onLoad={() => {
          // Ensure the CSS is applied immediately
          const link = document.createElement('link')
          link.rel = 'stylesheet'
          link.href = '/_next/static/css/app/layout.css'
          document.head.appendChild(link)
        }}
      />
      
      {/* Preload critical fonts */}
      <link
        rel="preload"
        href="/fonts/inter-var.woff2"
        as="font"
        type="font/woff2"
        crossOrigin="anonymous"
      />
      
      {/* DNS prefetch for external resources */}
      <link rel="dns-prefetch" href="//fonts.googleapis.com" />
      <link rel="dns-prefetch" href="//fonts.gstatic.com" />
    </Head>
  )
}
