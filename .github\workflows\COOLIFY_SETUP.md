# TradePivot Coolify Deployment Setup

## 🎯 Overview

This guide explains how to configure your GitHub Actions workflow to deploy TradePivot to Coolify from your production repository with optimized Docker configurations.

## 🔄 Deployment Flow

```
Development Repo (master) → Manual GitHub Actions → Production Repo → Coolify Auto-Deploy
```

```mermaid
graph TD
    A[shawa0507/trademaster<br/>master branch] --> B[Manual GitHub Actions Trigger]
    B --> C[Build & Filter Files]
    C --> D[Create Coolify Configs]
    D --> E[Push to Production Repo]
    E --> F[shawa0507/trade_journal_production]

    F --> G[Coolify Auto-Deploy]
    G --> H[Coolify Production]
```

## 🛠️ Enhanced Workflow Features

### **Deployment Target Options**
- **coolify-production**: Deploy with optimized Coolify configurations
- **repository-only**: Just sync files (no deployment configs)

### **Coolify-Specific Configurations**
The workflow automatically creates:
- **Dockerfile**: Multi-stage optimized for production
- **docker-compose.yml**: Service configuration with health checks
- **.dockerignore**: Efficient build optimization

## 📋 Coolify Requirements & Compatibility

### ✅ **Coolify Auto-Detection Requirements**

Coolify will automatically detect and deploy your Next.js application if:

1. **Repository Structure**:
   ```
   production-repo/
   ├── package.json          ✅ Node.js detection
   ├── next.config.js        ✅ Next.js framework detection
   ├── Dockerfile            ✅ Docker deployment method
   ├── docker-compose.yml    ✅ Service configuration (optional)
   └── src/                  ✅ Application source
   ```

2. **Package.json Scripts**:
   ```json
   {
     "scripts": {
       "build": "next build",
       "start": "next start"
     }
   }
   ```

3. **Environment Variables**: Configured in Coolify dashboard

### ✅ **Current Repository Compatibility**

Your production repository structure **perfectly meets** Coolify requirements:
- ✅ **Next.js Framework**: Detected via `package.json` and `next.config.js`
- ✅ **Build Scripts**: Standard Next.js build commands
- ✅ **Clean Structure**: No dev artifacts that could cause issues
- ✅ **Docker Support**: Dockerfile automatically generated
- ✅ **Environment Ready**: Supports environment variable injection

## 🔧 Coolify Configuration Steps

### **Step 1: Create New Application in Coolify**

1. **Access Coolify Dashboard**
2. **Click "New Application"**
3. **Select "Git Repository"**
4. **Configure Repository**:
   - **Repository URL**: `https://github.com/shawa0507/trade_journal_production`
   - **Branch**: `main` (or `master` if that's your default)
   - **Build Pack**: Select "Docker" or let Coolify auto-detect

### **Step 2: Configure Environment Variables**

Add these environment variables in Coolify:

```bash
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Next.js Configuration
NODE_ENV=production
PORT=3000
```

### **Step 3: Configure Auto-Deployment**

1. **Enable Git Auto-Deploy**:
   - ✅ **Watch for changes**: Enable
   - ✅ **Auto-deploy on push**: Enable
   - ✅ **Branch**: `main` (or your default branch)

2. **Build Configuration**:
   - **Build Command**: `npm run build` (auto-detected)
   - **Start Command**: `npm start` (auto-detected)
   - **Port**: `3000`

### **Step 4: Domain Configuration**

1. **Set Custom Domain** (optional):
   - Add your domain in Coolify
   - Configure DNS records
   - Enable SSL/TLS

## 🚀 Multi-Platform Deployment Usage

### **Option 1: Deploy to Both Platforms**
```yaml
# GitHub Actions Workflow Trigger
Deployment Type: production-release
Target Platforms: both
```
**Result**: Creates configs for both Vercel and Coolify

### **Option 2: Coolify Only**
```yaml
# GitHub Actions Workflow Trigger
Deployment Type: production-release
Target Platforms: coolify-only
```
**Result**: Creates Dockerfile and docker-compose.yml only

### **Option 3: Vercel Only**
```yaml
# GitHub Actions Workflow Trigger
Deployment Type: production-release
Target Platforms: vercel-only
```
**Result**: Creates vercel.json only

## ⚡ Auto-Deployment Behavior

### **When Workflow Pushes to Production Repository**:

1. **Vercel** (if configured):
   - Detects push to production repository
   - Reads `vercel.json` configuration
   - Automatically builds and deploys
   - Updates production URL

2. **Coolify** (if configured):
   - Detects push to production repository
   - Uses Dockerfile for containerized build
   - Automatically builds and deploys
   - Updates application instance

### **No Conflicts Between Platforms**:
- ✅ **Independent Deployments**: Each platform deploys independently
- ✅ **Same Source Code**: Both use identical production files
- ✅ **Platform-Specific Configs**: Each uses its own configuration files
- ✅ **Separate Domains**: Each can have its own domain/URL

## 🔍 Verification Steps

### **After Running the Workflow**:

1. **Check Production Repository**:
   ```bash
   # Should contain platform-specific files
   ls trade_journal_production/
   # Expected: Dockerfile, docker-compose.yml, vercel.json (depending on selection)
   ```

2. **Monitor Coolify Dashboard**:
   - Check deployment logs
   - Verify build completion
   - Test application URL

3. **Monitor Vercel Dashboard**:
   - Check deployment status
   - Verify build completion
   - Test application URL

## 🚨 Troubleshooting

### **Coolify Not Detecting Changes**:
- Verify webhook is configured
- Check repository permissions
- Ensure branch name matches configuration

### **Build Failures**:
- Check environment variables are set
- Verify Dockerfile syntax
- Review build logs in Coolify

### **Port Conflicts**:
- Ensure PORT=3000 in environment variables
- Check Coolify port mapping configuration

## 📊 Benefits of Multi-Platform Setup

### **Redundancy**:
- ✅ **High Availability**: If one platform has issues, the other is still available
- ✅ **Load Distribution**: Can serve different regions/users

### **Flexibility**:
- ✅ **Platform Comparison**: Test performance and features of both platforms
- ✅ **Migration Options**: Easy to switch between platforms if needed

### **Development**:
- ✅ **Staging Environments**: Use different platforms for staging vs production
- ✅ **A/B Testing**: Compare platform performance

## 🎯 Next Steps

1. **Configure Coolify** as described above
2. **Run the enhanced workflow** with "both" platforms selected
3. **Verify both deployments** are successful
4. **Set up monitoring** for both platforms
5. **Configure custom domains** if needed

Your TradePivot application will now have robust multi-platform deployment capabilities! 🚀
