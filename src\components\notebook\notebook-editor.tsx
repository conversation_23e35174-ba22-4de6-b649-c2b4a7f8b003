"use client"

import React from 'react';
import { QuillNotebookEditor } from './quill-notebook-editor-new';
import { EditorContent } from '@/types/notebook';

interface NotebookEditorProps {
  content?: EditorContent | null
  onChange?: (content: EditorContent, htmlContent: string) => void
  placeholder?: string
  readOnly?: boolean
  autoFocus?: boolean
  className?: string
}

export function NotebookEditor({
  content,
  onChange,
  placeholder = "Start writing...",
  readOnly = false,
  autoFocus = false,
  className
}: NotebookEditorProps) {
  return (
    <QuillNotebookEditor
      content={content}
      onChange={onChange}
      editable={!readOnly}
      autofocus={autoFocus}
      className={className}
    />
  )
}
