"use client"

import React, { forwardRef, useEffect, useLayoutEffect, useRef, useState } from 'react';
import 'quill/dist/quill.snow.css';
import './QuillEditor.css';

// We need to import Quill dynamically to avoid SSR issues
let Quill: any = null;

// Editor is an uncontrolled React component
const QuillEditorDirect = forwardRef<any, {
  readOnly?: boolean;
  defaultValue?: any;
  placeholder?: string;
  onTextChange?: (delta: any, oldContents: any, source: string) => void;
  onSelectionChange?: (range: any, oldRange: any, source: string) => void;
  className?: string;
}>(
  ({
    readOnly = false,
    defaultValue,
    placeholder = 'Start typing...',
    onTextChange,
    onSelectionChange,
    className = ''
  }, ref) => {
    const containerRef = useRef<HTMLDivElement>(null);
    const defaultValueRef = useRef(defaultValue);
    const onTextChangeRef = useRef(onTextChange);
    const onSelectionChangeRef = useRef(onSelectionChange);
    const [isClient, setIsClient] = useState(false);
    const [quillLoaded, setQuillLoaded] = useState(false);

    // Update refs when props change
    useLayoutEffect(() => {
      onTextChangeRef.current = onTextChange;
      onSelectionChangeRef.current = onSelectionChange;
    });

    // Update readOnly state
    useEffect(() => {
      if (ref && typeof ref !== 'function' && ref.current) {
        ref.current.enable(!readOnly);
      }
    }, [ref, readOnly]);

    // Load Quill dynamically on client side
    useEffect(() => {
      setIsClient(true);

      const loadQuill = async () => {
        if (!Quill) {
          Quill = (await import('quill')).default;
          setQuillLoaded(true);
        } else {
          setQuillLoaded(true);
        }
      };

      loadQuill();
    }, []);

    // Initialize Quill
    useEffect(() => {
      if (!isClient || !quillLoaded || !containerRef.current) return;

      const container = containerRef.current;

      // Clear any existing content
      container.innerHTML = '';

      // Create editor container
      const editorContainer = container.appendChild(
        container.ownerDocument.createElement('div')
      );

      // Define toolbar options
      const modules = {
        toolbar: [
          [{ 'header': [1, 2, 3, 4, false] }],
          ['bold', 'italic', 'underline', 'strike'],
          [{ 'list': 'ordered' }, { 'list': 'bullet' }, { 'list': 'check' }],
          [{ 'align': [] }],
          ['link', 'image'],
          ['clean'],
          ['code-block', 'blockquote'],
          [{ 'color': [] }, { 'background': [] }],
          ['superscript', 'subscript'],
        ],
        clipboard: {
          matchVisual: false,
        },
        history: {
          delay: 1000,
          maxStack: 100,
          userOnly: true
        }
      };

      // Define toolbar options if not provided in modules
      if (!modules.toolbar) {
        modules.toolbar = [
          [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
          ['bold', 'italic', 'underline', 'strike'],
          [{ 'list': 'ordered' }, { 'list': 'bullet' }, { 'list': 'check' }],
          [{ 'align': [] }],
          ['link', 'image'],
          ['clean'],
          ['code-block', 'blockquote'],
          [{ 'color': [] }, { 'background': [] }],
          ['superscript', 'subscript'],
        ];
      }

      // Initialize Quill with toolbar
      const quill = new Quill(editorContainer, {
        theme: 'snow', // 'snow' theme includes the toolbar
        modules,
        placeholder,
      });

      // Set ref to Quill instance
      if (ref) {
        if (typeof ref === 'function') {
          ref(quill);
        } else {
          ref.current = quill;
        }
      }

      // Set initial content if provided
      if (defaultValueRef.current) {
        quill.setContents(defaultValueRef.current);
      }

      // Set up event listeners
      quill.on('text-change', (delta: any, oldContents: any, source: string) => {
        if (onTextChangeRef.current) {
          onTextChangeRef.current(delta, oldContents, source);
        }
      });

      quill.on('selection-change', (range: any, oldRange: any, source: string) => {
        if (onSelectionChangeRef.current) {
          onSelectionChangeRef.current(range, oldRange, source);
        }
      });

      // Focus the editor
      setTimeout(() => {
        quill.focus();
      }, 0);

      // Cleanup
      return () => {
        if (ref) {
          if (typeof ref === 'function') {
            ref(null);
          } else {
            ref.current = null;
          }
        }
        container.innerHTML = '';
      };
    }, [isClient, quillLoaded, ref, placeholder]);

    return (
      <div
        ref={containerRef}
        className={`quill-editor-direct ${className}`}
      />
    );
  }
);

QuillEditorDirect.displayName = 'QuillEditorDirect';

export default QuillEditorDirect;
