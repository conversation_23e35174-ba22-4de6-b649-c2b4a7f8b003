"use client"

import React from "react"
import { Strategy } from "@/types/playbook"
import { formatDistanceToNow } from "date-fns"
import { getCardGradient } from "@/lib/card-utils"

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { BarChart, FileText, Layers } from "lucide-react"

interface StrategyListViewProps {
  strategies: Strategy[]
  onSelectStrategy: (strategy: Strategy) => void
}

export function StrategyListView({
  strategies,
  onSelectStrategy,
}: StrategyListViewProps) {
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-emerald-500 text-white font-medium">Active</Badge>
      case 'testing':
        return <Badge className="bg-amber-500 text-white font-medium">Testing</Badge>
      case 'archived':
        return <Badge variant="outline" className="text-muted-foreground font-medium">Archived</Badge>
      default:
        return <Badge variant="outline" className="font-medium">{status}</Badge>
    }
  }

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-[250px]">Strategy Name</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Market Conditions</TableHead>
            <TableHead>Timeframes</TableHead>
            <TableHead className="text-center">Win Rate</TableHead>
            <TableHead className="text-center">Avg Win/Loss</TableHead>
            <TableHead className="text-center">Last Updated</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {strategies.map((strategy) => (
            <TableRow 
              key={strategy.id} 
              className="cursor-pointer hover:bg-muted/50 transition-colors"
              onClick={() => onSelectStrategy(strategy)}
            >
              <TableCell className="font-medium">
                <div className="flex items-center">
                  <div 
                    className="w-2 h-2 rounded-full mr-2 dark:shadow-glow"
                    style={{
                      backgroundColor: `var(--mode-light, ${getCardGradient(strategy.name).light.accent}) var(--mode-dark, ${getCardGradient(strategy.name).dark.accent})`,
                      '--mode-light': 'initial',
                      '--mode-dark': 'initial'
                    } as React.CSSProperties}
                  />
                  {strategy.name}
                </div>
              </TableCell>
              <TableCell>{getStatusBadge(strategy.status)}</TableCell>
              <TableCell>
                <div className="flex flex-wrap gap-1 max-w-[200px]">
                  {strategy.market_conditions.map((condition) => (
                    <Badge
                      key={condition}
                      variant="outline"
                      className="text-xs bg-background/60"
                    >
                      {condition.charAt(0).toUpperCase() + condition.slice(1).replace('_', ' ')}
                    </Badge>
                  ))}
                </div>
              </TableCell>
              <TableCell>
                <div className="flex flex-wrap gap-1">
                  {strategy.timeframes.map((timeframe) => (
                    <Badge
                      key={timeframe}
                      variant="outline"
                      className="text-xs bg-background/60"
                    >
                      {timeframe.toUpperCase()}
                    </Badge>
                  ))}
                </div>
              </TableCell>
              <TableCell className="text-center">
                {strategy.expected_win_rate !== null ? (
                  <span className="font-medium">{strategy.expected_win_rate}%</span>
                ) : (
                  <span className="text-muted-foreground">N/A</span>
                )}
              </TableCell>
              <TableCell className="text-center">
                {strategy.risk_reward_ratio !== null ? (
                  <span className="font-medium">{strategy.risk_reward_ratio}</span>
                ) : (
                  <span className="text-muted-foreground">N/A</span>
                )}
              </TableCell>
              <TableCell className="text-center text-muted-foreground">
                {formatDistanceToNow(new Date(strategy.updated_at), { addSuffix: true })}
              </TableCell>
            </TableRow>
          ))}
          {strategies.length === 0 && (
            <TableRow>
              <TableCell colSpan={7} className="h-24 text-center">
                No strategies found.
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  )
}
