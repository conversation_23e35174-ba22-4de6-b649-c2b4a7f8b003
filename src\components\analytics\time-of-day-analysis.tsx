"use client"

import { useState, useMemo } from "react"
import { Trade } from "@/types/trade"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { BarChart3, DollarSign, Hash, LineChartIcon, Percent as PercentIcon, TrendingUp } from "lucide-react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Ta<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  LineChart, Line, BarChart, Bar, XAxis, YAxis, CartesianGrid,
  Tooltip, Legend, ResponsiveContainer, Cell, ReferenceLine
} from "recharts"

interface TimeOfDayAnalysisProps {
  trades: Trade[]
}

type MetricType = "profit" | "winRate" | "tradeCount" | "averageProfit"
type TimeGrouping = "hour" | "session" | "day"

export function TimeOfDayAnalysis({ trades }: TimeOfDayAnalysisProps) {
  const [selectedMetric, setSelectedMetric] = useState<MetricType>("profit")
  const [timeGrouping, setTimeGrouping] = useState<TimeGrouping>("hour")
  const [chartType, setChartType] = useState<"bar" | "line">("bar")
  const [timeRange, setTimeRange] = useState<"all" | "1m" | "3m" | "6m" | "1y">("all")

  // Filter trades by time range only (include weekend trades)
  const filteredTrades = useMemo(() => {
    // Filter by time range
    let timeFilteredTrades = trades;
    if (timeRange !== "all") {
      const now = new Date()
      let startDate = new Date()

      switch (timeRange) {
        case "1m":
          startDate.setMonth(now.getMonth() - 1)
          break
        case "3m":
          startDate.setMonth(now.getMonth() - 3)
          break
        case "6m":
          startDate.setMonth(now.getMonth() - 6)
          break
        case "1y":
          startDate.setFullYear(now.getFullYear() - 1)
          break
      }

      timeFilteredTrades = trades.filter(trade => new Date(trade.time_close) >= startDate)
    }

    // Return all trades including weekends
    return timeFilteredTrades;
  }, [trades, timeRange])

  // Calculate total number of trades after filtering
  const totalFilteredTrades = useMemo(() => {
    return filteredTrades.length;
  }, [filteredTrades]);

  // Find weekend trades for debugging
  const weekendTrades = useMemo(() => {
    return filteredTrades.filter(trade => {
      const tradeDate = new Date(trade.time_close)
      const dayOfWeek = tradeDate.getUTCDay() // 0 = Sunday, 6 = Saturday
      return dayOfWeek === 0 || dayOfWeek === 6; // Weekend days
    });
  }, [filteredTrades]);

  // Count weekend trades
  const weekendTradesCount = useMemo(() => {
    return weekendTrades.length;
  }, [weekendTrades]);

  // Find Sunday trades specifically
  const sundayTrades = useMemo(() => {
    return filteredTrades.filter(trade => {
      const tradeDate = new Date(trade.time_close)
      const dayOfWeek = tradeDate.getUTCDay() // 0 = Sunday
      return dayOfWeek === 0; // Sunday only
    });
  }, [filteredTrades]);

  // Calculate total trades in the grouped data
  const calculateTotalGroupedTrades = (data: any[]) => {
    return data.reduce((total, item) => total + item.tradeCount, 0);
  };

  // Group trades by time
  const timeData = useMemo(() => {
    const timeMap = new Map<string, {
      timeLabel: string
      hour?: number
      totalProfit: number
      wins: number
      losses: number
      tradeCount: number
      sortOrder: number
    }>()

    // Initialize time slots based on grouping
    if (timeGrouping === "hour") {
      for (let i = 0; i < 24; i++) {
        const hourLabel = i.toString().padStart(2, '0') + ":00"
        timeMap.set(hourLabel, {
          timeLabel: hourLabel,
          hour: i,
          totalProfit: 0,
          wins: 0,
          losses: 0,
          tradeCount: 0,
          sortOrder: i
        })
      }
    } else if (timeGrouping === "session") {
      // Define trading sessions (times in UTC)
      const sessions = [
        { label: "Sydney", sortOrder: 0 }, // 20:00-04:00 UTC
        { label: "Tokyo", sortOrder: 1 },  // 04:00-08:00 UTC
        { label: "London", sortOrder: 2 }, // 08:00-16:00 UTC
        { label: "New York", sortOrder: 3 } // 16:00-20:00 UTC
      ]

      sessions.forEach(session => {
        timeMap.set(session.label, {
          timeLabel: session.label,
          totalProfit: 0,
          wins: 0,
          losses: 0,
          tradeCount: 0,
          sortOrder: session.sortOrder
        })
      })
    } else if (timeGrouping === "day") {
      // Include all days of the week including weekends
      const days = [
        { label: "Monday", sortOrder: 0 },
        { label: "Tuesday", sortOrder: 1 },
        { label: "Wednesday", sortOrder: 2 },
        { label: "Thursday", sortOrder: 3 },
        { label: "Friday", sortOrder: 4 },
        { label: "Saturday", sortOrder: 5 },
        { label: "Sunday", sortOrder: 6 }
      ]

      days.forEach(day => {
        timeMap.set(day.label, {
          timeLabel: day.label,
          totalProfit: 0,
          wins: 0,
          losses: 0,
          tradeCount: 0,
          sortOrder: day.sortOrder
        })
      })
    }

    filteredTrades.forEach(trade => {
      const tradeDate = new Date(trade.time_close)
      const isWin = trade.profit > 0

      let timeKey: string

      if (timeGrouping === "hour") {
        const hour = tradeDate.getUTCHours()
        timeKey = hour.toString().padStart(2, '0') + ":00"
      } else if (timeGrouping === "session") {
        // Determine trading session based on UTC hour
        const hour = tradeDate.getUTCHours()

        // Assign to primary session (avoiding overlaps)
        if (hour >= 20 || hour < 4) {
          timeKey = "Sydney" // Sydney session: 20:00-04:00 UTC
        } else if (hour >= 4 && hour < 8) {
          timeKey = "Tokyo"  // Tokyo session: 04:00-08:00 UTC (peak hours)
        } else if (hour >= 8 && hour < 16) {
          timeKey = "London" // London session: 08:00-16:00 UTC
        } else {
          timeKey = "New York" // New York session: 16:00-20:00 UTC
        }
      } else {
        // Day of week
        const days = ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"]
        const dayOfWeek = tradeDate.getUTCDay()

        // Special handling for Sunday trades in forex
        if (dayOfWeek === 0) { // Sunday
          // Check if this is a forex trade that might be incorrectly timestamped
          // For now, we'll just log it for investigation
          console.log('Sunday trade detected:', trade.id, trade.symbol, tradeDate.toISOString())

          // If it's a forex trade on Sunday, it's likely a data error or edge case
          // We'll reassign it to Friday for analysis purposes
          // This is a temporary fix until we can investigate the root cause
          if (trade.symbol.includes('/') || trade.symbol.includes('USD') || trade.symbol.includes('EUR')) {
            // This is likely a forex trade, reassign to Friday
            timeKey = "Friday"
          } else {
            // Keep as Sunday for other instruments that might legitimately trade on Sunday
            timeKey = days[dayOfWeek]
          }
        } else {
          timeKey = days[dayOfWeek]
        }
      }

      if (timeMap.has(timeKey)) {
        const data = timeMap.get(timeKey)!
        data.totalProfit += trade.profit
        data.tradeCount += 1

        if (isWin) {
          data.wins += 1
        } else {
          data.losses += 1
        }
      }
    })

    // Convert map to array and calculate additional metrics
    return Array.from(timeMap.values())
      .map(data => ({
        ...data,
        winRate: data.tradeCount > 0 ? (data.wins / data.tradeCount) * 100 : 0,
        averageProfit: data.tradeCount > 0 ? data.totalProfit / data.tradeCount : 0
      }))
      .sort((a, b) => a.sortOrder - b.sortOrder)
  }, [filteredTrades, timeGrouping])

  // Format the metric value for display
  const formatMetricValue = (value: number, metric: MetricType) => {
    switch (metric) {
      case "profit":
        return `$${value.toFixed(2)}`
      case "winRate":
        return `${value.toFixed(2)}%`
      case "tradeCount":
        return Math.round(value).toString()
      case "averageProfit":
        return `$${value.toFixed(2)}`
      default:
        return value.toFixed(2)
    }
  }

  // Get the label for the selected metric
  const getMetricLabel = (metric: MetricType) => {
    switch (metric) {
      case "profit":
        return "Total Profit"
      case "winRate":
        return "Win Rate (%)"
      case "tradeCount":
        return "Number of Trades"
      case "averageProfit":
        return "Average Profit per Trade"
      default:
        return ""
    }
  }

  // Custom tooltip for the chart
  const CustomTooltip = ({ active, payload }: { active?: boolean, payload?: any[] }) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload
      return (
        <div className="bg-background border rounded-md p-3 shadow-md">
          <p className="font-medium">{data.timeLabel}</p>
          <p className="text-sm text-muted-foreground">
            {getMetricLabel(selectedMetric)}: {formatMetricValue(data[selectedMetric === "profit" ? "totalProfit" : selectedMetric], selectedMetric)}
          </p>
          <p className="text-sm text-muted-foreground">Win Rate: {data.winRate.toFixed(2)}%</p>
          <p className="text-sm text-muted-foreground">Trades: {data.tradeCount}</p>
          <p className="text-sm text-muted-foreground">Avg Profit: ${data.averageProfit.toFixed(2)}</p>
        </div>
      )
    }
    return null
  }

  // Get colors for the bars/lines based on profit
  const getBarColor = (data: any) => {
    const value = data[selectedMetric === "profit" ? "totalProfit" : selectedMetric]
    if (selectedMetric === "profit" || selectedMetric === "averageProfit") {
      return value >= 0 ? "#10b981" : "#ef4444"
    }
    return "#3b82f6"
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row gap-4 justify-between">
        <div className="flex flex-col sm:flex-row gap-2">
          <Tabs value={selectedMetric} onValueChange={(value) => setSelectedMetric(value as MetricType)}>
            <TabsList className="grid w-[450px] grid-cols-4 rounded-none border-b bg-transparent">
              <TabsTrigger
                value="profit"
                className="rounded-none data-[state=active]:bg-background data-[state=active]:border-b data-[state=active]:border-primary data-[state=active]:font-medium"
              >
                <DollarSign className="mr-2 h-4 w-4" />
                Total Profit
              </TabsTrigger>
              <TabsTrigger
                value="winRate"
                className="rounded-none data-[state=active]:bg-background data-[state=active]:border-b data-[state=active]:border-primary data-[state=active]:font-medium"
              >
                <PercentIcon className="mr-2 h-4 w-4" />
                Win Rate
              </TabsTrigger>
              <TabsTrigger
                value="tradeCount"
                className="rounded-none data-[state=active]:bg-background data-[state=active]:border-b data-[state=active]:border-primary data-[state=active]:font-medium"
              >
                <Hash className="mr-2 h-4 w-4" />
                Trade Count
              </TabsTrigger>
              <TabsTrigger
                value="averageProfit"
                className="rounded-none data-[state=active]:bg-background data-[state=active]:border-b data-[state=active]:border-primary data-[state=active]:font-medium"
              >
                <TrendingUp className="mr-2 h-4 w-4" />
                Avg Profit
              </TabsTrigger>
            </TabsList>
          </Tabs>

          <Select value={timeGrouping} onValueChange={(value) => setTimeGrouping(value as TimeGrouping)}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Time Grouping" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="hour">Hour of Day</SelectItem>
              <SelectItem value="session">Trading Session</SelectItem>
              <SelectItem value="day">Day of Week</SelectItem>
            </SelectContent>
          </Select>

          <Select value={timeRange} onValueChange={(value) => setTimeRange(value as any)}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Time Range" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Time</SelectItem>
              <SelectItem value="1m">Last Month</SelectItem>
              <SelectItem value="3m">Last 3 Months</SelectItem>
              <SelectItem value="6m">Last 6 Months</SelectItem>
              <SelectItem value="1y">Last Year</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <Tabs value={chartType} onValueChange={(value) => setChartType(value as "bar" | "line")}>
          <TabsList className="grid w-[140px] grid-cols-2 rounded-none border-b bg-transparent">
            <TabsTrigger
              value="bar"
              className="rounded-none data-[state=active]:bg-background data-[state=active]:border-b data-[state=active]:border-primary data-[state=active]:font-medium"
            >
              <BarChart3 className="mr-2 h-4 w-4" />
              Bar
            </TabsTrigger>
            <TabsTrigger
              value="line"
              className="rounded-none data-[state=active]:bg-background data-[state=active]:border-b data-[state=active]:border-primary data-[state=active]:font-medium"
            >
              <LineChartIcon className="mr-2 h-4 w-4" />
              Line
            </TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      {timeData.length === 0 ? (
        <div className="flex justify-center items-center h-[400px] border rounded-md">
          <p className="text-muted-foreground">No data available for the selected time range</p>
        </div>
      ) : (
        <div className="h-[500px]">
          {chartType === "bar" ? (
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={timeData}
                margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
              >
                <CartesianGrid
                  strokeDasharray="3 3"
                  stroke="hsl(var(--border))"
                  opacity={0.4}
                  vertical={true}
                />
                <XAxis
                  dataKey="timeLabel"
                  tick={{
                    fontSize: 11,
                    fill: 'hsl(var(--muted-foreground))',
                    opacity: 0.7
                  }}
                  axisLine={{ stroke: 'hsl(var(--border))', opacity: 0.5 }}
                />
                <YAxis
                  tickFormatter={(value) => formatMetricValue(value, selectedMetric)}
                  label={{
                    value: getMetricLabel(selectedMetric),
                    angle: -90,
                    position: 'insideLeft',
                    offset: -10,
                    style: {
                      textAnchor: 'middle',
                      fill: 'hsl(var(--muted-foreground))',
                      fontSize: 12
                    }
                  }}
                  tick={{
                    fontSize: 11,
                    fill: 'hsl(var(--muted-foreground))',
                    opacity: 0.7
                  }}
                  axisLine={{ stroke: 'hsl(var(--border))', opacity: 0.5 }}
                  width={60}
                />
                <Tooltip content={<CustomTooltip />} />
                <Legend
                  verticalAlign="bottom"
                  height={36}
                  wrapperStyle={{
                    paddingBottom: '10px',
                    fontSize: '12px'
                  }}
                />
                {selectedMetric === "profit" || selectedMetric === "averageProfit" ? (
                  <ReferenceLine y={0} stroke="#666" />
                ) : null}
                <Bar
                  dataKey={selectedMetric === "profit" ? "totalProfit" :
                          selectedMetric === "averageProfit" ? "averageProfit" :
                          selectedMetric}
                  name={getMetricLabel(selectedMetric)}
                >
                  {timeData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={getBarColor(entry)} />
                  ))}
                </Bar>
              </BarChart>
            </ResponsiveContainer>
          ) : (
            <ResponsiveContainer width="100%" height="100%">
              <LineChart
                data={timeData}
                margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
              >
                <CartesianGrid
                  strokeDasharray="3 3"
                  stroke="hsl(var(--border))"
                  opacity={0.4}
                  vertical={true}
                />
                <XAxis
                  dataKey="timeLabel"
                  tick={{
                    fontSize: 11,
                    fill: 'hsl(var(--muted-foreground))',
                    opacity: 0.7
                  }}
                  axisLine={{ stroke: 'hsl(var(--border))', opacity: 0.5 }}
                />
                <YAxis
                  tickFormatter={(value) => formatMetricValue(value, selectedMetric)}
                  label={{
                    value: getMetricLabel(selectedMetric),
                    angle: -90,
                    position: 'insideLeft',
                    offset: -10,
                    style: {
                      textAnchor: 'middle',
                      fill: 'hsl(var(--muted-foreground))',
                      fontSize: 12
                    }
                  }}
                  tick={{
                    fontSize: 11,
                    fill: 'hsl(var(--muted-foreground))',
                    opacity: 0.7
                  }}
                  axisLine={{ stroke: 'hsl(var(--border))', opacity: 0.5 }}
                  width={60}
                />
                <Tooltip content={<CustomTooltip />} />
                <Legend
                  verticalAlign="bottom"
                  height={36}
                  wrapperStyle={{
                    paddingBottom: '10px',
                    fontSize: '12px'
                  }}
                />
                {selectedMetric === "profit" || selectedMetric === "averageProfit" ? (
                  <ReferenceLine y={0} stroke="#666" />
                ) : null}
                <Line
                  type="monotone"
                  dataKey={selectedMetric === "profit" ? "totalProfit" :
                          selectedMetric === "averageProfit" ? "averageProfit" :
                          selectedMetric}
                  name={getMetricLabel(selectedMetric)}
                  stroke="#3b82f6"
                  strokeWidth={2}
                  dot={{ r: 4 }}
                  activeDot={{ r: 6 }}
                />
              </LineChart>
            </ResponsiveContainer>
          )}
        </div>
      )}

      {/* Trade Count Verification */}
      <div className="mb-4 p-3 border rounded-md bg-muted/20">
        <div className="text-sm font-medium">Trade Count Verification</div>
        <div className="flex flex-col gap-2 mt-1">
          <div className="flex flex-wrap gap-4">
            <div className="text-sm">
              <span className="text-muted-foreground">Total Trades: </span>
              <span className="font-medium">{totalFilteredTrades}</span>
            </div>
            <div className="text-sm">
              <span className="text-muted-foreground">Weekend Trades: </span>
              <span className="font-medium">{weekendTradesCount}</span>
            </div>
            <div className="text-sm">
              <span className="text-muted-foreground">Weekday Trades: </span>
              <span className="font-medium">{totalFilteredTrades - weekendTradesCount}</span>
            </div>
          </div>

          <div className="flex flex-wrap gap-4">
            <div className="text-sm">
              <span className="text-muted-foreground">Total Grouped Trades: </span>
              <span className="font-medium">{calculateTotalGroupedTrades(timeData)}</span>
            </div>
            <div className="text-sm">
              <span className="text-muted-foreground">Verification: </span>
              <span className={`font-medium ${totalFilteredTrades === calculateTotalGroupedTrades(timeData) ? 'text-green-500' : 'text-red-500'}`}>
                {totalFilteredTrades === calculateTotalGroupedTrades(timeData) ? 'Matched ✓' : 'Mismatch ✗'}
              </span>
            </div>
            <div className="text-sm">
              <span className="text-muted-foreground">Note: </span>
              <span className="font-medium">All trades are included. Forex trades on Sunday are reassigned to Friday.</span>
            </div>
          </div>

          {/* Display Sunday trades for investigation */}
          {sundayTrades.length > 0 && (
            <div className="mt-2 p-2 bg-yellow-100 dark:bg-yellow-900/30 rounded-md">
              <div className="text-sm font-medium mb-1">Sunday Trade Investigation:</div>
              {sundayTrades.map((trade, index) => {
                const tradeDate = new Date(trade.time_close);
                return (
                  <div key={index} className="text-xs space-y-1">
                    <div><span className="font-medium">ID:</span> {trade.id}</div>
                    <div><span className="font-medium">Symbol:</span> {trade.symbol}</div>
                    <div><span className="font-medium">Close Time:</span> {tradeDate.toISOString()}</div>
                    <div><span className="font-medium">Local Time:</span> {tradeDate.toString()}</div>
                    <div><span className="font-medium">UTC Day:</span> {tradeDate.getUTCDay()} (0=Sunday)</div>
                    <div><span className="font-medium">Local Day:</span> {tradeDate.getDay()} (0=Sunday)</div>
                  </div>
                );
              })}
            </div>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Best Time Card */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-base">Best Time</CardTitle>
            <CardDescription>
              Highest {getMetricLabel(selectedMetric).toLowerCase()}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {timeData.length > 0 ? (
              (() => {
                const bestTime = [...timeData].sort((a, b) => {
                  const aValue = a[selectedMetric === "profit" ? "totalProfit" : selectedMetric]
                  const bValue = b[selectedMetric === "profit" ? "totalProfit" : selectedMetric]
                  return bValue - aValue
                })[0]

                return (
                  <div className="space-y-1">
                    <div className="text-2xl font-bold">{bestTime.timeLabel}</div>
                    <div className="text-sm text-muted-foreground">
                      {getMetricLabel(selectedMetric)}: {formatMetricValue(bestTime[selectedMetric === "profit" ? "totalProfit" : selectedMetric], selectedMetric)}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      Win Rate: {bestTime.winRate.toFixed(2)}% ({bestTime.wins}/{bestTime.tradeCount})
                    </div>
                  </div>
                )
              })()
            ) : (
              <div className="text-muted-foreground">No data available</div>
            )}
          </CardContent>
        </Card>

        {/* Worst Time Card */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-base">Worst Time</CardTitle>
            <CardDescription>
              Lowest {getMetricLabel(selectedMetric).toLowerCase()}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {timeData.length > 0 ? (
              (() => {
                const worstTime = [...timeData].sort((a, b) => {
                  const aValue = a[selectedMetric === "profit" ? "totalProfit" : selectedMetric]
                  const bValue = b[selectedMetric === "profit" ? "totalProfit" : selectedMetric]
                  return aValue - bValue
                })[0]

                return (
                  <div className="space-y-1">
                    <div className="text-2xl font-bold">{worstTime.timeLabel}</div>
                    <div className="text-sm text-muted-foreground">
                      {getMetricLabel(selectedMetric)}: {formatMetricValue(worstTime[selectedMetric === "profit" ? "totalProfit" : selectedMetric], selectedMetric)}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      Win Rate: {worstTime.winRate.toFixed(2)}% ({worstTime.wins}/{worstTime.tradeCount})
                    </div>
                  </div>
                )
              })()
            ) : (
              <div className="text-muted-foreground">No data available</div>
            )}
          </CardContent>
        </Card>

        {/* Most Active Time Card */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-base">Most Active Time</CardTitle>
            <CardDescription>
              Highest number of trades
            </CardDescription>
          </CardHeader>
          <CardContent>
            {timeData.length > 0 ? (
              (() => {
                const mostActiveTime = [...timeData].sort((a, b) => b.tradeCount - a.tradeCount)[0]

                return (
                  <div className="space-y-1">
                    <div className="text-2xl font-bold">{mostActiveTime.timeLabel}</div>
                    <div className="text-sm text-muted-foreground">
                      Trades: {mostActiveTime.tradeCount}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      Win Rate: {mostActiveTime.winRate.toFixed(2)}% ({mostActiveTime.wins}/{mostActiveTime.tradeCount})
                    </div>
                  </div>
                )
              })()
            ) : (
              <div className="text-muted-foreground">No data available</div>
            )}
          </CardContent>
        </Card>

        {/* Highest Win Rate Card */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-base">Highest Win Rate</CardTitle>
            <CardDescription>
              Best win percentage
            </CardDescription>
          </CardHeader>
          <CardContent>
            {timeData.length > 0 ? (
              (() => {
                // Filter times with at least 5 trades for statistical significance
                const significantTimes = timeData.filter(time => time.tradeCount >= 5)

                if (significantTimes.length === 0) {
                  return <div className="text-muted-foreground">Insufficient data</div>
                }

                const highestWinRateTime = [...significantTimes].sort((a, b) => b.winRate - a.winRate)[0]

                return (
                  <div className="space-y-1">
                    <div className="text-2xl font-bold">{highestWinRateTime.timeLabel}</div>
                    <div className="text-sm text-muted-foreground">
                      Win Rate: {highestWinRateTime.winRate.toFixed(2)}%
                    </div>
                    <div className="text-sm text-muted-foreground">
                      Trades: {highestWinRateTime.wins}/{highestWinRateTime.tradeCount}
                    </div>
                  </div>
                )
              })()
            ) : (
              <div className="text-muted-foreground">No data available</div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
