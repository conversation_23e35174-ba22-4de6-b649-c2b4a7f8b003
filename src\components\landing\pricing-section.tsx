"use client"

import { motion } from 'framer-motion'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Check } from 'lucide-react'

export default function PricingSection() {
    return (
        <section id="pricing" className="py-16 md:py-32 bg-muted/30">
            <div className="mx-auto max-w-6xl px-6">
                <motion.div 
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true, margin: "-100px" }}
                    transition={{ duration: 0.6 }}
                    className="mx-auto max-w-2xl space-y-6 text-center">
                    <h2 className="text-center text-3xl font-bold md:text-4xl lg:text-5xl">Pricing Plans for Every Trader</h2>
                    <p className="text-muted-foreground">Choose the plan that fits your trading needs, from casual traders to professional fund managers.</p>
                </motion.div>

                <motion.div 
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true, margin: "-100px" }}
                    transition={{ duration: 0.6, delay: 0.2 }}
                    className="mt-8 grid gap-6 md:mt-16 md:grid-cols-3">
                    <Card>
                        <CardHeader>
                            <CardTitle className="font-medium">Free</CardTitle>

                            <span className="my-3 block text-2xl font-semibold">$0 / mo</span>

                            <CardDescription className="text-sm">Perfect for beginners</CardDescription>
                            <Button asChild variant="outline" className="mt-4 w-full">
                                <Link href="/auth?tab=register">Get Started</Link>
                            </Button>
                        </CardHeader>

                        <CardContent className="space-y-4">
                            <hr className="border-dashed" />

                            <ul className="list-outside space-y-3 text-sm">
                                {[
                                    'Basic Analytics Dashboard', 
                                    'Up to 100 trades', 
                                    'Trading Journal', 
                                    'Performance Metrics',
                                    'Trade Import from MT4/MT5'
                                ].map((item, index) => (
                                    <li key={index} className="flex items-center gap-2">
                                        <Check className="size-3 text-primary" />
                                        {item}
                                    </li>
                                ))}
                            </ul>
                        </CardContent>
                    </Card>

                    <Card className="relative">
                        <span className="absolute inset-x-0 -top-3 mx-auto flex h-6 w-fit items-center rounded-full bg-gradient-to-r from-primary/80 to-primary px-3 py-1 text-xs font-medium text-primary-foreground">Popular</span>

                        <CardHeader>
                            <CardTitle className="font-medium">Pro</CardTitle>

                            <span className="my-3 block text-2xl font-semibold">$19 / mo</span>

                            <CardDescription className="text-sm">For active traders</CardDescription>

                            <Button asChild className="mt-4 w-full">
                                <Link href="/auth?tab=register">Get Started</Link>
                            </Button>
                        </CardHeader>

                        <CardContent className="space-y-4">
                            <hr className="border-dashed" />

                            <ul className="list-outside space-y-3 text-sm">
                                {[
                                    'Everything in Free Plan', 
                                    'Unlimited Trades', 
                                    'Advanced Analytics', 
                                    'Strategy Management',
                                    'Custom Metrics & Goals',
                                    'Trading Calendar',
                                    'Performance Reporting',
                                    'Data Export',
                                    'Email Support'
                                ].map((item, index) => (
                                    <li key={index} className="flex items-center gap-2">
                                        <Check className="size-3 text-primary" />
                                        {item}
                                    </li>
                                ))}
                            </ul>
                        </CardContent>
                    </Card>

                    <Card className="flex flex-col">
                        <CardHeader>
                            <CardTitle className="font-medium">Enterprise</CardTitle>

                            <span className="my-3 block text-2xl font-semibold">$49 / mo</span>

                            <CardDescription className="text-sm">For professional traders</CardDescription>

                            <Button asChild variant="outline" className="mt-4 w-full">
                                <Link href="/auth?tab=register">Get Started</Link>
                            </Button>
                        </CardHeader>

                        <CardContent className="space-y-4">
                            <hr className="border-dashed" />

                            <ul className="list-outside space-y-3 text-sm">
                                {[
                                    'Everything in Pro Plan', 
                                    'Multi-Account Management', 
                                    'Team Collaboration', 
                                    'API Access',
                                    'Custom Integrations',
                                    'Priority Support',
                                    'Dedicated Account Manager'
                                ].map((item, index) => (
                                    <li key={index} className="flex items-center gap-2">
                                        <Check className="size-3 text-primary" />
                                        {item}
                                    </li>
                                ))}
                            </ul>
                        </CardContent>
                    </Card>
                </motion.div>
            </div>
        </section>
    )
}
