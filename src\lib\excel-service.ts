import { type ProcessedData } from "@/lib/excel-processor"

// Maximum file size (9MB)
const MAX_FILE_SIZE = 9 * 1024 * 1024

/**
 * Uploads an Excel file to the API route for parsing
 * @param file The Excel file to upload
 * @returns The processed data from the Excel file
 */
export async function uploadExcelFile(file: File): Promise<ProcessedData> {
  // Check file size
  if (file.size > MAX_FILE_SIZE) {
    throw new Error(`File size exceeds the 9MB limit (${(file.size / (1024 * 1024)).toFixed(2)}MB)`)
  }

  // Check file type
  const validTypes = [
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
    'application/vnd.ms-excel', // .xls
    'text/csv', // .csv
  ]

  if (!validTypes.includes(file.type)) {
    throw new Error('Invalid file type. Only Excel (.xlsx, .xls) and CSV files are supported')
  }

  try {
    // Create form data
    const formData = new FormData()
    formData.append('file', file)

    // Call the API route
    const response = await fetch('/api/parse-excel', {
      method: 'POST',
      body: formData,
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.error || 'Failed to process Excel file')
    }

    const result = await response.json()

    if (!result.data) {
      throw new Error('Failed to process Excel file')
    }

    return result.data as ProcessedData
  } catch (error) {
    console.error('Error uploading Excel file:', error)
    throw new Error(error instanceof Error ? error.message : 'Failed to upload Excel file')
  }
}

/**
 * Validates the processed data to ensure it contains the required fields
 * @param data The processed data to validate
 * @returns True if the data is valid, false otherwise
 */
export function validateProcessedData(data: ProcessedData): boolean {
  if (!data) return false

  // Check account info
  if (!data.account || !data.account.account) return false

  // Check trades
  if (!data.trades || !Array.isArray(data.trades) || data.trades.length === 0) return false

  // Check summary
  if (!data.summary || !data.summary.total_trades) return false

  return true
}
