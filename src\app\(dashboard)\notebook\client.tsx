"use client"

import { useState, useRef, use<PERSON><PERSON>back, useEffect } from "react"
import { NotebookEntry, NotebookCategory, NotebookTag, NotebookFolderWithMeta, EditorContent } from "@/types/notebook"
import {
  useNotebookEntries,
  useCreateNotebookEntry,
  useImportJournalEntries,
  useNotebookFolders,
  useNotebookTags,
  useCreateNotebookFolder,
  useDeleteNotebookFolder,
  useUpdateNotebookFolder
} from "@/hooks/use-notebook"
import { useAccount } from "@/contexts/account-context"
import { NotebookList } from "@/components/notebook/notebook-list"
import { NotebookSidebar } from "@/components/notebook/notebook-sidebar"
import { NewNotebookEditor } from "@/components/notebook/new-notebook-editor"
import { CompactNoteHeader } from "@/components/notebook/compact-note-header"
import { EditorTransitionWrapper } from "@/components/notebook/editor-transition-wrapper"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Button } from "@/components/ui/button"
// Dialog components are used in the sidebar component
import { toast } from "sonner"

interface NotebookClientProps {
  userId: string
  initialEntries: NotebookEntry[]
  initialCount: number
  initialCategories: NotebookCategory[]
  initialTags: NotebookTag[]
  initialFolders: NotebookFolderWithMeta[]
  serverSelectedAccountId: string | null // Add server-provided account ID
}

export default function NotebookClient({
  // userId is required for the type but not used directly
  userId: _userId,
  initialEntries,
  initialCount,
  initialCategories,
  initialTags,
  initialFolders,
  serverSelectedAccountId
}: NotebookClientProps) {
  // No router needed
  const { selectedAccountId, isInitializing } = useAccount()

  // State for filters
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState<string | undefined>(undefined)
  const [selectedFolderId, setSelectedFolderId] = useState<string | undefined>(undefined)
  const [selectedTags, setSelectedTags] = useState<string[]>([])
  const [showTemplates, setShowTemplates] = useState(false)

  // Function to expand a category folder - will be set by the sidebar component
  const expandCategoryFolderRef = useRef<((categoryName: string) => void) | null>(null)

  // State for note editor
  const [isCreatingNewNote, setIsCreatingNewNote] = useState(false)
  const [isEditingNote, setIsEditingNote] = useState(false)
  const [newNoteTitle, setNewNoteTitle] = useState("")
  const [newNoteCategory, setNewNoteCategory] = useState("")
  const [newNoteFolderId, setNewNoteFolderId] = useState<string | null>(null)
  const [newNoteTags, setNewNoteTags] = useState<string[]>([])
  const [newTagInput, setNewTagInput] = useState("")
  const [newNoteContent, setNewNoteContent] = useState<EditorContent | null>(null)
  const [newNoteHtmlContent, setNewNoteHtmlContent] = useState("")
  const [newNoteScreenshots, setNewNoteScreenshots] = useState<string[]>([])
  const [isTemplate, setIsTemplate] = useState(false)
  const [selectedNoteId, setSelectedNoteId] = useState<string | null>(null)

  // State for transition animations
  const [isTransitioning, setIsTransitioning] = useState(false)
  const editorRef = useRef<any>(null)

  // Use custom hooks
  const { createEntry, isCreating } = useCreateNotebookEntry()
  const { importEntries, isImporting } = useImportJournalEntries()
  const { folders, isLoading: isFolderLoading, refetch: refetchFolders } = useNotebookFolders(initialFolders, serverSelectedAccountId)
  const { tags, isLoading: isTagsLoading, refetch: refetchTags } = useNotebookTags(initialTags)
  const { createFolder } = useCreateNotebookFolder()
  const { deleteFolder } = useDeleteNotebookFolder()
  const { updateFolder } = useUpdateNotebookFolder()

  // Refetch folders and tags when account changes to update counts
  useEffect(() => {
    refetchFolders()
    refetchTags()
  }, [selectedAccountId, refetchFolders, refetchTags])

  // Handle transition from server account ID to client account ID
  useEffect(() => {
    // When account context finishes initializing, refetch if account IDs don't match
    if (!isInitializing && selectedAccountId !== serverSelectedAccountId) {
      refetchFolders()
      refetchTags()
    }
  }, [isInitializing, selectedAccountId, serverSelectedAccountId, refetchFolders, refetchTags])

  // We'll implement the update functionality directly in this component

  const {
    entries,
    count,
    isLoading,
    page,
    setPage,
    refetch
  } = useNotebookEntries(initialEntries, initialCount, {
    filters: {
      searchTerm,
      category: selectedCategory,
      folderId: selectedFolderId,
      tags: selectedTags.length > 0 ? selectedTags : undefined,
      isTemplate: showTemplates ? true : undefined,
      accountId: selectedAccountId
    }
  })

  // ===== NEW CLEAN SEARCH LOGIC =====

  // Simple search handler - just updates search term, no automatic smart detection
  const handleSearch = (term: string) => {
    console.log(`🔍 Search input: "${term}"`)
    setSearchTerm(term)
    setPage(1)
  }

  // Helper function to find matching folder (only regular folders, not category folders)
  const findMatchingFolder = (searchTerm: string) => {
    if (!searchTerm.trim() || folders.length === 0) return null

    const trimmedTerm = searchTerm.trim().toLowerCase()
    console.log(`🔍 Looking for folder matching: "${trimmedTerm}"`)

    // Filter to only include regular folders (exclude category folders)
    const regularFolders = folders.filter(folder => {
      // Exclude category folders (those with "IsCategory: true" in description)
      const isCategory = folder.description?.includes('IsCategory: true')
      return !isCategory
    })

    console.log(`📁 Searching in ${regularFolders.length} regular folders (excluding ${folders.length - regularFolders.length} category folders)`)

    // Try exact match first
    let matchingFolder = regularFolders.find(folder =>
      folder.name.toLowerCase() === trimmedTerm
    )

    // If no exact match, try partial match (minimum 3 characters)
    if (!matchingFolder && trimmedTerm.length >= 3) {
      matchingFolder = regularFolders.find(folder =>
        folder.name.toLowerCase().includes(trimmedTerm)
      )
    }

    if (matchingFolder) {
      console.log(`📁 Found matching regular folder: "${matchingFolder.name}" (ID: ${matchingFolder.id})`)
      console.log(`📁 Folder type: ${matchingFolder.is_system ? 'System' : 'User-created'}`)
    } else {
      console.log(`📁 No matching regular folder found for: "${trimmedTerm}"`)
      console.log(`📁 Available regular folders:`, regularFolders.map(f => f.name))
    }

    return matchingFolder
  }

  // Helper function to find matching tag
  const findMatchingTag = (searchTerm: string) => {
    if (!searchTerm.trim() || tags.length === 0) return null

    const trimmedTerm = searchTerm.trim().toLowerCase()
    console.log(`🔍 Looking for tag matching: "${trimmedTerm}"`)

    const matchingTag = tags.find(tag =>
      tag.name.toLowerCase() === trimmedTerm
    )

    if (matchingTag) {
      console.log(`🏷️ Found matching tag: "${matchingTag.name}"`)
    } else {
      console.log(`🏷️ No matching tag found for: "${trimmedTerm}"`)
    }

    return matchingTag
  }

  // Function to manually apply folder filter from search
  const applyFolderFilter = () => {
    const folder = findMatchingFolder(searchTerm)
    if (!folder) {
      toast.error("No regular folder found matching your search. Only folders that contain notes are searchable.")
      return false
    }

    if (selectedFolderId === folder.id) {
      toast.info(`📁 Folder "${folder.name}" is already selected`)
      return false
    }

    console.log(`✅ Applying folder filter: ${folder.name}`)

    // Clear other filters
    setSelectedCategory(undefined)
    setSelectedTags([])
    setShowTemplates(false)

    // Set folder
    setSelectedFolderId(folder.id)

    // Clear search term
    setSearchTerm("")

    // Show success message
    toast.success(`📁 Showing all content in folder: ${folder.name}`)

    // Refetch data
    setTimeout(() => refetch(), 100)

    return true
  }

  // Function to manually apply tag filter from search
  const applyTagFilter = () => {
    const tag = findMatchingTag(searchTerm)
    if (!tag) {
      toast.error("No tag found matching your search")
      return false
    }

    if (selectedTags.includes(tag.name)) {
      toast.info(`🏷️ Tag "${tag.name}" is already selected`)
      return false
    }

    console.log(`✅ Applying tag filter: ${tag.name}`)

    // Add tag to selected tags
    setSelectedTags(prev => [...prev, tag.name])

    // Clear search term
    setSearchTerm("")

    // Show success message
    toast.success(`🏷️ Added tag filter: ${tag.name}`)

    return true
  }

  // Handle category selection
  const handleSelectCategory = (category: string | undefined) => {
    setSelectedCategory(category)
    setPage(1)
  }

  // Handle tag selection
  const handleSelectTag = (tag: string) => {
    setSelectedTags(prev =>
      prev.includes(tag)
        ? prev.filter(t => t !== tag)
        : [...prev, tag]
    )
    setPage(1)
  }

  // Handle templates toggle
  const handleToggleTemplates = () => {
    setShowTemplates(prev => !prev)
    setPage(1)
  }

  // Handle create category
  const handleCreateCategory = async (category: string) => {
    try {
      // Create a category folder with the IsCategory flag
      await handleCreateFolder(category, "#725ac1", "category", undefined, true);

      // Select the category
      setSelectedCategory(category);

      // Refetch folders to update the UI
      refetchFolders();

      // Log success for debugging
      console.log(`Category "${category}" created successfully`);
    } catch (error) {
      console.error("Error creating category:", error);
      toast.error("Failed to create category");
    }
  }

  // Handle folder selection
  const handleSelectFolder = (folderId: string | undefined) => {
    // Clear other filters when selecting a folder
    if (folderId) {
      // First clear all filters to ensure clean state
      setSelectedCategory(undefined)
      setSelectedTags([])
      setShowTemplates(false)
      setSearchTerm("")

      // Then set the folder ID
      setSelectedFolderId(folderId)

      // Log the folder selection for debugging
      console.log(`Selected folder ID: ${folderId}`)
    } else {
      setSelectedFolderId(undefined)
      console.log("Cleared folder selection")
    }

    // Reset to first page
    setPage(1)

    // Force an immediate refetch of entries with the new filter
    // This is critical to ensure the UI updates correctly
    refetch().then(() => {
      console.log("Refetched entries after folder selection")
    }).catch(error => {
      console.error("Error refetching entries after folder selection:", error)
    })
  }

  // Handle create folder
  const handleCreateFolder = async (name: string, color?: string, icon?: string, parentCategory?: string, isCategory?: boolean) => {
    try {
      // If it's a category folder, store that in the description
      const description = isCategory
        ? `IsCategory: true`
        : parentCategory
          ? `Category: ${parentCategory}`
          : null;

      // Create the folder
      const newFolder = await createFolder({
        name,
        color: color || "#725ac1",
        icon: icon || (isCategory ? "category" : "folder"),
        description,
        is_system: false,
        is_category: isCategory || false
      });

      // If a parent category was specified, select it after creating the folder
      if (parentCategory) {
        setSelectedCategory(parentCategory);

        // Expand the category folder to show the new folder immediately
        if (isCategory === false && expandCategoryFolderRef.current) {
          // Use the function provided by the sidebar to expand the category
          expandCategoryFolderRef.current(parentCategory);
          console.log(`Expanded category "${parentCategory}" to show new folder "${name}"`);
        }
      }

      // Log success for debugging
      console.log(`${isCategory ? 'Category' : 'Folder'} "${name}" created successfully with ID: ${newFolder?.id}`);

      // Return the newly created folder
      return newFolder;
    } catch (error) {
      // Error handling is now done in the useCreateNotebookFolder hook
      console.error("Error creating folder:", error);
      return null;
    }
  }

  // Handle delete folder
  const handleDeleteFolder = async (id: string) => {
    try {
      // Use the optimized deleteFolder function from the hook
      await deleteFolder(id);

      // If the deleted folder was selected, reset the selection
      if (selectedFolderId === id) {
        setSelectedFolderId(undefined);
      }

      // Refetch entries to update the UI
      refetch();
    } catch (error) {
      console.error("Error deleting folder:", error);
      // Error handling is done in the hook
    }
  }

  // Handle move folder to category
  const handleMoveFolderToCategory = async (folderId: string, categoryName: string | null) => {
    try {
      const folder = folders.find(f => f.id === folderId);
      if (!folder) {
        throw new Error("Folder not found");
      }

      // Update the folder's description to include the category
      const description = categoryName
        ? `Category: ${categoryName}`
        : null;

      // Use the optimized updateFolder function from the hook
      await updateFolder(folderId, { description });

      // If the category folder is expanded, make sure it stays expanded
      if (categoryName && expandCategoryFolderRef.current) {
        expandCategoryFolderRef.current(categoryName);
      }

      // Refetch entries to update the UI
      refetch();
    } catch (error) {
      console.error("Error moving folder to category:", error);
      // Error handling is done in the hook
    }
  }

  // Handle rename folder
  const handleRenameFolder = async (folderId: string, newName: string) => {
    try {
      // Use the optimized updateFolder function from the hook
      await updateFolder(folderId, { name: newName });

      // Refetch entries to update the UI
      refetch();
    } catch (error) {
      console.error("Error renaming folder:", error);
      // Error handling is done in the hook
    }
  }

  // Handle create new note
  const handleCreateNewNote = () => {
    // Start transition if switching from viewing a note
    if (selectedNoteId) {
      setIsTransitioning(true)
    }

    setIsCreatingNewNote(true)
    setSelectedNoteId(null)

    // Set the folder ID if a folder is selected
    if (selectedFolderId) {
      setNewNoteFolderId(selectedFolderId)
    }

    resetNewNoteForm()
  }

  // Handle create note from folder
  const handleCreateNoteFromFolder = (folderId: string) => {
    setIsCreatingNewNote(true)
    setSelectedNoteId(null)
    setNewNoteFolderId(folderId)

    // Find the folder to get its name for the title
    const folder = folders.find(f => f.id === folderId)
    if (folder) {
      setNewNoteTitle(`Note in ${folder.name}`)
    }

    resetNewNoteForm()
  }

  // Handle import journal entries
  const handleImportJournalEntries = async () => {
    try {
      await importEntries()
      refetch()
      refetchFolders()
    } catch (error) {
      console.error("Error importing journal entries:", error)
    }
  }

  // Handle transition completion
  const handleTransitionComplete = useCallback(() => {
    // Focus the editor after transition completes
    if (editorRef.current && typeof editorRef.current.focus === 'function') {
      setTimeout(() => {
        try {
          editorRef.current.focus()
        } catch (error) {
          console.warn('Could not focus editor after transition:', error)
        }
      }, 100)
    }
  }, [])

  // Handle view note with transition
  const handleViewNote = (id: string | null) => {
    if (!id) return;

    // Start transition if switching to a different note
    const isDifferentNote = selectedNoteId !== id
    if (isDifferentNote) {
      setIsTransitioning(true)
    }

    // Reset state first to ensure clean loading
    setNewNoteContent(null)
    setNewNoteHtmlContent("")

    // Then set the new note ID and mode
    setSelectedNoteId(id)
    setIsCreatingNewNote(false)
    setIsEditingNote(false) // Ensure we start in view mode

    // Fetch the note data
    const selectedNote = entries.find(entry => entry.id === id)
    if (selectedNote) {
      try {
        // Parse the content if it's a string
        if (typeof selectedNote.content === 'string') {
          try {
            // First try to parse it as JSON
            const parsedContent = JSON.parse(selectedNote.content);

            // Check if it's already in the correct format
            if (parsedContent && typeof parsedContent === 'object') {
              if (parsedContent.type === 'doc') {
                // It's already in the correct format for Tiptap/ProseMirror
                setNewNoteContent(parsedContent);
                console.log("Content loaded successfully in doc format:", parsedContent);
              } else if (parsedContent.ops) {
                // It's in Quill Delta format
                setNewNoteContent(parsedContent);
                console.log("Content loaded successfully in Quill Delta format:", parsedContent);
              } else {
                // It might be nested, try to parse it again
                try {
                  const nestedContent = typeof parsedContent === 'string'
                    ? JSON.parse(parsedContent)
                    : parsedContent;

                  if (nestedContent && (nestedContent.type === 'doc' || nestedContent.ops)) {
                    setNewNoteContent(nestedContent);
                    console.log("Content loaded successfully from nested format:", nestedContent);
                  } else {
                    console.error("Invalid content format:", nestedContent);
                    // Create a fallback document
                    setNewNoteContent({
                      type: "doc",
                      content: [
                        {
                          type: "paragraph",
                          content: [{ type: "text", text: JSON.stringify(parsedContent) }]
                        }
                      ]
                    });
                  }
                } catch (e) {
                  console.error("Error parsing nested content:", e);
                  // Use the first level parsed content as fallback
                  if (typeof parsedContent === 'string') {
                    setNewNoteContent({
                      type: "doc",
                      content: [
                        {
                          type: "paragraph",
                          content: [{ type: "text", text: parsedContent }]
                        }
                      ]
                    });
                  } else {
                    setNewNoteContent(parsedContent);
                  }
                }
              }
            } else {
              console.error("Invalid content format:", parsedContent);
              // Create a fallback document
              setNewNoteContent({
                type: "doc",
                content: [
                  {
                    type: "paragraph",
                    content: [{ type: "text", text: JSON.stringify(parsedContent) }]
                  }
                ]
              });
            }
          } catch (e) {
            console.error("Error parsing note content:", e);
            // Create a simple document with the content as text
            setNewNoteContent({
              type: "doc",
              content: [
                {
                  type: "paragraph",
                  content: [{ type: "text", text: selectedNote.content }]
                }
              ]
            });
          }
        } else if (selectedNote.content) {
          // If it's not a string but exists, it should already be in the correct format
          setNewNoteContent(selectedNote.content as unknown as EditorContent);
          console.log("Content loaded from non-string format:", selectedNote.content);
        } else {
          // If there's no content, create an empty document
          setNewNoteContent({
            type: "doc",
            content: [{ type: "paragraph", content: [] }]
          });
          console.log("Created empty document for note with no content");
        }

        // Set HTML content - this is important for Quill
        setNewNoteHtmlContent(selectedNote.html_content || '');

        // Set other note properties
        setNewNoteTitle(selectedNote.title || '');
        setNewNoteCategory(selectedNote.category || '');
        setNewNoteFolderId(selectedNote.folder_id);
        setNewNoteTags(selectedNote.tags || []);
        setNewNoteScreenshots(selectedNote.screenshots || []);
        setIsTemplate(selectedNote.is_template || false);

        // Log the loaded content for debugging
        console.log("Note loaded:", {
          id,
          title: selectedNote.title,
          content: selectedNote.content,
          htmlContent: selectedNote.html_content
        });
      } catch (error) {
        console.error("Error parsing note content:", error);
        toast.error("Error loading note content");
      }
    }
  }

  // Handle add tag to new note
  const handleAddTag = () => {
    if (newTagInput.trim() && !newNoteTags.includes(newTagInput.trim())) {
      setNewNoteTags(prev => [...prev, newTagInput.trim()])
      setNewTagInput("")
    }
  }

  // Handle remove tag from new note
  const handleRemoveTag = (tag: string) => {
    setNewNoteTags(prev => prev.filter(t => t !== tag))
  }

  // Optimized editor change handler
  const handleEditorChange = useCallback((content: EditorContent, htmlContent: string) => {
    // For QuillNotebookEditor, we receive content and htmlContent directly
    setNewNoteContent(content)
    setNewNoteHtmlContent(htmlContent)

    // Only log in development mode and reduce frequency
    if (process.env.NODE_ENV === 'development') {
      console.log("Editor content updated:", {
        contentType: content?.type,
        htmlLength: htmlContent?.length
      })
    }
  }, [])

  // Handle save new note
  const handleSaveNewNote = async () => {
    if (!newNoteTitle.trim()) {
      toast.error("Please enter a title for your note")
      return
    }

    try {
      // Convert editor content to JSON string for storage
      const contentToSave = newNoteContent
        ? JSON.stringify(newNoteContent)
        : JSON.stringify({ type: 'doc', content: [] });

      // If a folder is selected in the sidebar but not explicitly chosen in the dropdown,
      // use the selected folder
      const folderIdToUse = newNoteFolderId || selectedFolderId;

      const entry = await createEntry({
        title: newNoteTitle,
        content: contentToSave,
        html_content: newNoteHtmlContent,
        category: newNoteCategory || null,
        folder_id: folderIdToUse,
        tags: newNoteTags,
        screenshots: newNoteScreenshots,
        is_template: isTemplate,
        is_pinned: false
      })

      if (entry) {
        toast.success("Note created successfully")
        setIsCreatingNewNote(false)
        resetNewNoteForm()

        // Force a refetch to ensure the UI is updated
        setTimeout(() => {
          refetch()
          refetchFolders()
        }, 0)

        // Set the selected note to the new note
        setSelectedNoteId(entry.id)
      }
    } catch (error) {
      console.error("Error creating note:", error)
      toast.error("Failed to create note")
    }
  }

  // Optimized save handler for existing notes
  const handleSaveExistingNote = async () => {
    if (!newNoteTitle.trim()) {
      toast.error("Please enter a title for your note")
      return
    }

    if (!selectedNoteId) {
      toast.error("No note selected")
      return
    }

    try {
      // Get the selected note
      const selectedNote = entries.find(e => e.id === selectedNoteId)
      if (!selectedNote) {
        toast.error("Note not found")
        return
      }

      // Exit edit mode immediately for better UX
      setIsEditingNote(false)

      // Convert editor content to JSON string for storage
      const contentToSave = newNoteContent
        ? JSON.stringify(newNoteContent)
        : JSON.stringify({ type: 'doc', content: [] })

      // Update the note in the database with manual sync
      console.log('=== SENDING NOTEBOOK UPDATE ===');
      console.log('Notebook ID:', selectedNoteId);
      console.log('Linked trade IDs:', selectedNote.linked_trade_ids);
      console.log('Linked strategy IDs:', selectedNote.linked_strategy_ids);
      console.log('Screenshots count:', newNoteScreenshots.length);

      const updatePromise = fetch(`/api/notebook/${selectedNoteId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          title: newNoteTitle,
          content: contentToSave,
          html_content: newNoteHtmlContent,
          category: newNoteCategory || null,
          folder_id: newNoteFolderId,
          tags: newNoteTags,
          screenshots: newNoteScreenshots,
          is_template: isTemplate,
          // Include any linked IDs from the original note
          linked_trade_ids: selectedNote.linked_trade_ids || [],
          linked_strategy_ids: selectedNote.linked_strategy_ids || []
        })
      })

      // Handle the response with manual sync
      updatePromise.then(async (response) => {
        console.log('=== NOTEBOOK UPDATE RESPONSE ===');
        console.log('Response status:', response.status);
        console.log('Response ok:', response.ok);

        if (!response.ok) {
          const errorText = await response.text();
          console.error('Update failed with response:', errorText);
          throw new Error('Failed to update note')
        }

        const responseData = await response.json();
        console.log('Update successful, response data keys:', Object.keys(responseData));

        // Step 2: Manually trigger sync to linked trades
        try {
          const { syncNotebookToTrades } = await import('@/lib/manual-sync')
          const syncResult = await syncNotebookToTrades(selectedNoteId)

          if (syncResult.success) {
            toast.success("Note updated and synced successfully")
          } else {
            toast.success("Note updated (sync warning: " + syncResult.error + ")")
          }
        } catch (syncError) {
          console.warn("Sync failed but note was saved:", syncError)
          toast.success("Note updated (sync failed)")
        }

        // Refetch data to show updates
        setTimeout(() => {
          refetch()
        }, 100)

      }).catch((error) => {
        console.error('Error updating note:', error)
        toast.error('Failed to update note - changes may not be saved')
        // Optionally revert to edit mode if save failed
        // setIsEditingNote(true)
      })

    } catch (error) {
      console.error('Error updating note:', error)
      toast.error('Failed to update note')
    }
  }

  // Reset new note form
  const resetNewNoteForm = () => {
    setNewNoteTitle("")
    setNewNoteCategory("")
    // Don't reset folder ID here to maintain the current folder selection
    setNewNoteTags([])
    setNewTagInput("")
    setNewNoteContent(null)
    setNewNoteHtmlContent("")
    setNewNoteScreenshots([])
    setIsTemplate(false)
  }

  return (
    <div className="h-[calc(100vh-8rem)] flex overflow-hidden">
      {/* Sidebar - maintain current width */}
      <NotebookSidebar
        categories={initialCategories}
        tags={tags}
        folders={folders}
        selectedCategory={selectedCategory}
        selectedFolderId={selectedFolderId}
        selectedTags={selectedTags}
        showTemplates={showTemplates}
        onSelectCategory={handleSelectCategory}
        onSelectFolder={handleSelectFolder}
        onSelectTag={handleSelectTag}
        onToggleTemplates={handleToggleTemplates}
        onCreateCategory={handleCreateCategory}
        onCreateFolder={handleCreateFolder}
        onDeleteFolder={handleDeleteFolder}
        onRenameFolder={handleRenameFolder}
        onMoveFolder={handleMoveFolderToCategory}
        onCreateNote={handleCreateNoteFromFolder}
        onImportJournalEntries={handleImportJournalEntries}
        isImporting={isImporting}
        isFolderLoading={isFolderLoading}
        onExpandCategory={(fn) => {
          expandCategoryFolderRef.current = fn;
        }}
      />

      {/* Note List Panel - make responsive to fill remaining space */}
      <div className="flex-1 border-r h-full overflow-hidden">
        <NotebookList
          entries={entries}
          count={count}
          page={page}
          limit={20}
          folders={folders}
          onPageChange={setPage}
          onSearch={handleSearch}
          onCreateNew={handleCreateNewNote}
          onViewEntry={handleViewNote}
          searchTerm={searchTerm}
          selectedNoteId={selectedNoteId}
          selectedFolderId={selectedFolderId}
          isLoading={isLoading}
          defaultViewMode="list"
          onSearchFolder={applyFolderFilter}
          onSearchTag={applyTagFilter}
        />
      </div>

      {/* Editor Panel - fixed at exactly 43% width */}
      <div className="w-[43%] hidden md:block border-l h-full overflow-hidden">
        {isCreatingNewNote ? (
          <div className="h-full flex flex-col p-3">
            <CompactNoteHeader
              title={newNoteTitle}
              onTitleChange={setNewNoteTitle}
              category={newNoteCategory}
              onCategoryChange={setNewNoteCategory}
              folderId={newNoteFolderId}
              onFolderChange={setNewNoteFolderId}
              tags={newNoteTags}
              tagInput={newTagInput}
              onTagInputChange={setNewTagInput}
              onAddTag={handleAddTag}
              onRemoveTag={handleRemoveTag}
              isTemplate={isTemplate}
              onTemplateChange={setIsTemplate}
              onSave={handleSaveNewNote}
              onCancel={() => {
                setIsCreatingNewNote(false)
                resetNewNoteForm()
              }}
              isCreating={true}
              folders={folders}
              categoryOptions={initialCategories.map(c => c.name)}
              tagOptions={tags.map(t => t.name)}
            />

            <div className="flex-grow overflow-hidden">
              <ScrollArea className="h-full">
                <NewNotebookEditor
                  key="new-note-editor" // Add key to force re-render
                  content={newNoteContent}
                  htmlContent={newNoteHtmlContent}
                  screenshots={newNoteScreenshots}
                  onChange={handleEditorChange}
                  onScreenshotsChange={setNewNoteScreenshots}
                  autoFocus
                  className="h-full"
                  onSave={handleSaveNewNote}
                  onCancel={() => {
                    setIsCreatingNewNote(false)
                    resetNewNoteForm()
                  }}
                  isEditing={true}
                />
              </ScrollArea>
            </div>
          </div>
        ) : selectedNoteId ? (
          <div className="h-full flex flex-col p-3">
            <div className="flex items-center justify-between">
              {!isEditingNote && (
                <h2 className="text-sm font-medium text-muted-foreground">
                  {entries.find(e => e.id === selectedNoteId)?.title || "Note Details"}
                </h2>
              )}
              {!isEditingNote && (
                <Button
                  variant="outline"
                  size="sm"
                  className="h-7 text-xs"
                  onClick={() => {
                    // Simply set editing mode to true
                    setIsEditingNote(true);
                  }}
                >
                  Edit
                </Button>
              )}
            </div>

            {isEditingNote && (
              <CompactNoteHeader
                title={newNoteTitle}
                onTitleChange={setNewNoteTitle}
                category={newNoteCategory}
                onCategoryChange={setNewNoteCategory}
                folderId={newNoteFolderId}
                onFolderChange={setNewNoteFolderId}
                tags={newNoteTags}
                tagInput={newTagInput}
                onTagInputChange={setNewTagInput}
                onAddTag={handleAddTag}
                onRemoveTag={handleRemoveTag}
                isTemplate={isTemplate}
                onTemplateChange={setIsTemplate}
                onSave={handleSaveExistingNote}
                onCancel={() => {
                  // Reset to original values but keep editor open
                  handleViewNote(selectedNoteId)
                  setIsEditingNote(false)
                }}
                isCreating={false}
                folders={folders}
                categoryOptions={initialCategories.map(c => c.name)}
                tagOptions={tags.map(t => t.name)}
              />
            )}

            <div className="flex-grow overflow-hidden">
              <EditorTransitionWrapper
                noteId={selectedNoteId}
                isEditing={isEditingNote}
                onTransitionComplete={handleTransitionComplete}
                className="h-full"
              >
                <ScrollArea className="h-full">
                  <NewNotebookEditor
                  ref={editorRef}
                  key={`notebook-editor-${selectedNoteId}-${isEditingNote ? 'edit' : 'view'}`} // Add key to force re-render
                  content={newNoteContent}
                  htmlContent={newNoteHtmlContent}
                  screenshots={newNoteScreenshots}
                  onChange={handleEditorChange}
                  onScreenshotsChange={setNewNoteScreenshots}
                  readOnly={!isEditingNote}
                  isEditing={isEditingNote}
                  noteId={selectedNoteId}
                  autoFocus={isEditingNote}
                  onSave={handleSaveExistingNote}
                onCancel={() => {
                  // Reset to original values but keep editor open
                  handleViewNote(selectedNoteId)
                  setIsEditingNote(false)
                }}
                className="h-full"
              />
                </ScrollArea>
              </EditorTransitionWrapper>
            </div>
          </div>
        ) : (
          <div className="flex items-center justify-center h-full text-muted-foreground">
            Select a note to view or edit
          </div>
        )}
      </div>
    </div>
  )
}
