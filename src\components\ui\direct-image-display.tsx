"use client"

import { useState, useEffect, useMemo } from "react"
import { ImageOff, ZoomIn } from "lucide-react"
import { cn } from "@/lib/utils"
import { ImageLightbox } from "@/components/ui/image-lightbox"
import { Button } from "@/components/ui/button"

interface DirectImageDisplayProps {
  src: string
  alt: string
  className?: string
  aspectRatio?: "square" | "video" | "auto"
  lightboxEnabled?: boolean
  lightboxGroup?: string[]
  lightboxIndex?: number
}

export function DirectImageDisplay({
  src,
  alt,
  className = "",
  aspectRatio = "video",
  lightboxEnabled = true,
  lightboxGroup,
  lightboxIndex = 0
}: DirectImageDisplayProps) {
  const [error, setError] = useState(false)
  const [loading, setLoading] = useState(true)
  const [lightboxOpen, setLightboxOpen] = useState(false)
  const [finalSrc, setFinalSrc] = useState<string | null>(null)

  // Try to load an image with the given URL
  const tryLoadImage = (url: string): Promise<string> => {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => resolve(url);
      img.onerror = () => reject(new Error(`Failed to load image: ${url}`));
      img.src = url;
    });
  };

  // Effect to handle image loading
  useEffect(() => {
    if (!src) {
      setError(true);
      setLoading(false);
      return;
    }

    const loadImage = async () => {
      setLoading(true);
      setError(false);

      try {
        // Add a cache-busting parameter to force a fresh load
        const cacheBustUrl = `${src}${src.includes('?') ? '&' : '?'}_t=${Date.now()}`;
        console.log('Loading image directly with URL:', cacheBustUrl);

        // Try loading the image directly
        await tryLoadImage(cacheBustUrl);
        setFinalSrc(cacheBustUrl);
        setLoading(false);
      } catch (e) {
        console.error('Failed to load image directly:', e);

        // Try one more time with a delay
        setTimeout(async () => {
          try {
            const retryUrl = `${src}${src.includes('?') ? '&' : '?'}_retry=true&_t=${Date.now()}`;
            console.log('Retrying with URL:', retryUrl);
            await tryLoadImage(retryUrl);
            setFinalSrc(retryUrl);
            setLoading(false);
          } catch (retryError) {
            console.error('Retry failed:', retryError);
            setError(true);
            setLoading(false);
          }
        }, 1000);
      }
    };

    loadImage();
  }, [src]);

  // Process lightbox images
  const processedLightboxGroup = useMemo(() => {
    if (!lightboxGroup) {
      return finalSrc ? [finalSrc] : [];
    }

    // Add cache-busting to all lightbox images
    return lightboxGroup.map(url => 
      `${url}${url.includes('?') ? '&' : '?'}_t=${Date.now()}`
    );
  }, [lightboxGroup, finalSrc]);

  // Skip old format URLs
  if (src.includes('/setup_')) {
    return (
      <div className={cn(
        "flex items-center justify-center bg-muted/30 rounded-lg",
        className,
        aspectRatio === "square" ? "aspect-square" :
        aspectRatio === "video" ? "aspect-video" : ""
      )}>
        <div className="flex flex-col items-center text-muted-foreground">
          <ImageOff className="h-8 w-8 mb-2" />
          <span className="text-sm">Image not available</span>
        </div>
      </div>
    )
  }

  // Show loading state
  if (loading) {
    return (
      <div className={cn(
        "flex items-center justify-center bg-muted/30 rounded-lg",
        className,
        aspectRatio === "square" ? "aspect-square" :
        aspectRatio === "video" ? "aspect-video" : ""
      )}>
        <div className="flex flex-col items-center text-muted-foreground">
          <div className="h-8 w-8 mb-2 rounded-full border-2 border-purple-500 border-t-transparent animate-spin" />
          <span className="text-sm">Loading image...</span>
        </div>
      </div>
    )
  }

  // Show error state
  if (error || !finalSrc) {
    return (
      <div className={cn(
        "flex items-center justify-center bg-muted/30 rounded-lg",
        className,
        aspectRatio === "square" ? "aspect-square" :
        aspectRatio === "video" ? "aspect-video" : ""
      )}>
        <div className="flex flex-col items-center text-muted-foreground">
          <ImageOff className="h-8 w-8 mb-2" />
          <span className="text-sm">Failed to load image</span>
          {/* Add a retry button */}
          <Button
            variant="outline"
            size="sm"
            className="mt-2"
            onClick={() => {
              // Force a reload of the image
              const timestamp = Date.now();
              const retryUrl = `${src}${src.includes('?') ? '&' : '?'}_force=true&_t=${timestamp}`;

              // Try loading again
              setLoading(true);
              setError(false);

              // Log the retry attempt
              console.log('Manual retry with URL:', retryUrl);

              const img = new Image();
              img.onload = () => {
                console.log('Manual retry successful for:', retryUrl);
                setFinalSrc(retryUrl);
                setLoading(false);
              };
              img.onerror = () => {
                console.error('Manual retry failed for:', retryUrl);
                setError(true);
                setLoading(false);
              };
              img.src = retryUrl;
            }}
          >
            Retry
          </Button>
        </div>
      </div>
    )
  }

  // Show the successfully loaded image
  return (
    <>
      <div
        className={cn(
          "relative bg-muted/30 rounded-lg overflow-hidden group",
          className,
          aspectRatio === "square" ? "aspect-square" :
          aspectRatio === "video" ? "aspect-video" : "",
          lightboxEnabled && "cursor-pointer"
        )}
        onClick={lightboxEnabled ? () => setLightboxOpen(true) : undefined}
      >
        <img
          src={finalSrc}
          alt={alt}
          className={cn(
            "w-full object-contain",
            aspectRatio === "auto" ? "h-auto" : "h-full"
          )}
        />

        {/* Zoom indicator overlay */}
        {lightboxEnabled && (
          <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 dark:group-hover:bg-black/40 transition-colors flex items-center justify-center opacity-0 group-hover:opacity-100">
            <Button
              variant="secondary"
              size="icon"
              className="h-9 w-9 rounded-full shadow-lg bg-background/80 backdrop-blur-sm"
            >
              <ZoomIn className="h-5 w-5" />
            </Button>
          </div>
        )}
      </div>

      {/* Image Lightbox */}
      {lightboxEnabled && finalSrc && (
        <ImageLightbox
          images={processedLightboxGroup}
          initialIndex={lightboxIndex}
          open={lightboxOpen}
          onOpenChange={setLightboxOpen}
        />
      )}
    </>
  )
}
