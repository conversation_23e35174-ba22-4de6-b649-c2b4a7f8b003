import { useSearchParams } from 'next/navigation'
import { getSupabaseBrowser } from '@/lib/supabase'

export function useAuth() {
  const searchParams = useSearchParams()
  const supabase = getSupabaseBrowser()

  const signIn = async ({
    email,
    password,
  }: {
    email: string
    password: string
  }) => {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      })

      if (error) {
        throw error
      }

      if (!data.session) {
        throw new Error('No session created')
      }

      // Get the redirect URL from query params or default to dashboard
      const redirect = searchParams?.get('redirect') || '/dashboard'

      // Use window.location for a hard redirect to ensure session is loaded
      window.location.href = redirect

      return { data, error: null }
    } catch (error) {
      console.error('Sign in error:', error)
      throw error
    }
  }

  const signUp = async ({
    email,
    password,
  }: {
    email: string
    password: string
  }) => {
    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          emailRedirectTo: `${window.location.origin}/auth/callback`,
        },
      })

      if (error) {
        throw error
      }

      // Get the redirect URL from query params or default to dashboard
      const redirect = searchParams?.get('redirect') || '/dashboard'

      // Use window.location for a hard redirect to ensure session is loaded
      window.location.href = redirect

      return { data, error: null }
    } catch (error) {
      console.error('Sign up error:', error)
      throw error
    }
  }

  const signOut = async () => {
    try {
      const { error } = await supabase.auth.signOut()
      if (error) {
        throw error
      }

      // Use window.location for a hard redirect to ensure session is cleared
      window.location.href = '/auth'
    } catch (error) {
      console.error('Sign out error:', error)
      throw error
    }
  }

  return {
    signIn,
    signUp,
    signOut,
  }
}
