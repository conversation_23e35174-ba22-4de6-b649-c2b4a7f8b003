"use client"

import { useState, useRef, useEffect } from "react"
import { toast } from "sonner"
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { TagInput } from "@/components/tag-input"
import { ImageDisplay } from "@/components/ui/image-display"
import { uploadImage } from "@/lib/image-uploader"
import { getSupabaseBrowser } from "@/lib/supabase"
import { getAllTags } from "@/lib/journal-service"
import { Loader2, Save, X, Upload, Trash2 } from "lucide-react"

interface TradeDetailsEditDialogProps {
  isOpen: boolean
  onOpenChange: (open: boolean) => void
  trade: any
  userId: string
  onUpdate: () => void
}

export function TradeDetailsEditDialog({
  isOpen,
  onOpenChange,
  trade,
  userId,
  onUpdate
}: TradeDetailsEditDialogProps) {
  // State for notes, screenshots, and tags
  const [tradeNotes, setTradeNotes] = useState(trade?.notes || "")
  const [screenshots, setScreenshots] = useState<string[]>(
    Array.isArray(trade?.screenshots) ? trade.screenshots : []
  )
  const [tradeTags, setTradeTags] = useState<string[]>(
    Array.isArray(trade?.tags) ? trade.tags : []
  )
  const [availableTags, setAvailableTags] = useState<string[]>([])

  // State for loading states
  const [isSaving, setIsSaving] = useState(false)
  const [isUploading, setIsUploading] = useState(false)

  // Ref for file input
  const fileInputRef = useRef<HTMLInputElement>(null)

  // Fetch available tags when component mounts
  useEffect(() => {
    const fetchTags = async () => {
      try {
        const tags = await getAllTags(userId)
        setAvailableTags(tags)
      } catch (error) {
        console.error("Error fetching tags:", error)
      }
    }

    if (isOpen && userId) {
      fetchTags()
    }
  }, [userId, isOpen])

  // Reset form when trade changes
  useEffect(() => {
    if (trade) {
      setTradeNotes(trade.notes || "")
      setScreenshots(Array.isArray(trade.screenshots) ? trade.screenshots : [])
      setTradeTags(Array.isArray(trade.tags) ? trade.tags : [])
    }
  }, [trade])

  // Handle image upload
  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    // Validate file type
    if (!file.type.startsWith('image/')) {
      toast.error("Please upload an image file")
      return
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      toast.error("File size should not exceed 5MB")
      return
    }

    setIsUploading(true)

    try {
      const imageUrl = await uploadImage(file)
      if (imageUrl) {
        setScreenshots([...screenshots, imageUrl])
        toast.success("Screenshot uploaded successfully")
      }
    } catch (error) {
      console.error("Error uploading screenshot:", error)
      toast.error("Failed to upload screenshot")
    } finally {
      setIsUploading(false)
      // Clear the file input
      if (fileInputRef.current) {
        fileInputRef.current.value = ''
      }
    }
  }

  // Handle removing a screenshot
  const removeScreenshot = (index: number) => {
    setScreenshots(prev => {
      const updated = [...prev]
      updated.splice(index, 1)
      return updated
    })
    toast.success("Screenshot removed")
  }

  // Handle saving trade details
  const handleSave = async () => {
    if (!trade || !userId) return

    setIsSaving(true)

    try {
      const supabase = getSupabaseBrowser()

      // Create the update object
      const updateData = {
        strategy_id: trade.strategy_id || null,
        setup_id: trade.setup_id || null,
        notes: tradeNotes || null,
        screenshots: screenshots || [],
        followed_rules: Array.isArray(trade.followed_rules) ? trade.followed_rules : [],
        followed_setup_criteria: Array.isArray(trade.followed_setup_criteria) ? trade.followed_setup_criteria : [],
        tags: tradeTags || [],
        // Add a flag to indicate if this trade has journal content
        has_journal_content: (tradeNotes && tradeNotes.trim().length > 0) || screenshots.length > 0
      }

      console.log("Saving trade details:", updateData)

      // Try to use the RPC function first
      try {
        console.log("Attempting to update trade with RPC function")
        const { data: rpcResult, error: rpcError } = await supabase.rpc('update_trade_details', {
          p_trade_id: trade.id,
          p_strategy_id: updateData.strategy_id,
          p_setup_id: updateData.setup_id,
          p_notes: updateData.notes,
          p_screenshots: updateData.screenshots,
          p_followed_rules: updateData.followed_rules,
          p_followed_setup_criteria: updateData.followed_setup_criteria,
          p_tags: updateData.tags,
          p_has_journal_content: updateData.has_journal_content
        })

        if (!rpcError) {
          console.log("Trade updated successfully via RPC function")
          toast.success("Trade details saved successfully")
          onOpenChange(false)
          onUpdate()
          return
        }

        console.warn("RPC function failed, falling back to standard update:", rpcError)
      } catch (rpcError) {
        console.warn("RPC function threw an exception, falling back to standard update:", rpcError)
      }

      // Perform standard update as fallback
      console.log("Performing standard database update")
      const { error } = await supabase
        .from("trades")
        .update({
          notes: updateData.notes,
          screenshots: updateData.screenshots,
          tags: updateData.tags,
          has_journal_content: updateData.has_journal_content,
          updated_at: new Date().toISOString()
        })
        .eq("id", trade.id)

      if (error) {
        console.error("Error updating trade:", error)
        toast.error(`Failed to save trade details: ${error.message || "Unknown error"}`)
        return
      }

      // Update was successful
      console.log("Trade updated successfully in database")

      // Trigger auto-migration queue processing (for new notebook entries)
      try {
        const queueResponse = await fetch('/api/migrate/process-queue', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' }
        });

        if (queueResponse.ok) {
          console.log('Auto-migration queue processed successfully after trade details update');
        } else {
          console.warn('Auto-migration queue processing failed after trade details update');
        }
      } catch (error) {
        console.warn('Auto-migration queue processing error:', error);
      }

      toast.success("Trade details saved successfully")
      onOpenChange(false)
      onUpdate()

      // Dispatch a global event to notify other components that trade data has changed
      const globalEvent = new CustomEvent('global-data-change', {
        detail: { type: 'trade-details-updated', tradeId: trade.id }
      })
      window.dispatchEvent(globalEvent)
    } catch (error) {
      console.error("Error saving trade details:", error)
      toast.error("Failed to save trade details")
    } finally {
      setIsSaving(false)
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Edit Trade Details</DialogTitle>
          <DialogDescription>
            Update notes, screenshots, and tags for this trade.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-4">
          {/* Trade Notes */}
          <div className="space-y-2">
            <h3 className="text-sm font-medium">Trade Notes</h3>
            <Textarea
              placeholder="Enter your notes about this trade..."
              className="min-h-[150px]"
              value={tradeNotes}
              onChange={(e) => setTradeNotes(e.target.value)}
            />
          </div>

          {/* Trade Tags */}
          <div className="space-y-2">
            <h3 className="text-sm font-medium">Tags</h3>
            <TagInput
              value={tradeTags}
              onChange={setTradeTags}
              suggestions={availableTags}
              placeholder="Add tags..."
            />
            <p className="text-xs text-muted-foreground">
              Tags help you filter and categorize your trades. Examples: "Gap Up", "Breakout", "FOMC", etc.
            </p>
          </div>

          {/* Screenshots */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <h3 className="text-sm font-medium">Screenshots</h3>
              <div className="flex items-center">
                <input
                  type="file"
                  ref={fileInputRef}
                  onChange={handleImageUpload}
                  accept="image/*"
                  className="hidden"
                />
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => fileInputRef.current?.click()}
                  disabled={isUploading}
                >
                  {isUploading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Uploading...
                    </>
                  ) : (
                    <>
                      <Upload className="mr-2 h-4 w-4" />
                      Upload Screenshot
                    </>
                  )}
                </Button>
              </div>
            </div>

            {screenshots.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {screenshots.map((screenshot, index) => (
                  <div key={index} className="relative group">
                    <ImageDisplay
                      src={screenshot}
                      alt={`Trade screenshot ${index + 1}`}
                      aspectRatio="video"
                      lightboxGroup={screenshots}
                      lightboxIndex={index}
                    />
                    <Button
                      variant="destructive"
                      size="icon"
                      className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity"
                      onClick={() => removeScreenshot(index)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-sm text-muted-foreground">
                No screenshots added yet. Upload screenshots to document your trade.
              </p>
            )}
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            <X className="mr-2 h-4 w-4" />
            Cancel
          </Button>
          <Button onClick={handleSave} disabled={isSaving}>
            {isSaving ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                Save Changes
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
