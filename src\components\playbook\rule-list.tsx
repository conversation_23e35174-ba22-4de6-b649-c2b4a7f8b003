"use client"

import React, { useState } from "react"
import { toast } from "sonner"
import { StrategyRule, Strategy, RULE_TYPES } from "@/types/playbook"
import { deleteStrategyRule } from "@/lib/playbook-service"
import { formatDistanceToNow } from "date-fns"
import { getCardGradient } from "@/lib/card-utils"

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { <PERSON><PERSON> } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import {
  MoreVertical,
  Edit,
  Trash,
  Plus,
  ArrowLeft
} from "lucide-react"

interface RuleListProps {
  userId: string
  rules: StrategyRule[]
  strategy?: Strategy
  onEdit: (rule: StrategyRule) => void
  onDelete: (ruleId: string) => void
  onAdd: () => void
  onBack?: () => void
}

export function RuleList({
  userId,
  rules,
  strategy,
  onEdit,
  onDelete,
  onAdd,
  onBack
}: RuleListProps) {
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [ruleToDelete, setRuleToDelete] = useState<StrategyRule | null>(null)
  const [activeTab, setActiveTab] = useState("all")

  const handleDeleteClick = (rule: StrategyRule) => {
    setRuleToDelete(rule)
    setDeleteDialogOpen(true)
  }

  const confirmDelete = async () => {
    if (!ruleToDelete) return

    try {
      const success = await deleteStrategyRule(userId, ruleToDelete.id)
      if (success) {
        toast.success("Rule deleted successfully")
        onDelete(ruleToDelete.id)
      } else {
        toast.error("Failed to delete rule")
      }
    } catch (error) {
      console.error("Error deleting rule:", error)
      toast.error("An error occurred while deleting the rule")
    } finally {
      setDeleteDialogOpen(false)
      setRuleToDelete(null)
    }
  }

  const getRuleTypeLabel = (type: string) => {
    const ruleType = RULE_TYPES.find(rt => rt.value === type)
    return ruleType ? ruleType.label : type
  }

  const filteredRules = activeTab === "all"
    ? rules
    : rules.filter(rule => rule.rule_type === activeTab)

  // Group rules by type for better organization
  const rulesByType = rules.reduce((acc, rule) => {
    if (!acc[rule.rule_type]) {
      acc[rule.rule_type] = []
    }
    acc[rule.rule_type].push(rule)
    return acc
  }, {} as Record<string, StrategyRule[]>)

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-2">
          {onBack && (
            <Button variant="ghost" size="icon" onClick={onBack}>
              <ArrowLeft className="h-4 w-4" />
            </Button>
          )}
          <h2 className="text-2xl font-bold tracking-tight">
            {strategy ? `Rules for ${strategy.name}` : "Trading Rules"}
          </h2>
        </div>
        <Button onClick={onAdd}>
          <Plus className="mr-2 h-4 w-4" /> Add Rule
        </Button>
      </div>

      {rules.length === 0 ? (
        <Card className="bg-card/30 shadow-sm">
          <CardContent className="pt-6 text-center">
            <p className="text-muted-foreground mb-4">
              {strategy
                ? `You haven't created any rules for the "${strategy.name}" strategy yet.`
                : "You haven't created any trading rules yet."}
            </p>
            <Button onClick={onAdd} variant="outline">
              <Plus className="mr-2 h-4 w-4" /> Create Your First Rule
            </Button>
          </CardContent>
        </Card>
      ) : (
        <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="mb-4">
            <TabsTrigger value="all">All Rules</TabsTrigger>
            {RULE_TYPES.map(type => (
              <TabsTrigger
                key={type.value}
                value={type.value}
                disabled={!rulesByType[type.value]}
              >
                {type.label}
              </TabsTrigger>
            ))}
          </TabsList>

          <TabsContent value="all" className="mt-0">
            <div className="grid grid-cols-1 gap-4">
              {Object.entries(rulesByType).map(([type, typeRules]) => (
                <div key={type} className="space-y-3">
                  <h3 className="text-lg font-semibold">{getRuleTypeLabel(type)}</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {typeRules
                      .sort((a, b) => a.priority - b.priority)
                      .map(rule => (
                        <RuleCard
                          key={rule.id}
                          rule={rule}
                          strategy={strategy}
                          onEdit={() => onEdit(rule)}
                          onDelete={() => handleDeleteClick(rule)}
                        />
                      ))
                    }
                  </div>
                </div>
              ))}
            </div>
          </TabsContent>

          {RULE_TYPES.map(type => (
            <TabsContent key={type.value} value={type.value} className="mt-0">
              {rulesByType[type.value] ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {rulesByType[type.value]
                    .sort((a, b) => a.priority - b.priority)
                    .map(rule => (
                      <RuleCard
                        key={rule.id}
                        rule={rule}
                        strategy={strategy}
                        onEdit={() => onEdit(rule)}
                        onDelete={() => handleDeleteClick(rule)}
                      />
                    ))
                  }
                </div>
              ) : (
                <Card className="bg-card/30 shadow-sm">
                  <CardContent className="pt-6 text-center">
                    <p className="text-muted-foreground mb-4">
                      No {type.label.toLowerCase()} have been created yet.
                    </p>
                    <Button onClick={onAdd} variant="outline">
                      <Plus className="mr-2 h-4 w-4" /> Add {type.label}
                    </Button>
                  </CardContent>
                </Card>
              )}
            </TabsContent>
          ))}
        </Tabs>
      )}

      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete the rule &quot;{ruleToDelete?.name}&quot;.
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDelete} className="bg-destructive text-destructive-foreground">
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}

interface RuleCardProps {
  rule: StrategyRule
  strategy?: Strategy
  onEdit: () => void
  onDelete: () => void
}

function RuleCard({ rule, strategy, onEdit, onDelete }: RuleCardProps) {
  return (
    <Card
      className="flex flex-col overflow-hidden group hover:shadow-[0_8px_20px_rgba(0,0,0,0.08)] transition-all duration-300 border-t-4 dark:shadow-lg dark:hover:shadow-xl"
      style={{
        background: `var(--mode-light, ${getCardGradient(rule.name).light.background}) var(--mode-dark, ${getCardGradient(rule.name).dark.background})`,
        borderImageSource: `var(--mode-light, ${getCardGradient(rule.name).light.border}) var(--mode-dark, ${getCardGradient(rule.name).dark.border})`,
        borderImageSlice: 1,
        borderTopColor: `var(--mode-light, ${getCardGradient(rule.name).light.accent}) var(--mode-dark, ${getCardGradient(rule.name).dark.accent})`,
        ...({
          '--mode-light': 'initial',
          '--mode-dark': 'initial'
        } as React.CSSProperties)
      }}
    >
      <CardHeader className="pb-2 relative">
        <div className="absolute top-0 right-0 p-2">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="h-8 w-8 rounded-full opacity-70 hover:opacity-100 hover:bg-background/80">
                <MoreVertical className="h-4 w-4" />
                <span className="sr-only">Open menu</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              <DropdownMenuItem onClick={onEdit} className="cursor-pointer">
                <Edit className="mr-2 h-4 w-4" />
                Edit
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={onDelete}
                className="text-destructive focus:text-destructive cursor-pointer"
              >
                <Trash className="mr-2 h-4 w-4" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        <div className="space-y-1">
          <div className="flex items-center gap-2">
            <CardTitle className="text-lg font-bold">{rule.name}</CardTitle>
            <Badge
              variant="outline"
              className="text-xs bg-background/60 hover:bg-background/80 transition-colors"
              style={{
                borderColor: `var(--mode-light, ${getCardGradient(rule.name).light.accent}) var(--mode-dark, ${getCardGradient(rule.name).dark.accent})`,
                ...({
                  '--mode-light': 'initial',
                  '--mode-dark': 'initial'
                } as React.CSSProperties)
              }}
            >
              Priority: {rule.priority}
            </Badge>
          </div>
          {!strategy && rule.strategy && (
            <CardDescription className="line-clamp-2 text-sm opacity-90">
              Strategy: {rule.strategy.name}
            </CardDescription>
          )}
        </div>
      </CardHeader>
      <CardContent className="pb-2 flex-grow">
        <div className="space-y-4">
          <div className={`${getCardGradient(rule.name).light.cardBg} dark:${getCardGradient(rule.name).dark.cardBg} rounded-lg p-3 backdrop-blur-sm`}>
            <h4 className="text-sm font-medium mb-2 flex items-center">
              <span className="inline-block w-2 h-2 rounded-full mr-1 dark:shadow-glow"
                style={{
                  backgroundColor: `var(--mode-light, ${getCardGradient(rule.name).light.accent}) var(--mode-dark, ${getCardGradient(rule.name).dark.accent})`,
                  ...({
                    '--mode-light': 'initial',
                    '--mode-dark': 'initial'
                  } as React.CSSProperties)
                }}>
              </span>
              Description
            </h4>
            <p className="text-sm text-muted-foreground whitespace-pre-line">
              {rule.description}
            </p>
          </div>
        </div>
      </CardContent>
      <CardFooter className={`pt-2 text-xs text-muted-foreground border-t border-border/30 mt-auto ${getCardGradient(rule.name).light.footerBg} dark:${getCardGradient(rule.name).dark.footerBg} dark:border-border/10`}>
        <div className="flex items-center">
          <div
            className="w-2 h-2 rounded-full mr-2 animate-pulse dark:shadow-glow"
            style={{
              backgroundColor: `var(--mode-light, ${getCardGradient(rule.name).light.accent}) var(--mode-dark, ${getCardGradient(rule.name).dark.accent})`,
              ...({
                '--mode-light': 'initial',
                '--mode-dark': 'initial'
              } as React.CSSProperties)
            }}>
          </div>
          Updated {formatDistanceToNow(new Date(rule.updated_at), { addSuffix: true })}
        </div>
      </CardFooter>
    </Card>
  )
}
