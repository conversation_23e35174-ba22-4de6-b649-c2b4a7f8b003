import { cookies } from "next/headers"
import { createServerClient } from "@supabase/ssr"
import { redirect } from "next/navigation"
import type { Database } from "@/types/supabase"
import { NotebookEntry, NotebookCategory, NotebookTag, NotebookFolderWithMeta } from "@/types/notebook"
// Import the client component directly
import NotebookClient from "./client"

export default async function NotebookPage() {
  // Get cookies for server-side Supabase client
  const cookieStore = await cookies()

  // Create a Supabase client with proper cookie handling
  const supabase = createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value
        },
        set(_name: string, _value: string, _options: any) {
          // Server components can't set cookies directly
        },
        remove(_name: string, _options: any) {
          // Server components can't remove cookies directly
        }
      },
    }
  )

  // Get authenticated user
  const { data: { user }, error: userError } = await supabase.auth.getUser()
  if (userError || !user) {
    redirect("/auth")
  }

  const userId = user.id

  // Get user's selected account
  const { data: userProfile } = await supabase
    .from('user_profiles')
    .select('selected_account_id')
    .eq('user_id', userId)
    .single()

  const selectedAccountId = userProfile?.selected_account_id || null

  // Fetch initial notebook entries - but we'll pass empty initial data
  // to ensure client-side filtering works correctly on first load
  const { count } = await supabase
    .from("notebook_entries")
    .select("*", { count: "exact", head: true })
    .eq("user_id", userId)

  // We're not fetching actual entries here to avoid showing unfiltered data on load

  // No error handling needed here since we're only getting the count

  // Fetch categories
  const { data: categoriesData, error: categoriesError } = await supabase
    .from("notebook_entries")
    .select("category")
    .eq("user_id", userId)
    .not("category", "is", null)

  if (categoriesError) {
    console.error("Error fetching categories:", categoriesError)
  }

  // Count occurrences of each category
  const categoryCounts: Record<string, number> = {}
  categoriesData?.forEach(entry => {
    if (entry.category) {
      categoryCounts[entry.category] = (categoryCounts[entry.category] || 0) + 1
    }
  })

  // Convert to array of category objects
  const categories: NotebookCategory[] = Object.entries(categoryCounts).map(([name, count]) => ({
    name,
    count
  }))

  // Don't fetch tags on server-side to avoid showing incorrect account-filtered data
  // Client-side React Query will fetch accurate account-filtered tags
  const tags: NotebookTag[] = []

  // Fetch folders - include system folders and account-specific user folders
  // System folders should be visible across all accounts
  // User folders should be filtered by account_id
  let folderQuery = supabase
    .from("notebook_folders")
    .select("*")

  if (selectedAccountId === null) {
    // Show system folders and user folders with no account_id
    folderQuery = folderQuery.or(`is_system.eq.true,and(user_id.eq.${userId},account_id.is.null)`)
  } else {
    // Show system folders and user folders for specific account
    folderQuery = folderQuery.or(`is_system.eq.true,and(user_id.eq.${userId},account_id.eq.${selectedAccountId})`)
  }

  const { data: folders, error: foldersError } = await folderQuery.order("name")

  if (foldersError) {
    console.error("Error fetching folders:", foldersError)
  }

  // Don't show any counts on server-side to avoid showing incorrect values
  // Client-side React Query will fetch and display accurate account-filtered counts
  const foldersWithCounts: NotebookFolderWithMeta[] = (folders || []).map(folder => ({
    ...folder,
    count: undefined // No count initially - client will populate with correct values
  }))

  return (
    <div className="h-full overflow-hidden -m-4 md:-m-6">
      <NotebookClient
        userId={userId}
        initialEntries={[]} // Pass empty array to force client-side filtering on first load
        initialCount={count || 0}
        initialCategories={categories}
        initialTags={tags}
        initialFolders={foldersWithCounts}
        serverSelectedAccountId={selectedAccountId}
      />
    </div>
  )
}
