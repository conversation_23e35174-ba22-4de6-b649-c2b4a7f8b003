"use client"

import React from 'react';
import Link from 'next/link';

export default function EditorTestPage() {
  return (
    <div className="container mx-auto p-4">
      <h1 className="text-3xl font-bold mb-6">Rich Text Editor Tests</h1>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="p-6 border rounded-lg shadow-sm hover:shadow-md transition-shadow">
          <h2 className="text-xl font-semibold mb-3">Basic Quill Editor</h2>
          <p className="text-gray-600 dark:text-gray-300 mb-4">
            A simple implementation of the Quill rich text editor with basic formatting options.
          </p>
          <Link
            href="/quill-test"
            className="inline-block px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
          >
            Try Basic Quill Editor
          </Link>
        </div>

        <div className="p-6 border rounded-lg shadow-sm hover:shadow-md transition-shadow">
          <h2 className="text-xl font-semibold mb-3">Advanced Quill Editor</h2>
          <p className="text-gray-600 dark:text-gray-300 mb-4">
            An enhanced version of the Quill editor with a fixed toolbar and additional features.
          </p>
          <Link
            href="/advanced-quill-test"
            className="inline-block px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
          >
            Try Advanced Quill Editor
          </Link>
        </div>

        <div className="p-6 border rounded-lg shadow-sm hover:shadow-md transition-shadow bg-green-50 dark:bg-green-900/20">
          <h2 className="text-xl font-semibold mb-3">Direct Quill Editor (Recommended)</h2>
          <p className="text-gray-600 dark:text-gray-300 mb-4">
            A direct implementation of Quill based on the Slab/Quill approach for better React 18 compatibility.
          </p>
          <Link
            href="/quill-direct-test"
            className="inline-block px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors"
          >
            Try Direct Quill Editor
          </Link>
        </div>

        <div className="p-6 border rounded-lg shadow-sm hover:shadow-md transition-shadow">
          <h2 className="text-xl font-semibold mb-3">Tiptap Editor (Current)</h2>
          <p className="text-gray-600 dark:text-gray-300 mb-4">
            The current Tiptap editor implementation for comparison.
          </p>
          <Link
            href="/tiptap-test"
            className="inline-block px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
          >
            Try Tiptap Editor
          </Link>
        </div>
      </div>

      <div className="mt-8 p-4 bg-gray-100 dark:bg-gray-800 rounded-lg">
        <h2 className="text-xl font-semibold mb-3">Comparison</h2>
        <p className="mb-2">
          This page allows you to test and compare different rich text editor implementations:
        </p>
        <ul className="list-disc pl-6 space-y-2">
          <li><strong>Basic Quill Editor:</strong> A simple implementation using react-quill wrapper.</li>
          <li><strong>Advanced Quill Editor:</strong> Enhanced version with fixed toolbar and additional features.</li>
          <li><strong>Direct Quill Editor (Recommended):</strong> A direct implementation of Quill based on the Slab/Quill approach for better React 18 compatibility.</li>
          <li><strong>Tiptap Editor:</strong> The current implementation based on ProseMirror.</li>
        </ul>
        <p className="mt-4">
          We recommend the <strong>Direct Quill Editor</strong> as it provides the best compatibility with React 18 and Next.js, while maintaining excellent typing performance and cursor positioning.
        </p>
      </div>
    </div>
  );
}
