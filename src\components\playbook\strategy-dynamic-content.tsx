"use client"

import { useState, useEffect } from "react"
import { Strategy, StrategyPerformance, Setup, StrategyRule, RULE_TYPES } from "@/types/playbook"
import { Trade } from "@/types/trade"
import { FallbackImage } from "@/components/ui/fallback-image";
import {
  calculateStrategyPerformance,
  getSetups,
  getStrategyRules,
  updateSetup,
  createSetup,
  deleteSetup,
  updateStrategyRule,
  createStrategyRule,
  deleteStrategyRule
} from "@/lib/server/playbook-service"
import { getTradesByStrategy } from "@/lib/server/trade-service"
import { getSupabaseBrowser } from "@/lib/supabase"
import { format, subMonths, startOfMonth, endOfMonth } from "date-fns"
import { useAccount } from "@/contexts/account-context"

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  <PERSON><PERSON>,
  <PERSON>bs<PERSON>ontent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs"
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  ReferenceLine,
} from "recharts"
import { RefreshCw, ArrowUpDown, Edit, Trash, Plus, Save, X, Upload, Loader2 } from "lucide-react"
import { toast } from "sonner"
import { cn } from "@/lib/utils"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

// Form validation schemas
const setupFormSchema = z.object({
  name: z.string().min(1, "Name is required"),
  description: z.string().optional(),
  visual_cues: z.string().optional(),
  confirmation_criteria: z.string().optional(),
  image_urls: z.array(z.string()).optional().default([]),
  image_file: z.instanceof(File).optional(),
})

const ruleFormSchema = z.object({
  name: z.string().min(1, "Name is required"),
  description: z.string().optional(),
  rule_type: z.enum(["entry", "exit", "stop_loss", "take_profit", "position_sizing", "other"]),
  priority: z.coerce.number().int().min(1).optional().default(1),
})

type SetupFormValues = {
  name: string
  description?: string
  visual_cues?: string
  confirmation_criteria?: string
  image_urls?: string[]
  image_file?: File
}
type RuleFormValues = {
  name: string
  description?: string
  rule_type: "entry" | "exit" | "stop_loss" | "take_profit" | "position_sizing" | "other"
  priority?: number
}

interface StrategyDynamicContentProps {
  userId: string
  strategy: Strategy
  view: "summary" | "setups" | "rules" | "performance"
  prefetchedData?: any
}

export function StrategyDynamicContent({
  userId,
  strategy,
  view,
  prefetchedData,
}: StrategyDynamicContentProps) {
  const { selectedAccountId } = useAccount()
  const [isLoading, setIsLoading] = useState(true)
  const [trades, setTrades] = useState<Trade[]>([])
  const [performance, setPerformance] = useState<StrategyPerformance | null>(null)
  const [activeTab, setActiveTab] = useState("overview")
  const [isCalculating, setIsCalculating] = useState(false)
  const [setups, setSetups] = useState<Setup[]>([])
  const [rules, setRules] = useState<StrategyRule[]>([])
  const [isLoadingSetups, setIsLoadingSetups] = useState(false)
  const [isLoadingRules, setIsLoadingRules] = useState(false)

  // Edit states
  const [isEditingSetup, setIsEditingSetup] = useState(false)
  const [currentSetup, setCurrentSetup] = useState<Setup | null>(null)
  const [isEditingRule, setIsEditingRule] = useState(false)
  const [currentRule, setCurrentRule] = useState<StrategyRule | null>(null)
  const [isCreatingSetup, setIsCreatingSetup] = useState(false)
  const [isCreatingRule, setIsCreatingRule] = useState(false)

  // For trades table
  const [page, setPage] = useState(1)
  const [sortField, setSortField] = useState<string>("time_close")
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("desc")
  const itemsPerPage = 10

  // Fetch trades and performance data
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true)
      try {
        // Check if we have prefetched data
        if (prefetchedData && prefetchedData.performance) {
          console.log('Using prefetched performance data')
          setPerformance(prefetchedData.performance)
        }

        // Fetch trades for this strategy
        console.log(`Fetching trades for strategy ${strategy.id}${selectedAccountId ? ` and account ${selectedAccountId}` : ''}`)
        const tradesData = await getTradesByStrategy(strategy.id, selectedAccountId)
        setTrades(tradesData)

        // Only calculate performance if we don't have prefetched data
        if (!prefetchedData || !prefetchedData.performance) {
          // Calculate performance metrics even if we don't have trades
          // This will help us debug issues with the performance calculation
          try {
            // Use last 3 months as default period
            const endDate = new Date()
            const startDate = subMonths(endDate, 3)

            const periodStart = startOfMonth(startDate).toISOString()
            const periodEnd = endOfMonth(endDate).toISOString()

            console.log(`Calculating performance for strategy ${strategy.id} from ${periodStart} to ${periodEnd}`)

            // If we have no trades, we'll still try to calculate performance
            // but we'll handle the null result gracefully
            const performanceData = await calculateStrategyPerformance(
              strategy.id,
              periodStart,
              periodEnd,
              selectedAccountId
            )

            if (performanceData) {
              console.log('Performance data received:', performanceData)
              setPerformance(performanceData)
            } else {
              console.log('No performance data returned')
              // Create a default performance object with zeros
              // This prevents errors when trying to display performance metrics
              if (tradesData.length > 0) {
                // Only show a toast if we actually have trades but couldn't calculate performance
                toast.warning("Could not calculate performance metrics")
              }
            }
          } catch (perfError) {
            console.error("Error calculating performance:", perfError)
            // Don't show an error toast for this, as it's not critical
          }
        }
      } catch (error) {
        console.error("Error fetching strategy data:", error)
        toast.error("Failed to load strategy data")
      } finally {
        setIsLoading(false)
      }
    }

    // Initial data fetch
    fetchData()

    // Listen for refresh-trades-data events
    const handleRefreshTradesData = (event: Event) => {
      const customEvent = event as CustomEvent;
      console.log("Global refresh-trades-data event received in strategy-dynamic-content:", customEvent.detail || "")

      // Add a small delay to ensure the database has been updated
      setTimeout(() => {
        fetchData()
      }, 300)
    }

    // Listen for strategy-performance-updated events
    const handlePerformanceUpdated = (event: Event) => {
      const customEvent = event as CustomEvent;
      console.log("Strategy performance updated event received:", customEvent.detail)

      // Check if this event is for this strategy
      if (customEvent.detail?.strategyId === strategy.id) {
        console.log("Performance update is for this strategy, updating performance data")

        // Update the performance data directly
        if (customEvent.detail?.performance) {
          setPerformance(customEvent.detail.performance)
        } else {
          // If no performance data is provided, fetch it
          fetchData()
        }
      }
    }

    // Listen for global data change events
    const handleGlobalDataChange = (event: Event) => {
      const customEvent = event as CustomEvent;
      console.log("Global data change event received in strategy-dynamic-content:", customEvent.detail)

      if (customEvent.detail?.type === 'strategy-assignment' &&
          (customEvent.detail?.strategyId === strategy.id || !customEvent.detail?.strategyId)) {
        console.log("Strategy assignment changed via global event, refreshing data")

        // Add a small delay to ensure the database has been updated
        setTimeout(() => {
          fetchData()
        }, 300)
      }
    }

    // Add event listeners
    window.addEventListener('refresh-trades-data', handleRefreshTradesData)
    window.addEventListener('strategy-performance-updated', handlePerformanceUpdated)
    window.addEventListener('global-data-change', handleGlobalDataChange)

    // Clean up
    return () => {
      window.removeEventListener('refresh-trades-data', handleRefreshTradesData)
      window.removeEventListener('strategy-performance-updated', handlePerformanceUpdated)
      window.removeEventListener('global-data-change', handleGlobalDataChange)
    }
  }, [userId, strategy.id, selectedAccountId])

  // Fetch setups when the view changes to "setups"
  useEffect(() => {
    if (view === "setups") {
      // Check if we have prefetched data
      if (prefetchedData && prefetchedData.setups) {
        console.log('Using prefetched setups data')
        setSetups(prefetchedData.setups)
        setIsLoadingSetups(false)
        return;
      }

      const fetchSetups = async () => {
        setIsLoadingSetups(true)
        try {
          console.log(`Fetching setups for strategy ${strategy.id}`)
          const setupsData = await getSetups(strategy.id)
          console.log('Setups data received:', setupsData)
          setSetups(setupsData)
        } catch (error) {
          console.error("Error fetching setups:", error)
          toast.error("Failed to load setups")
        } finally {
          setIsLoadingSetups(false)
        }
      }

      fetchSetups()

      // Listen for force-rerender events
      const handleForceRerender = () => {
        console.log('Force rerender event detected, refreshing setups data');
        fetchSetups();
      };

      window.addEventListener('force-rerender', handleForceRerender);

      return () => {
        window.removeEventListener('force-rerender', handleForceRerender);
      };
    }
  }, [userId, strategy.id, view, prefetchedData])

  // Fetch rules when the view changes to "rules"
  useEffect(() => {
    if (view === "rules") {
      // Check if we have prefetched data
      if (prefetchedData && prefetchedData.rules) {
        console.log('Using prefetched rules data')
        setRules(prefetchedData.rules)
        setIsLoadingRules(false)
        return;
      }

      const fetchRules = async () => {
        setIsLoadingRules(true)
        try {
          console.log(`Fetching rules for strategy ${strategy.id}`)
          const rulesData = await getStrategyRules(strategy.id)
          console.log('Rules data received:', rulesData)
          setRules(rulesData)
        } catch (error) {
          console.error("Error fetching rules:", error)
          toast.error("Failed to load rules")
        } finally {
          setIsLoadingRules(false)
        }
      }

      fetchRules()
    }
  }, [userId, strategy.id, view, prefetchedData])

  // Calculate P&L chart data
  const chartData = trades.length > 0
    ? (() => {
        // Sort trades by close time
        const sortedTrades = [...trades].sort(
          (a, b) => new Date(a.time_close).getTime() - new Date(b.time_close).getTime()
        )

        // Calculate cumulative performance
        let runningBalance = 0
        return sortedTrades.map((trade) => {
          runningBalance += trade.profit
          return {
            date: format(new Date(trade.time_close), "yyyy-MM-dd"),
            balance: parseFloat(runningBalance.toFixed(2)),
            profit: parseFloat(trade.profit.toFixed(2)),
          }
        })
      })()
    : []

  // Sort trades for the table
  const sortedTrades = [...trades].sort((a, b) => {
    const aValue = sortField === "profit" ? a.profit :
                  sortField === "time_close" ? new Date(a.time_close).getTime() :
                  String(a[sortField as keyof Trade]);

    const bValue = sortField === "profit" ? b.profit :
                  sortField === "time_close" ? new Date(b.time_close).getTime() :
                  String(b[sortField as keyof Trade]);

    if (sortDirection === "asc") {
      return aValue > bValue ? 1 : -1;
    } else {
      return aValue < bValue ? 1 : -1;
    }
  });

  // Paginate trades
  const startIndex = (page - 1) * itemsPerPage;
  const paginatedTrades = sortedTrades.slice(startIndex, startIndex + itemsPerPage);
  const totalPages = Math.ceil(sortedTrades.length / itemsPerPage);

  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortField(field);
      setSortDirection("desc");
    }
  };

  const calculatePerformance = async () => {
    setIsCalculating(true)
    try {
      // Use last 3 months as default period
      const endDate = new Date()
      const startDate = subMonths(endDate, 3)

      const periodStart = startOfMonth(startDate).toISOString()
      const periodEnd = endOfMonth(endDate).toISOString()

      const result = await calculateStrategyPerformance(
        strategy.id,
        periodStart,
        periodEnd,
        selectedAccountId
      )

      if (result) {
        setPerformance(result)
        toast.success("Performance calculated successfully")
      } else {
        toast.error("No trades found in the selected period")
      }
    } catch (error) {
      console.error("Error calculating performance:", error)
      toast.error("Failed to calculate performance")
    } finally {
      setIsCalculating(false)
    }
  }

  // Common loading state
  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-40">
        <div className="animate-pulse">Loading strategy data...</div>
      </div>
    )
  }

  // Common empty state - only show for performance and summary views
  if (trades.length === 0 && (view === "performance" || view === "summary")) {
    return (
      <div className="space-y-4">
        <p className="text-muted-foreground">
          No trades found for this strategy. Assign trades to this strategy to see performance metrics.
        </p>
        <Button onClick={calculatePerformance} disabled={isCalculating}>
          <RefreshCw className={`mr-2 h-4 w-4 ${isCalculating ? "animate-spin" : ""}`} />
          {isCalculating ? "Calculating..." : "Calculate Performance"}
        </Button>
      </div>
    )
  }

  // Render summary view (default)
  if (view === "summary") {
    return (
      <div className="space-y-6">
        {/* Performance Summary - This is the only thing shown by default */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-base">Performance Summary</CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            {performance ? (
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="bg-muted/30 p-3 rounded-lg">
                  <div className="text-sm text-muted-foreground">Total Trades</div>
                  <div className="text-xl font-semibold">{performance.total_trades}</div>
                </div>
                <div className="bg-muted/30 p-3 rounded-lg">
                  <div className="text-sm text-muted-foreground">Win Rate</div>
                  <div className="text-xl font-semibold">{(performance.win_rate * 100).toFixed(2)}%</div>
                </div>
                <div className="bg-muted/30 p-3 rounded-lg">
                  <div className="text-sm text-muted-foreground">Net P&L</div>
                  <div className={cn(
                    "text-xl font-semibold",
                    performance.profit_loss >= 0 ? "text-emerald-500" : "text-rose-500"
                  )}>
                    {performance.profit_loss >= 0 ?
                      `$${performance.profit_loss.toFixed(2)}` :
                      `-$${Math.abs(performance.profit_loss).toFixed(2)}`}
                  </div>
                </div>
                <div className="bg-muted/30 p-3 rounded-lg">
                  <div className="text-sm text-muted-foreground">Avg Win/Loss</div>
                  <div className="text-xl font-semibold">{performance.average_risk_reward.toFixed(2)}</div>
                </div>
              </div>
            ) : (
              <div className="py-4">
                {trades.length > 0 ? (
                  <div className="text-center">
                    <p className="text-muted-foreground mb-2">
                      Performance metrics could not be calculated.
                    </p>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={calculatePerformance}
                      disabled={isCalculating}
                    >
                      <RefreshCw className={`mr-2 h-4 w-4 ${isCalculating ? "animate-spin" : ""}`} />
                      {isCalculating ? "Calculating..." : "Try Again"}
                    </Button>
                  </div>
                ) : (
                  <div className="text-center">
                    <p className="text-muted-foreground">
                      No performance data available. Assign trades to this strategy to see metrics.
                    </p>
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    )
  }

  // Render performance view (when "View Performance" is clicked)
  if (view === "performance") {
    return (
      <div className="space-y-6">
        {/* Main Tabs for Performance View */}
        <Tabs defaultValue="metrics" onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="metrics">Metrics</TabsTrigger>
            <TabsTrigger value="executed-trades">Executed Trades</TabsTrigger>
          </TabsList>

          {/* Metrics Tab Content */}
          <TabsContent value="metrics" className="pt-4 space-y-6">
            {/* P&L Chart */}
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base">P&L Performance</CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                {chartData.length > 0 ? (
                  <div className="h-[200px]">
                    <ResponsiveContainer width="100%" height="100%">
                      <LineChart
                        data={chartData}
                        margin={{ top: 10, right: 10, left: 10, bottom: 10 }}
                      >
                        <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" opacity={0.3} />
                        <XAxis
                          dataKey="date"
                          tickFormatter={(value) => format(new Date(value), "MMM d")}
                          tick={{ fontSize: 10, fill: 'hsl(var(--muted-foreground))' }}
                        />
                        <YAxis
                          tickFormatter={(value) => `$${value.toFixed(2)}`}
                          tick={{ fontSize: 10, fill: 'hsl(var(--muted-foreground))' }}
                        />
                        <Tooltip
                          formatter={(value: number) => [`$${value.toFixed(2)}`, 'Balance']}
                          labelFormatter={(label) => format(new Date(label), "MMM d, yyyy")}
                        />
                        <ReferenceLine y={0} stroke="hsl(var(--border))" />
                        <Line
                          type="monotone"
                          dataKey="balance"
                          stroke="#2563eb"
                          strokeWidth={2}
                          dot={false}
                          activeDot={{ r: 6 }}
                        />
                      </LineChart>
                    </ResponsiveContainer>
                  </div>
                ) : (
                  <div className="flex justify-center items-center h-[200px] text-muted-foreground">
                    No trade data available to display chart
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Performance Summary */}
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base">Performance Summary</CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                {performance ? (
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="bg-muted/30 p-3 rounded-lg">
                      <div className="text-sm text-muted-foreground">Total Trades</div>
                      <div className="text-xl font-semibold">{performance.total_trades}</div>
                    </div>
                    <div className="bg-muted/30 p-3 rounded-lg">
                      <div className="text-sm text-muted-foreground">Win Rate</div>
                      <div className="text-xl font-semibold">{(performance.win_rate * 100).toFixed(2)}%</div>
                    </div>
                    <div className="bg-muted/30 p-3 rounded-lg">
                      <div className="text-sm text-muted-foreground">Net P&L</div>
                      <div className={cn(
                        "text-xl font-semibold",
                        performance.profit_loss >= 0 ? "text-emerald-500" : "text-rose-500"
                      )}>
                        {performance.profit_loss >= 0 ?
                          `$${performance.profit_loss.toFixed(2)}` :
                          `-$${Math.abs(performance.profit_loss).toFixed(2)}`}
                      </div>
                    </div>
                    <div className="bg-muted/30 p-3 rounded-lg">
                      <div className="text-sm text-muted-foreground">Avg Win/Loss</div>
                      <div className="text-xl font-semibold">{performance.average_risk_reward.toFixed(2)}</div>
                    </div>
                  </div>
                ) : (
                  <div className="py-4">
                    {trades.length > 0 ? (
                      <div className="text-center">
                        <p className="text-muted-foreground mb-2">
                          Performance metrics could not be calculated.
                        </p>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={calculatePerformance}
                          disabled={isCalculating}
                        >
                          <RefreshCw className={`mr-2 h-4 w-4 ${isCalculating ? "animate-spin" : ""}`} />
                          {isCalculating ? "Calculating..." : "Try Again"}
                        </Button>
                      </div>
                    ) : (
                      <div className="text-center">
                        <p className="text-muted-foreground">
                          No performance data available. Assign trades to this strategy to see metrics.
                        </p>
                      </div>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Detailed Metrics */}
            {performance && (
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-base">Detailed Metrics</CardTitle>
                </CardHeader>
                <CardContent className="pt-0">
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                    <div>
                      <div className="text-sm text-muted-foreground">Winning Trades</div>
                      <div className="text-base font-medium text-emerald-500">{performance.winning_trades}</div>
                    </div>
                    <div>
                      <div className="text-sm text-muted-foreground">Losing Trades</div>
                      <div className="text-base font-medium text-rose-500">{performance.losing_trades}</div>
                    </div>
                    <div>
                      <div className="text-sm text-muted-foreground">Expectancy</div>
                      <div className="text-base font-medium">${performance.expectancy.toFixed(2)}</div>
                    </div>
                    <div>
                      <div className="text-sm text-muted-foreground">Average Win</div>
                      <div className="text-base font-medium text-emerald-500">${performance.average_win.toFixed(2)}</div>
                    </div>
                    <div>
                      <div className="text-sm text-muted-foreground">Average Loss</div>
                      <div className="text-base font-medium text-rose-500">-${Math.abs(performance.average_loss).toFixed(2)}</div>
                    </div>
                    <div>
                      <div className="text-sm text-muted-foreground">Largest Win</div>
                      <div className="text-base font-medium text-emerald-500">${performance.largest_win.toFixed(2)}</div>
                    </div>
                    <div>
                      <div className="text-sm text-muted-foreground">Largest Loss</div>
                      <div className="text-base font-medium text-rose-500">-${Math.abs(performance.largest_loss).toFixed(2)}</div>
                    </div>
                    <div>
                      <div className="text-sm text-muted-foreground">Period</div>
                      <div className="text-base font-medium">
                        {format(new Date(performance.period_start), "MMM d, yyyy")} - {format(new Date(performance.period_end), "MMM d, yyyy")}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          {/* Executed Trades Tab Content */}
          <TabsContent value="executed-trades" className="pt-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base">Executed Trades</CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="cursor-pointer" onClick={() => handleSort("time_close")}>
                          Date/Time
                          <ArrowUpDown className="ml-1 h-3 w-3 inline" />
                        </TableHead>
                        <TableHead className="cursor-pointer" onClick={() => handleSort("symbol")}>
                          Symbol
                          <ArrowUpDown className="ml-1 h-3 w-3 inline" />
                        </TableHead>
                        <TableHead className="cursor-pointer" onClick={() => handleSort("type")}>
                          Type
                          <ArrowUpDown className="ml-1 h-3 w-3 inline" />
                        </TableHead>
                        <TableHead className="cursor-pointer" onClick={() => handleSort("volume")}>
                          Volume
                          <ArrowUpDown className="ml-1 h-3 w-3 inline" />
                        </TableHead>
                        <TableHead className="cursor-pointer" onClick={() => handleSort("profit")}>
                          P&L
                          <ArrowUpDown className="ml-1 h-3 w-3 inline" />
                        </TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {paginatedTrades.map((trade) => (
                        <TableRow key={trade.id} className="hover:bg-muted/50">
                          <TableCell className="font-medium">
                            {format(new Date(trade.time_close), "MMM d, yyyy h:mm a")}
                          </TableCell>
                          <TableCell>{trade.symbol}</TableCell>
                          <TableCell>
                            <Badge
                              variant="outline"
                              className={cn(
                                trade.type.toLowerCase() === "buy"
                                  ? "border-emerald-500 text-emerald-500"
                                  : "border-rose-500 text-rose-500"
                              )}
                            >
                              {trade.type.toUpperCase()}
                            </Badge>
                          </TableCell>
                          <TableCell>{trade.volume}</TableCell>
                          <TableCell className={cn(
                            "font-medium",
                            trade.profit >= 0 ? "text-emerald-500" : "text-rose-500"
                          )}>
                            {trade.profit >= 0 ?
                              `$${trade.profit.toFixed(2)}` :
                              `-$${Math.abs(trade.profit).toFixed(2)}`}
                          </TableCell>
                        </TableRow>
                      ))}
                      {trades.length === 0 && (
                        <TableRow>
                          <TableCell colSpan={5} className="h-24 text-center">
                            No trades found for this strategy.
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </div>

                {/* Pagination */}
                {trades.length > itemsPerPage && (
                  <div className="flex justify-center mt-4">
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setPage(Math.max(1, page - 1))}
                        disabled={page === 1}
                      >
                        Previous
                      </Button>
                      <span className="text-sm text-muted-foreground">
                        Page {page} of {totalPages}
                      </span>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setPage(Math.min(totalPages, page + 1))}
                        disabled={page === totalPages}
                      >
                        Next
                      </Button>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    )
  }

  // Setup form component
  const SetupForm = ({ setup, onSave, onCancel }: {
    setup: Setup | null,
    onSave: (data: SetupFormValues) => void,
    onCancel: () => void
  }) => {
    const [isUploading, setIsUploading] = useState(false);
    const [previewUrl, setPreviewUrl] = useState<string | null>(
      setup && setup.image_urls && setup.image_urls.length > 0 ? setup.image_urls[0] : null
    );
    const supabase = getSupabaseBrowser();

    const form = useForm<SetupFormValues>({
      resolver: zodResolver(setupFormSchema),
      defaultValues: setup ? {
        name: setup.name,
        description: setup.description || "",
        visual_cues: setup.visual_cues || "",
        confirmation_criteria: setup.confirmation_criteria || "",
        image_urls: setup.image_urls || [],
      } : {
        name: "",
        description: "",
        visual_cues: "",
        confirmation_criteria: "",
        image_urls: [],
      }
    });

    const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
      const file = event.target.files?.[0];
      if (!file) return;

      // Validate file type
      if (!file.type.startsWith('image/')) {
        toast.error("Please upload an image file");
        return;
      }

      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        toast.error("File size should not exceed 5MB");
        return;
      }

      try {
        // Set the file in the form
        form.setValue('image_file', file);

        // Create a preview URL
        const objectUrl = URL.createObjectURL(file);
        console.log("Image preview created:", objectUrl);

        // Set the preview URL
        setPreviewUrl(objectUrl);

        // Create a cleanup function
        const cleanup = () => {
          console.log("Cleaning up object URL");
          URL.revokeObjectURL(objectUrl);
        };

        // Add an event listener to clean up when the component unmounts
        window.addEventListener('beforeunload', cleanup);

        // Return the cleanup function
        return () => {
          cleanup();
          window.removeEventListener('beforeunload', cleanup);
        };
      } catch (error) {
        console.error("Error handling image upload:", error);
        toast.error("Failed to process the image. Please try another image.");

        // Reset the file input
        event.target.value = '';
      }
    };

    // Function to create a data URL from a file (as a fallback when Supabase upload fails)
    const createLocalImageUrl = async (file: File): Promise<string> => {
      return new Promise((resolve) => {
        const reader = new FileReader();
        reader.onload = (e) => {
          if (e.target && typeof e.target.result === 'string') {
            resolve(e.target.result);
          } else {
            resolve('');
          }
        };
        reader.readAsDataURL(file);
      });
    };

    const uploadImageToSupabase = async (file: File): Promise<string | null> => {
      setIsUploading(true);
      try {
        // Import the uploadImageToStorage function
        const { uploadImageToStorage } = await import('@/lib/image-service');

        // Use our improved upload function
        const imageUrl = await uploadImageToStorage(file);
        console.log("Generated public URL:", imageUrl);

        return imageUrl;
      } catch (error) {
        console.error("Error uploading image:", error);

        // As a last resort, try to create a data URL
        try {
          console.log("Using local data URL as fallback after error");
          const dataUrl = await createLocalImageUrl(file);
          toast.warning("Image will be stored locally due to storage access issues");
          return dataUrl;
        } catch (e) {
          console.error("Failed to create data URL fallback:", e);
          toast.error("An unexpected error occurred during image handling");
          return null;
        }
      } finally {
        setIsUploading(false);
      }
    };

    const onSubmit = async (data: SetupFormValues) => {
      // Ensure image_urls is always an array
      const currentImageUrls = data.image_urls || [];

      // If there's a new image file, upload it first
      if (data.image_file) {
        try {
          console.log("Uploading image file:", data.image_file.name);
          const imageUrl = await uploadImageToSupabase(data.image_file);

          if (imageUrl) {
            console.log("Image uploaded successfully, URL:", imageUrl);
            // Add the new image URL to the existing ones
            data.image_urls = [...currentImageUrls, imageUrl];
          } else {
            console.warn("Image upload returned null URL");
            // If upload fails, we can still save the setup without the image
            toast.warning("Setup will be saved without the image due to upload issues");
            data.image_urls = currentImageUrls;
          }
        } catch (error) {
          console.error("Error in image upload process:", error);

          // Try to create a data URL as a last resort
          try {
            console.log("Attempting to create data URL as fallback");
            const dataUrl = await createLocalImageUrl(data.image_file);
            if (dataUrl) {
              console.log("Created data URL fallback");
              data.image_urls = [...currentImageUrls, dataUrl];
              toast.warning("Image stored locally due to upload issues");
            } else {
              data.image_urls = currentImageUrls;
            }
          } catch (fallbackError) {
            console.error("Failed to create data URL fallback:", fallbackError);
            toast.warning("Setup will be saved without the image due to upload issues");
            data.image_urls = currentImageUrls;
          }
        }
      } else {
        // No new image file, keep existing URLs
        data.image_urls = currentImageUrls;
      }

      // Remove the file from the data before saving
      const { image_file, ...dataToSave } = data;
      onSave(dataToSave as SetupFormValues);
    };

    return (
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Setup Name</FormLabel>
                <FormControl>
                  <Input placeholder="E.g., Double Bottom, Bull Flag, etc." {...field} />
                </FormControl>
                <FormDescription>
                  Give your setup a clear, descriptive name that you'll recognize
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="description"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Description</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="E.g., A reversal pattern that forms after a downtrend and signals a potential change in direction..."
                    className="min-h-[100px]"
                    {...field}
                    value={field.value || ""}
                  />
                </FormControl>
                <FormDescription>
                  Provide a detailed explanation of what this setup is and when it typically appears
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="visual_cues"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Visual Cues</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="E.g., 1. Price makes a new low, then rebounds\n2. Price tests the low again but doesn't break it\n3. Volume decreases on the second low..."
                    className="min-h-[100px]"
                    {...field}
                    value={field.value || ""}
                  />
                </FormControl>
                <FormDescription>
                  List the specific visual patterns, indicators, or chart formations to look for
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="confirmation_criteria"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Confirmation Criteria</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="E.g., 1. Price breaks above the neckline\n2. Volume increases on the breakout\n3. RSI shows bullish divergence..."
                    className="min-h-[100px]"
                    {...field}
                    value={field.value || ""}
                  />
                </FormControl>
                <FormDescription>
                  Define the specific conditions that must be met before taking a trade based on this setup
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Image Upload */}
          <div className="space-y-2">
            <FormLabel>Setup Image</FormLabel>
            <div className="flex flex-col items-center p-4 border-2 border-dashed rounded-md border-muted-foreground/25 hover:border-muted-foreground/50 transition-colors">
              {previewUrl ? (
                <div className="space-y-2 w-full">
                  <div className="relative aspect-video w-full max-h-[200px] overflow-hidden rounded-md">
                    <FallbackImage
                      src={previewUrl || ""}
                      alt="Setup preview"
                      className="w-full h-full"
                      style={{
                        backgroundColor: '#f0f0f0',
                        minHeight: '100px'
                      }}
                      fallbackComponent={
                        <div className="flex items-center justify-center w-full h-full min-h-[100px] bg-muted/20">
                          <span className="text-sm text-muted-foreground">Image preview</span>
                        </div>
                      }
                    />
                  </div>
                  <div className="flex justify-center">
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={async () => {
                        // Clear the preview
                        setPreviewUrl(null);
                        form.setValue('image_file', undefined);

                        // If this is a URL from storage (not a local preview), delete it
                        if (previewUrl && previewUrl.includes('storage/v1/object/public')) {
                          try {
                            // Import the deleteImageFromStorage function
                            const { deleteImageFromStorage } = await import('@/lib/image-service');

                            // Delete the image from storage
                            await deleteImageFromStorage(previewUrl);
                            console.log('Successfully deleted image from storage:', previewUrl);
                          } catch (error) {
                            console.error('Error deleting image from storage:', error);
                            // Continue even if deletion fails
                          }
                        }
                      }}
                    >
                      <Trash className="h-4 w-4 mr-2" />
                      Remove Image
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="flex flex-col items-center space-y-2 py-4">
                  <Upload className="h-8 w-8 text-muted-foreground" />
                  <p className="text-sm text-muted-foreground">
                    Drag & drop or click to upload an image
                  </p>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => document.getElementById('setup-image-upload')?.click()}
                    disabled={isUploading}
                  >
                    {isUploading ? (
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    ) : (
                      <Upload className="h-4 w-4 mr-2" />
                    )}
                    Select Image
                  </Button>
                  <FormDescription className="text-center text-xs">
                    Upload a chart example or diagram of this setup (max 5MB)
                  </FormDescription>
                </div>
              )}
              <input
                id="setup-image-upload"
                type="file"
                accept="image/*"
                className="hidden"
                onChange={handleImageUpload}
                disabled={isUploading}
              />
            </div>
          </div>

          <div className="flex justify-end space-x-2 pt-6 pb-2">
            <Button type="button" variant="outline" onClick={onCancel} disabled={isUploading}>
              Cancel
            </Button>
            <Button type="submit" disabled={isUploading}>
              {isUploading ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Uploading...
                </>
              ) : (
                'Save Setup'
              )}
            </Button>
          </div>
        </form>
      </Form>
    );
  };

  // Render setups view
  if (view === "setups") {
    if (isLoadingSetups) {
      return (
        <div className="flex justify-center items-center h-40">
          <div className="animate-pulse">Loading strategy setups...</div>
        </div>
      )
    }

    // Handle setup operations
    const handleCreateSetup = async (data: SetupFormValues) => {
      try {
        // Ensure image_urls is always an array
        const setupData = {
          ...data,
          image_urls: data.image_urls || [],
          strategy_id: strategy.id,
        };

        const newSetup = await createSetup(setupData);

        if (newSetup) {
          toast.success("Setup created successfully");
          setIsCreatingSetup(false);
          // Refresh setups
          const updatedSetups = await getSetups(strategy.id);
          setSetups(updatedSetups);
        }
      } catch (error) {
        console.error("Error creating setup:", error);
        toast.error("Failed to create setup");
      }
    };

    const handleUpdateSetup = async (data: SetupFormValues) => {
      if (!currentSetup) return;

      try {
        // Ensure image_urls is always an array
        const imageUrls = data.image_urls || [];

        // Check if any images were removed
        if (currentSetup.image_urls && imageUrls) {
          const removedImages = currentSetup.image_urls.filter(
            url => !imageUrls.includes(url)
          );

          // Delete removed images from storage
          if (removedImages.length > 0) {
            // Import the deleteImageFromStorage function
            const { deleteImageFromStorage } = await import('@/lib/image-service');

            // Delete all removed images
            for (const imageUrl of removedImages) {
              try {
                await deleteImageFromStorage(imageUrl);
                console.log('Successfully deleted image from storage:', imageUrl);
              } catch (imgError) {
                console.error('Error deleting removed setup image:', imgError);
                // Continue with update even if image deletion fails
              }
            }
          }
        }

        // Prepare update data with guaranteed array
        const updateData = {
          ...data,
          image_urls: imageUrls
        };

        // Update the setup in the database
        const updatedSetup = await updateSetup(currentSetup.id, updateData);

        if (updatedSetup) {
          toast.success("Setup updated successfully");
          setIsEditingSetup(false);
          setCurrentSetup(null);
          // Refresh setups
          const updatedSetups = await getSetups(strategy.id);
          setSetups(updatedSetups);
        }
      } catch (error) {
        console.error("Error updating setup:", error);
        toast.error("Failed to update setup");
      }
    };

    const handleDeleteSetup = async (setupId: string) => {
      if (!confirm("Are you sure you want to delete this setup?")) return;

      try {
        const result = await deleteSetup(setupId);
        const success = result.success;

        if (success) {
          toast.success("Setup deleted successfully");
          // Refresh setups
          const updatedSetups = await getSetups(strategy.id);
          setSetups(updatedSetups);
        }
      } catch (error) {
        console.error("Error deleting setup:", error);
        toast.error("Failed to delete setup");
      }
    };

    return (
      <div className="space-y-6">
        {/* Setup Form Dialogs */}
        <Dialog open={isCreatingSetup} onOpenChange={setIsCreatingSetup}>
          <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto p-6">
            <DialogHeader className="mb-4">
              <DialogTitle>Create New Setup</DialogTitle>
              <DialogDescription>
                Add a new setup for this strategy
              </DialogDescription>
            </DialogHeader>
            <div className="py-2">
              <SetupForm
                setup={null}
                onSave={handleCreateSetup}
                onCancel={() => setIsCreatingSetup(false)}
              />
            </div>
          </DialogContent>
        </Dialog>

        <Dialog open={isEditingSetup} onOpenChange={setIsEditingSetup}>
          <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto p-6">
            <DialogHeader className="mb-4">
              <DialogTitle>Edit Setup</DialogTitle>
              <DialogDescription>
                Update this setup's details
              </DialogDescription>
            </DialogHeader>
            <div className="py-2">
              <SetupForm
                setup={currentSetup}
                onSave={handleUpdateSetup}
                onCancel={() => {
                  setIsEditingSetup(false);
                  setCurrentSetup(null);
                }}
              />
            </div>
          </DialogContent>
        </Dialog>

        <Card>
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-base">Strategy Setups</CardTitle>
                <CardDescription>
                  Entry conditions and setup patterns for this strategy
                </CardDescription>
              </div>
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    // Refresh setups data
                    setIsLoadingSetups(true);
                    getSetups(strategy.id)
                      .then(data => {
                        setSetups(data);
                        console.log('Refreshed setups data:', data);
                      })
                      .catch(error => {
                        console.error("Error refreshing setups:", error);
                        toast.error("Failed to refresh setups");
                      })
                      .finally(() => setIsLoadingSetups(false));
                  }}
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Refresh
                </Button>
                <Button
                  size="sm"
                  onClick={() => setIsCreatingSetup(true)}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Setup
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {setups.length > 0 ? (
                setups.map((setup, index) => (
                  <div key={setup.id || index} className="p-4 border rounded-md">
                    <div className="flex justify-between items-start mb-3">
                      <h3 className="font-medium text-base">{setup.name}</h3>
                      <div className="flex space-x-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            setCurrentSetup(setup);
                            setIsEditingSetup(true);
                          }}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDeleteSetup(setup.id)}
                        >
                          <Trash className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>

                    {/* Display image if available */}
                    {setup.image_urls && setup.image_urls.length > 0 && (
                      <div className="mb-4">
                        <div className="relative aspect-video w-full max-h-[200px] overflow-hidden rounded-md mb-2 bg-muted/30">
                          <FallbackImage
                            src={setup.image_urls[0] || ""}
                            alt={`${setup.name} chart example`}
                            className="w-full h-full"
                            style={{
                              backgroundColor: '#f0f0f0',
                              minHeight: '100px'
                            }}
                            fallbackComponent={
                              <div className="flex items-center justify-center w-full h-full min-h-[100px] bg-muted/20">
                                <span className="text-sm text-muted-foreground">{setup.name} chart</span>
                              </div>
                            }
                          />
                        </div>
                      </div>
                    )}

                    {setup.description && (
                      <div className="mb-4">
                        <h4 className="text-sm font-medium mb-1">Description:</h4>
                        <p className="text-sm text-muted-foreground">{setup.description}</p>
                      </div>
                    )}

                    {setup.visual_cues && (
                      <div className="mb-4">
                        <h4 className="text-sm font-medium mb-1">Visual Cues:</h4>
                        <p className="text-sm text-muted-foreground">{setup.visual_cues}</p>
                      </div>
                    )}

                    {setup.confirmation_criteria && (
                      <div>
                        <h4 className="text-sm font-medium mb-1">Confirmation Criteria:</h4>
                        <p className="text-sm text-muted-foreground">{setup.confirmation_criteria}</p>
                      </div>
                    )}
                  </div>
                ))
              ) : (
                <div className="text-center py-8">
                  <p className="text-muted-foreground">No setups defined for this strategy yet.</p>
                  <Button
                    variant="outline"
                    size="sm"
                    className="mt-4"
                    onClick={() => setIsCreatingSetup(true)}
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Create Your First Setup
                  </Button>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Rule form component
  const RuleForm = ({ rule, onSave, onCancel }: {
    rule: StrategyRule | null,
    onSave: (data: RuleFormValues) => void,
    onCancel: () => void
  }) => {
    const form = useForm<RuleFormValues>({
      resolver: zodResolver(ruleFormSchema),
      defaultValues: rule ? {
        name: rule.name,
        description: rule.description || "",
        rule_type: rule.rule_type,
        priority: rule.priority,
      } : {
        name: "",
        description: "",
        rule_type: "entry",
        priority: 1,
      }
    });

    return (
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSave)} className="space-y-6">
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Rule Name</FormLabel>
                <FormControl>
                  <Input placeholder="E.g., Only Enter on Pullbacks, 2% Risk per Trade, etc." {...field} />
                </FormControl>
                <FormDescription>
                  Give your rule a clear, concise name that describes its purpose
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="rule_type"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Rule Type</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select rule type" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {RULE_TYPES.map((type) => (
                      <SelectItem key={type.value} value={type.value}>
                        {type.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormDescription>
                  Categorize this rule to organize your strategy and make it easier to follow
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="description"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Description</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="E.g., Only enter trades when price pulls back to the 20-period moving average after confirming the trend direction. This helps to get better entries with improved risk/reward ratios..."
                    className="min-h-[150px]"
                    {...field}
                    value={field.value || ""}
                  />
                </FormControl>
                <FormDescription>
                  Provide a detailed explanation of the rule, including when and how to apply it, and why it's important
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="priority"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Priority</FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    min={1}
                    placeholder="1"
                    {...field}
                    onChange={(e) => field.onChange(parseInt(e.target.value))}
                  />
                </FormControl>
                <FormDescription>
                  Set the importance level (1 is highest) - higher priority rules are displayed first and should be followed more strictly
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="flex justify-end space-x-2 pt-6 pb-2">
            <Button type="button" variant="outline" onClick={onCancel}>
              Cancel
            </Button>
            <Button type="submit">
              Save Rule
            </Button>
          </div>
        </form>
      </Form>
    );
  };

  // Render rules view
  if (view === "rules") {
    if (isLoadingRules) {
      return (
        <div className="flex justify-center items-center h-40">
          <div className="animate-pulse">Loading strategy rules...</div>
        </div>
      )
    }

    // Handle rule operations
    const handleCreateRule = async (data: RuleFormValues) => {
      try {
        const newRule = await createStrategyRule({
          ...data,
          priority: data.priority || 1,
          strategy_id: strategy.id,
        });

        if (newRule) {
          toast.success("Rule created successfully");
          setIsCreatingRule(false);
          // Refresh rules
          const updatedRules = await getStrategyRules(strategy.id);
          setRules(updatedRules);
        }
      } catch (error) {
        console.error("Error creating rule:", error);
        toast.error("Failed to create rule");
      }
    };

    const handleUpdateRule = async (data: RuleFormValues) => {
      if (!currentRule) return;

      try {
        const updatedRule = await updateStrategyRule(currentRule.id, {
          ...data,
          priority: data.priority || 1,
        });

        if (updatedRule) {
          toast.success("Rule updated successfully");
          setIsEditingRule(false);
          setCurrentRule(null);
          // Refresh rules
          const updatedRules = await getStrategyRules(strategy.id);
          setRules(updatedRules);
        }
      } catch (error) {
        console.error("Error updating rule:", error);
        toast.error("Failed to update rule");
      }
    };

    const handleDeleteRule = async (ruleId: string) => {
      if (!confirm("Are you sure you want to delete this rule?")) return;

      try {
        const success = await deleteStrategyRule(ruleId);

        if (success) {
          toast.success("Rule deleted successfully");
          // Refresh rules
          const updatedRules = await getStrategyRules(strategy.id);
          setRules(updatedRules);
        }
      } catch (error) {
        console.error("Error deleting rule:", error);
        toast.error("Failed to delete rule");
      }
    };

    // Group rules by type
    const entryRules = rules.filter(rule => rule.rule_type === 'entry');
    const exitRules = rules.filter(rule => rule.rule_type === 'exit');
    const stopLossRules = rules.filter(rule => rule.rule_type === 'stop_loss');
    const takeProfitRules = rules.filter(rule => rule.rule_type === 'take_profit');
    const positionSizingRules = rules.filter(rule => rule.rule_type === 'position_sizing');
    const otherRules = rules.filter(rule => rule.rule_type === 'other');

    return (
      <div className="space-y-6">
        {/* Rule Form Dialogs */}
        <Dialog open={isCreatingRule} onOpenChange={setIsCreatingRule}>
          <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto p-6">
            <DialogHeader className="mb-4">
              <DialogTitle>Create New Rule</DialogTitle>
              <DialogDescription>
                Add a new rule for this strategy
              </DialogDescription>
            </DialogHeader>
            <div className="py-2">
              <RuleForm
                rule={null}
                onSave={handleCreateRule}
                onCancel={() => setIsCreatingRule(false)}
              />
            </div>
          </DialogContent>
        </Dialog>

        <Dialog open={isEditingRule} onOpenChange={setIsEditingRule}>
          <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto p-6">
            <DialogHeader className="mb-4">
              <DialogTitle>Edit Rule</DialogTitle>
              <DialogDescription>
                Update this rule's details
              </DialogDescription>
            </DialogHeader>
            <div className="py-2">
              <RuleForm
                rule={currentRule}
                onSave={handleUpdateRule}
                onCancel={() => {
                  setIsEditingRule(false);
                  setCurrentRule(null);
                }}
              />
            </div>
          </DialogContent>
        </Dialog>

        <Card>
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-base">Strategy Rules</CardTitle>
                <CardDescription>
                  Trading rules and guidelines for this strategy
                </CardDescription>
              </div>
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    // Refresh rules data
                    setIsLoadingRules(true);
                    getStrategyRules(strategy.id)
                      .then(data => {
                        setRules(data);
                        console.log('Refreshed rules data:', data);
                      })
                      .catch(error => {
                        console.error("Error refreshing rules:", error);
                        toast.error("Failed to refresh rules");
                      })
                      .finally(() => setIsLoadingRules(false));
                  }}
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Refresh
                </Button>
                <Button
                  size="sm"
                  onClick={() => setIsCreatingRule(true)}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Rule
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {rules.length > 0 ? (
                <>
                  {/* Entry Rules */}
                  {entryRules.length > 0 && (
                    <div>
                      <h3 className="font-medium text-base mb-3">Entry Rules</h3>
                      <div className="space-y-3">
                        {entryRules.map((rule, index) => (
                          <div key={rule.id || index} className="p-3 bg-muted/30 rounded-md">
                            <div className="flex justify-between items-start mb-2">
                              <h4 className="text-sm font-medium">{rule.name}</h4>
                              <div className="flex space-x-2">
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => {
                                    setCurrentRule(rule);
                                    setIsEditingRule(true);
                                  }}
                                >
                                  <Edit className="h-4 w-4" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleDeleteRule(rule.id)}
                                >
                                  <Trash className="h-4 w-4" />
                                </Button>
                              </div>
                            </div>
                            <p className="text-sm text-muted-foreground">{rule.description}</p>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Exit Rules */}
                  {exitRules.length > 0 && (
                    <div>
                      <h3 className="font-medium text-base mb-3">Exit Rules</h3>
                      <div className="space-y-3">
                        {exitRules.map((rule, index) => (
                          <div key={rule.id || index} className="p-3 bg-muted/30 rounded-md">
                            <div className="flex justify-between items-start mb-2">
                              <h4 className="text-sm font-medium">{rule.name}</h4>
                              <div className="flex space-x-2">
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => {
                                    setCurrentRule(rule);
                                    setIsEditingRule(true);
                                  }}
                                >
                                  <Edit className="h-4 w-4" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleDeleteRule(rule.id)}
                                >
                                  <Trash className="h-4 w-4" />
                                </Button>
                              </div>
                            </div>
                            <p className="text-sm text-muted-foreground">{rule.description}</p>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Stop Loss Rules */}
                  {stopLossRules.length > 0 && (
                    <div>
                      <h3 className="font-medium text-base mb-3">Stop Loss Rules</h3>
                      <div className="space-y-3">
                        {stopLossRules.map((rule, index) => (
                          <div key={rule.id || index} className="p-3 bg-muted/30 rounded-md">
                            <div className="flex justify-between items-start mb-2">
                              <h4 className="text-sm font-medium">{rule.name}</h4>
                              <div className="flex space-x-2">
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => {
                                    setCurrentRule(rule);
                                    setIsEditingRule(true);
                                  }}
                                >
                                  <Edit className="h-4 w-4" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleDeleteRule(rule.id)}
                                >
                                  <Trash className="h-4 w-4" />
                                </Button>
                              </div>
                            </div>
                            <p className="text-sm text-muted-foreground">{rule.description}</p>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Take Profit Rules */}
                  {takeProfitRules.length > 0 && (
                    <div>
                      <h3 className="font-medium text-base mb-3">Take Profit Rules</h3>
                      <div className="space-y-3">
                        {takeProfitRules.map((rule, index) => (
                          <div key={rule.id || index} className="p-3 bg-muted/30 rounded-md">
                            <div className="flex justify-between items-start mb-2">
                              <h4 className="text-sm font-medium">{rule.name}</h4>
                              <div className="flex space-x-2">
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => {
                                    setCurrentRule(rule);
                                    setIsEditingRule(true);
                                  }}
                                >
                                  <Edit className="h-4 w-4" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleDeleteRule(rule.id)}
                                >
                                  <Trash className="h-4 w-4" />
                                </Button>
                              </div>
                            </div>
                            <p className="text-sm text-muted-foreground">{rule.description}</p>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Position Sizing Rules */}
                  {positionSizingRules.length > 0 && (
                    <div>
                      <h3 className="font-medium text-base mb-3">Position Sizing Rules</h3>
                      <div className="space-y-3">
                        {positionSizingRules.map((rule, index) => (
                          <div key={rule.id || index} className="p-3 bg-muted/30 rounded-md">
                            <div className="flex justify-between items-start mb-2">
                              <h4 className="text-sm font-medium">{rule.name}</h4>
                              <div className="flex space-x-2">
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => {
                                    setCurrentRule(rule);
                                    setIsEditingRule(true);
                                  }}
                                >
                                  <Edit className="h-4 w-4" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleDeleteRule(rule.id)}
                                >
                                  <Trash className="h-4 w-4" />
                                </Button>
                              </div>
                            </div>
                            <p className="text-sm text-muted-foreground">{rule.description}</p>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Other Rules */}
                  {otherRules.length > 0 && (
                    <div>
                      <h3 className="font-medium text-base mb-3">Other Rules</h3>
                      <div className="space-y-3">
                        {otherRules.map((rule, index) => (
                          <div key={rule.id || index} className="p-3 bg-muted/30 rounded-md">
                            <div className="flex justify-between items-start mb-2">
                              <h4 className="text-sm font-medium">{rule.name}</h4>
                              <div className="flex space-x-2">
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => {
                                    setCurrentRule(rule);
                                    setIsEditingRule(true);
                                  }}
                                >
                                  <Edit className="h-4 w-4" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleDeleteRule(rule.id)}
                                >
                                  <Trash className="h-4 w-4" />
                                </Button>
                              </div>
                            </div>
                            <p className="text-sm text-muted-foreground">{rule.description}</p>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </>
              ) : (
                <div className="text-center py-8">
                  <p className="text-muted-foreground">No rules defined for this strategy yet.</p>
                  <Button
                    variant="outline"
                    size="sm"
                    className="mt-4"
                    onClick={() => setIsCreatingRule(true)}
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Create Your First Rule
                  </Button>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Fallback for any other view
  return (
    <div className="flex justify-center items-center h-40">
      <div className="text-muted-foreground">
        Select an option from the Strategy Actions panel to view details.
      </div>
    </div>
  )
}
