import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { createServerClient } from '@supabase/ssr';
import type { Database } from '@/types/supabase';
import { syncTradeToNotebook } from '@/lib/sync-utils';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { tradeId, action } = body;

    // Get cookies for server-side Supabase client
    const cookieStore = await cookies();
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
          set(_name: string, _value: string, _options: any) {
            // API routes can't set cookies directly
          },
          remove(_name: string, _options: any) {
            // API routes can't remove cookies directly
          }
        },
      }
    );

    // Get authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    if (action === 'test-trade-sync') {
      console.log('Testing trade sync for:', tradeId);

      // Get the trade data
      const { data: trade, error: tradeError } = await supabase
        .from('trades')
        .select('id, notes, tags, screenshots')
        .eq('id', tradeId)
        .eq('user_id', user.id)
        .single();

      if (tradeError || !trade) {
        return NextResponse.json({ error: 'Trade not found' }, { status: 404 });
      }

      console.log('Found trade:', trade);

      // Test the sync function
      await syncTradeToNotebook(supabase, user.id, tradeId, {
        html_content: trade.notes,
        tags: trade.tags,
        screenshots: trade.screenshots
      });

      return NextResponse.json({
        success: true,
        message: 'Sync test completed',
        trade: trade
      });
    }

    if (action === 'manual-notebook-sync') {
      console.log('=== MANUAL NOTEBOOK SYNC START ===');
      console.log('Manual notebook sync for:', tradeId);

      try {
        // Get the notebook entry
        const { data: notebook, error: notebookError } = await supabase
          .from('notebook_entries')
          .select('*')
          .eq('id', tradeId)
          .eq('user_id', user.id)
          .single();

        if (notebookError || !notebook) {
          console.error('Notebook not found:', notebookError);
          return NextResponse.json({ error: 'Notebook entry not found' }, { status: 404 });
        }

        console.log('Found notebook entry:', {
          id: notebook.id,
          title: notebook.title,
          linked_trade_ids: notebook.linked_trade_ids,
          linked_strategy_ids: notebook.linked_strategy_ids,
          screenshots_count: notebook.screenshots ? notebook.screenshots.length : 0
        });

        // Test the sync function
        console.log('=== IMPORTING SYNC FUNCTION ===');
        const { handleNotebookSync } = await import('@/lib/sync-utils');

        console.log('=== CALLING SYNC FUNCTION ===');
        await handleNotebookSync(supabase, user.id, {
          html_content: notebook.html_content,
          tags: notebook.tags,
          screenshots: notebook.screenshots,
          linked_trade_ids: notebook.linked_trade_ids,
          linked_strategy_ids: notebook.linked_strategy_ids,
          category: notebook.category,
          title: notebook.title
        });

        console.log('=== MANUAL NOTEBOOK SYNC COMPLETED ===');
        return NextResponse.json({
          success: true,
          message: 'Manual notebook sync completed',
          notebook: {
            id: notebook.id,
            title: notebook.title,
            linked_trade_ids: notebook.linked_trade_ids,
            linked_strategy_ids: notebook.linked_strategy_ids,
            screenshots_count: notebook.screenshots ? notebook.screenshots.length : 0
          }
        });
      } catch (syncError) {
        console.error('=== MANUAL SYNC ERROR ===', syncError);
        return NextResponse.json({
          error: 'Sync failed',
          details: syncError instanceof Error ? syncError.message : 'Unknown error'
        }, { status: 500 });
      }
    }

    if (action === 'check-functions') {
      // Test finding linked entries using the new approach
      const { data: allEntries, error: fetchError } = await supabase
        .from('notebook_entries')
        .select('id, linked_trade_ids, title')
        .eq('user_id', user.id)
        .not('linked_trade_ids', 'is', null);

      if (fetchError) {
        console.error('Fetch error:', fetchError);
        return NextResponse.json({ error: fetchError.message }, { status: 500 });
      }

      // Filter entries that contain the trade ID
      const linkedEntries = allEntries?.filter((entry: any) =>
        entry.linked_trade_ids && entry.linked_trade_ids.includes(tradeId)
      ) || [];

      return NextResponse.json({
        success: true,
        allEntries: allEntries?.length || 0,
        linkedEntries: linkedEntries,
        tradeId: tradeId
      });
    }

    return NextResponse.json({ error: 'Invalid action' }, { status: 400 });

  } catch (error) {
    console.error('Error in test sync:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
