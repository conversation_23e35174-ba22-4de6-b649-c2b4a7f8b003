-- Add account_id column to strategy_performance table
ALTER TABLE public.strategy_performance
ADD COLUMN account_id UUID REFERENCES public.trading_accounts(id) ON DELETE SET NULL;

-- Create index for better performance
CREATE INDEX IF NOT EXISTS strategy_performance_account_id_idx ON public.strategy_performance(account_id);

-- Update the unique constraint to include account_id
ALTER TABLE public.strategy_performance
DROP CONSTRAINT IF EXISTS strategy_performance_strategy_id_period_start_period_end_key;

ALTER TABLE public.strategy_performance
ADD CONSTRAINT strategy_performance_strategy_id_period_start_period_end_account_id_key
UNIQUE (strategy_id, period_start, period_end, account_id);
