"use client"

import { useRouter } from "next/navigation"
import { useEffect } from "react"
import { getSupabaseBrowser } from "@/lib/supabase"

export function SessionProvider({ children }: { children: React.ReactNode }) {
  const router = useRouter()
  const supabase = getSupabaseBrowser()

  useEffect(() => {
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange((event) => {
      if (event === 'SIGNED_IN' || event === 'SIGNED_OUT') {
        // Only refresh the page to update auth state, but don't redirect
        router.refresh()
      }
    })

    return () => {
      subscription.unsubscribe()
    }
  }, [supabase, router])

  return children
}