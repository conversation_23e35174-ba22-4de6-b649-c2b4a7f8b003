import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

/**
 * Convert plain text to basic HTML for notebook display
 * Preserves paragraph spacing and line breaks
 */
function plainTextToHtml(text: string): string {
  if (!text) return '';

  // Split text into paragraphs (separated by double line breaks)
  const paragraphs = text.split(/\n\s*\n/);
  
  return paragraphs
    .filter(paragraph => paragraph.trim()) // Only filter completely empty paragraphs
    .map(paragraph => {
      // Convert single line breaks within paragraphs to <br> tags
      const htmlContent = paragraph
        .trim()
        .split('\n')
        .map(line => line.trim())
        .filter(line => line) // Remove empty lines within paragraphs
        .join('<br>');
      
      return `<p>${htmlContent}</p>`;
    })
    .join('');
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // Get unprocessed queue items
    const { data: queueItems, error: queueError } = await supabaseClient
      .from('auto_migrate_queue')
      .select('*')
      .eq('processed', false)
      .order('created_at', { ascending: true })

    if (queueError) {
      console.error('Error fetching queue items:', queueError)
      return new Response(
        JSON.stringify({ error: 'Failed to fetch queue items' }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    if (!queueItems || queueItems.length === 0) {
      return new Response(
        JSON.stringify({ message: 'No items to process', processed: 0 }),
        { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    let processedCount = 0
    const errors: string[] = []

    for (const item of queueItems) {
      try {
        console.log(`Processing queue item: ${item.id} for ${item.table_name}:${item.record_id}`)

        // Get the source record
        const { data: sourceRecord, error: sourceError } = await supabaseClient
          .from(item.table_name)
          .select('*')
          .eq('id', item.record_id)
          .single()

        if (sourceError || !sourceRecord) {
          console.error(`Source record not found: ${item.table_name}:${item.record_id}`)
          await markAsProcessed(supabaseClient, item.id, `Source record not found: ${sourceError?.message}`)
          continue
        }

        // Check if notebook entry already exists
        const linkedField = item.table_name === 'trades' ? 'linked_trade_ids' : 'linked_daily_journal_ids'
        const { data: existingEntry } = await supabaseClient
          .from('notebook_entries')
          .select('id')
          .contains(linkedField, [item.record_id])
          .single()

        if (existingEntry) {
          console.log(`Notebook entry already exists for ${item.table_name}:${item.record_id}`)
          await markAsProcessed(supabaseClient, item.id, 'Notebook entry already exists')
          continue
        }

        // Get the appropriate folder
        const folderName = item.table_name === 'trades' ? 'Trade Notes' : 'Daily Journal'
        const { data: folder } = await supabaseClient
          .from('notebook_folders')
          .select('id')
          .eq('name', folderName)
          .eq('user_id', sourceRecord.user_id)
          .single()

        if (!folder) {
          console.error(`Folder '${folderName}' not found for user ${sourceRecord.user_id}`)
          await markAsProcessed(supabaseClient, item.id, `Folder '${folderName}' not found`)
          continue
        }

        // Create notebook entry
        const notebookEntry = await createNotebookEntry(supabaseClient, sourceRecord, item.table_name, folder.id)
        
        if (notebookEntry) {
          console.log(`Created notebook entry: ${notebookEntry.id}`)
          await markAsProcessed(supabaseClient, item.id, null)
          processedCount++
        } else {
          await markAsProcessed(supabaseClient, item.id, 'Failed to create notebook entry')
        }

      } catch (error) {
        console.error(`Error processing queue item ${item.id}:`, error)
        const errorMessage = error instanceof Error ? error.message : 'Unknown error'
        errors.push(`Item ${item.id}: ${errorMessage}`)
        await markAsProcessed(supabaseClient, item.id, errorMessage)
      }
    }

    return new Response(
      JSON.stringify({ 
        message: `Processed ${processedCount} items`, 
        processed: processedCount,
        errors: errors.length > 0 ? errors : undefined
      }),
      { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )

  } catch (error) {
    console.error('Edge function error:', error)
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }
})

async function createNotebookEntry(supabaseClient: any, sourceRecord: any, tableName: string, folderId: string) {
  try {
    let title: string
    let htmlContent: string
    let tags: string[] = []
    let screenshots: string[] = []
    let linkedIds: { [key: string]: string[] } = {}

    if (tableName === 'trades') {
      title = `${sourceRecord.symbol} - ${sourceRecord.type} - ${new Date(sourceRecord.time_open).toLocaleDateString()}`
      htmlContent = plainTextToHtml(sourceRecord.notes || '')
      tags = sourceRecord.tags || []
      screenshots = sourceRecord.screenshots || []
      linkedIds = { linked_trade_ids: [sourceRecord.id] }
    } else if (tableName === 'daily_journal_entries') {
      title = `Daily Journal - ${new Date(sourceRecord.date).toLocaleDateString()}`
      htmlContent = plainTextToHtml(sourceRecord.note || '')
      tags = sourceRecord.tags || []
      screenshots = sourceRecord.screenshots || []
      linkedIds = { linked_daily_journal_ids: [sourceRecord.id] }
    } else {
      throw new Error(`Unsupported table: ${tableName}`)
    }

    const { data: notebookEntry, error } = await supabaseClient
      .from('notebook_entries')
      .insert({
        user_id: sourceRecord.user_id,
        folder_id: folderId,
        title,
        html_content: htmlContent,
        tags,
        screenshots,
        ...linkedIds,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single()

    if (error) {
      console.error('Error creating notebook entry:', error)
      return null
    }

    return notebookEntry
  } catch (error) {
    console.error('Error in createNotebookEntry:', error)
    return null
  }
}

async function markAsProcessed(supabaseClient: any, queueItemId: string, errorMessage: string | null) {
  const { error } = await supabaseClient
    .from('auto_migrate_queue')
    .update({
      processed: true,
      processed_at: new Date().toISOString(),
      error_message: errorMessage
    })
    .eq('id', queueItemId)

  if (error) {
    console.error('Error marking queue item as processed:', error)
  }
}
