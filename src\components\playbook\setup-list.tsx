"use client"

import React, { useState } from "react"
import { toast } from "sonner"
import { Setup, Strategy } from "@/types/playbook"
import { deleteSetup } from "@/lib/playbook-service"
import { formatDistanceToNow } from "date-fns"
import { getCardGradient } from "@/lib/card-utils"

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { Badge } from "@/components/ui/badge"
import { FallbackImage } from "@/components/ui/fallback-image"
import {
  MoreVertical,
  Edit,
  Trash,
  Plus,
  ArrowLeft,
  Image,
  Link
} from "lucide-react"

interface SetupListProps {
  userId: string
  setups: Setup[]
  strategy?: Strategy
  onEdit: (setup: Setup) => void
  onDelete: (setupId: string) => void
  onAdd: () => void
  onBack?: () => void
}

export function SetupList({
  userId,
  setups,
  strategy,
  onEdit,
  onDelete,
  onAdd,
  onBack
}: SetupListProps) {
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [setupToDelete, setSetupToDelete] = useState<Setup | null>(null)
  const [imageDialogOpen, setImageDialogOpen] = useState(false)
  const [selectedImages, setSelectedImages] = useState<string[]>([])

  const handleDeleteClick = (setup: Setup) => {
    setSetupToDelete(setup)
    setDeleteDialogOpen(true)
  }

  const confirmDelete = async () => {
    if (!setupToDelete) return

    try {
      const success = await deleteSetup(userId, setupToDelete.id)
      if (success) {
        toast.success("Setup deleted successfully")
        onDelete(setupToDelete.id)
      } else {
        toast.error("Failed to delete setup")
      }
    } catch (error) {
      console.error("Error deleting setup:", error)
      toast.error("An error occurred while deleting the setup")
    } finally {
      setDeleteDialogOpen(false)
      setSetupToDelete(null)
    }
  }

  const viewImages = (images: string[]) => {
    setSelectedImages(images)
    setImageDialogOpen(true)
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-2">
          {onBack && (
            <Button variant="ghost" size="icon" onClick={onBack}>
              <ArrowLeft className="h-4 w-4" />
            </Button>
          )}
          <h2 className="text-2xl font-bold tracking-tight">
            {strategy ? `Setups for ${strategy.name}` : "Trading Setups"}
          </h2>
        </div>
        <Button onClick={onAdd}>
          <Plus className="mr-2 h-4 w-4" /> Add Setup
        </Button>
      </div>

      {setups.length === 0 ? (
        <Card className="border-dashed">
          <CardContent className="pt-6 text-center">
            <p className="text-muted-foreground mb-4">
              {strategy
                ? `You haven't created any setups for the "${strategy.name}" strategy yet.`
                : "You haven't created any trading setups yet."}
            </p>
            <Button onClick={onAdd} variant="outline">
              <Plus className="mr-2 h-4 w-4" /> Create Your First Setup
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {setups.map((setup) => (
            <Card
              key={setup.id}
              className="flex flex-col overflow-hidden group hover:shadow-md transition-all duration-300 border-t-4 dark:shadow-lg dark:hover:shadow-xl"
              style={{
                background: `var(--mode-light, ${getCardGradient(setup.name).light.background}) var(--mode-dark, ${getCardGradient(setup.name).dark.background})`,
                borderImageSource: `var(--mode-light, ${getCardGradient(setup.name).light.border}) var(--mode-dark, ${getCardGradient(setup.name).dark.border})`,
                borderImageSlice: 1,
                borderTopColor: `var(--mode-light, ${getCardGradient(setup.name).light.accent}) var(--mode-dark, ${getCardGradient(setup.name).dark.accent})`,
                ...({
                  '--mode-light': 'initial',
                  '--mode-dark': 'initial'
                } as React.CSSProperties)
              }}
            >
              <CardHeader className="pb-2 relative">
                <div className="absolute top-0 right-0 p-2">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon" className="h-8 w-8 rounded-full opacity-70 hover:opacity-100 hover:bg-background/80">
                        <MoreVertical className="h-4 w-4" />
                        <span className="sr-only">Open menu</span>
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="w-48">
                      {setup.image_urls.length > 0 && (
                        <DropdownMenuItem onClick={() => viewImages(setup.image_urls)} className="cursor-pointer">
                          <Image className="mr-2 h-4 w-4" />
                          View Images
                        </DropdownMenuItem>
                      )}
                      <DropdownMenuItem onClick={() => onEdit(setup)} className="cursor-pointer">
                        <Edit className="mr-2 h-4 w-4" />
                        Edit
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem
                        onClick={() => handleDeleteClick(setup)}
                        className="text-destructive focus:text-destructive cursor-pointer"
                      >
                        <Trash className="mr-2 h-4 w-4" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>

                <div className="space-y-1">
                  <div className="flex items-center gap-2">
                    <CardTitle className="text-lg font-bold">{setup.name}</CardTitle>
                  </div>
                  {!strategy && setup.strategy && (
                    <CardDescription className="line-clamp-2 text-sm opacity-90">
                      Strategy: {setup.strategy.name}
                    </CardDescription>
                  )}
                </div>
              </CardHeader>
              <CardContent className="pb-2 flex-grow">
                <div className="space-y-4">
                  {setup.description && (
                    <div className={`${getCardGradient(setup.name).light.cardBg} dark:${getCardGradient(setup.name).dark.cardBg} rounded-lg p-3 backdrop-blur-sm`}>
                      <h4 className="text-sm font-medium mb-2 flex items-center">
                        <span className="inline-block w-2 h-2 rounded-full mr-1 dark:shadow-glow"
                          style={{
                            backgroundColor: `var(--mode-light, ${getCardGradient(setup.name).light.accent}) var(--mode-dark, ${getCardGradient(setup.name).dark.accent})`,
                            ...({
                              '--mode-light': 'initial',
                              '--mode-dark': 'initial'
                            } as React.CSSProperties)
                          }}>
                        </span>
                        Description
                      </h4>
                      <p className="text-sm text-muted-foreground line-clamp-3">
                        {setup.description}
                      </p>
                    </div>
                  )}

                  {setup.visual_cues && (
                    <div className={`${getCardGradient(setup.name).light.cardBg} dark:${getCardGradient(setup.name).dark.cardBg} rounded-lg p-3 backdrop-blur-sm`}>
                      <h4 className="text-sm font-medium mb-2 flex items-center">
                        <span className="inline-block w-2 h-2 rounded-full mr-1 dark:shadow-glow"
                          style={{
                            backgroundColor: `var(--mode-light, ${getCardGradient(setup.name).light.accent}) var(--mode-dark, ${getCardGradient(setup.name).dark.accent})`,
                            ...({
                              '--mode-light': 'initial',
                              '--mode-dark': 'initial'
                            } as React.CSSProperties)
                          }}>
                        </span>
                        Visual Cues
                      </h4>
                      <p className="text-sm text-muted-foreground line-clamp-3">
                        {setup.visual_cues}
                      </p>
                    </div>
                  )}

                  {setup.confirmation_criteria && (
                    <div className={`${getCardGradient(setup.name).light.cardBg} dark:${getCardGradient(setup.name).dark.cardBg} rounded-lg p-3 backdrop-blur-sm`}>
                      <h4 className="text-sm font-medium mb-2 flex items-center">
                        <span className="inline-block w-2 h-2 rounded-full mr-1 dark:shadow-glow"
                          style={{
                            backgroundColor: `var(--mode-light, ${getCardGradient(setup.name).light.accent}) var(--mode-dark, ${getCardGradient(setup.name).dark.accent})`,
                            ...({
                              '--mode-light': 'initial',
                              '--mode-dark': 'initial'
                            } as React.CSSProperties)
                          }}>
                        </span>
                        Confirmation Criteria
                      </h4>
                      <p className="text-sm text-muted-foreground line-clamp-3">
                        {setup.confirmation_criteria}
                      </p>
                    </div>
                  )}

                  {setup.image_urls.length > 0 && (
                    <div className={`${getCardGradient(setup.name).light.cardBg} dark:${getCardGradient(setup.name).dark.cardBg} rounded-lg p-3 backdrop-blur-sm`}>
                      <h4 className="text-sm font-medium mb-2 flex items-center">
                        <span className="inline-block w-2 h-2 rounded-full mr-1 dark:shadow-glow"
                          style={{
                            backgroundColor: `var(--mode-light, ${getCardGradient(setup.name).light.accent}) var(--mode-dark, ${getCardGradient(setup.name).dark.accent})`,
                            ...({
                              '--mode-light': 'initial',
                              '--mode-dark': 'initial'
                            } as React.CSSProperties)
                          }}>
                        </span>
                        Example Images
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-5 w-5 ml-1 opacity-70 hover:opacity-100"
                          onClick={() => viewImages(setup.image_urls)}
                        >
                          <Image className="h-3 w-3" />
                        </Button>
                      </h4>
                      <div className="flex items-center gap-1">
                        <Badge
                          variant="outline"
                          className="text-xs bg-background/60 hover:bg-background/80 transition-colors"
                          style={{
                            borderColor: `var(--mode-light, ${getCardGradient(setup.name).light.accent}) var(--mode-dark, ${getCardGradient(setup.name).dark.accent})`,
                            ...({
                              '--mode-light': 'initial',
                              '--mode-dark': 'initial'
                            } as React.CSSProperties)
                          }}
                        >
                          {setup.image_urls.length} image{setup.image_urls.length !== 1 ? 's' : ''}
                        </Badge>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
              <CardFooter className={`pt-2 text-xs text-muted-foreground border-t border-border/30 mt-auto ${getCardGradient(setup.name).light.footerBg} dark:${getCardGradient(setup.name).dark.footerBg} dark:border-border/10`}>
                <div className="flex items-center">
                  <div
                    className="w-2 h-2 rounded-full mr-2 animate-pulse dark:shadow-glow"
                    style={{
                      backgroundColor: `var(--mode-light, ${getCardGradient(setup.name).light.accent}) var(--mode-dark, ${getCardGradient(setup.name).dark.accent})`,
                      ...({
                        '--mode-light': 'initial',
                        '--mode-dark': 'initial'
                      } as React.CSSProperties)
                    }}>
                  </div>
                  Updated {formatDistanceToNow(new Date(setup.updated_at), { addSuffix: true })}
                </div>
              </CardFooter>
            </Card>
          ))}
        </div>
      )}

      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete the setup &quot;{setupToDelete?.name}&quot;.
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDelete} className="bg-destructive text-destructive-foreground">
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      <AlertDialog open={imageDialogOpen} onOpenChange={setImageDialogOpen}>
        <AlertDialogContent className="max-w-3xl">
          <AlertDialogHeader>
            <AlertDialogTitle>Setup Images</AlertDialogTitle>
          </AlertDialogHeader>
          <div className="grid grid-cols-1 gap-4 py-4">
            {selectedImages.map((url, index) => (
              <div key={index} className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Image {index + 1}</span>
                  <a
                    href={url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-sm text-blue-500 hover:underline flex items-center"
                  >
                    <Link className="h-3 w-3 mr-1" />
                    Open in new tab
                  </a>
                </div>
                <div className="relative rounded-md overflow-hidden border">
                  <FallbackImage
                    src={url}
                    alt={`Setup example ${index + 1}`}
                    className="object-contain w-full max-h-[400px]"
                    fallbackSrc="https://placehold.co/600x400?text=Image+Not+Found"
                  />
                </div>
              </div>
            ))}
          </div>
          <AlertDialogFooter>
            <AlertDialogCancel>Close</AlertDialogCancel>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
