"use client"

import { useState, useEffect } from "react"
import { usePathname, useSearchParams } from "next/navigation"
import { useAccount } from "@/contexts/account-context"

export function NavigationLoading() {
  const pathname = usePathname()
  const searchParams = useSearchParams()
  const [isLoading, setIsLoading] = useState(false)
  const { isInitializing } = useAccount()

  // Track navigation changes using custom events
  useEffect(() => {
    let navigationTimeout: NodeJS.Timeout | null = null
    let loadingTimeout: NodeJS.Timeout | null = null

    // Create custom events for navigation
    const startNavigation = () => {
      // Clear any existing timeouts
      if (navigationTimeout) {
        clearTimeout(navigationTimeout)
        navigationTimeout = null
      }

      if (loadingTimeout) {
        clearTimeout(loadingTimeout)
        loadingTimeout = null
      }

      // Only show loading indicator if navigation takes longer than 100ms
      loadingTimeout = setTimeout(() => {
        setIsLoading(true)
        loadingTimeout = null
      }, 50) // Very short delay before showing loading indicator
    }

    const completeNavigation = () => {
      // Clear the loading timeout if navigation completed quickly
      if (loadingTimeout) {
        clearTimeout(loadingTimeout)
        loadingTimeout = null
      }

      // Hide loading indicator immediately
      setIsLoading(false)
    }

    // Add event listeners for custom navigation events
    window.addEventListener('navigationStart', startNavigation)
    window.addEventListener('navigationComplete', completeNavigation)

    // Cleanup
    return () => {
      window.removeEventListener('navigationStart', startNavigation)
      window.removeEventListener('navigationComplete', completeNavigation)

      if (navigationTimeout) {
        clearTimeout(navigationTimeout)
      }

      if (loadingTimeout) {
        clearTimeout(loadingTimeout)
      }
    }
  }, [])

  // Reset loading state immediately when pathname or search params change
  useEffect(() => {
    // This ensures the loading state is reset when the navigation is complete
    setIsLoading(false)
  }, [pathname, searchParams])

  // Show loading indicator during navigation or initialization
  if (!isLoading && !isInitializing) return null

  return (
    <div className="fixed top-0 left-0 right-0 z-50 h-1 overflow-hidden">
      <div className="h-full bg-primary animate-progress-indeterminate"></div>
    </div>
  )
}
