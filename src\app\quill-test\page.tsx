"use client"

import React, { useState } from 'react';
import QuillEditor from '@/components/quill-editor/QuillEditor';

export default function QuillTestPage() {
  const [content, setContent] = useState('');

  const handleEditorChange = (newContent: { html: string }) => {
    setContent(newContent.html);
  };

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">Quill Editor Test</h1>
      
      <div className="mb-8 h-[500px]">
        <QuillEditor 
          initialContent="<h2>Welcome to the Quill Editor!</h2><p>This is a test of the Quill rich text editor. Try typing here to see how it works.</p>"
          onChange={handleEditorChange}
          autofocus={true}
        />
      </div>
      
      <div className="mt-8">
        <h2 className="text-xl font-semibold mb-2">Editor Output:</h2>
        <div className="p-4 border rounded bg-gray-50 dark:bg-gray-800">
          <pre className="whitespace-pre-wrap">{content}</pre>
        </div>
      </div>
    </div>
  );
}
