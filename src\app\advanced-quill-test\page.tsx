"use client"

import React, { useState } from 'react';
import AdvancedQuillEditor from '@/components/quill-editor/AdvancedQuillEditor';

export default function AdvancedQuillTestPage() {
  const [content, setContent] = useState({
    html: '',
    text: ''
  });

  const handleEditorChange = (newContent: { html: string; text: string }) => {
    setContent(newContent);
  };

  const initialContent = `
    <h1>Advanced Quill Editor Demo</h1>
    <p>This is a demonstration of the advanced Quill editor with a fixed toolbar.</p>
    <h2>Features:</h2>
    <ul>
      <li>Fixed toolbar that stays at the top while scrolling</li>
      <li>Rich text formatting options</li>
      <li>Support for lists, blockquotes, and code blocks</li>
      <li>Image embedding</li>
      <li>Text alignment options</li>
    </ul>
    <p>Try scrolling down to see how the toolbar remains fixed at the top.</p>
    <h2>Formatting Examples:</h2>
    <p><strong>Bold text</strong>, <em>italic text</em>, <u>underlined text</u>, <s>strikethrough text</s></p>
    <blockquote>This is a blockquote. It can be used to highlight important information or quotes.</blockquote>
    <pre class="ql-syntax">// This is a code block
function helloWorld() {
  console.log("Hello, world!");
}
</pre>
    <p>You can also use different text colors and backgrounds.</p>
    <p>Try typing below this line to test the editor:</p>
  `;

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">Advanced Quill Editor Test</h1>
      
      <div className="mb-8 h-[600px]">
        <AdvancedQuillEditor 
          initialContent={initialContent}
          onChange={handleEditorChange}
          autofocus={true}
          placeholder="Start typing here..."
          className="h-full"
        />
      </div>
      
      <div className="mt-8">
        <h2 className="text-xl font-semibold mb-2">Editor Output:</h2>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <h3 className="text-lg font-medium mb-2">HTML:</h3>
            <div className="p-4 border rounded bg-gray-50 dark:bg-gray-800 max-h-[300px] overflow-auto">
              <pre className="whitespace-pre-wrap text-sm">{content.html}</pre>
            </div>
          </div>
          <div>
            <h3 className="text-lg font-medium mb-2">Plain Text:</h3>
            <div className="p-4 border rounded bg-gray-50 dark:bg-gray-800 max-h-[300px] overflow-auto">
              <pre className="whitespace-pre-wrap text-sm">{content.text}</pre>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
