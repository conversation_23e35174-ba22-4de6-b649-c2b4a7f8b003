"use client"

import { useQuery, useQueryClient } from "@tanstack/react-query"
import { format } from "date-fns"
import { useEffect } from "react"

// Define types for calendar data
export interface DayMetrics {
  totalProfit: number;
  tradeCount: number;
  winCount: number;
  lossCount: number;
  winRate: string;
  isProfitable: boolean;
}

export interface CalendarData {
  currentMonth: string;
  daysToDisplay: string[];
  tradesByDate: Record<string, any[]>;
  dayMetrics: Record<string, DayMetrics | null>;
  weeklyMetrics: (DayMetrics | null)[];
  tradingNotes: Record<string, string>;
  datesWithJournalEntries: string[];
  weeks: string[][];
}

// Function to fetch calendar data from the API
export async function fetchCalendarData(
  year: number,
  month: number,
  accountId: string | null,
  view: string = 'month',
  filters?: {
    dateRange?: { from?: Date; to?: Date };
    strategyId?: string | null;
    tradeType?: string;
    symbols?: string[];
    showWeekends?: boolean;
  }
): Promise<CalendarData | null> {
  if (!accountId) return null

  // Build query parameters
  const params = new URLSearchParams({
    year: year.toString(),
    month: month.toString(),
    accountId: accountId,
    view: view,
    _t: new Date().getTime().toString() // Cache-busting timestamp
  });

  // Add filter parameters if provided
  if (filters) {
    if (filters.dateRange?.from) {
      params.append('filterDateFrom', filters.dateRange.from.toISOString());
    }
    if (filters.dateRange?.to) {
      params.append('filterDateTo', filters.dateRange.to.toISOString());
    }
    if (filters.strategyId) {
      params.append('strategyId', filters.strategyId);
    }
    if (filters.tradeType && filters.tradeType !== 'all') {
      params.append('tradeType', filters.tradeType);
    }
    if (filters.symbols && filters.symbols.length > 0) {
      params.append('symbols', filters.symbols.join(','));
    }
    if (filters.showWeekends !== undefined) {
      params.append('showWeekends', filters.showWeekends.toString());
    }
  }

  const response = await fetch(`/api/calendar-data?${params.toString()}`, {
    next: { tags: ['calendar-data'] },
    cache: 'no-store' // Ensure we don't use cached responses
  })

  if (!response.ok) {
    throw new Error('Failed to fetch calendar data')
  }

  return response.json()
}

// Hook to fetch calendar data with account change detection
export function useCalendarData(
  year: number,
  month: number,
  accountId: string | null,
  view: string = 'month',
  initialData: CalendarData | null = null,
  filters?: {
    dateRange?: { from?: Date; to?: Date };
    strategyId?: string | null;
    tradeType?: string;
    symbols?: string[];
    showWeekends?: boolean;
  }
) {
  const queryClient = useQueryClient()

  // Effect to invalidate cache when account changes or filters change
  useEffect(() => {
    if (accountId) {
      // Force refetch when account or filters change by invalidating the query
      queryClient.invalidateQueries({ queryKey: ['calendar-data', year, month, accountId, view, filters] })
    }
  }, [accountId, year, month, view, filters, queryClient])

  const query = useQuery({
    queryKey: ['calendar-data', year, month, accountId, view, filters],
    queryFn: () => fetchCalendarData(year, month, accountId, view, filters),
    enabled: !!accountId,
    initialData: accountId ? undefined : initialData, // Only use initialData if no accountId
    staleTime: 0, // Always consider data stale to force refetch on account change
    refetchOnMount: true, // Always refetch when component mounts
    // Add error handling to prevent crashes if the API returns an error
    retry: 1, // Only retry once to avoid excessive retries on schema issues
  })

  // Handle errors using the query result
  if (query.error) {
    console.error('Error fetching calendar data:', query.error)
    // We could add a toast notification here if needed
  }

  return query
}

// Helper function to get day trades from the calendar data
export function getDayTrades(calendarData: CalendarData | null, date: Date): any[] {
  if (!calendarData) return []

  const dateKey = format(date, 'yyyy-MM-dd')
  return calendarData.tradesByDate[dateKey] || []
}

// Helper function to get day metrics from the calendar data
export function getDayMetrics(calendarData: CalendarData | null, date: Date): DayMetrics | null {
  if (!calendarData) return null

  const dateKey = format(date, 'yyyy-MM-dd')
  return calendarData.dayMetrics[dateKey] || null
}

// Helper function to check if a date has notes
export function hasNotes(calendarData: CalendarData | null, date: Date): boolean {
  if (!calendarData) return false

  const dateKey = format(date, 'yyyy-MM-dd')
  const hasTradingNote = !!calendarData.tradingNotes[dateKey] && calendarData.tradingNotes[dateKey].trim() !== ""
  const hasJournal = calendarData.datesWithJournalEntries.includes(dateKey)

  return hasTradingNote || hasJournal
}

// Helper function to get a note for a date
export function getNote(calendarData: CalendarData | null, date: Date): string {
  if (!calendarData) return ""

  const dateKey = format(date, 'yyyy-MM-dd')
  return calendarData.tradingNotes[dateKey] || ""
}

// Helper function to get weekly metrics from the calendar data
export function getWeeklyMetrics(calendarData: CalendarData | null, weekIndex: number): DayMetrics | null {
  if (!calendarData || !calendarData.weeklyMetrics) return null

  return calendarData.weeklyMetrics[weekIndex] || null
}
