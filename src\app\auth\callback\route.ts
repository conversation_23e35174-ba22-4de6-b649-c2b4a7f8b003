import { NextResponse } from "next/server"

export const dynamic = 'force-dynamic'

export async function GET(request: Request) {
  try {
    // After the user is redirected back from Supabase auth, redirect to auth page
    // The user must explicitly sign in after registration
    return NextResponse.redirect(new URL('/auth?registration=success', request.url))
  } catch (error) {
    console.error('Error in auth callback:', error)
    return NextResponse.redirect(new URL('/auth', request.url))
  }
}