import { NextResponse } from 'next/server';
import { getSupabaseServer } from '@/lib/supabase-server';
import { getTradingSummary, getUserAccounts } from '@/lib/data/account-data';
import { parseSupabaseError, AuthError } from '@/lib/error-handler';

// Define cache configuration
export const revalidate = 0; // Disable caching to ensure fresh data on each request

// GET handler to fetch dashboard statistics
export async function GET(request: Request) {
  try {
    // Get URL parameters
    const url = new URL(request.url);
    const accountId = url.searchParams.get('accountId');

    // Get authenticated user using our standardized client
    const supabase = await getSupabaseServer();
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      throw new AuthError('Authentication required');
    }

    const userId = user.id;

    // If accountId is explicitly null (user has unselected all accounts), return null
    if (accountId === 'null') {
      console.log('No account selected, returning null for summary');
      return NextResponse.json(null);
    }

    // If an account ID is specified, get the summary for that account
    if (accountId) {
      console.log(`Fetching summary for specific account: ${accountId}`);
      const summary = await getTradingSummary(accountId, { useServer: true });

      console.log('Trading summary retrieved successfully');
      return NextResponse.json(summary);
    }

    // Otherwise, get the summary for the user's most recently updated account
    // First, get the user's accounts
    const accounts = await getUserAccounts(userId, { useServer: true });

    if (!accounts || accounts.length === 0) {
      console.log('No trading accounts found for user');
      return NextResponse.json(null);
    }

    const defaultAccountId = accounts[0].id;
    console.log(`Found default account ID: ${defaultAccountId}`);

    // Now get the summary for this account
    const summary = await getTradingSummary(defaultAccountId, { useServer: true });

    console.log('Trading summary retrieved successfully');
    return NextResponse.json(summary);
  } catch (error) {
    // Parse the error to get a standardized AppError
    const parsedError = parseSupabaseError(error);

    console.error(`Error in dashboard-stats API:`, parsedError);

    // Return an appropriate error response
    return NextResponse.json(
      { error: parsedError.message },
      { status: parsedError.status }
    );
  }
}
