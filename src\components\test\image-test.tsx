'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';
import { uploadImageToStorage } from '@/lib/image-service';
import { RefreshCw, Upload } from 'lucide-react';

export function ImageTest() {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadedImageUrl, setUploadedImageUrl] = useState<string | null>(null);
  const [testResults, setTestResults] = useState<string[]>([]);

  const addTestResult = (result: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${result}`]);
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      addTestResult(`File selected: ${file.name} (${file.type}, ${file.size} bytes)`);
    }
  };

  const handleUpload = async () => {
    if (!selectedFile) {
      toast.error('Please select a file first');
      return;
    }

    setIsUploading(true);
    addTestResult('Starting upload...');
    
    try {
      const imageUrl = await uploadImageToStorage(selectedFile);
      setUploadedImageUrl(imageUrl);
      addTestResult(`Upload successful: ${imageUrl}`);
      toast.success('Image uploaded successfully');
      
      // Test if the image is immediately accessible
      testImageAccess(imageUrl);
    } catch (error) {
      console.error('Error uploading image:', error);
      addTestResult(`Upload failed: ${error}`);
      toast.error('Failed to upload image');
    } finally {
      setIsUploading(false);
    }
  };

  const testImageAccess = async (url: string) => {
    addTestResult('Testing image access...');
    
    // Test 1: Try to fetch the image
    try {
      const response = await fetch(url, { method: 'HEAD' });
      addTestResult(`Fetch test: ${response.status} ${response.statusText}`);
    } catch (error) {
      addTestResult(`Fetch test failed: ${error}`);
    }

    // Test 2: Try to load the image
    const img = new Image();
    img.onload = () => {
      addTestResult('Image load test: SUCCESS');
    };
    img.onerror = (error) => {
      addTestResult(`Image load test: FAILED - ${error}`);
    };
    img.src = url;

    // Test 3: Try with cache busting
    setTimeout(() => {
      const cacheBustUrl = `${url}?t=${Date.now()}`;
      const img2 = new Image();
      img2.onload = () => {
        addTestResult('Cache-bust image load test: SUCCESS');
      };
      img2.onerror = (error) => {
        addTestResult(`Cache-bust image load test: FAILED - ${error}`);
      };
      img2.src = cacheBustUrl;
    }, 2000);
  };

  const clearResults = () => {
    setTestResults([]);
    setUploadedImageUrl(null);
    setSelectedFile(null);
  };

  return (
    <div className="max-w-2xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Image Upload Test</CardTitle>
          <CardDescription>
            Test image upload and access to debug screenshot issues
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="test-file">Select Image File</Label>
            <Input
              id="test-file"
              type="file"
              accept="image/*"
              onChange={handleFileChange}
            />
          </div>

          <div className="flex gap-2">
            <Button
              onClick={handleUpload}
              disabled={!selectedFile || isUploading}
              className="flex-1"
            >
              {isUploading ? (
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Upload className="h-4 w-4 mr-2" />
              )}
              {isUploading ? "Uploading..." : "Upload & Test"}
            </Button>
            
            <Button variant="outline" onClick={clearResults}>
              Clear
            </Button>
          </div>

          {uploadedImageUrl && (
            <div className="space-y-2">
              <Label>Uploaded Image</Label>
              <div className="border rounded-lg p-4">
                <img
                  src={uploadedImageUrl}
                  alt="Uploaded test image"
                  className="max-w-full h-auto max-h-64 object-contain"
                  onLoad={() => addTestResult('Image displayed successfully in UI')}
                  onError={() => addTestResult('Image failed to display in UI')}
                />
              </div>
              <p className="text-sm text-muted-foreground break-all">
                URL: {uploadedImageUrl}
              </p>
            </div>
          )}

          {testResults.length > 0 && (
            <div className="space-y-2">
              <Label>Test Results</Label>
              <div className="bg-muted rounded-lg p-4 max-h-64 overflow-y-auto">
                {testResults.map((result, index) => (
                  <div key={index} className="text-sm font-mono">
                    {result}
                  </div>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
