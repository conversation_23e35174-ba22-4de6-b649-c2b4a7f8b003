"use client"

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card"
import { type ProcessedData } from "@/lib/excel-processor"
import { ChartTooltip } from "@/components/ui/chart-tooltip"
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from "recharts"

interface TradePerformanceChartProps {
  trades: ProcessedData["trades"]
  initialBalance?: number
}

// Calculate optimal Y-axis domain to show the equity curve from mid-point
function calculateYAxisDomain(data: any[]): [number, number] {
  if (!data || data.length === 0) return [0, 1000]

  // Get min and max balance values
  const balances = data.map(item => item.balance)
  const minBalance = Math.min(...balances)
  const maxBalance = Math.max(...balances)

  // Calculate the range
  const range = maxBalance - minBalance

  // If range is very small, add some padding
  if (range < 100) {
    return [Math.max(0, minBalance - 50), maxBalance + 50]
  }

  // Calculate a lower bound that's below the minimum to show the curve from mid-point
  // We want to show about 40% of the chart below the starting point
  const lowerBound = Math.max(0, minBalance - (range * 0.4))

  // Calculate an upper bound with some padding
  const upperBound = maxBalance + (range * 0.1)

  return [lowerBound, upperBound]
}

export function TradePerformanceChart({ trades, initialBalance = 0 }: TradePerformanceChartProps) {
  // Calculate cumulative profit for each trade
  const sortedTrades = [...trades].sort(
    (a, b) => new Date(a.time_close).getTime() - new Date(b.time_close).getTime()
  )

  // Start with initial balance point
  const chartData = sortedTrades.length > 0 ? [
    {
      date: new Date(sortedTrades[0].time_close).toLocaleDateString(),
      profit: 0,
      balance: initialBalance,
    }
  ] : []

  // Add all trades
  sortedTrades.reduce((acc, trade) => {
    const lastBalance = acc[acc.length - 1].balance
    acc.push({
      date: new Date(trade.time_close).toLocaleDateString(),
      profit: trade.profit,
      balance: lastBalance + trade.profit,
    })
    return acc
  }, chartData)

  return (
    <ResponsiveContainer width="100%" height="100%">
      <LineChart
        data={chartData}
        margin={{
          top: 10,
          right: 10,
          left: 20,
          bottom: 40, // Increased to accommodate slanted labels
        }}
      >
        <CartesianGrid
          strokeDasharray="3 3"
          vertical={false}
          stroke="hsl(var(--border))"
          opacity={0.4}
        />
        <XAxis
          dataKey="date"
          tick={{
            fill: 'hsl(var(--muted-foreground))',
            fontSize: 11,
            opacity: 0.7,
            textAnchor: 'end'
          }}
          stroke="hsl(var(--border))"
          strokeOpacity={0.5}
          interval="preserveStartEnd"
          height={60}
          axisLine={{ stroke: 'hsl(var(--border))', opacity: 0.5 }}
        />
        <YAxis
          tick={{ fill: 'hsl(var(--muted-foreground))', fontSize: 11, opacity: 0.7 }}
          stroke="hsl(var(--border))"
          strokeOpacity={0.5}
          axisLine={{ stroke: 'hsl(var(--border))', opacity: 0.5 }}
          tickFormatter={(value) => `$${value.toFixed(2)}`}
          domain={calculateYAxisDomain(chartData)}
          interval={0}
          tickCount={7}
        />
        <Tooltip
          content={({ active, payload }) => (
            <ChartTooltip active={active} payload={payload}
              formatter={(value) => `$${value.toFixed(2)}`}
            />
          )}
        />
        <Line
          type="monotone"
          dataKey="balance"
          stroke="#2563eb"
          strokeWidth={2}
          dot={false}
          activeDot={{ r: 6, strokeWidth: 0 }}
          isAnimationActive={true}
          animationDuration={1000}
        />
      </LineChart>
    </ResponsiveContainer>
  )
}