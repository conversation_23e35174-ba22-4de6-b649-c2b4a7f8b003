"use client"

import { useEffect } from "react"
import { usePathname, useSearchPara<PERSON>, useRouter } from "next/navigation"

export function NavigationEvents() {
  const pathname = usePathname()
  const searchParams = useSearchParams()
  const router = useRouter()

  // Handle router navigation events
  useEffect(() => {
    // Dispatch custom events for navigation
    const dispatchStartEvent = () => {
      window.dispatchEvent(new Event('navigationStart'))
    }

    const dispatchCompleteEvent = () => {
      // Dispatch immediately for faster response
      window.dispatchEvent(new Event('navigationComplete'))
    }

    // Patch the router's push method to dispatch events
    const originalPush = router.push
    const originalReplace = router.replace
    const originalBack = router.back
    const originalForward = router.forward

    // @ts-ignore - Monkey patch router methods
    router.push = (...args: any[]) => {
      dispatchStartEvent()
      // @ts-ignore
      return originalPush.apply(router, args)
    }

    // @ts-ignore - Monkey patch router methods
    router.replace = (...args: any[]) => {
      dispatchStartEvent()
      // @ts-ignore
      return originalReplace.apply(router, args)
    }

    // @ts-ignore - Monkey patch router methods
    router.back = () => {
      dispatchStartEvent()
      return originalBack.apply(router)
    }

    // @ts-ignore - Monkey patch router methods
    router.forward = () => {
      dispatchStartEvent()
      return originalForward.apply(router)
    }

    // Add click event listeners to all internal links
    const handleLinkClick = (event: MouseEvent) => {
      const target = event.target as HTMLElement
      const link = target.closest('a')

      if (link && link.href && link.href.startsWith(window.location.origin)) {
        dispatchStartEvent()
      }
    }

    document.addEventListener('click', handleLinkClick)

    // Cleanup
    return () => {
      // @ts-ignore - Restore original methods
      router.push = originalPush
      // @ts-ignore
      router.replace = originalReplace
      // @ts-ignore
      router.back = originalBack
      // @ts-ignore
      router.forward = originalForward

      document.removeEventListener('click', handleLinkClick)
    }
  }, [router])

  // Dispatch complete event when pathname or search params change
  useEffect(() => {
    window.dispatchEvent(new Event('navigationComplete'))

    // Dispatch a custom event when navigating to the dashboard
    if (pathname === '/dashboard') {
      window.dispatchEvent(new Event('dashboardNavigated'))
    }
  }, [pathname, searchParams])

  return null
}
