/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  compiler: {
    removeConsole: process.env.NODE_ENV === "production",
  },
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'bhwezablsdekzssfyshx.supabase.co',
        pathname: '/storage/v1/object/public/**',
      },
      {
        protocol: 'https',
        hostname: 'bhwezablsdekzssfyshx.supabase.co',
        pathname: '/storage/v1/object/**',
      },
    ],
    // Increase image size limit and cache duration
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
    formats: ['image/webp'],
  },
  // Optimize CSS loading and fix preload warnings
  experimental: {
    optimizeCss: true,
    // Disable automatic CSS preloading to fix the warning
    optimisticClientCache: false
  },
  // Configure webpack to handle CSS preloading properly
  webpack: (config, { dev, isServer }) => {
    if (!dev && !isServer) {
      // Optimize CSS loading in production
      config.optimization.splitChunks.cacheGroups.styles = {
        name: 'styles',
        test: /\.(css|scss|sass)$/,
        chunks: 'all',
        enforce: true,
      };
    }
    return config;
  },
};

module.exports = nextConfig;