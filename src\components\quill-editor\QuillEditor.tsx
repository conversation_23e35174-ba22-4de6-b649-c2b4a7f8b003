"use client"

import React, { useState, useEffect } from 'react';
import dynamic from 'next/dynamic';
import 'react-quill/dist/quill.snow.css'; // Import Quill styles
import './QuillEditor.css'; // We'll create this file for custom styling

// Import ReactQuill dynamically to avoid SSR issues
const ReactQuill = dynamic(() => import('react-quill'), {
  ssr: false,
  loading: () => <div className="h-full w-full bg-gray-100 animate-pulse rounded-md" />
});

interface QuillEditorProps {
  initialContent?: string;
  onChange?: (content: { html: string }) => void;
  editable?: boolean;
  autofocus?: boolean;
}

const QuillEditor: React.FC<QuillEditorProps> = ({
  initialContent = '',
  onChange,
  editable = true,
  autofocus = false,
}) => {
  const [content, setContent] = useState(initialContent);

  // Define the modules for the Quill editor
  const modules = {
    toolbar: [
      [{ 'header': [1, 2, 3, 4, false] }],
      ['bold', 'italic', 'underline', 'strike'],
      [{ 'list': 'ordered' }, { 'list': 'bullet' }, { 'list': 'check' }],
      [{ 'align': [] }],
      ['link', 'image'],
      ['clean'],
      ['code-block', 'blockquote'],
      [{ 'color': [] }, { 'background': [] }],
      ['superscript', 'subscript'],
    ],
    clipboard: {
      // Prevent unwanted paste behavior
      matchVisual: false,
    },
    history: {
      delay: 1000,
      maxStack: 100,
      userOnly: true
    }
  };

  // Define the formats we want to support
  const formats = [
    'header',
    'bold', 'italic', 'underline', 'strike',
    'list', 'bullet', 'check',
    'align',
    'link', 'image',
    'code-block', 'blockquote',
    'color', 'background',
    'superscript', 'subscript',
  ];

  // Handle content changes
  const handleChange = (value: string) => {
    setContent(value);
    if (onChange) {
      onChange({ html: value });
    }
  };

  // Focus the editor on mount if autofocus is true
  useEffect(() => {
    if (autofocus) {
      // We can't reliably use refs in React 18, so we'll use a different approach
      setTimeout(() => {
        const editorElement = document.querySelector('.quill-editor-wrapper .ql-editor');
        if (editorElement) {
          (editorElement as HTMLElement).focus();
        }
      }, 100);
    }
  }, [autofocus]);

  return (
    <div className="quill-editor-wrapper">
      <ReactQuill
        theme="snow"
        value={content}
        onChange={handleChange}
        modules={modules}
        formats={formats}
        readOnly={!editable}
        placeholder="Start typing..."
      />
    </div>
  );
};

export default QuillEditor;
