"use client"

import React, { useState, useEffect } from "react"
import { Dialog, DialogTitle } from "@/components/ui/dialog"
import { CustomDialogContent } from "@/components/ui/custom-dialog-content"
import { But<PERSON> } from "@/components/ui/button"
import { X, ChevronLeft, ChevronRight, ZoomIn, ZoomOut, RotateCw, RefreshCw } from "lucide-react"
import { cn } from "@/lib/utils"

interface ImageLightboxProps {
  images: string[]
  initialIndex?: number
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function ImageLightbox({
  images,
  initialIndex = 0,
  open,
  onOpenChange
}: ImageLightboxProps) {
  const [currentIndex, setCurrentIndex] = useState(initialIndex)
  const [zoom, setZoom] = useState(1)
  const [rotation, setRotation] = useState(0)
  const [position, setPosition] = useState({ x: 0, y: 0 })
  const [isDragging, setIsDragging] = useState(false)
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 })
  const [imageLoaded, setImageLoaded] = useState(false)
  const [preloadedImages, setPreloadedImages] = useState<Set<string>>(new Set())

  // Preload images when lightbox opens
  useEffect(() => {
    if (open && images.length > 0) {
      const preloadImage = (src: string) => {
        return new Promise<void>((resolve) => {
          const img = new Image()
          img.onload = () => {
            setPreloadedImages(prev => new Set([...prev, src]))
            resolve()
          }
          img.onerror = () => resolve() // Still resolve on error to prevent hanging
          img.src = src
        })
      }

      // Preload current image first, then adjacent images
      const currentSrc = images[currentIndex]
      if (currentSrc && !preloadedImages.has(currentSrc)) {
        preloadImage(currentSrc).then(() => {
          setImageLoaded(true)
        })
      } else if (preloadedImages.has(currentSrc)) {
        setImageLoaded(true)
      }

      // Preload adjacent images in background
      const adjacentIndices = [
        currentIndex - 1 >= 0 ? currentIndex - 1 : images.length - 1,
        currentIndex + 1 < images.length ? currentIndex + 1 : 0
      ]

      adjacentIndices.forEach(index => {
        const src = images[index]
        if (src && !preloadedImages.has(src)) {
          preloadImage(src)
        }
      })
    }
  }, [open, currentIndex, images, preloadedImages])

  // Reset zoom, rotation, and position when the lightbox is opened or image changes
  useEffect(() => {
    setZoom(1)
    setRotation(0)
    setPosition({ x: 0, y: 0 })
    setImageLoaded(false) // Reset image loaded state when changing images
  }, [open, currentIndex])

  // Update current index when initialIndex changes
  useEffect(() => {
    if (initialIndex >= 0 && initialIndex < images.length) {
      setCurrentIndex(initialIndex)
    }
  }, [initialIndex, images.length])

  const handlePrevious = () => {
    setCurrentIndex((prev) => (prev === 0 ? images.length - 1 : prev - 1))
  }

  const handleNext = () => {
    setCurrentIndex((prev) => (prev === images.length - 1 ? 0 : prev + 1))
  }

  const handleZoomIn = () => {
    setZoom((prev) => Math.min(prev + 0.25, 3))
  }

  const handleZoomOut = () => {
    setZoom((prev) => Math.max(prev - 0.25, 0.5))
  }

  const handleRotate = () => {
    setRotation((prev) => (prev + 90) % 360)
  }

  const handleReset = () => {
    setZoom(1)
    setRotation(0)
    setPosition({ x: 0, y: 0 })
  }

  // Mouse event handlers for panning
  const handleMouseDown = (e: React.MouseEvent<HTMLDivElement>) => {
    if (zoom > 1) {
      setIsDragging(true)
      setDragStart({ x: e.clientX, y: e.clientY })
    }
  }

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (isDragging && zoom > 1) {
      const deltaX = e.clientX - dragStart.x
      const deltaY = e.clientY - dragStart.y

      setPosition(prev => ({
        x: prev.x + deltaX,
        y: prev.y + deltaY
      }))

      setDragStart({ x: e.clientX, y: e.clientY })
    }
  }

  const handleMouseUp = () => {
    setIsDragging(false)
  }

  const handleMouseLeave = () => {
    setIsDragging(false)
  }

  // Touch event handlers for mobile panning
  const handleTouchStart = (e: React.TouchEvent<HTMLDivElement>) => {
    if (zoom > 1 && e.touches.length === 1) {
      setIsDragging(true)
      setDragStart({
        x: e.touches[0].clientX,
        y: e.touches[0].clientY
      })
    }
  }

  const handleTouchMove = (e: React.TouchEvent<HTMLDivElement>) => {
    if (isDragging && zoom > 1 && e.touches.length === 1) {
      e.preventDefault() // Prevent scrolling when panning

      const deltaX = e.touches[0].clientX - dragStart.x
      const deltaY = e.touches[0].clientY - dragStart.y

      setPosition(prev => ({
        x: prev.x + deltaX,
        y: prev.y + deltaY
      }))

      setDragStart({
        x: e.touches[0].clientX,
        y: e.touches[0].clientY
      })
    }
  }

  const handleTouchEnd = () => {
    setIsDragging(false)
  }

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!open) return

      switch (e.key) {
        case "ArrowLeft":
          handlePrevious()
          break
        case "ArrowRight":
          handleNext()
          break
        case "+":
        case "=":
          handleZoomIn()
          break
        case "-":
          handleZoomOut()
          break
        case "r":
          handleRotate()
          break
        case "0":
          handleReset()
          break
        case "Escape":
          onOpenChange(false)
          break
      }
    }

    window.addEventListener("keydown", handleKeyDown)
    return () => window.removeEventListener("keydown", handleKeyDown)
  }, [open, onOpenChange, handlePrevious, handleNext, handleZoomIn, handleZoomOut, handleRotate, handleReset])

  if (!images.length) return null

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <CustomDialogContent
        className="max-w-[90vw] max-h-[80vh] p-0 bg-background/95 backdrop-blur-sm border-none overflow-hidden"
        aria-describedby="lightbox-description"
      >
        {/* Add DialogTitle for accessibility, but visually hide it */}
        <DialogTitle className="sr-only">Image Viewer</DialogTitle>
        {/* Custom description for this specific dialog */}
        <div id="lightbox-description" className="sr-only">
          Image lightbox viewer with zoom, rotation and navigation controls. Use arrow keys to navigate between images, plus and minus keys to zoom, R key to rotate, and Escape to close.
        </div>

        <div className="relative w-full h-full flex flex-col max-h-[80vh]">
          {/* Header with controls - made more compact */}
          <div className="flex items-center justify-between p-1.5 bg-background/80 backdrop-blur-sm border-b">
            <div className="text-xs sm:text-sm text-muted-foreground">
              {currentIndex + 1} / {images.length}
            </div>
            <div className="flex items-center gap-1">
              <Button
                variant="ghost"
                size="icon"
                className="h-7 w-7"
                onClick={handleZoomOut}
                title="Zoom out"
              >
                <ZoomOut className="h-3.5 w-3.5" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                className="h-7 w-7"
                onClick={handleZoomIn}
                title="Zoom in"
              >
                <ZoomIn className="h-3.5 w-3.5" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                className="h-7 w-7"
                onClick={handleRotate}
                title="Rotate"
              >
                <RotateCw className="h-3.5 w-3.5" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                className="h-7 w-7"
                onClick={handleReset}
                title="Reset"
                disabled={zoom === 1 && rotation === 0 && position.x === 0 && position.y === 0}
              >
                <RefreshCw className="h-3.5 w-3.5" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                className="h-7 w-7"
                onClick={() => onOpenChange(false)}
                title="Close"
              >
                <X className="h-3.5 w-3.5" />
              </Button>
            </div>
          </div>

          {/* Image container - adjusted to ensure visibility on smaller screens */}
          <div
            className="flex-1 overflow-hidden relative flex items-center justify-center p-2 cursor-grab"
            onMouseDown={handleMouseDown}
            onMouseMove={handleMouseMove}
            onMouseUp={handleMouseUp}
            onMouseLeave={handleMouseLeave}
            onTouchStart={handleTouchStart}
            onTouchMove={handleTouchMove}
            onTouchEnd={handleTouchEnd}
            style={{ cursor: isDragging ? 'grabbing' : (zoom > 1 ? 'grab' : 'default') }}
          >
            {/* Loading indicator */}
            {!imageLoaded && (
              <div className="absolute inset-0 flex items-center justify-center bg-background/50">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              </div>
            )}

            <img
              src={images[currentIndex]}
              alt={`Image ${currentIndex + 1}`}
              className={cn(
                "max-w-full max-h-[calc(80vh-60px)] object-contain transition-all duration-300",
                imageLoaded ? "opacity-100" : "opacity-0"
              )}
              style={{
                transform: `translate(${position.x}px, ${position.y}px) scale(${zoom}) rotate(${rotation}deg)`,
                transition: isDragging ? 'none' : 'transform 0.2s, opacity 0.3s',
              }}
              onLoad={() => setImageLoaded(true)}
              draggable={false}
            />
          </div>

          {/* Navigation buttons - made more compact */}
          {images.length > 1 && (
            <>
              <Button
                variant="ghost"
                size="icon"
                className="absolute left-1 sm:left-2 top-1/2 -translate-y-1/2 h-8 w-8 sm:h-10 sm:w-10 rounded-full bg-background/50 hover:bg-background/80 backdrop-blur-sm"
                onClick={handlePrevious}
              >
                <ChevronLeft className="h-4 w-4 sm:h-6 sm:w-6" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                className="absolute right-1 sm:right-2 top-1/2 -translate-y-1/2 h-8 w-8 sm:h-10 sm:w-10 rounded-full bg-background/50 hover:bg-background/80 backdrop-blur-sm"
                onClick={handleNext}
              >
                <ChevronRight className="h-4 w-4 sm:h-6 sm:w-6" />
              </Button>
            </>
          )}
        </div>
      </CustomDialogContent>
    </Dialog>
  )
}
