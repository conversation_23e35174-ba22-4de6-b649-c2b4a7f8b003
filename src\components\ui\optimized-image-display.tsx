"use client"

import { useState, useEffect, useMemo, useRef } from "react"
import { ImageOff, ZoomIn } from "lucide-react"
import { cn } from "@/lib/utils"
import { ImageLightbox } from "@/components/ui/image-lightbox"
import { <PERSON><PERSON> } from "@/components/ui/button"

interface OptimizedImageDisplayProps {
  src: string
  alt: string
  className?: string
  aspectRatio?: "square" | "video" | "auto"
  lightboxEnabled?: boolean
  lightboxGroup?: string[]
  lightboxIndex?: number
  isNewlyUploaded?: boolean
}

export function OptimizedImageDisplay({
  src,
  alt,
  className = "",
  aspectRatio = "video",
  lightboxEnabled = true,
  lightboxGroup,
  lightboxIndex = 0,
  isNewlyUploaded = false
}: OptimizedImageDisplayProps) {
  const [error, setError] = useState(false)
  const [loading, setLoading] = useState(!isNewlyUploaded) // Skip initial loading state for newly uploaded images
  const [lightboxOpen, setLightboxOpen] = useState(false)
  const [finalSrc, setFinalSrc] = useState<string | null>(isNewlyUploaded ? src : null)
  const [imageLoaded, setImageLoaded] = useState(isNewlyUploaded)
  const imgRef = useRef<HTMLImageElement>(null)

  // Extract the filename from the URL
  const getFilename = (url: string): string => {
    try {
      // Check if it's already just a filename (no slashes or protocol)
      if (!url.includes('/') && !url.includes('://')) {
        return url;
      }

      // Remove query parameters
      const cleanUrl = url.split('?')[0];
      // Get the last part of the path
      const filename = cleanUrl.split('/').pop() || '';
      return filename;
    } catch (e) {
      console.error('Error extracting filename:', e);
      return '';
    }
  };

  // Create a proxy URL that will serve the image through our API
  const createProxyUrl = (url: string): string => {
    // For newly uploaded images, use the direct URL for faster loading
    if (isNewlyUploaded && url === src) {
      return url;
    }

    const filename = getFilename(url);
    if (!filename) {
      return url;
    }

    // Use our server-side API to proxy the image
    return `/api/image-proxy?filename=${encodeURIComponent(filename)}`;
  };

  // Try to load an image with the given URL
  const tryLoadImage = (url: string): Promise<string> => {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => resolve(url);
      img.onerror = () => reject(new Error(`Failed to load image: ${url}`));
      img.src = url;
    });
  };

  // Effect to handle image loading
  useEffect(() => {
    if (!src) {
      setError(true);
      setLoading(false);
      return;
    }

    // For newly uploaded images, we can skip the proxy and use the direct URL
    if (isNewlyUploaded) {
      setFinalSrc(src);
      setLoading(false);
      return;
    }

    const loadImage = async () => {
      setLoading(true);
      setError(false);

      // Create a proxy URL that will serve the image through our API
      const proxyUrl = createProxyUrl(src);

      try {
        // Try loading the image directly first
        await tryLoadImage(proxyUrl);
        setFinalSrc(proxyUrl);
        setLoading(false);
      } catch (e) {
        // Try with cache-busting
        try {
          const cacheBustUrl = `${proxyUrl}&_t=${Date.now()}`;
          await tryLoadImage(cacheBustUrl);
          setFinalSrc(cacheBustUrl);
          setLoading(false);
        } catch (cacheBustError) {
          console.error('Failed to load image:', cacheBustError);
          setError(true);
          setLoading(false);
        }
      }
    };

    loadImage();
  }, [src, isNewlyUploaded]);

  // Process lightbox images to use the proxy
  const processedLightboxGroup = useMemo(() => {
    if (!lightboxGroup) {
      return finalSrc ? [finalSrc] : [];
    }

    // Convert all lightbox images to use the proxy
    return lightboxGroup.map(createProxyUrl);
  }, [lightboxGroup, finalSrc]);

  // Handle image load event
  const handleImageLoad = () => {
    setImageLoaded(true);
  };

  // Skip old format URLs
  if (src.includes('/setup_')) {
    return (
      <div className={cn(
        "flex items-center justify-center bg-muted/30 rounded-lg",
        className,
        aspectRatio === "square" ? "aspect-square" :
        aspectRatio === "video" ? "aspect-video" : ""
      )}>
        <div className="flex flex-col items-center text-muted-foreground">
          <ImageOff className="h-8 w-8 mb-2" />
          <span className="text-sm">Image not available</span>
        </div>
      </div>
    )
  }

  // Show loading state
  if (loading && !isNewlyUploaded) {
    return (
      <div className={cn(
        "flex items-center justify-center bg-muted/30 rounded-lg",
        className,
        aspectRatio === "square" ? "aspect-square" :
        aspectRatio === "video" ? "aspect-video" : ""
      )}>
        <div className="flex flex-col items-center text-muted-foreground">
          <div className="h-8 w-8 mb-2 rounded-full border-2 border-purple-500 border-t-transparent animate-spin" />
          <span className="text-sm">Loading image...</span>
        </div>
      </div>
    )
  }

  // Show error state
  if (error || !finalSrc) {
    return (
      <div className={cn(
        "flex items-center justify-center bg-muted/30 rounded-lg",
        className,
        aspectRatio === "square" ? "aspect-square" :
        aspectRatio === "video" ? "aspect-video" : ""
      )}>
        <div className="flex flex-col items-center text-muted-foreground">
          <ImageOff className="h-8 w-8 mb-2" />
          <span className="text-sm">Failed to load image</span>
          <Button
            variant="outline"
            size="sm"
            className="mt-2"
            onClick={() => {
              const timestamp = Date.now();
              const filename = getFilename(src);
              const proxyUrl = `/api/image-proxy?filename=${encodeURIComponent(filename)}&_t=${timestamp}&_force=true`;
              
              setLoading(true);
              setError(false);
              
              const img = new Image();
              img.onload = () => {
                setFinalSrc(proxyUrl);
                setLoading(false);
              };
              img.onerror = () => {
                setError(true);
                setLoading(false);
              };
              img.src = proxyUrl;
            }}
          >
            Retry
          </Button>
        </div>
      </div>
    )
  }

  // Show the successfully loaded image
  return (
    <>
      <div
        className={cn(
          "relative bg-muted/30 rounded-lg overflow-hidden group",
          className,
          aspectRatio === "square" ? "aspect-square" :
          aspectRatio === "video" ? "aspect-video" : "",
          lightboxEnabled && "cursor-pointer",
          !imageLoaded && !isNewlyUploaded && "animate-pulse"
        )}
        onClick={lightboxEnabled ? () => setLightboxOpen(true) : undefined}
      >
        <img
          ref={imgRef}
          src={finalSrc}
          alt={alt}
          className={cn(
            "w-full object-contain transition-opacity duration-300",
            aspectRatio === "auto" ? "h-auto" : "h-full",
            imageLoaded ? "opacity-100" : "opacity-0"
          )}
          onLoad={handleImageLoad}
          loading={isNewlyUploaded ? "eager" : "lazy"}
        />

        {/* Zoom indicator overlay */}
        {lightboxEnabled && imageLoaded && (
          <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 dark:group-hover:bg-black/40 transition-colors flex items-center justify-center opacity-0 group-hover:opacity-100">
            <Button
              variant="secondary"
              size="icon"
              className="h-9 w-9 rounded-full shadow-lg bg-background/80 backdrop-blur-sm"
            >
              <ZoomIn className="h-5 w-5" />
            </Button>
          </div>
        )}
      </div>

      {/* Image Lightbox */}
      {lightboxEnabled && finalSrc && (
        <ImageLightbox
          images={processedLightboxGroup}
          initialIndex={lightboxIndex}
          open={lightboxOpen}
          onOpenChange={setLightboxOpen}
        />
      )}
    </>
  )
}
