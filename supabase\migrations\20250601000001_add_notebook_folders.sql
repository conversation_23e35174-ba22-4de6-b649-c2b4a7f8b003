-- Create notebook_folders table
CREATE TABLE IF NOT EXISTS public.notebook_folders (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  description TEXT,
  color TEXT,
  icon TEXT,
  parent_id UUID REFERENCES public.notebook_folders(id) ON DELETE SET NULL,
  is_system BOOLEAN DEFAULT false,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  CONSTRAINT check_user_id_or_system CHECK (user_id IS NOT NULL OR is_system = true)
);

-- Create a partial unique index instead of a constraint with WHERE clause
CREATE UNIQUE INDEX IF NOT EXISTS idx_notebook_folders_user_id_name
ON public.notebook_folders (user_id, name)
WHERE user_id IS NOT NULL;

-- Add folder_id to notebook_entries table
ALTER TABLE public.notebook_entries
ADD COLUMN folder_id UUID REFERENCES public.notebook_folders(id) ON DELETE SET NULL;

-- Create index for faster queries
CREATE INDEX IF NOT EXISTS idx_notebook_entries_folder_id ON public.notebook_entries(folder_id);

-- Enable Row Level Security
ALTER TABLE public.notebook_folders ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Users can view their own notebook folders and system folders"
  ON public.notebook_folders FOR SELECT
  USING (auth.uid() = user_id OR is_system = true);

CREATE POLICY "Users can insert their own notebook folders"
  ON public.notebook_folders FOR INSERT
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own notebook folders"
  ON public.notebook_folders FOR UPDATE
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete their own notebook folders"
  ON public.notebook_folders FOR DELETE
  USING (auth.uid() = user_id);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION public.handle_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for updated_at
CREATE TRIGGER set_notebook_folders_updated_at
BEFORE UPDATE ON public.notebook_folders
FOR EACH ROW
EXECUTE FUNCTION public.handle_updated_at();

-- Create system folders for imported content
INSERT INTO public.notebook_folders (user_id, name, description, is_system, color, icon)
VALUES
  (NULL, 'Trade Notes', 'Imported trade journal entries', true, '#725ac1', 'file-text'),
  (NULL, 'Daily Journal', 'Imported daily journal entries', true, '#725ac1', 'calendar');
