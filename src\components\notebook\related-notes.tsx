"use client"

import { NotebookEntry } from "@/types/notebook"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { formatDistanceToNow } from "date-fns"
import { Tag, Calendar, FileText } from "lucide-react"

interface RelatedNotesProps {
  entries: NotebookEntry[]
  onSelect: (id: string) => void
}

export function RelatedNotes({ entries, onSelect }: RelatedNotesProps) {
  if (entries.length === 0) {
    return null
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
      {entries.map(entry => (
        <Card 
          key={entry.id}
          className="cursor-pointer hover:shadow-md transition-shadow"
          onClick={() => onSelect(entry.id)}
        >
          <CardContent className="p-3">
            <div className="flex flex-col gap-1">
              <h4 className="font-medium text-sm line-clamp-1">{entry.title}</h4>
              
              <div className="flex items-center justify-between text-xs text-muted-foreground">
                <div className="flex items-center gap-1">
                  <Calendar className="h-3 w-3" />
                  <span>
                    {formatDistanceToNow(new Date(entry.updated_at || entry.created_at), { addSuffix: true })}
                  </span>
                </div>
                
                <div className="flex items-center gap-1">
                  {entry.category && (
                    <Badge variant="outline" className="text-[10px] h-5 px-1">
                      <FileText className="h-3 w-3 mr-1" />
                      {entry.category}
                    </Badge>
                  )}
                  
                  {entry.tags && entry.tags.length > 0 && (
                    <Badge variant="secondary" className="text-[10px] h-5 px-1">
                      <Tag className="h-3 w-3 mr-1" />
                      {entry.tags.length}
                    </Badge>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
