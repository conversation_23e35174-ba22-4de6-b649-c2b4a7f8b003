"use client"

import React, { useState } from "react"
import { toast } from "sonner"
import { Strategy } from "@/types/playbook"
import { deleteStrategy } from "@/lib/playbook-service"
import { formatDistanceToNow } from "date-fns"
import { getCardGradient } from "@/lib/card-utils"

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { Badge } from "@/components/ui/badge"
import {
  MoreVert<PERSON>,
  <PERSON>,
  <PERSON>rash,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>ers,
  Bar<PERSON>hart3,
  List,
  Grid,
} from "lucide-react"
import { StrategyListView } from "./strategy-list-view"

interface StrategyListProps {
  userId: string
  strategies: Strategy[]
  onEdit: (strategy: Strategy) => void
  onDelete: (strategyId: string) => void
  onAdd: () => void
  onAddWizard: () => void
  onViewSetups: (strategy: Strategy) => void
  onViewRules: (strategy: Strategy) => void
  onViewPerformance: (strategy: Strategy) => void
  onCompare?: () => void
  onSelectStrategy: (strategy: Strategy) => void
}

export function StrategyList({
  userId,
  strategies,
  onEdit,
  onDelete,
  onAdd,
  onAddWizard,
  onViewSetups,
  onViewRules,
  onViewPerformance,
  onCompare,
  onSelectStrategy
}: StrategyListProps) {
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [strategyToDelete, setStrategyToDelete] = useState<Strategy | null>(null)
  const [viewMode, setViewMode] = useState<'card' | 'list'>('card')

  const handleDeleteClick = (strategy: Strategy) => {
    setStrategyToDelete(strategy)
    setDeleteDialogOpen(true)
  }

  const confirmDelete = async () => {
    if (!strategyToDelete) return

    try {
      const success = await deleteStrategy(userId, strategyToDelete.id)
      if (success) {
        toast.success("Strategy deleted successfully")
        onDelete(strategyToDelete.id)
      } else {
        toast.error("Failed to delete strategy")
      }
    } catch (error) {
      console.error("Error deleting strategy:", error)
      toast.error("An error occurred while deleting the strategy")
    } finally {
      setDeleteDialogOpen(false)
      setStrategyToDelete(null)
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-emerald-500 text-white font-medium">Active</Badge>
      case 'testing':
        return <Badge className="bg-amber-500 text-white font-medium">Testing</Badge>
      case 'archived':
        return <Badge variant="outline" className="text-muted-foreground font-medium">Archived</Badge>
      default:
        return <Badge variant="outline" className="font-medium">{status}</Badge>
    }
  }

  // Use the shared card gradient utility

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold tracking-tight">Trading Strategies</h2>
        <div className="flex gap-2">
          {/* View Toggle */}
          <div className="border rounded-md flex overflow-hidden mr-2">
            <Button
              variant={viewMode === 'card' ? 'default' : 'ghost'}
              size="sm"
              className="rounded-none"
              onClick={() => setViewMode('card')}
            >
              <Grid className="h-4 w-4 mr-1" />
              Cards
            </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'ghost'}
              size="sm"
              className="rounded-none"
              onClick={() => setViewMode('list')}
            >
              <List className="h-4 w-4 mr-1" />
              List
            </Button>
          </div>

          {strategies.length > 1 && onCompare && (
            <Button variant="outline" onClick={onCompare}>
              <BarChart3 className="mr-2 h-4 w-4" /> Compare
            </Button>
          )}
          <Button
            onClick={onAddWizard}
            variant="outline"
            style={{ backgroundColor: "#FAFAFA", color: "#000000", borderColor: "#E5E7EB" }}
          >
            <Plus className="mr-2 h-4 w-4" /> Create Your Strategy
          </Button>
        </div>
      </div>

      {strategies.length === 0 ? (
        <Card className="border-dashed">
          <CardContent className="pt-6 text-center">
            <p className="text-muted-foreground mb-4">
              You haven't created any trading strategies yet.
            </p>
            <Button
              onClick={onAddWizard}
              variant="outline"
              style={{ backgroundColor: "#FAFAFA", color: "#000000", borderColor: "#E5E7EB" }}
            >
              <Plus className="mr-2 h-4 w-4" /> Create Your Strategy
            </Button>
          </CardContent>
        </Card>
      ) : viewMode === 'list' ? (
        <StrategyListView
          strategies={strategies}
          onSelectStrategy={onSelectStrategy}
        />
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {strategies.map((strategy) => (
            <Card
              key={strategy.id}
              className="flex flex-col overflow-hidden group hover:shadow-md transition-all duration-300 border-t-4 dark:shadow-lg dark:hover:shadow-xl cursor-pointer"
              onClick={(e) => {
                // Prevent click when clicking on dropdown menu
                const target = e.target as HTMLElement;
                if (target.closest('.strategy-menu-trigger') || target.closest('.dropdown-menu-content')) {
                  return;
                }
                onSelectStrategy(strategy);
              }}
              style={{
                background: `var(--mode-light, ${getCardGradient(strategy.name).light.background}) var(--mode-dark, ${getCardGradient(strategy.name).dark.background})`,
                borderImageSource: `var(--mode-light, ${getCardGradient(strategy.name).light.border}) var(--mode-dark, ${getCardGradient(strategy.name).dark.border})`,
                borderImageSlice: 1,
                borderTopColor: `var(--mode-light, ${getCardGradient(strategy.name).light.accent}) var(--mode-dark, ${getCardGradient(strategy.name).dark.accent})`,
                ...({
                  '--mode-light': 'initial',
                  '--mode-dark': 'initial'
                } as React.CSSProperties)
              }}
            >
              <CardHeader className="pb-2 relative">
                <div className="absolute top-0 right-0 p-2">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8 rounded-full opacity-70 hover:opacity-100 hover:bg-background/80 strategy-menu-trigger"
                        onClick={(e) => e.stopPropagation()}
                      >
                        <MoreVertical className="h-4 w-4" />
                        <span className="sr-only">Open menu</span>
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="w-48 dropdown-menu-content">
                      <DropdownMenuItem onClick={() => onSelectStrategy(strategy)} className="cursor-pointer">
                        <BarChart3 className="mr-2 h-4 w-4" />
                        View Details
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem onClick={() => onEdit(strategy)} className="cursor-pointer">
                        <Edit className="mr-2 h-4 w-4" />
                        Edit
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => handleDeleteClick(strategy)}
                        className="text-destructive focus:text-destructive cursor-pointer"
                      >
                        <Trash className="mr-2 h-4 w-4" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>

                <div className="space-y-1">
                  <div className="flex items-center gap-2">
                    <CardTitle className="text-lg font-bold">{strategy.name}</CardTitle>
                    {getStatusBadge(strategy.status)}
                  </div>
                  <CardDescription className="line-clamp-2 text-sm opacity-90">
                    {strategy.description || "No description provided"}
                  </CardDescription>
                </div>
              </CardHeader>

              <CardContent className="pb-2 flex-grow">
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-3">
                    {strategy.risk_reward_ratio !== null && (
                      <div className={`${getCardGradient(strategy.name).light.cardBg} dark:${getCardGradient(strategy.name).dark.cardBg} rounded-lg p-2 text-center backdrop-blur-sm`}>
                        <div className="text-sm text-muted-foreground mb-1">Avg Win/Avg Loss</div>
                        <div className="text-lg font-semibold"
                          style={{
                            color: `var(--mode-light, ${getCardGradient(strategy.name).light.accent}) var(--mode-dark, ${getCardGradient(strategy.name).dark.accent})`,
                            '--mode-light': 'initial',
                            '--mode-dark': 'initial'
                          } as React.CSSProperties & { [key: string]: any }}
                        >
                          {strategy.risk_reward_ratio}
                        </div>
                      </div>
                    )}

                    {strategy.expected_win_rate !== null && (
                      <div className={`${getCardGradient(strategy.name).light.cardBg} dark:${getCardGradient(strategy.name).dark.cardBg} rounded-lg p-2 text-center backdrop-blur-sm`}>
                        <div className="text-sm text-muted-foreground mb-1">Win Rate</div>
                        <div className="text-lg font-semibold"
                          style={{
                            color: `var(--mode-light, ${getCardGradient(strategy.name).light.accent}) var(--mode-dark, ${getCardGradient(strategy.name).dark.accent})`,
                            ...({
                              '--mode-light': 'initial',
                              '--mode-dark': 'initial'
                            } as React.CSSProperties)
                          }}
                        >
                          {strategy.expected_win_rate}%
                        </div>
                      </div>
                    )}
                  </div>

                  <div>
                    <h4 className="text-sm font-medium mb-2 flex items-center">
                      <span className="inline-block w-2 h-2 rounded-full mr-1 dark:shadow-glow"
                        style={{
                          backgroundColor: `var(--mode-light, ${getCardGradient(strategy.name).light.accent}) var(--mode-dark, ${getCardGradient(strategy.name).dark.accent})`,
                          '--mode-light': 'initial',
                          '--mode-dark': 'initial'
                        } as React.CSSProperties & { [key: string]: any }}>
                      </span>
                      Market Conditions
                    </h4>
                    <div className="flex flex-wrap gap-1">
                      {strategy.market_conditions.map((condition) => (
                        <Badge
                          key={condition}
                          variant="outline"
                          className="text-xs bg-background/60 hover:bg-background/80 transition-colors"
                        >
                          {condition.charAt(0).toUpperCase() + condition.slice(1).replace('_', ' ')}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <h4 className="text-sm font-medium mb-2 flex items-center">
                        <span className="inline-block w-2 h-2 rounded-full mr-1 dark:shadow-glow"
                          style={{
                            backgroundColor: `var(--mode-light, ${getCardGradient(strategy.name).light.accent}) var(--mode-dark, ${getCardGradient(strategy.name).dark.accent})`,
                            '--mode-light': 'initial',
                            '--mode-dark': 'initial'
                          } as React.CSSProperties & { [key: string]: any }}>
                        </span>
                        Timeframes
                      </h4>
                      <div className="flex flex-wrap gap-1">
                        {strategy.timeframes.map((timeframe) => (
                          <Badge
                            key={timeframe}
                            variant="outline"
                            className="text-xs bg-background/60 hover:bg-background/80 transition-colors"
                          >
                            {timeframe.toUpperCase()}
                          </Badge>
                        ))}
                      </div>
                    </div>

                    <div>
                      <h4 className="text-sm font-medium mb-2 flex items-center">
                        <span className="inline-block w-2 h-2 rounded-full mr-1 dark:shadow-glow"
                          style={{
                            backgroundColor: `var(--mode-light, ${getCardGradient(strategy.name).light.accent}) var(--mode-dark, ${getCardGradient(strategy.name).dark.accent})`,
                            '--mode-light': 'initial',
                            '--mode-dark': 'initial'
                          } as React.CSSProperties & { [key: string]: any }}>
                        </span>
                        Instruments
                      </h4>
                      <div className="flex flex-wrap gap-1">
                        {strategy.instruments.map((instrument) => (
                          <Badge
                            key={instrument}
                            variant="outline"
                            className="text-xs bg-background/60 hover:bg-background/80 transition-colors"
                          >
                            {instrument}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>

              <CardFooter className={`pt-2 text-xs text-muted-foreground border-t border-border/30 mt-auto ${getCardGradient(strategy.name).light.footerBg} dark:${getCardGradient(strategy.name).dark.footerBg} dark:border-border/10`}>
                <div className="flex items-center">
                  <div
                    className="w-2 h-2 rounded-full mr-2 animate-pulse dark:shadow-glow"
                    style={{
                      backgroundColor: `var(--mode-light, ${getCardGradient(strategy.name).light.accent}) var(--mode-dark, ${getCardGradient(strategy.name).dark.accent})`,
                      '--mode-light': 'initial',
                      '--mode-dark': 'initial'
                    } as React.CSSProperties & { [key: string]: any }}>
                  </div>
                  Updated {formatDistanceToNow(new Date(strategy.updated_at), { addSuffix: true })}
                </div>
              </CardFooter>
            </Card>
          ))}
        </div>
      )}

      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete the strategy &quot;{strategyToDelete?.name}&quot; and all associated setups and rules.
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDelete} className="bg-destructive text-destructive-foreground">
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
