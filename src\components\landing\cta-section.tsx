"use client"

import { motion } from 'framer-motion'
import { ArrowRight } from 'lucide-react'
import { Button } from '@/components/ui/button'
import Link from 'next/link'

export default function CtaSection() {
    return (
        <section className="py-16 md:py-24 relative overflow-hidden">
            {/* Background gradient */}
            <div className="absolute inset-0 bg-gradient-to-r from-primary/10 via-primary/5 to-background"></div>
            
            <div className="container relative z-10">
                <motion.div 
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true, margin: "-100px" }}
                    transition={{ duration: 0.6 }}
                    className="mx-auto max-w-3xl text-center">
                    <h2 className="text-3xl font-bold md:text-4xl lg:text-5xl">Ready to Transform Your Trading?</h2>
                    <p className="mt-6 text-lg text-muted-foreground">
                        Join thousands of traders who are using TradePivot to analyze their performance, refine their strategies, and improve their results.
                    </p>
                    
                    <motion.div 
                        initial={{ opacity: 0, y: 20 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        viewport={{ once: true, margin: "-100px" }}
                        transition={{ duration: 0.6, delay: 0.2 }}
                        className="mt-10 flex flex-wrap items-center justify-center gap-4">
                        <Link href="/auth?tab=register">
                            <Button size="lg" className="gap-2 group">
                                Start Your Free Journal
                                <ArrowRight className="h-4 w-4 transition-transform group-hover:translate-x-1" />
                            </Button>
                        </Link>
                        <Link href="#pricing">
                            <Button variant="outline" size="lg">
                                View Pricing
                            </Button>
                        </Link>
                    </motion.div>
                    
                    <motion.p 
                        initial={{ opacity: 0, y: 20 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        viewport={{ once: true, margin: "-100px" }}
                        transition={{ duration: 0.6, delay: 0.3 }}
                        className="mt-6 text-sm text-muted-foreground">
                        No credit card required. Free plan includes all essential features.
                    </motion.p>
                </motion.div>
            </div>
            
            {/* Decorative elements */}
            <div className="absolute -bottom-16 -left-16 h-64 w-64 rounded-full bg-primary/5 blur-3xl"></div>
            <div className="absolute -top-16 -right-16 h-64 w-64 rounded-full bg-primary/5 blur-3xl"></div>
        </section>
    )
}
