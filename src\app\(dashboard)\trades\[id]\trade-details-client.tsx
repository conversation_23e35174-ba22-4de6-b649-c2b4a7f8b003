"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { ArrowLeft } from "lucide-react"
import { Button } from "@/components/ui/button"
import { EnhancedTradeDetailView } from "@/components/enhanced-trade-detail-view"
import { getSupabaseClient } from "@/lib/supabase-singleton"
import { toast } from "sonner"
import type { Trade } from "@/types/trade"
import type { Strategy } from "@/types/playbook"

interface TradeDetailsClientProps {
  trade: Trade
  strategies: Strategy[]
  source: string | null
  symbol: string | null
  userId: string
}

export function TradeDetailsClient({
  trade: initialTrade,
  strategies: initialStrategies,
  source,
  symbol,
  userId
}: TradeDetailsClientProps) {
  const router = useRouter()
  const [trade, setTrade] = useState(initialTrade)
  const [strategies, setStrategies] = useState(initialStrategies)

  const handleUpdate = async () => {
    if (!trade) return

    try {
      const supabase = getSupabaseClient()

      // Fetch the updated trade
      const { data: updatedTrade, error } = await supabase
        .from("trades")
        .select("*")
        .eq("id", trade.id)
        .single()

      if (error) {
        console.error("Error fetching updated trade:", error)
        return
      }

      // Check if strategy assignment has changed
      const strategyChanged = trade.strategy_id !== updatedTrade.strategy_id

      // Update the trade state with the fresh data
      setTrade(updatedTrade)

      // If strategy changed, refresh strategies to ensure we have the latest data
      if (strategyChanged) {
        console.log("Strategy assignment changed, refreshing strategies")

        // Fetch updated strategies
        const { data: strategiesData, error: strategiesError } = await supabase
          .from("strategies")
          .select("*")
          .eq("user_id", userId)
          .order("name", { ascending: true })

        if (strategiesError) {
          console.error("Error refreshing strategies:", strategiesError)
          return
        }

        setStrategies(strategiesData || [])
      }
    } catch (error) {
      console.error("Error updating trade:", error)
      toast.error("Failed to update trade details")
    }
  }

  return (
    <>
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center">
          <Button
            variant="outline"
            size="sm"
            className="mr-4 shadow-sm hover:shadow transition-all"
            onClick={() => {
              // Handle different sources
              if (source === 'calendar') {
                router.push("/dashboard")
              } else if (source === 'symbol' && symbol) {
                router.push(`/symbols/${symbol}`)
              } else {
                router.push("/trades")
              }
            }}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            {source === 'calendar'
              ? 'Back'
              : source === 'symbol'
                ? `Back to ${symbol}`
                : 'Back to Trades'}
          </Button>
          <h1 className="text-2xl font-bold bg-gradient-to-r from-purple-500 to-blue-500 bg-clip-text text-transparent dark:from-purple-400 dark:to-blue-400">
            Trade Details
          </h1>
        </div>
      </div>

      <div className="bg-background rounded-lg shadow-sm">
        <EnhancedTradeDetailView
          userId={userId}
          trade={trade}
          strategies={strategies}
          onUpdate={handleUpdate}
        />
      </div>
    </>
  )
}
