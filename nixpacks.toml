providers = ["node"]

[variables]
NODE_ENV = "production"
NIXPACKS_NO_CACHE = "1"
NPM_CONFIG_PACKAGE_MANAGER = "npm"
COREPACK_ENABLE_STRICT = "0"
NIXPACKS_NODE_VERSION = "18"

[phases.setup]
nixPkgs = ["nodejs_18"]
cmds = [
  "echo 'Setting up npm environment'",
  "rm -f pnpm-lock.yaml yarn.lock .pnpmfile.cjs || true",
  "npm --version"
]

[phases.install]
cmd = "npm install --no-fund --no-audit"

[phases.build]
cmd = "npm run build"

[start]
cmd = "npm start"
