"use client"

import React, { useRef, useState } from 'react';
import QuillEditorDirect from '@/components/quill-editor/QuillEditorDirect';

export default function QuillDirectTestPage() {
  const quillRef = useRef<any>(null);
  const [content, setContent] = useState('');
  const [readOnly, setReadOnly] = useState(false);
  const [range, setRange] = useState<any>(null);

  // Handle text changes
  const handleTextChange = (delta: any, oldContents: any, source: string) => {
    if (quillRef.current) {
      // Quill doesn't have getHTML() method, use root.innerHTML instead
      setContent(quillRef.current.root.innerHTML);
    }
  };

  // Handle selection changes
  const handleSelectionChange = (range: any, oldRange: any, source: string) => {
    setRange(range);
  };

  // Get the editor content
  const getContent = () => {
    if (quillRef.current) {
      alert(`Content length: ${quillRef.current.getLength()}`);
    }
  };

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">Direct Quill Editor Test</h1>

      <div className="mb-4 flex items-center space-x-4">
        <label className="flex items-center space-x-2">
          <input
            type="checkbox"
            checked={readOnly}
            onChange={(e) => setReadOnly(e.target.checked)}
            className="rounded"
          />
          <span>Read Only</span>
        </label>

        <button
          onClick={getContent}
          className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
        >
          Get Content Length
        </button>
      </div>

      <div className="mb-8 h-[500px]">
        <QuillEditorDirect
          ref={quillRef}
          readOnly={readOnly}
          onTextChange={handleTextChange}
          onSelectionChange={handleSelectionChange}
          defaultValue={{
            ops: [
              { insert: 'Direct Quill Editor Demo\n', attributes: { header: 1 } },
              { insert: 'This is a demonstration of the direct Quill editor implementation based on the Slab/Quill approach.\n' },
              { insert: 'Features:\n', attributes: { header: 2 } },
              { insert: { list: 'bullet' }, attributes: { list: 'bullet' } },
              { insert: 'Direct Quill instance access\n', attributes: { list: 'bullet' } },
              { insert: 'No React-Quill wrapper\n', attributes: { list: 'bullet' } },
              { insert: 'Better compatibility with React 18\n', attributes: { list: 'bullet' } },
              { insert: 'Try typing below this line to test the editor:\n' },
            ]
          }}
          placeholder="Start typing here..."
          className="h-full"
        />
      </div>

      <div className="mt-8">
        <h2 className="text-xl font-semibold mb-2">Editor Output:</h2>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <h3 className="text-lg font-medium mb-2">HTML:</h3>
            <div className="p-4 border rounded bg-gray-50 dark:bg-gray-800 max-h-[300px] overflow-auto">
              <pre className="whitespace-pre-wrap text-sm">{content}</pre>
            </div>
          </div>
          <div>
            <h3 className="text-lg font-medium mb-2">Current Selection:</h3>
            <div className="p-4 border rounded bg-gray-50 dark:bg-gray-800 max-h-[300px] overflow-auto">
              <pre className="whitespace-pre-wrap text-sm">{range ? JSON.stringify(range, null, 2) : 'No selection'}</pre>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
