import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { createServerClient } from '@supabase/ssr';
import type { Database } from '@/types/supabase';

// POST handler for importing existing journal entries into notebook
export async function POST(request: NextRequest) {
  try {
    // Get cookies for server-side Supabase client
    const cookieStore = await cookies();
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
          set(_name: string, _value: string, _options: any) {
            // API routes can't set cookies directly
          },
          remove(_name: string, _options: any) {
            // API routes can't remove cookies directly
          }
        },
      }
    );

    // Get authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // 0. Get or create system folders for imported content
    let tradeFolderId: string | null = null;
    let journalFolderId: string | null = null;

    // Check if Trade Notes folder exists
    const { data: tradeFolder, error: tradeFolderError } = await supabase
      .from('notebook_folders')
      .select('id')
      .eq('user_id', user.id)
      .eq('name', 'Trade Notes')
      .single();

    if (tradeFolderError && tradeFolderError.code !== 'PGRST116') {
      console.error('Error fetching trade folder:', tradeFolderError);
      return NextResponse.json({ error: tradeFolderError.message }, { status: 500 });
    }

    // Create Trade Notes folder if it doesn't exist
    if (!tradeFolder) {
      const { data: newTradeFolder, error: newTradeFolderError } = await supabase
        .from('notebook_folders')
        .insert({
          user_id: user.id,
          name: 'Trade Notes',
          description: 'Imported trade journal entries',
          color: '#725ac1',
          icon: 'file-text',
          is_system: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single();

      if (newTradeFolderError) {
        console.error('Error creating trade folder:', newTradeFolderError);
        return NextResponse.json({ error: newTradeFolderError.message }, { status: 500 });
      }

      tradeFolderId = newTradeFolder.id;
    } else {
      tradeFolderId = tradeFolder.id;
    }

    // Check if Daily Journal folder exists
    const { data: journalFolder, error: journalFolderError } = await supabase
      .from('notebook_folders')
      .select('id')
      .eq('user_id', user.id)
      .eq('name', 'Daily Journal')
      .single();

    if (journalFolderError && journalFolderError.code !== 'PGRST116') {
      console.error('Error fetching journal folder:', journalFolderError);
      return NextResponse.json({ error: journalFolderError.message }, { status: 500 });
    }

    // Create Daily Journal folder if it doesn't exist
    if (!journalFolder) {
      const { data: newJournalFolder, error: newJournalFolderError } = await supabase
        .from('notebook_folders')
        .insert({
          user_id: user.id,
          name: 'Daily Journal',
          description: 'Imported daily journal entries',
          color: '#725ac1',
          icon: 'calendar',
          is_system: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single();

      if (newJournalFolderError) {
        console.error('Error creating journal folder:', newJournalFolderError);
        return NextResponse.json({ error: newJournalFolderError.message }, { status: 500 });
      }

      journalFolderId = newJournalFolder.id;
    } else {
      journalFolderId = journalFolder.id;
    }

    // 1. Import trade journal entries with journal content
    const { data: trades, error: tradesError } = await supabase
      .from('trades')
      .select('*')
      .eq('user_id', user.id)
      .or('notes.not.is.null,journal_content.not.is.null,has_journal_content.eq.true');

    if (tradesError) {
      console.error('Error fetching trades:', tradesError);
      return NextResponse.json({ error: tradesError.message }, { status: 500 });
    }

    console.log(`Found ${trades?.length || 0} trades with journal content`);

    // Debug: Log a few trades to see their content
    if (trades && trades.length > 0) {
      console.log('Sample trade:', {
        id: trades[0].id,
        hasNotes: !!trades[0].notes,
        hasJournalContent: !!trades[0].journal_content,
        hasJournalFlag: !!trades[0].has_journal_content
      });
    }

    // 2. Import daily journal entries
    const { data: dailyJournals, error: dailyJournalsError } = await supabase
      .from('daily_journal_entries')
      .select('*')
      .eq('user_id', user.id)
      .not('note', 'is', null);

    if (dailyJournalsError) {
      console.error('Error fetching daily journals:', dailyJournalsError);
      return NextResponse.json({ error: dailyJournalsError.message }, { status: 500 });
    }

    // 3. Check if entries already exist in notebook to avoid duplicates
    const { data: existingEntries, error: existingError } = await supabase
      .from('notebook_entries')
      .select('id, linked_trade_ids, title, category');

    if (existingError) {
      console.error('Error fetching existing entries:', existingError);
      return NextResponse.json({ error: existingError.message }, { status: 500 });
    }

    console.log(`Found ${existingEntries?.length || 0} existing notebook entries`);

    // Extract existing trade IDs
    const existingTradeIds = existingEntries
      ?.flatMap(entry => entry.linked_trade_ids || []) || [];

    console.log(`Found ${existingTradeIds.length} existing linked trade IDs`);
    if (existingTradeIds.length > 0) {
      console.log('Sample existing trade IDs:', existingTradeIds.slice(0, 3));
    }

    // Extract existing daily journal entries by checking date in title
    // This is more reliable than using IDs since we want to avoid duplicate dates
    const existingDailyJournalDates = existingEntries
      ?.filter(entry => entry.category === 'Daily Journal')
      ?.map(entry => {
        // Extract date from title (format: "Daily Journal - MM/DD/YYYY")
        const dateMatch = entry.title?.match(/Daily Journal - (\d{1,2}\/\d{1,2}\/\d{4})/);
        return dateMatch ? dateMatch[1] : null;
      })
      .filter(Boolean) || [];

    // 4. Prepare trade entries for import
    const tradeEntries = trades
      ?.filter(trade => {
        // Only import trades that have journal content and aren't already imported
        const hasContent = trade.notes || trade.journal_content || trade.has_journal_content;
        const isNotImported = !existingTradeIds.includes(trade.id);

        if (hasContent && !isNotImported) {
          console.log(`Skipping already imported trade: ${trade.id}`);
        }

        return hasContent && isNotImported;
      })
      ?.map(trade => {
        // Get the journal content from the appropriate field
        const journalText = trade.notes || trade.journal_content || '';

        // Format the date properly
        let dateStr = '';
        try {
          dateStr = new Date(trade.time_open).toLocaleDateString();
        } catch (e) {
          dateStr = 'Unknown Date';
          console.error(`Error formatting date for trade ${trade.id}:`, e);
        }

        return {
          user_id: user.id,
          account_id: trade.account_id,
          title: `${trade.symbol || 'Unknown'} - ${trade.type || 'Trade'} - ${dateStr}`,
          content: JSON.stringify({
            type: 'doc',
            content: [{
              type: 'paragraph',
              content: [{
                type: 'text',
                text: journalText
              }]
            }]
          }),
          html_content: journalText,
          category: 'Trade Notes',
          folder_id: tradeFolderId,
          tags: Array.isArray(trade.tags) ? trade.tags : [],
          is_pinned: false,
          is_template: false,
          linked_trade_ids: [trade.id],
          linked_strategy_ids: trade.strategy_id ? [trade.strategy_id] : [],
          screenshots: Array.isArray(trade.screenshots) ? trade.screenshots : [],
          created_at: trade.created_at || new Date().toISOString(),
          updated_at: trade.updated_at || trade.created_at || new Date().toISOString()
        };
      }) || [];

    console.log(`Prepared ${tradeEntries.length} trade entries for import`);

    // 5. Prepare daily journal entries for import
    const dailyJournalEntries = dailyJournals
      ?.filter(journal => {
        // Format the date to match the format in the title
        const journalDate = new Date(journal.date).toLocaleDateString();
        // Skip if we already have an entry for this date
        return !existingDailyJournalDates.includes(journalDate);
      })
      ?.map(journal => ({
        user_id: user.id,
        account_id: journal.account_id,
        title: `Daily Journal - ${new Date(journal.date).toLocaleDateString()}`,
        content: JSON.stringify({ type: 'doc', content: [{ type: 'paragraph', content: [{ type: 'text', text: journal.note || '' }] }] }),
        html_content: journal.note || '',
        category: 'Daily Journal',
        folder_id: journalFolderId,
        tags: journal.tags || [],
        is_pinned: false,
        is_template: false,
        linked_trade_ids: [],
        linked_strategy_ids: [],
        screenshots: Array.isArray(journal.screenshots) ? journal.screenshots : [],
        created_at: journal.created_at,
        updated_at: journal.updated_at || journal.created_at
      })) || [];

    // 6. Insert trade entries
    let tradeImportCount = 0;
    if (tradeEntries.length > 0) {
      try {
        // Insert entries in batches to avoid potential size limits
        const batchSize = 10;
        const batches = [];

        for (let i = 0; i < tradeEntries.length; i += batchSize) {
          batches.push(tradeEntries.slice(i, i + batchSize));
        }

        console.log(`Splitting ${tradeEntries.length} trade entries into ${batches.length} batches`);

        for (const batch of batches) {
          const { data: insertedTrades, error: insertTradesError } = await supabase
            .from('notebook_entries')
            .insert(batch)
            .select();

          if (insertTradesError) {
            console.error('Error importing trade entries batch:', insertTradesError);
            console.error('Failed batch:', batch);
          } else {
            tradeImportCount += insertedTrades?.length || 0;
            console.log(`Successfully imported ${insertedTrades?.length || 0} trade entries`);
          }
        }
      } catch (error) {
        console.error('Error during trade entries batch import:', error);
      }
    }

    // 7. Insert daily journal entries
    let journalImportCount = 0;
    if (dailyJournalEntries.length > 0) {
      try {
        // Insert entries in batches to avoid potential size limits
        const batchSize = 10;
        const batches = [];

        for (let i = 0; i < dailyJournalEntries.length; i += batchSize) {
          batches.push(dailyJournalEntries.slice(i, i + batchSize));
        }

        console.log(`Splitting ${dailyJournalEntries.length} daily journal entries into ${batches.length} batches`);

        for (const batch of batches) {
          const { data: insertedJournals, error: insertJournalsError } = await supabase
            .from('notebook_entries')
            .insert(batch)
            .select();

          if (insertJournalsError) {
            console.error('Error importing daily journal entries batch:', insertJournalsError);
          } else {
            journalImportCount += insertedJournals?.length || 0;
            console.log(`Successfully imported ${insertedJournals?.length || 0} daily journal entries`);
          }
        }
      } catch (error) {
        console.error('Error during daily journal entries batch import:', error);
      }
    }

    return NextResponse.json({
      success: true,
      imported: {
        trades: tradeImportCount,
        dailyJournals: journalImportCount,
        total: tradeImportCount + journalImportCount
      }
    });
  } catch (error) {
    console.error('Error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
