# Profile Page SSR Implementation Plan

This document outlines the detailed implementation plan for migrating the Profile page from client-side rendering to server-side rendering while maintaining the current UI and functionality.

## Current Implementation Analysis

The current Profile page (`src/app/(dashboard)/profile/page.tsx`) is implemented as a client component with the following characteristics:

1. **UI Structure**: Uses a tabbed interface with four tabs:
   - Personal Info
   - Password
   - Profile Picture
   - Preferences
2. **Components**: Each tab contains a separate component:
   - `PersonalInfoForm`: Manages user profile information
   - `PasswordChangeForm`: Handles password changes
   - `ProfilePictureUpload`: Manages profile picture uploads
   - `AccountPreferences`: Handles user preferences
3. **State Management**: Uses a simple state variable to track the active tab.
4. **Data Fetching**: Each component fetches its own data directly from Supabase.

## Implementation Steps

### Step 1: Create Server Component

Create a new server component that will handle authentication and initial data fetching:

```tsx
// src/app/(dashboard)/profile/page.tsx
import { cookies } from 'next/headers';
import { createServerClient } from '@supabase/ssr';
import { redirect } from 'next/navigation';
import type { Database } from '@/types/supabase';
import ClientWrapper from './client-wrapper';

export default async function ProfilePage() {
  // Get cookies for server-side Supabase client
  const cookieStore = await cookies();
  const supabase = createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value;
        },
        set(_name: string, _value: string, _options: any) {
          // Server components can't set cookies directly
        },
        remove(_name: string, _options: any) {
          // Server components can't remove cookies directly
        }
      },
    }
  );

  // Get authenticated user
  const { data: { user }, error: userError } = await supabase.auth.getUser();
  if (userError || !user) {
    redirect('/login');
  }

  const userId = user.id;

  // Fetch user profile
  const { data: profile, error: profileError } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', userId)
    .single();

  if (profileError && profileError.code !== 'PGRST116') { // PGRST116 is "no rows returned" error
    console.error('Error fetching user profile:', profileError);
  }

  // Fetch user preferences
  const { data: preferences, error: preferencesError } = await supabase
    .from('user_preferences')
    .select('*')
    .eq('user_id', userId)
    .single();

  if (preferencesError && preferencesError.code !== 'PGRST116') {
    console.error('Error fetching user preferences:', preferencesError);
  }

  // Pass the fetched data to the client component
  return (
    <ClientWrapper
      userId={userId}
      userEmail={user.email || ''}
      initialProfile={profile || null}
      initialPreferences={preferences || null}
    />
  );
}
```

### Step 2: Create Client Wrapper

Create a client wrapper component that will dynamically import the client component:

```tsx
// src/app/(dashboard)/profile/client-wrapper.tsx
"use client"

import dynamic from 'next/dynamic';

// Dynamically import the client component with no SSR
const ProfileClient = dynamic(() => import('./client'), {
  ssr: false
});

interface ClientWrapperProps {
  userId: string;
  userEmail: string;
  initialProfile: any | null;
  initialPreferences: any | null;
}

export default function ClientWrapper({ 
  userId, 
  userEmail,
  initialProfile, 
  initialPreferences 
}: ClientWrapperProps) {
  return (
    <ProfileClient
      userId={userId}
      userEmail={userEmail}
      initialProfile={initialProfile}
      initialPreferences={initialPreferences}
    />
  );
}
```

### Step 3: Create Client Component

Create a client component that will handle the UI logic and state management:

```tsx
// src/app/(dashboard)/profile/client.tsx
"use client"

import { useState } from "react"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { PersonalInfoForm } from "@/components/profile/personal-info-form"
import { PasswordChangeForm } from "@/components/profile/password-change-form"
import { ProfilePictureUpload } from "@/components/profile/profile-picture-upload"
import { AccountPreferences } from "@/components/profile/account-preferences"

interface ProfileClientProps {
  userId: string;
  userEmail: string;
  initialProfile: any | null;
  initialPreferences: any | null;
}

export default function ProfileClient({
  userId,
  userEmail,
  initialProfile,
  initialPreferences
}: ProfileClientProps) {
  const [activeTab, setActiveTab] = useState("personal-info")

  return (
    <div className="container py-6 space-y-6">
      <div>
        <h1 className="text-2xl font-semibold">Profile Settings</h1>
        <p className="text-muted-foreground">
          Manage your account settings and preferences
        </p>
      </div>

      <div className="bg-yellow-500/10 border border-yellow-500/50 rounded-md p-4 text-sm">
        <h3 className="font-medium text-yellow-500 mb-1">Database Setup Required</h3>
        <p className="text-muted-foreground">
          Some profile features may be limited until the database setup is complete. Please refer to the <code className="bg-muted px-1 py-0.5 rounded">PROFILE_SETUP.md</code> file for instructions on how to set up the required database tables and storage buckets.
        </p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid grid-cols-4 w-full max-w-3xl">
          <TabsTrigger value="personal-info">Personal Info</TabsTrigger>
          <TabsTrigger value="password">Password</TabsTrigger>
          <TabsTrigger value="profile-picture">Profile Picture</TabsTrigger>
          <TabsTrigger value="preferences">Preferences</TabsTrigger>
        </TabsList>

        <TabsContent value="personal-info">
          <Card>
            <CardHeader>
              <CardTitle>Personal Information</CardTitle>
              <CardDescription>
                Update your personal information and contact details
              </CardDescription>
            </CardHeader>
            <CardContent>
              <PersonalInfoForm 
                userId={userId} 
                userEmail={userEmail}
                initialProfile={initialProfile}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="password">
          <Card>
            <CardHeader>
              <CardTitle>Change Password</CardTitle>
              <CardDescription>
                Update your password to keep your account secure
              </CardDescription>
            </CardHeader>
            <CardContent>
              <PasswordChangeForm 
                userId={userId}
                userEmail={userEmail}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="profile-picture">
          <Card>
            <CardHeader>
              <CardTitle>Profile Picture</CardTitle>
              <CardDescription>
                Upload or update your profile picture
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ProfilePictureUpload 
                userId={userId}
                initialProfile={initialProfile}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="preferences">
          <Card>
            <CardHeader>
              <CardTitle>Account Preferences</CardTitle>
              <CardDescription>
                Customize your account settings and preferences
              </CardDescription>
            </CardHeader>
            <CardContent>
              <AccountPreferences 
                userId={userId}
                initialPreferences={initialPreferences}
              />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
```

### Step 4: Create API Routes

Create API routes for user profile and settings:

```tsx
// src/app/api/user-profile/route.ts
import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { createServerClient } from '@supabase/ssr';
import type { Database } from '@/types/supabase';

export async function GET(request: Request) {
  try {
    // Get cookies for server-side Supabase client
    const cookieStore = await cookies();
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
          set(_name: string, _value: string, _options: any) {
            // API routes can't set cookies directly
          },
          remove(_name: string, _options: any) {
            // API routes can't remove cookies directly
          }
        },
      }
    );

    // Get authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    const userId = user.id;

    // Fetch user profile
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single();

    if (error && error.code !== 'PGRST116') {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json(data || {});
  } catch (error) {
    console.error('Error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function PUT(request: Request) {
  try {
    const body = await request.json();

    // Get cookies for server-side Supabase client
    const cookieStore = await cookies();
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
          set(_name: string, _value: string, _options: any) {
            // API routes can't set cookies directly
          },
          remove(_name: string, _options: any) {
            // API routes can't remove cookies directly
          }
        },
      }
    );

    // Get authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    const userId = user.id;

    // Check if profile exists
    const { data: existingProfile, error: checkError } = await supabase
      .from('profiles')
      .select('id')
      .eq('id', userId)
      .single();

    if (checkError && checkError.code === 'PGRST116') {
      // Profile doesn't exist, create it
      const { data, error } = await supabase
        .from('profiles')
        .insert([
          {
            id: userId,
            ...body,
            updated_at: new Date().toISOString()
          }
        ])
        .select()
        .single();

      if (error) {
        return NextResponse.json({ error: error.message }, { status: 500 });
      }

      return NextResponse.json(data);
    } else {
      // Profile exists, update it
      const { data, error } = await supabase
        .from('profiles')
        .update({
          ...body,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId)
        .select()
        .single();

      if (error) {
        return NextResponse.json({ error: error.message }, { status: 500 });
      }

      return NextResponse.json(data);
    }
  } catch (error) {
    console.error('Error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
```

```tsx
// src/app/api/user-settings/route.ts
import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { createServerClient } from '@supabase/ssr';
import type { Database } from '@/types/supabase';

export async function GET(request: Request) {
  try {
    // Get cookies for server-side Supabase client
    const cookieStore = await cookies();
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
          set(_name: string, _value: string, _options: any) {
            // API routes can't set cookies directly
          },
          remove(_name: string, _options: any) {
            // API routes can't remove cookies directly
          }
        },
      }
    );

    // Get authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    const userId = user.id;

    // Fetch user preferences
    const { data, error } = await supabase
      .from('user_preferences')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (error && error.code !== 'PGRST116') {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json(data || {});
  } catch (error) {
    console.error('Error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function PUT(request: Request) {
  try {
    const body = await request.json();

    // Get cookies for server-side Supabase client
    const cookieStore = await cookies();
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
          set(_name: string, _value: string, _options: any) {
            // API routes can't set cookies directly
          },
          remove(_name: string, _options: any) {
            // API routes can't remove cookies directly
          }
        },
      }
    );

    // Get authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    const userId = user.id;

    // Check if preferences exist
    const { data: existingPreferences, error: checkError } = await supabase
      .from('user_preferences')
      .select('user_id')
      .eq('user_id', userId)
      .single();

    if (checkError && checkError.code === 'PGRST116') {
      // Preferences don't exist, create them
      const { data, error } = await supabase
        .from('user_preferences')
        .insert([
          {
            user_id: userId,
            ...body,
            updated_at: new Date().toISOString()
          }
        ])
        .select()
        .single();

      if (error) {
        return NextResponse.json({ error: error.message }, { status: 500 });
      }

      return NextResponse.json(data);
    } else {
      // Preferences exist, update them
      const { data, error } = await supabase
        .from('user_preferences')
        .update({
          ...body,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId)
        .select()
        .single();

      if (error) {
        return NextResponse.json({ error: error.message }, { status: 500 });
      }

      return NextResponse.json(data);
    }
  } catch (error) {
    console.error('Error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
```

## Testing Plan

1. **Authentication Testing**:
   - Verify that unauthenticated users are redirected to the login page.
   - Verify that authenticated users can access the profile page.

2. **Data Loading Testing**:
   - Verify that initial profile data is loaded correctly from the server.
   - Verify that initial preferences data is loaded correctly from the server.

3. **UI Testing**:
   - Verify that all tabs render correctly.
   - Verify that tab switching works correctly.

4. **Form Testing**:
   - Verify that the personal info form works correctly.
   - Verify that the password change form works correctly.
   - Verify that the profile picture upload works correctly.
   - Verify that the account preferences form works correctly.

## Implementation Checklist

- [ ] Create server component (`page.tsx`)
- [ ] Create client wrapper (`client-wrapper.tsx`)
- [ ] Create client component (`client.tsx`)
- [ ] Create API routes:
  - [ ] `/api/user-profile/route.ts`
  - [ ] `/api/user-settings/route.ts`
- [ ] Update profile components to accept props:
  - [ ] `PersonalInfoForm`
  - [ ] `PasswordChangeForm`
  - [ ] `ProfilePictureUpload`
  - [ ] `AccountPreferences`
- [ ] Test authentication flow
- [ ] Test data loading
- [ ] Test UI rendering
- [ ] Test form submissions
