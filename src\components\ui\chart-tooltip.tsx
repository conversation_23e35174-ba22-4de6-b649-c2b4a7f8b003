"use client"

import * as React from "react"
import { cn } from "@/lib/utils"
import { motion } from "framer-motion"

interface ChartTooltipProps {
  active?: boolean
  payload?: any[]
  label?: string
  formatter?: (value: any, name: string, props: any) => React.ReactNode
  labelFormatter?: (label: string) => React.ReactNode
  className?: string
  children?: React.ReactNode
}

export function ChartTooltip({
  active,
  payload,
  label,
  formatter,
  labelFormatter,
  className,
  children
}: ChartTooltipProps) {
  if (!active || !payload?.length) return null

  const data = payload[0].payload

  return (
    <motion.div
      initial={{ opacity: 0, y: 5 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.2 }}
      className={cn(
        "rounded-lg border bg-card/95 backdrop-blur p-3 shadow-lg dark:shadow-none text-xs",
        className
      )}
    >
      {children || (
        <div className="grid grid-cols-2 gap-2">
          {label && (
            <>
              <div className="font-medium text-muted-foreground">Date:</div>
              <div className="font-medium">{labelFormatter ? labelFormatter(label) : label}</div>
            </>
          )}
          {payload.map((entry, index) => (
            <React.Fragment key={`tooltip-item-${index}`}>
              <div className="font-medium text-muted-foreground flex items-center gap-2">
                <span
                  className="h-2.5 w-2.5 rounded-full"
                  style={{ backgroundColor: entry.color }}
                />
                {entry.name}:
              </div>
              <div className={cn(
                "font-medium",
                typeof entry.value === 'number' && (entry.value >= 0 ? "text-emerald-500" : "text-rose-500")
              )}>
                {formatter ? formatter(entry.value, entry.name, entry) : entry.value}
              </div>
            </React.Fragment>
          ))}
        </div>
      )}
    </motion.div>
  )
}
