-- Create Journal Entries table
create table if not exists journal_entries (
    id uuid default uuid_generate_v4() primary key,
    user_id uuid references auth.users(id) on delete cascade not null,
    trade_id uuid references trades(id) on delete set null,
    title text not null,
    content text not null,
    tags text[] default '{}',
    entry_date date not null,
    created_at timestamp with time zone default timezone('utc'::text, now()) not null,
    updated_at timestamp with time zone default timezone('utc'::text, now()) not null
);

-- Enable Row Level Security
alter table journal_entries enable row level security;

-- Create policies
create policy "Users can view their own journal entries"
    on journal_entries for select
    using (auth.uid() = user_id);

create policy "Users can insert their own journal entries"
    on journal_entries for insert
    with check (auth.uid() = user_id);

create policy "Users can update their own journal entries"
    on journal_entries for update
    using (auth.uid() = user_id);

create policy "Users can delete their own journal entries"
    on journal_entries for delete
    using (auth.uid() = user_id);

-- Create indexes for better performance
create index if not exists journal_entries_user_id_idx on journal_entries(user_id);
create index if not exists journal_entries_trade_id_idx on journal_entries(trade_id);
create index if not exists journal_entries_entry_date_idx on journal_entries(entry_date);
