"use client"

import { CustomMetric, Goal } from '@/types/metrics';
import { Trade } from '@/types/trade';
import MetricsGoalsClient from './client';

interface ClientWrapperProps {
  userId: string;
  selectedAccountId: string | null;
  initialMetrics: CustomMetric[];
  initialGoals: Goal[];
  initialTrades: Trade[];
  initialMetricValues: Record<string, number>;
}

export default function ClientWrapper({
  userId,
  selectedAccountId,
  initialMetrics,
  initialGoals,
  initialTrades,
  initialMetricValues
}: ClientWrapperProps) {
  console.log('[ClientWrapper] Received SSR data:', {
    userId,
    selectedAccountId,
    metricsCount: initialMetrics.length,
    goalsCount: initialGoals.length,
    tradesCount: initialTrades.length,
    metricValuesCount: Object.keys(initialMetricValues).length,
    metricValues: initialMetricValues
  });

  return (
    <MetricsGoalsClient
      userId={userId}
      selectedAccountId={selectedAccountId}
      initialMetrics={initialMetrics}
      initialGoals={initialGoals}
      initialTrades={initialTrades}
      initialMetricValues={initialMetricValues}
    />
  );
}
