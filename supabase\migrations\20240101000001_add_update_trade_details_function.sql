-- Create a function to update trade details with proper permissions
CREATE OR REPLACE FUNCTION public.update_trade_details(
  p_trade_id UUID,
  p_strategy_id UUID,
  p_setup_id UUID,
  p_notes TEXT,
  p_screenshots TEXT[],
  p_followed_rules TEXT[],
  p_followed_setup_criteria TEXT[]
) RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  -- Update the trade with the provided details
  UPDATE public.trades
  SET 
    strategy_id = p_strategy_id,
    setup_id = p_setup_id,
    notes = p_notes,
    screenshots = p_screenshots,
    followed_rules = p_followed_rules,
    followed_setup_criteria = p_followed_setup_criteria,
    updated_at = NOW()
  WHERE id = p_trade_id;
  
  -- Return true if the update was successful
  RETURN FOUND;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.update_trade_details TO authenticated;

-- Add comment to the function
COMMENT ON FUNCTION public.update_trade_details IS 'Updates trade details with proper permissions';
