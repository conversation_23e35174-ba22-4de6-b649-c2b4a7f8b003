"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { DirectImageDisplay } from "@/components/ui/direct-image-display"
import { Input } from "@/components/ui/input"
import { Plus, Trash2 } from "lucide-react"
import { toast } from "sonner"
import { uploadImageToStorage } from "@/lib/image-service"

interface ScreenshotDisplayProps {
  screenshots: string[]
  isEditing: boolean
  onChange?: (screenshots: string[]) => void
  className?: string
}

export function ScreenshotDisplay({
  screenshots = [],
  isEditing = false,
  onChange,
  className = ""
}: ScreenshotDisplayProps) {
  const [newImageUrl, setNewImageUrl] = useState("")
  const [isUploading, setIsUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [showUrlInput, setShowUrlInput] = useState(false)

  const handleAddImageUrl = () => {
    if (!newImageUrl.trim()) {
      toast.error("Please enter a valid image URL")
      return
    }

    const updatedScreenshots = [...screenshots, newImageUrl.trim()]
    onChange?.(updatedScreenshots)
    setNewImageUrl("")
    setShowUrlInput(false)
    toast.success("Screenshot added (will be saved when you save the note)")
  }

  const handleRemoveImage = (index: number) => {
    const updatedScreenshots = screenshots.filter((_, i) => i !== index)
    onChange?.(updatedScreenshots)
    toast.success("Screenshot removed (will be saved when you save the note)")
  }

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    // Validate file type
    if (!file.type.startsWith('image/')) {
      toast.error("Please select a valid image file")
      return
    }

    // Validate file size (5MB limit)
    if (file.size > 5 * 1024 * 1024) {
      toast.error("File size must be less than 5MB")
      return
    }

    setIsUploading(true)
    setUploadProgress(0)

    try {
      // Simulate progress for better UX
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          const newProgress = prev + Math.random() * 15;
          return newProgress > 90 ? 90 : newProgress;
        });
      }, 150);

      const imageUrl = await uploadImageToStorage(file)

      // Clear progress interval
      clearInterval(progressInterval)

      // Set progress to 100% when upload is complete
      setUploadProgress(100)

      const updatedScreenshots = [...screenshots, imageUrl]
      onChange?.(updatedScreenshots)
      toast.success("Screenshot uploaded (will be saved when you save the note)")

      // Log the successful upload
      console.log("Screenshot uploaded successfully:", imageUrl)
    } catch (error) {
      console.error("Error uploading screenshot:", error)
      toast.error("Failed to upload screenshot")
    } finally {
      setIsUploading(false)
      setUploadProgress(0)
      // Reset the input
      event.target.value = ""
    }
  }

  if (!isEditing && screenshots.length === 0) {
    return null
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Screenshots Section - Unified Grid Layout like Daily Journal */}
      <div className="space-y-3">
        <h4 className="text-sm font-medium text-foreground">Screenshots</h4>
        <div className="grid grid-cols-2 gap-3">
          {/* Existing Screenshots */}
          {screenshots.map((imageUrl, index) => (
            <div key={index} className="relative group">
              <DirectImageDisplay
                src={imageUrl}
                alt={`Screenshot ${index + 1}`}
                aspectRatio="video"
                lightboxEnabled={true}
                lightboxGroup={screenshots}
                lightboxIndex={index}
                className="rounded-md border hover:border-purple-500/50 dark:hover:border-purple-400/50 transition-colors"
              />
              {isEditing && (
                <Button
                  variant="destructive"
                  size="icon"
                  className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity h-7 w-7 z-10"
                  onClick={() => handleRemoveImage(index)}
                  type="button"
                >
                  <Trash2 className="h-3.5 w-3.5" />
                </Button>
              )}
            </div>
          ))}

          {/* Add Screenshot Button - Part of the same grid */}
          {isEditing && (
            <div
              className="relative aspect-video rounded-md border-2 border-dashed border-muted-foreground/25 bg-muted/30 hover:bg-muted/50 transition-colors cursor-pointer flex flex-col items-center justify-center group"
              onClick={() => document.getElementById('screenshot-upload')?.click()}
            >
              <input
                id="screenshot-upload"
                type="file"
                accept="image/*"
                onChange={handleFileUpload}
                className="hidden"
                disabled={isUploading}
              />

              {isUploading ? (
                <>
                  <div className="absolute inset-0 bg-background/80 flex flex-col items-center justify-center z-10">
                    <div className="w-full max-w-[80%] h-2 bg-muted rounded-full overflow-hidden mb-2">
                      <div
                        className="h-full bg-primary transition-all duration-300 ease-out"
                        style={{ width: `${uploadProgress}%` }}
                      />
                    </div>
                    <span className="text-xs text-muted-foreground">Uploading... {Math.round(uploadProgress)}%</span>
                  </div>
                  <Plus className="h-6 w-6 mb-1 text-muted-foreground opacity-50" />
                  <span className="text-xs text-muted-foreground opacity-50">Uploading...</span>
                </>
              ) : (
                <>
                  <Plus className="h-6 w-6 mb-1 text-muted-foreground group-hover:text-foreground transition-colors" />
                  <span className="text-xs text-muted-foreground group-hover:text-foreground transition-colors">Add Screenshot</span>
                </>
              )}
            </div>
          )}
        </div>
      </div>

      {/* URL Input - Collapsible (only show when editing) */}
      {isEditing && (
        <div className="space-y-2">
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={() => setShowUrlInput(!showUrlInput)}
            className="text-xs text-muted-foreground hover:text-foreground"
          >
            <Plus className="h-3 w-3 mr-1" />
            Add from URL
          </Button>

          {showUrlInput && (
            <div className="flex gap-2">
              <Input
                type="url"
                placeholder="https://example.com/image.jpg"
                value={newImageUrl}
                onChange={(e) => setNewImageUrl(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault()
                    handleAddImageUrl()
                  }
                }}
                className="flex-1 text-sm"
              />
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={handleAddImageUrl}
                disabled={!newImageUrl.trim()}
              >
                Add
              </Button>
            </div>
          )}
        </div>
      )}
    </div>
  )
}
