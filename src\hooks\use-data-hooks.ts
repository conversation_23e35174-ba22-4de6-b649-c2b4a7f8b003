"use client"

import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query"
import { useEffect } from "react"
import { getUserTrades, getTradeById } from "@/lib/data/trade-data"
import { getJournalEntries, getJournalEntryById, getDailyJournalEntries, getDailyJournalEntry } from "@/lib/data/journal-data"
import { getUserAccounts, getSelectedAccountId, getTradingSummary, setSelectedAccountId } from "@/lib/data/account-data"
import { getAuthenticatedUser } from "@/lib/supabase-browser"

// User hooks
export function useUser() {
  return useQuery({
    queryKey: ['user'],
    queryFn: async () => {
      const { user, error } = await getAuthenticatedUser()
      if (error || !user) {
        throw error || new Error('User not found')
      }
      return user
    },
    staleTime: 1000 * 60 * 5, // 5 minutes
  })
}

// Account hooks
export function useAccounts(userId: string, initialData: any[] = []) {
  const queryClient = useQueryClient()

  useEffect(() => {
    if (userId) {
      queryClient.invalidateQueries({ queryKey: ['accounts', userId] })
    }
  }, [userId, queryClient])

  return useQuery({
    queryKey: ['accounts', userId],
    queryFn: () => getUserAccounts(userId),
    enabled: !!userId,
    initialData: userId ? undefined : initialData,
    staleTime: 1000 * 60 * 5, // 5 minutes
  })
}

export function useSelectedAccountId(userId: string, initialData: string | null = null) {
  const queryClient = useQueryClient()

  useEffect(() => {
    if (userId) {
      queryClient.invalidateQueries({ queryKey: ['selectedAccountId', userId] })
    }
  }, [userId, queryClient])

  return useQuery({
    queryKey: ['selectedAccountId', userId],
    queryFn: () => getSelectedAccountId(userId),
    enabled: !!userId,
    initialData: userId ? undefined : initialData,
    staleTime: 1000 * 60 * 5, // 5 minutes
  })
}

export function useSetSelectedAccountId(userId: string) {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (accountId: string | null) => setSelectedAccountId(userId, accountId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['selectedAccountId', userId] })
    },
  })
}

export function useTradingSummary(accountId: string | null, initialData: any = null) {
  const queryClient = useQueryClient()

  useEffect(() => {
    if (accountId) {
      queryClient.invalidateQueries({ queryKey: ['tradingSummary', accountId] })
    }
  }, [accountId, queryClient])

  return useQuery({
    queryKey: ['tradingSummary', accountId],
    queryFn: () => accountId ? getTradingSummary(accountId) : null,
    enabled: !!accountId,
    initialData: accountId ? undefined : initialData,
    staleTime: 1000 * 60 * 5, // 5 minutes
  })
}

// Trade hooks
export function useTrades(userId: string, options?: {
  accountId?: string | null,
  startDate?: string | Date | null,
  endDate?: string | Date | null,
  symbol?: string | null,
  tradeType?: string | null,
  minProfit?: number | null,
  maxProfit?: number | null,
  tags?: string[] | null,
  initialData?: any[]
}) {
  const queryClient = useQueryClient()
  const queryKey = ['trades', userId, options?.accountId, options?.startDate, options?.endDate, options?.symbol, options?.tradeType, options?.minProfit, options?.maxProfit, options?.tags]

  useEffect(() => {
    if (userId) {
      queryClient.invalidateQueries({ queryKey: queryKey })
    }
  }, [userId, options?.accountId, queryClient, queryKey])

  return useQuery({
    queryKey,
    queryFn: () => getUserTrades(userId, options),
    enabled: !!userId,
    initialData: userId ? undefined : options?.initialData || [],
    staleTime: 1000 * 60 * 5, // 5 minutes
  })
}

export function useTrade(userId: string, tradeId: string | null, initialData: any = null) {
  const queryClient = useQueryClient()

  useEffect(() => {
    if (userId && tradeId) {
      queryClient.invalidateQueries({ queryKey: ['trade', userId, tradeId] })
    }
  }, [userId, tradeId, queryClient])

  return useQuery({
    queryKey: ['trade', userId, tradeId],
    queryFn: () => tradeId ? getTradeById(userId, tradeId) : null,
    enabled: !!userId && !!tradeId,
    initialData: (userId && tradeId) ? undefined : initialData,
    staleTime: 1000 * 60 * 5, // 5 minutes
  })
}

// Journal hooks
export function useJournalEntries(userId: string, options?: {
  tradeId?: string,
  startDate?: string,
  endDate?: string,
  tags?: string[],
  searchTerm?: string,
  accountId?: string | null,
  initialData?: any[]
}) {
  const queryClient = useQueryClient()
  const queryKey = ['journalEntries', userId, options?.tradeId, options?.startDate, options?.endDate, options?.tags, options?.searchTerm, options?.accountId]

  useEffect(() => {
    if (userId) {
      queryClient.invalidateQueries({ queryKey: queryKey })
    }
  }, [userId, options?.accountId, options?.tradeId, options?.startDate, options?.endDate, options?.tags, options?.searchTerm, queryClient, queryKey])

  return useQuery({
    queryKey,
    queryFn: () => getJournalEntries(userId, options),
    enabled: !!userId,
    initialData: userId ? undefined : options?.initialData || [],
    staleTime: 1000 * 60 * 5, // 5 minutes
  })
}

export function useJournalEntry(userId: string, entryId: string | null, initialData: any = null) {
  const queryClient = useQueryClient()

  useEffect(() => {
    if (userId && entryId) {
      queryClient.invalidateQueries({ queryKey: ['journalEntry', userId, entryId] })
    }
  }, [userId, entryId, queryClient])

  return useQuery({
    queryKey: ['journalEntry', userId, entryId],
    queryFn: () => entryId ? getJournalEntryById(userId, entryId) : null,
    enabled: !!userId && !!entryId,
    initialData: (userId && entryId) ? undefined : initialData,
    staleTime: 1000 * 60 * 5, // 5 minutes
  })
}

// Daily journal hooks
export function useDailyJournalEntries(userId: string, options?: {
  accountId?: string,
  startDate?: string,
  endDate?: string,
  tags?: string[],
  initialData?: any[]
}) {
  const queryClient = useQueryClient()
  const queryKey = ['dailyJournalEntries', userId, options?.accountId, options?.startDate, options?.endDate, options?.tags]

  useEffect(() => {
    if (userId) {
      queryClient.invalidateQueries({ queryKey: queryKey })
    }
  }, [userId, options?.accountId, options?.startDate, options?.endDate, options?.tags, queryClient, queryKey])

  return useQuery({
    queryKey,
    queryFn: () => getDailyJournalEntries(userId, options),
    enabled: !!userId,
    initialData: userId ? undefined : options?.initialData || [],
    staleTime: 1000 * 60 * 5, // 5 minutes
  })
}

export function useDailyJournalEntry(userId: string, date: Date | null, accountId?: string, initialData: any = null) {
  const queryClient = useQueryClient()
  const dateStr = date ? date.toISOString() : null
  const queryKey = ['dailyJournalEntry', userId, dateStr, accountId]

  useEffect(() => {
    if (userId && date) {
      queryClient.invalidateQueries({ queryKey: queryKey })
    }
  }, [userId, dateStr, accountId, queryClient, queryKey])

  return useQuery({
    queryKey,
    queryFn: () => date ? getDailyJournalEntry(userId, date, accountId) : null,
    enabled: !!userId && !!date,
    initialData: (userId && date) ? undefined : initialData,
    staleTime: 1000 * 60 * 5, // 5 minutes
  })
}
