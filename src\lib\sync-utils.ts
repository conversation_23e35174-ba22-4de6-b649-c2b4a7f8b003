import { createServerClient } from '@supabase/ssr';
import type { Database } from '@/types/supabase';

/**
 * Synchronization utility functions for bidirectional sync between notebook and journal entries
 */

/**
 * Convert HTML content to plain text for trade journal display
 * Preserves paragraph spacing and line breaks
 */
function htmlToPlainText(html: string): string {
  if (!html) return '';

  // Convert HTML to plain text while preserving formatting
  return html
    .replace(/<\/p>\s*<p>/gi, '\n\n') // Convert paragraph breaks to double line breaks
    .replace(/<p[^>]*>/gi, '') // Remove opening paragraph tags
    .replace(/<\/p>/gi, '') // Remove closing paragraph tags
    .replace(/<br\s*\/?>/gi, '\n') // Convert <br> tags to line breaks
    .replace(/<\/div>\s*<div>/gi, '\n\n') // Convert div breaks to double line breaks
    .replace(/<div[^>]*>/gi, '') // Remove opening div tags
    .replace(/<\/div>/gi, '') // Remove closing div tags
    .replace(/<[^>]*>/g, '') // Remove any remaining HTML tags
    .replace(/&nbsp;/g, ' ') // Replace non-breaking spaces
    .replace(/&amp;/g, '&') // Replace HTML entities
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&quot;/g, '"')
    .replace(/&#39;/g, "'")
    .replace(/\n\s*\n\s*\n/g, '\n\n') // Normalize multiple line breaks to double
    .replace(/^\s+|\s+$/g, '') // Trim leading/trailing whitespace
    .replace(/[ \t]+/g, ' '); // Normalize spaces but preserve line breaks
}

/**
 * Convert plain text to basic HTML for notebook display
 * Preserves paragraph spacing and line breaks
 */
function plainTextToHtml(text: string): string {
  if (!text) return '';

  // Split text into paragraphs (separated by double line breaks)
  const paragraphs = text.split(/\n\s*\n/);

  return paragraphs
    .filter(paragraph => paragraph.trim()) // Only filter completely empty paragraphs
    .map(paragraph => {
      // Convert single line breaks within paragraphs to <br> tags
      const htmlContent = paragraph
        .trim()
        .split('\n')
        .map(line => line.trim())
        .filter(line => line) // Remove empty lines within paragraphs
        .join('<br>');

      return `<p>${htmlContent}</p>`;
    })
    .join('');
}

/**
 * Merge content intelligently - append new content to existing content
 */
function mergeContent(existingContent: string, newContent: string, isHtml: boolean = false): string {
  if (!existingContent) return newContent;
  if (!newContent) return existingContent;

  // Avoid duplicating identical content
  if (existingContent.includes(newContent) || newContent.includes(existingContent)) {
    return existingContent.length > newContent.length ? existingContent : newContent;
  }

  // Merge with separator
  const separator = isHtml ? '<br><br>' : '\n\n';
  return `${existingContent}${separator}${newContent}`;
}

export interface SyncData {
  html_content?: string;
  tags?: string[];
  screenshots?: string[];
}

export interface NotebookSyncData extends SyncData {
  linked_trade_ids?: string[];
  linked_strategy_ids?: string[];
  linked_daily_journal_ids?: string[];
  category?: string;
  title?: string;
}

/**
 * Sync notebook changes to linked trade records
 */
export async function syncNotebookToTrades(
  supabase: any,
  userId: string,
  tradeIds: string[],
  syncData: SyncData
): Promise<void> {
  if (!tradeIds || tradeIds.length === 0) return;

  console.log('Syncing notebook changes to trades:', { tradeIds, syncData });

  for (const tradeId of tradeIds) {
    try {
      console.log(`=== SYNCING TO TRADE ${tradeId} ===`);

      const updateData: any = {
        updated_at: new Date().toISOString()
      };

      // Map notebook fields to trade fields
      if (syncData.html_content !== undefined) {
        // Convert HTML content to plain text for trade journal
        const plainTextContent = htmlToPlainText(syncData.html_content);
        updateData.notes = plainTextContent;
        updateData.journal_content = syncData.html_content; // Keep original HTML for reference
        updateData.has_journal_content = syncData.html_content ? true : false;
        console.log(`Setting notes to: ${plainTextContent.substring(0, 100)}...`);
      }

      if (syncData.tags !== undefined) {
        updateData.tags = syncData.tags;
        console.log(`Setting tags to:`, syncData.tags);
      }

      if (syncData.screenshots !== undefined) {
        updateData.screenshots = syncData.screenshots;
        console.log(`Setting screenshots count:`, syncData.screenshots.length);
      }

      console.log(`Update data for trade ${tradeId}:`, {
        notes_length: updateData.notes?.length,
        tags_count: updateData.tags?.length,
        screenshots_count: updateData.screenshots?.length,
        has_journal_content: updateData.has_journal_content
      });

      const { data: updatedTrade, error } = await supabase
        .from('trades')
        .update(updateData)
        .eq('id', tradeId)
        .eq('user_id', userId)
        .select('id, notes, tags, screenshots');

      if (error) {
        console.error(`Error syncing to trade ${tradeId}:`, error);
        // Continue with other trades even if one fails
      } else {
        console.log(`Successfully synced to trade ${tradeId}`, updatedTrade);
      }
    } catch (error) {
      console.error(`Exception syncing to trade ${tradeId}:`, error);
    }
  }
}

/**
 * Sync notebook changes to daily journal entries
 * Strategy IDs in notebook link to trades, then we find related daily journal entries
 */
export async function syncNotebookToDailyJournal(
  supabase: any,
  userId: string,
  strategyIds: string[],
  syncData: SyncData
): Promise<void> {
  if (!strategyIds || strategyIds.length === 0) return;

  console.log('Syncing notebook changes to daily journal entries via strategy IDs:', { strategyIds, syncData });

  for (const strategyId of strategyIds) {
    try {
      // First, find trades that belong to this strategy
      const { data: trades, error: tradesError } = await supabase
        .from('trades')
        .select('id, time_open, time_close')
        .eq('strategy_id', strategyId)
        .eq('user_id', userId);

      if (tradesError) {
        console.error(`Error finding trades for strategy ${strategyId}:`, tradesError);
        continue;
      }

      if (!trades || trades.length === 0) {
        console.log(`No trades found for strategy ${strategyId}`);
        continue;
      }

      console.log(`Found ${trades.length} trades for strategy ${strategyId}`);

      // For each trade, find daily journal entries on the same dates
      for (const trade of trades) {
        const tradeDate = new Date(trade.time_open).toISOString().split('T')[0];

        const updateData: any = {
          updated_at: new Date().toISOString()
        };

        // Map notebook fields to daily journal fields
        if (syncData.html_content !== undefined) {
          updateData.note = syncData.html_content;
        }

        if (syncData.tags !== undefined) {
          updateData.tags = syncData.tags;
        }

        if (syncData.screenshots !== undefined) {
          updateData.screenshots = syncData.screenshots;
        }

        // Update daily journal entries for this date
        const { data: updatedEntries, error } = await supabase
          .from('daily_journal_entries')
          .update(updateData)
          .eq('date', tradeDate)
          .eq('user_id', userId)
          .select('id');

        if (error) {
          console.error(`Error syncing to daily journal for date ${tradeDate}:`, error);
        } else if (updatedEntries && updatedEntries.length > 0) {
          console.log(`Successfully synced to ${updatedEntries.length} daily journal entries for date ${tradeDate}`);
        } else {
          console.log(`No daily journal entries found for date ${tradeDate}`);
        }
      }
    } catch (error) {
      console.error(`Exception syncing strategy ${strategyId} to daily journal:`, error);
    }
  }
}

/**
 * Sync notebook changes to daily journal entries using direct linking
 * Uses linked_daily_journal_ids for proper bidirectional sync
 */
export async function syncNotebookToDailyJournalEntries(
  supabase: any,
  userId: string,
  dailyJournalIds: string[],
  syncData: SyncData
): Promise<void> {
  if (!dailyJournalIds || dailyJournalIds.length === 0) return;

  console.log('Syncing notebook changes to daily journal entries:', { dailyJournalIds, syncData });

  for (const dailyJournalId of dailyJournalIds) {
    try {
      const updateData: any = {
        updated_at: new Date().toISOString()
      };

      // Map notebook fields to daily journal fields
      if (syncData.html_content !== undefined) {
        updateData.note = syncData.html_content;
      }

      if (syncData.tags !== undefined) {
        updateData.tags = syncData.tags;
      }

      if (syncData.screenshots !== undefined) {
        updateData.screenshots = syncData.screenshots;
      }

      // Update the specific daily journal entry
      const { data: updatedEntry, error } = await supabase
        .from('daily_journal_entries')
        .update(updateData)
        .eq('id', dailyJournalId)
        .eq('user_id', userId)
        .select('id, date')
        .single();

      if (error) {
        console.error(`Error syncing to daily journal ${dailyJournalId}:`, error);
      } else if (updatedEntry) {
        console.log(`Successfully synced to daily journal entry ${dailyJournalId} for date ${updatedEntry.date}`);
      }
    } catch (error) {
      console.error(`Exception syncing to daily journal ${dailyJournalId}:`, error);
    }
  }
}

/**
 * Sync trade changes back to linked notebook entries
 */
export async function syncTradeToNotebook(
  supabase: any,
  userId: string,
  tradeId: string,
  syncData: SyncData
): Promise<void> {
  console.log('Syncing trade changes to notebook entries:', { tradeId, syncData });

  try {
    // Find notebook entries linked to this trade using a different approach
    const { data: allEntries, error: fetchError } = await supabase
      .from('notebook_entries')
      .select('id, linked_trade_ids, html_content, tags, screenshots')
      .eq('user_id', userId)
      .not('linked_trade_ids', 'is', null);

    if (fetchError) {
      console.error('Error fetching notebook entries:', fetchError);
      return;
    }

    // Filter entries that contain the trade ID
    const notebookEntries = allEntries?.filter((entry: any) =>
      entry.linked_trade_ids && entry.linked_trade_ids.includes(tradeId)
    ) || [];

    if (!notebookEntries || notebookEntries.length === 0) {
      console.log('No linked notebook entries found for trade:', tradeId);
      return;
    }

    // Update each linked notebook entry
    for (const entry of notebookEntries) {
      const updateData: any = {
        updated_at: new Date().toISOString()
      };

      // Map trade fields to notebook fields with intelligent merging
      if (syncData.html_content !== undefined) {
        // Convert plain text from trade to HTML for notebook
        const htmlContent = plainTextToHtml(syncData.html_content);
        // Merge with existing content instead of replacing
        updateData.html_content = mergeContent(entry.html_content || '', htmlContent, true);
        console.log(`Merging trade content into notebook entry ${entry.id}`);
      }

      if (syncData.tags !== undefined) {
        // Merge tags instead of replacing
        const existingTags = entry.tags || [];
        const newTags = syncData.tags || [];
        const mergedTags = [...new Set([...existingTags, ...newTags])]; // Remove duplicates
        updateData.tags = mergedTags;
        console.log(`Merging tags: ${existingTags.length} existing + ${newTags.length} new = ${mergedTags.length} total`);
      }

      if (syncData.screenshots !== undefined) {
        // Merge screenshots instead of replacing
        const existingScreenshots = entry.screenshots || [];
        const newScreenshots = syncData.screenshots || [];
        const mergedScreenshots = [...new Set([...existingScreenshots, ...newScreenshots])]; // Remove duplicates
        updateData.screenshots = mergedScreenshots;
        console.log(`Merging screenshots: ${existingScreenshots.length} existing + ${newScreenshots.length} new = ${mergedScreenshots.length} total`);
      }

      const { error } = await supabase
        .from('notebook_entries')
        .update(updateData)
        .eq('id', entry.id)
        .eq('user_id', userId);

      if (error) {
        console.error(`Error syncing to notebook entry ${entry.id}:`, error);
      } else {
        console.log(`Successfully synced to notebook entry ${entry.id}`);
      }
    }
  } catch (error) {
    console.error('Exception in syncTradeToNotebook:', error);
  }
}

/**
 * Sync daily journal changes back to linked notebook entries
 */
export async function syncDailyJournalToNotebook(
  supabase: any,
  userId: string,
  dailyJournalId: string,
  syncData: SyncData
): Promise<void> {
  console.log('Syncing daily journal changes to notebook entries:', { dailyJournalId, syncData });

  try {
    // Find notebook entries that link to this daily journal entry using the new field
    const { data: notebookEntries, error: fetchError } = await supabase
      .from('notebook_entries')
      .select('id, title, linked_daily_journal_ids')
      .eq('user_id', userId)
      .contains('linked_daily_journal_ids', [dailyJournalId]);

    if (fetchError) {
      console.error('Error fetching notebook entries:', fetchError);
      return;
    }

    if (!notebookEntries || notebookEntries.length === 0) {
      console.log('No linked notebook entries found for daily journal:', dailyJournalId);
      return;
    }

    // Update each linked notebook entry
    for (const entry of notebookEntries) {
      const updateData: any = {
        updated_at: new Date().toISOString()
      };

      // Map daily journal fields to notebook fields
      if (syncData.html_content !== undefined) {
        updateData.html_content = syncData.html_content;
      }

      if (syncData.tags !== undefined) {
        updateData.tags = syncData.tags;
      }

      if (syncData.screenshots !== undefined) {
        updateData.screenshots = syncData.screenshots;
      }

      const { error } = await supabase
        .from('notebook_entries')
        .update(updateData)
        .eq('id', entry.id)
        .eq('user_id', userId);

      if (error) {
        console.error(`Error syncing to notebook entry ${entry.id}:`, error);
      } else {
        console.log(`Successfully synced to notebook entry ${entry.id}`);
      }
    }
  } catch (error) {
    console.error('Exception in syncDailyJournalToNotebook:', error);
  }
}

/**
 * Handle complete notebook synchronization (both directions)
 */
export async function handleNotebookSync(
  supabase: any,
  userId: string,
  notebookData: NotebookSyncData
): Promise<void> {
  const syncData: SyncData = {
    html_content: notebookData.html_content,
    tags: notebookData.tags,
    screenshots: notebookData.screenshots
  };

  // Sync to linked trades
  if (notebookData.linked_trade_ids && notebookData.linked_trade_ids.length > 0) {
    await syncNotebookToTrades(supabase, userId, notebookData.linked_trade_ids, syncData);
  }

  // Sync to linked daily journal entries (new method)
  if (notebookData.linked_daily_journal_ids && notebookData.linked_daily_journal_ids.length > 0) {
    await syncNotebookToDailyJournalEntries(supabase, userId, notebookData.linked_daily_journal_ids, syncData);
  }

  // Sync to linked daily journal entries (legacy method for backward compatibility)
  if (notebookData.linked_strategy_ids && notebookData.linked_strategy_ids.length > 0) {
    await syncNotebookToDailyJournal(supabase, userId, notebookData.linked_strategy_ids, syncData);
  }

  // Handle legacy Daily Journal category sync (for backward compatibility)
  if (notebookData.category === 'Daily Journal' && notebookData.title) {
    const dateMatch = notebookData.title.match(/Daily Journal - (\d{1,2}\/\d{1,2}\/\d{4})/);
    if (dateMatch && dateMatch[1]) {
      try {
        const journalDate = new Date(dateMatch[1]);
        const dateString = journalDate.toISOString().split('T')[0];

        const updateData: any = {
          updated_at: new Date().toISOString()
        };

        if (syncData.html_content !== undefined) {
          updateData.note = syncData.html_content;
        }

        if (syncData.tags !== undefined) {
          updateData.tags = syncData.tags;
        }

        if (syncData.screenshots !== undefined) {
          updateData.screenshots = syncData.screenshots;
        }

        await supabase
          .from('daily_journal_entries')
          .update(updateData)
          .eq('user_id', userId)
          .eq('date', dateString);

        console.log(`Legacy sync to daily journal for date: ${dateString}`);
      } catch (error) {
        console.error('Error in legacy daily journal sync:', error);
      }
    }
  }
}
