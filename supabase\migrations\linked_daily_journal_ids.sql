-- Add linked_daily_journal_ids column to notebook_entries table
-- This will properly link notebook entries to daily journal entries for bidirectional sync

DO $$
BEGIN
    -- Check if linked_daily_journal_ids column exists, if not add it
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_schema = 'public'
        AND table_name = 'notebook_entries'
        AND column_name = 'linked_daily_journal_ids'
    ) THEN
        ALTER TABLE public.notebook_entries 
        ADD COLUMN linked_daily_journal_ids UUID[] DEFAULT '{}';
        
        RAISE NOTICE 'Added linked_daily_journal_ids column to notebook_entries table';
    ELSE
        RAISE NOTICE 'linked_daily_journal_ids column already exists in notebook_entries table';
    END IF;
END
$$;

-- Create index for faster queries on linked_daily_journal_ids
CREATE INDEX IF NOT EXISTS idx_notebook_entries_linked_daily_journal_ids 
ON public.notebook_entries USING GIN (linked_daily_journal_ids);

-- Add comment to document the purpose of this column
COMMENT ON COLUMN public.notebook_entries.linked_daily_journal_ids IS 
'Array of daily_journal_entries.id values for bidirectional synchronization between notebook entries and daily journal entries';

-- Create function to sync notebook changes to linked daily journal entries
CREATE OR REPLACE FUNCTION public.sync_notebook_to_daily_journals()
RETURNS TRIGGER AS $$
DECLARE
    daily_journal_id UUID;
BEGIN
    -- Only process if linked_daily_journal_ids has values
    IF NEW.linked_daily_journal_ids IS NOT NULL AND array_length(NEW.linked_daily_journal_ids, 1) > 0 THEN
        -- Loop through each linked daily journal ID
        FOREACH daily_journal_id IN ARRAY NEW.linked_daily_journal_ids
        LOOP
            -- Update the corresponding daily journal entry
            UPDATE public.daily_journal_entries
            SET 
                note = NEW.html_content,
                tags = NEW.tags,
                screenshots = NEW.screenshots,
                updated_at = NOW()
            WHERE id = daily_journal_id 
            AND user_id = NEW.user_id;
            
            RAISE NOTICE 'Synced notebook entry % to daily journal %', NEW.id, daily_journal_id;
        END LOOP;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create function to sync daily journal changes to linked notebook entries
CREATE OR REPLACE FUNCTION public.sync_daily_journal_to_notebooks()
RETURNS TRIGGER AS $$
DECLARE
    notebook_entry RECORD;
BEGIN
    -- Find all notebook entries that link to this daily journal entry
    FOR notebook_entry IN 
        SELECT id, linked_daily_journal_ids
        FROM public.notebook_entries
        WHERE user_id = NEW.user_id
        AND NEW.id = ANY(linked_daily_journal_ids)
    LOOP
        -- Update the notebook entry with daily journal content
        UPDATE public.notebook_entries
        SET 
            html_content = NEW.note,
            tags = NEW.tags,
            screenshots = NEW.screenshots,
            updated_at = NOW()
        WHERE id = notebook_entry.id;
        
        RAISE NOTICE 'Synced daily journal % to notebook entry %', NEW.id, notebook_entry.id;
    END LOOP;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for bidirectional sync
-- Trigger for notebook -> daily journal sync
DROP TRIGGER IF EXISTS trigger_sync_notebook_to_daily_journals ON public.notebook_entries;
CREATE TRIGGER trigger_sync_notebook_to_daily_journals
    AFTER UPDATE ON public.notebook_entries
    FOR EACH ROW
    WHEN (OLD.html_content IS DISTINCT FROM NEW.html_content 
          OR OLD.tags IS DISTINCT FROM NEW.tags 
          OR OLD.screenshots IS DISTINCT FROM NEW.screenshots)
    EXECUTE FUNCTION public.sync_notebook_to_daily_journals();

-- Trigger for daily journal -> notebook sync
DROP TRIGGER IF EXISTS trigger_sync_daily_journal_to_notebooks ON public.daily_journal_entries;
CREATE TRIGGER trigger_sync_daily_journal_to_notebooks
    AFTER UPDATE ON public.daily_journal_entries
    FOR EACH ROW
    WHEN (OLD.note IS DISTINCT FROM NEW.note 
          OR OLD.tags IS DISTINCT FROM NEW.tags 
          OR OLD.screenshots IS DISTINCT FROM NEW.screenshots)
    EXECUTE FUNCTION public.sync_daily_journal_to_notebooks();

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT SELECT, UPDATE ON public.notebook_entries TO authenticated;
GRANT SELECT, UPDATE ON public.daily_journal_entries TO authenticated;