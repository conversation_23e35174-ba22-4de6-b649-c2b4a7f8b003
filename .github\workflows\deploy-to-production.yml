name: Deploy to Production Repository (Coolify)

on:
  workflow_dispatch:
    inputs:
      deployment_type:
        description: 'Type of deployment'
        required: true
        default: 'production-release'
        type: choice
        options:
        - production-release
        - hotfix-deployment
        - backup-sync
        - client-delivery
      deployment_target:
        description: 'Deployment target'
        required: true
        default: 'coolify-production'
        type: choice
        options:
        - coolify-production
        - repository-only
      force_push:
        description: 'Force push to production repository (use with caution)'
        required: false
        default: false
        type: boolean
      commit_message:
        description: 'Custom commit message (optional)'
        required: false
        default: ''
        type: string


# Ensure workflow only runs on master branch
jobs:
  validate-branch:
    runs-on: ubuntu-latest
    outputs:
      is-master: ${{ steps.check-branch.outputs.is-master }}
    steps:
      - name: Check if running on master branch
        id: check-branch
        run: |
          if [ "${{ github.ref }}" = "refs/heads/master" ]; then
            echo "is-master=true" >> $GITHUB_OUTPUT
            echo "✅ Running on master branch"
          else
            echo "is-master=false" >> $GITHUB_OUTPUT
            echo "❌ This workflow can only be run on the master branch"
            echo "Current branch: ${{ github.ref }}"
            exit 1
          fi

  deploy-to-production:
    needs: validate-branch
    if: needs.validate-branch.outputs.is-master == 'true'
    runs-on: ubuntu-latest
    
    env:
      PRODUCTION_REPO_URL: ${{ secrets.PRODUCTION_REPO_URL }}
      PRODUCTION_TOKEN: ${{ secrets.PRODUCTION_REPO_TOKEN }}
      
    steps:
      - name: 🔍 Validate Required Secrets
        run: |
          if [ -z "${{ secrets.PRODUCTION_REPO_URL }}" ]; then
            echo "❌ PRODUCTION_REPO_URL secret is not set"
            exit 1
          fi
          if [ -z "${{ secrets.PRODUCTION_REPO_TOKEN }}" ]; then
            echo "❌ PRODUCTION_REPO_TOKEN secret is not set"
            exit 1
          fi
          echo "✅ All required secrets are configured"

      - name: 📥 Checkout Source Repository
        uses: actions/checkout@v4
        with:
          ref: master
          fetch-depth: 0
          token: ${{ github.token }}

      - name: ✅ Skip Build Process
        run: |
          echo "✅ Skipping build process - Coolify will handle dependency installation and building"
          echo "📋 GitHub Actions Role: File filtering and repository sync only"
          echo "🐳 Coolify Role: Dependency installation, building, and deployment"
          echo ""
          echo "This is the correct approach for Coolify deployment:"
          echo "1. GitHub Actions: Clean and sync source files"
          echo "2. Coolify: Install dependencies, build, and deploy"

      - name: 🧹 Create Production Copy
        run: |
          echo "🧹 Creating clean production copy..."
          
          # Create temporary directory for production files
          mkdir -p /tmp/production-sync
          
          # Copy all files first
          cp -r . /tmp/production-sync/
          
          # Navigate to production directory
          cd /tmp/production-sync
          
          echo "🗑️ Removing development and test files..."
          
          # Remove test files and directories
          find . -name "*.test.*" -type f -delete
          find . -name "*.spec.*" -type f -delete
          find . -name "__tests__" -type d -exec rm -rf {} + 2>/dev/null || true
          find . -name "coverage" -type d -exec rm -rf {} + 2>/dev/null || true
          find . -name "*.test" -type d -exec rm -rf {} + 2>/dev/null || true
          
          # Remove documentation files
          find . -name "*.md" -type f -delete
          find . -name "README*" -type f -delete
          find . -name "CHANGELOG*" -type f -delete
          find . -name "CONTRIBUTING*" -type f -delete
          find . -name "LICENSE*" -type f -delete
          find . -name "docs" -type d -exec rm -rf {} + 2>/dev/null || true
          
          # Remove development configuration
          rm -f .env.local .env.development .env.test
          rm -rf .vscode .idea
          rm -f .eslintrc* .prettierrc* .editorconfig
          rm -f jest.config.* vitest.config.* cypress.json

          # Remove all pnpm traces to prevent package manager conflicts
          echo "🔧 Removing all pnpm traces to ensure npm usage in Coolify..."
          rm -f pnpm-lock.yaml
          rm -f .pnpmfile.cjs
          rm -f .pnpm-workspace.yaml
          rm -f pnpm-workspace.yaml

          # Disable corepack to prevent pnpm auto-detection
          echo "🚫 Creating corepack disable configuration..."
          echo "false" > .corepack-disable

          # Ensure package-lock.json exists for npm
          if [ ! -f "package-lock.json" ]; then
            echo "⚠️ package-lock.json not found, this may cause dependency issues"
            echo "💡 Consider running 'npm install' locally to generate package-lock.json"
          else
            echo "✅ package-lock.json found - npm will be used for dependency installation"
          fi
          
          # Remove build artifacts and dependencies (but keep package-lock.json for npm)
          rm -rf node_modules .next dist build out
          rm -f yarn.lock
          
          # Remove git directory but keep .gitignore
          rm -rf .git
          
          # Remove GitHub workflows and other CI/CD files
          rm -rf .github
          rm -f .travis.yml .circleci appveyor.yml
          
          # Remove other development files
          rm -f .nvmrc .node-version
          rm -f docker-compose.yml docker-compose.*.yml
          rm -f Dockerfile.dev
          
          # Remove any log files
          find . -name "*.log" -type f -delete
          find . -name "logs" -type d -exec rm -rf {} + 2>/dev/null || true
          
          echo "✅ Production copy created successfully"

          # Show what files remain
          echo "📋 Production files summary:"
          find . -type f | head -20
          total_files=$(find . -type f | wc -l)
          echo "📊 Total files in production copy: $total_files"

      - name: 🔄 Sync to Production Repository
        run: |
          cd /tmp/production-sync

          echo "🔄 Initializing production repository sync..."

          # Configure git
          git config --global user.name "GitHub Actions"
          git config --global user.email "<EMAIL>"
          git config --global init.defaultBranch main

          # Initialize git repository
          git init

          # Add production repository as remote with authentication
          echo "🔗 Setting up production repository remote..."
          echo "Repository URL: $PRODUCTION_REPO_URL"

          # Construct authenticated URL properly
          REPO_URL_WITH_TOKEN="https://$<EMAIL>/shawa0507/trade_journal_production.git"
          echo "✅ Authenticated URL constructed"

          git remote add production "$REPO_URL_WITH_TOKEN"
          echo "✅ Production remote added"

          # Create .gitignore for production
          cat > .gitignore << 'EOF'
          # Dependencies
          /node_modules
          /.pnp
          .pnp.js

          # Testing
          /coverage

          # Next.js
          /.next/
          /out/

          # Production
          /build

          # Misc
          .DS_Store
          *.pem

          # Debug
          npm-debug.log*
          yarn-debug.log*
          yarn-error.log*

          # Local env files
          .env*.local

          # Vercel
          .vercel

          # TypeScript
          *.tsbuildinfo
          next-env.d.ts
          EOF

          # Create Coolify deployment configuration files
          echo "📝 Creating Coolify deployment configurations..."

          # Create bulletproof Nixpacks configuration that forces npm
          echo "📦 Creating bulletproof Nixpacks configuration for npm..."

          # Create AGGRESSIVE nixpacks.toml that FORCES npm usage
          cat > nixpacks.toml << 'EOF'
          providers = ["node"]

          [variables]
          NODE_ENV = "production"
          NIXPACKS_NO_CACHE = "1"
          NPM_CONFIG_PACKAGE_MANAGER = "npm"
          COREPACK_ENABLE_STRICT = "0"

          [phases.setup]
          nixPkgs = ["nodejs_18"]
          cmds = [
            "echo 'Setting up npm environment'",
            "rm -f pnpm-lock.yaml yarn.lock .pnpmfile.cjs || true",
            "npm --version"
          ]

          [phases.install]
          cmd = "npm install --no-fund --no-audit"

          [phases.build]
          cmd = "npm run build"

          [start]
          cmd = "npm start"
          EOF
          echo "✅ Created nixpacks.toml with npm enforcement"

          # ENHANCED NIXPACKS: Use repository nixpacks.toml with npm enforcement
          echo "🔧 ENHANCED NIXPACKS: Using repository nixpacks.toml with aggressive npm enforcement"

          # Ensure nixpacks.toml exists (should be copied from repository)
          if [ ! -f "nixpacks.toml" ]; then
            echo "⚠️ nixpacks.toml not found, creating enhanced version..."
            cat > nixpacks.toml << 'EOF'
          providers = ["node"]

          [variables]
          NODE_ENV = "production"
          NIXPACKS_NO_CACHE = "1"
          NPM_CONFIG_PACKAGE_MANAGER = "npm"
          COREPACK_ENABLE_STRICT = "0"
          NIXPACKS_NODE_VERSION = "18"

          [phases.setup]
          nixPkgs = ["nodejs_18"]
          cmds = [
            "echo 'Setting up npm environment'",
            "rm -f pnpm-lock.yaml yarn.lock .pnpmfile.cjs || true",
            "npm --version"
          ]

          [phases.install]
          cmd = "npm install --no-fund --no-audit"

          [phases.build]
          cmd = "npm run build"

          [start]
          cmd = "npm start"
          EOF
          fi

          echo "✅ Enhanced nixpacks.toml configuration ready for Nixpacks deployment"

          # Create .dockerignore for clean builds
          echo "📝 Creating .dockerignore for optimized Docker builds..."
          cat > .dockerignore << 'EOF'
          # Node modules and logs
          node_modules
          npm-debug.log*
          yarn-debug.log*
          yarn-error.log*
          pnpm-debug.log*

          # PNPM files (we're using npm)
          pnpm-lock.yaml
          .pnpm-debug.log*
          .pnpmfile.cjs

          # Build outputs
          .next/
          out/
          dist/
          build/

          # Environment files
          .env
          .env.development.local
          .env.test.local

          # Git and documentation
          .git
          .gitignore
          README.md
          *.md
          docs/

          # IDE files
          .vscode/
          .idea/

          # OS files
          .DS_Store
          Thumbs.db

          # Test files
          coverage/
          test/
          tests/
          __tests__/

          # Logs
          logs
          *.log
          EOF
          echo "✅ Created .dockerignore for clean builds"

          # Create package-manager detection override
          echo "📝 Creating package manager detection files..."

          # Create .nvmrc to ensure Node 18
          echo "18" > .nvmrc

          # Create engines specification in package.json to force npm
          if [ -f "package.json" ]; then
            # Backup original package.json
            cp package.json package.json.backup

            # Use Node.js to modify package.json more reliably
            node -e "
              const fs = require('fs');
              const pkg = JSON.parse(fs.readFileSync('package.json', 'utf8'));

              // Force npm as package manager
              pkg.packageManager = 'npm@latest';

              // Set engines to require npm
              if (!pkg.engines) pkg.engines = {};
              pkg.engines.node = '>=18.0.0';
              pkg.engines.npm = '>=8.0.0';

              // Remove any pnpm configurations
              delete pkg.pnpm;
              delete pkg.packageManager;

              // Add npm-specific scripts if needed
              if (!pkg.scripts.postinstall) {
                pkg.scripts.postinstall = 'echo \"Using npm for package management\"';
              }

              fs.writeFileSync('package.json', JSON.stringify(pkg, null, 2));
              console.log('✅ Modified package.json for npm enforcement');
            " || echo "⚠️ Could not modify package.json with Node.js"
          fi

          # Create .npmrc to ensure npm is used consistently
          cat > .npmrc << 'EOF'
          # Force npm usage and disable warnings
          package-lock=true
          fund=false
          audit=false
          save-exact=true
          EOF
          echo "✅ Created .npmrc for npm configuration"

          # Modify package.json to explicitly disable corepack and set npm as package manager
          echo "📝 Modifying package.json to force npm usage..."
          if [ -f "package.json" ]; then
            # Use Node.js to modify package.json and add packageManager field
            node -e "
              const fs = require('fs');
              const pkg = JSON.parse(fs.readFileSync('package.json', 'utf8'));
              pkg.packageManager = 'npm@latest';
              if (!pkg.engines) pkg.engines = {};
              pkg.engines.npm = '>=8.0.0';
              delete pkg.pnpm;
              fs.writeFileSync('package.json', JSON.stringify(pkg, null, 2));
            "
            echo "✅ Modified package.json to force npm usage"
          fi

          # Create Nixpacks environment configuration
          if [ "${{ github.event.inputs.deployment_target }}" = "coolify-production" ]; then
            echo "📦 Creating Nixpacks environment configuration..."

            # Create .env.production for production environment variables
            cat > .env.production << 'EOF'
          # Production environment configuration
          NODE_ENV=production
          NEXT_TELEMETRY_DISABLED=1
          PORT=3000
          EOF
            echo "✅ Created .env.production"

            # Remove any Docker files to ensure Nixpacks detection
            rm -f Dockerfile docker-compose.yml .dockerignore
            echo "🚫 Removed Docker files to ensure Nixpacks detection"

            # Ensure nixpacks.toml is present and correct
            if [ -f "nixpacks.toml" ]; then
              echo "✅ nixpacks.toml is present for Nixpacks configuration"
            else
              echo "⚠️ nixpacks.toml missing - this may cause issues"
            fi

            echo "📦 Coolify will use Nixpacks with npm enforcement"
            echo "🔧 Using simplified npm-only configuration"
          fi

          # Add all production files
          git add .

          # Create commit message
          if [ -n "${{ github.event.inputs.commit_message }}" ]; then
            COMMIT_MSG="${{ github.event.inputs.commit_message }}"
          else
            DEPLOYMENT_TYPE="${{ github.event.inputs.deployment_type }}"
            COMMIT_MSG="$DEPLOYMENT_TYPE: TradePivot deployment - $(date '+%Y-%m-%d %H:%M:%S UTC')"
          fi

          # Add commit details
          FULL_COMMIT_MSG="$COMMIT_MSG

          Deployment Type: ${{ github.event.inputs.deployment_type }}
          Deployment Target: ${{ github.event.inputs.deployment_target }}
          Source: ${{ github.repository }}@${{ github.sha }}
          Workflow: ${{ github.workflow }}
          Run ID: ${{ github.run_id }}
          Triggered by: ${{ github.actor }}

          This is a manual Coolify deployment containing only production-necessary files.
          Development files, tests, and documentation have been excluded.
          Includes simplified Nixpacks configuration with npm enforcement.

          Note: Dependencies and building are handled by Coolify using Nixpacks with npm, not GitHub Actions."

          git commit -m "$FULL_COMMIT_MSG"

          echo "✅ Production commit created"

      - name: 🚀 Push to Production Repository
        run: |
          cd /tmp/production-sync

          echo "🚀 Pushing to production repository..."

          # Fetch existing branches to check if main exists
          git fetch production 2>/dev/null || echo "Production repository might be empty"

          # Check if we should force push
          if [ "${{ github.event.inputs.force_push }}" = "true" ]; then
            echo "⚠️ Force pushing to production repository..."
            git push production main --force
          else
            # Try normal push first
            if git push production main 2>/dev/null; then
              echo "✅ Successfully pushed to production repository"
            else
              echo "⚠️ Normal push failed, attempting force push..."
              echo "This might happen if the production repository has diverged"
              git push production main --force
            fi
          fi

          echo "🎉 Production deployment completed successfully!"

      - name: 📊 Deployment Summary
        if: always()
        run: |
          echo "## 🎉 Coolify Deployment Summary" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "- **Deployment Type**: ${{ github.event.inputs.deployment_type }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Deployment Target**: ${{ github.event.inputs.deployment_target }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Source Repository**: ${{ github.repository }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Source Branch**: ${{ github.ref_name }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Source Commit**: ${{ github.sha }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Triggered By**: ${{ github.actor }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Workflow Run**: [${{ github.run_id }}](${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }})" >> $GITHUB_STEP_SUMMARY
          echo "- **Timestamp**: $(date '+%Y-%m-%d %H:%M:%S UTC')" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY

          if [ "${{ job.status }}" = "success" ]; then
            echo "✅ **Status**: Deployment completed successfully" >> $GITHUB_STEP_SUMMARY
            echo "" >> $GITHUB_STEP_SUMMARY
            echo "### 🎯 What was deployed:" >> $GITHUB_STEP_SUMMARY
            echo "- ✅ Next.js application source code" >> $GITHUB_STEP_SUMMARY
            echo "- ✅ Production configuration files" >> $GITHUB_STEP_SUMMARY
            echo "- ✅ Supabase migrations and functions" >> $GITHUB_STEP_SUMMARY
            echo "- ✅ UI components and styling" >> $GITHUB_STEP_SUMMARY

            # Coolify-specific configurations
            if [ "${{ github.event.inputs.deployment_target }}" = "coolify-production" ]; then
              echo "- ✅ Simplified Nixpacks configuration (nixpacks.toml)" >> $GITHUB_STEP_SUMMARY
              echo "- ✅ NPM enforcement (.npmrc and package.json)" >> $GITHUB_STEP_SUMMARY
              echo "- ✅ Node version specification (.nvmrc)" >> $GITHUB_STEP_SUMMARY
              echo "- ✅ Production environment (.env.production)" >> $GITHUB_STEP_SUMMARY
              echo "- ✅ Health check API endpoint (/api/health)" >> $GITHUB_STEP_SUMMARY
            fi

            echo "" >> $GITHUB_STEP_SUMMARY
            echo "### 🗑️ What was excluded:" >> $GITHUB_STEP_SUMMARY
            echo "- ❌ Test files and coverage reports" >> $GITHUB_STEP_SUMMARY
            echo "- ❌ Documentation and README files" >> $GITHUB_STEP_SUMMARY
            echo "- ❌ Development environment files" >> $GITHUB_STEP_SUMMARY
            echo "- ❌ Build artifacts and dependencies" >> $GITHUB_STEP_SUMMARY
            echo "- ❌ IDE configuration files" >> $GITHUB_STEP_SUMMARY
            echo "" >> $GITHUB_STEP_SUMMARY
            echo "### 🚀 Expected Auto-Deployment:" >> $GITHUB_STEP_SUMMARY

            if [ "${{ github.event.inputs.deployment_target }}" = "coolify-production" ]; then
              echo "- 🟢 **Coolify**: Will auto-deploy from production repository" >> $GITHUB_STEP_SUMMARY
              echo "- 📦 **Nixpacks**: Simplified configuration with npm enforcement" >> $GITHUB_STEP_SUMMARY
              echo "- 🔄 **Auto-restart**: Service will restart automatically on failure" >> $GITHUB_STEP_SUMMARY
              echo "- 💚 **Health Checks**: Built-in health monitoring via /api/health" >> $GITHUB_STEP_SUMMARY
              echo "- ✅ **NPM Only**: Clean npm-only build process" >> $GITHUB_STEP_SUMMARY
            else
              echo "- 📁 **Repository Only**: Files synced without deployment configuration" >> $GITHUB_STEP_SUMMARY
            fi
          else
            echo "❌ **Status**: Deployment failed" >> $GITHUB_STEP_SUMMARY
            echo "" >> $GITHUB_STEP_SUMMARY
            echo "Please check the workflow logs for details." >> $GITHUB_STEP_SUMMARY
          fi

  cleanup:
    needs: [validate-branch, deploy-to-production]
    if: always()
    runs-on: ubuntu-latest
    steps:
      - name: 🧹 Cleanup
        run: |
          echo "🧹 Cleaning up temporary files..."
          rm -rf /tmp/production-sync 2>/dev/null || true
          echo "✅ Cleanup completed"
