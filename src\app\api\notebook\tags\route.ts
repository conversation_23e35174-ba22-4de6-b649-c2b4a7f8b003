import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { createServerClient } from '@supabase/ssr';
import type { Database } from '@/types/supabase';
import { NotebookTag } from '@/types/notebook';

// GET handler for fetching notebook tags
export async function GET(request: NextRequest) {
  try {
    // Get cookies for server-side Supabase client
    const cookieStore = await cookies();
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
          set(_name: string, _value: string, _options: any) {
            // API routes can't set cookies directly
          },
          remove(_name: string, _options: any) {
            // API routes can't remove cookies directly
          }
        },
      }
    );

    // Get authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get account filter from query parameters
    const url = new URL(request.url);
    const accountIdParam = url.searchParams.get('accountId');

    // Convert empty string back to null, handle undefined
    const accountId = accountIdParam === '' ? null : accountIdParam;

    // Get all tags from user's entries with account filtering
    let tagsQuery = supabase
      .from('notebook_entries')
      .select('tags')
      .eq('user_id', user.id);

    // Apply account filtering if specified
    if (accountIdParam !== null) { // If accountId parameter was provided (even if empty)
      if (accountId === null) {
        tagsQuery = tagsQuery.is('account_id', null);
      } else {
        tagsQuery = tagsQuery.eq('account_id', accountId);
      }
    }

    const { data: tagsData, error: tagsError } = await tagsQuery;

    if (tagsError) {
      console.error('Error fetching tags:', tagsError);
      return NextResponse.json({ error: tagsError.message }, { status: 500 });
    }

    // Count occurrences of each tag
    const tagCounts: Record<string, number> = {};
    tagsData?.forEach(entry => {
      if (entry.tags && Array.isArray(entry.tags)) {
        entry.tags.forEach(tag => {
          tagCounts[tag] = (tagCounts[tag] || 0) + 1;
        });
      }
    });

    // Convert to array of tag objects
    const tags: NotebookTag[] = Object.entries(tagCounts).map(([name, count]) => ({
      name,
      count
    }));

    return NextResponse.json(tags);
  } catch (error) {
    console.error('Error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
