"use client"

import { useState, useMemo } from "react"
import { Trade } from "@/types/trade"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { BarChart3, DollarSign, Scale } from "lucide-react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  BarChart, Bar, XAxis, YAxis, CartesianGrid,
  Tooltip, Legend, ResponsiveContainer, Cell, LineChart, Line, ReferenceLine
} from "recharts"
import { format } from "date-fns"
import { cn } from "@/lib/utils"

interface RiskManagementMetricsProps {
  trades: Trade[]
}

type ChartType = "riskReward" | "expectancy" | "rMultiple"

export function RiskManagementMetrics({ trades }: RiskManagementMetricsProps) {
  const [chartType, setChartType] = useState<ChartType>("riskReward")
  const [timeRange, setTimeRange] = useState<"all" | "1m" | "3m" | "6m" | "1y">("all")
  const [groupBy, setGroupBy] = useState<"month" | "week" | "symbol">("month")

  // Filter trades by time range
  const filteredTrades = useMemo(() => {
    if (timeRange === "all") return trades

    const now = new Date()
    let startDate = new Date()

    switch (timeRange) {
      case "1m":
        startDate.setMonth(now.getMonth() - 1)
        break
      case "3m":
        startDate.setMonth(now.getMonth() - 3)
        break
      case "6m":
        startDate.setMonth(now.getMonth() - 6)
        break
      case "1y":
        startDate.setFullYear(now.getFullYear() - 1)
        break
    }

    return trades.filter(trade => new Date(trade.time_close) >= startDate)
  }, [trades, timeRange])

  // Calculate max drawdown
  const calculateMaxDrawdown = (trades: Trade[]) => {
    if (trades.length === 0) return 0

    let peak = 0
    let maxDrawdown = 0
    let equity = 0

    trades.forEach(trade => {
      equity += trade.profit

      if (equity > peak) {
        peak = equity
      }

      const drawdown = peak - equity
      if (drawdown > maxDrawdown) {
        maxDrawdown = drawdown
      }
    })

    return maxDrawdown
  }



  // Calculate risk metrics
  const riskMetrics = useMemo(() => {
    if (filteredTrades.length === 0) return {
      totalTrades: 0,
      winRate: 0,
      averageWin: 0,
      averageLoss: 0,
      riskRewardRatio: 0,
      expectancy: 0,
      profitFactor: 0,
      sharpeRatio: 0,
      maxConsecutiveLosses: 0,
      recoveryFactor: 0,
      kellyPercentage: 0
    }

    const wins = filteredTrades.filter(t => t.profit > 0)
    const losses = filteredTrades.filter(t => t.profit <= 0)

    const totalTrades = filteredTrades.length
    const winRate = wins.length / totalTrades

    const totalProfit = filteredTrades.reduce((sum, t) => sum + t.profit, 0)
    const grossProfit = wins.reduce((sum, t) => sum + t.profit, 0)
    const grossLoss = Math.abs(losses.reduce((sum, t) => sum + t.profit, 0))

    const averageWin = wins.length > 0 ? grossProfit / wins.length : 0
    const averageLoss = losses.length > 0 ? grossLoss / losses.length : 0

    const riskRewardRatio = averageLoss > 0 ? averageWin / averageLoss : 0
    const expectancy = (winRate * averageWin) - ((1 - winRate) * averageLoss)
    const profitFactor = grossLoss > 0 ? grossProfit / grossLoss : grossProfit > 0 ? Infinity : 0

    // Calculate Sharpe Ratio (simplified)
    const returns = filteredTrades.map(t => t.profit)
    const averageReturn = returns.reduce((sum, r) => sum + r, 0) / returns.length
    const stdDeviation = Math.sqrt(
      returns.reduce((sum, r) => sum + Math.pow(r - averageReturn, 2), 0) / returns.length
    )
    const sharpeRatio = stdDeviation > 0 ? averageReturn / stdDeviation : 0

    // Calculate max consecutive losses
    let currentLossStreak = 0
    let maxConsecutiveLosses = 0

    filteredTrades.forEach(trade => {
      if (trade.profit <= 0) {
        currentLossStreak++
        maxConsecutiveLosses = Math.max(maxConsecutiveLosses, currentLossStreak)
      } else {
        currentLossStreak = 0
      }
    })

    // Calculate recovery factor
    const maxDrawdown = calculateMaxDrawdown(filteredTrades)
    const recoveryFactor = maxDrawdown > 0 ? totalProfit / maxDrawdown : 0

    // Calculate Kelly percentage
    const kellyPercentage = winRate - ((1 - winRate) / riskRewardRatio)

    return {
      totalTrades,
      winRate,
      averageWin,
      averageLoss,
      riskRewardRatio,
      expectancy,
      profitFactor,
      sharpeRatio,
      maxConsecutiveLosses,
      recoveryFactor,
      kellyPercentage
    }
  }, [filteredTrades])



  // Group trades for charts
  const chartData = useMemo(() => {
    if (filteredTrades.length === 0) return []

    if (groupBy === "symbol") {
      const symbolMap = new Map<string, {
        symbol: string
        trades: Trade[]
      }>()

      filteredTrades.forEach(trade => {
        const symbol = trade.symbol

        if (!symbolMap.has(symbol)) {
          symbolMap.set(symbol, {
            symbol,
            trades: []
          })
        }

        symbolMap.get(symbol)!.trades.push(trade)
      })

      return Array.from(symbolMap.values())
        .filter(group => group.trades.length >= 1) // Include symbols with at least 1 trade
        .map(group => {
          const wins = group.trades.filter(t => t.profit > 0)
          const losses = group.trades.filter(t => t.profit <= 0)

          const winRate = group.trades.length > 0 ? wins.length / group.trades.length : 0
          const averageWin = wins.length > 0 ? wins.reduce((sum, t) => sum + t.profit, 0) / wins.length : 0
          const averageLoss = losses.length > 0 ? Math.abs(losses.reduce((sum, t) => sum + t.profit, 0)) / losses.length : 0
          const riskRewardRatio = averageLoss > 0 ? averageWin / averageLoss : 0
          const expectancy = (winRate * averageWin) - ((1 - winRate) * averageLoss)

          // Calculate R-multiple (assuming 1R = averageLoss)
          const rMultiple = averageLoss > 0 ?
            group.trades.reduce((sum, t) => sum + (t.profit / averageLoss), 0) / group.trades.length : 0

          return {
            name: group.symbol,
            winRate: winRate * 100,
            riskRewardRatio,
            expectancy,
            rMultiple,
            tradeCount: group.trades.length
          }
        })
        .sort((a, b) => b.expectancy - a.expectancy)
    } else {
      // Group by time period
      const sortedTrades = [...filteredTrades].sort((a, b) =>
        new Date(a.time_close).getTime() - new Date(b.time_close).getTime()
      )

      const periodMap = new Map<string, {
        period: string
        trades: Trade[]
        date: Date
      }>()

      sortedTrades.forEach(trade => {
        const date = new Date(trade.time_close)
        let periodKey: string

        if (groupBy === "month") {
          periodKey = format(date, 'MMM yyyy')
        } else { // week
          // Get the week number
          const weekNum = Math.ceil((date.getDate() + (date.getDay() + 6) % 7) / 7)
          periodKey = `W${weekNum} ${format(date, 'MMM yyyy')}`
        }

        if (!periodMap.has(periodKey)) {
          periodMap.set(periodKey, {
            period: periodKey,
            trades: [],
            date
          })
        }

        periodMap.get(periodKey)!.trades.push(trade)
      })

      return Array.from(periodMap.values())
        .map(group => {
          const wins = group.trades.filter(t => t.profit > 0)
          const losses = group.trades.filter(t => t.profit <= 0)

          const winRate = group.trades.length > 0 ? wins.length / group.trades.length : 0
          const averageWin = wins.length > 0 ? wins.reduce((sum, t) => sum + t.profit, 0) / wins.length : 0
          const averageLoss = losses.length > 0 ? Math.abs(losses.reduce((sum, t) => sum + t.profit, 0)) / losses.length : 0
          const riskRewardRatio = averageLoss > 0 ? averageWin / averageLoss : 0
          const expectancy = (winRate * averageWin) - ((1 - winRate) * averageLoss)

          // Calculate R-multiple (assuming 1R = averageLoss)
          const rMultiple = averageLoss > 0 ?
            group.trades.reduce((sum, t) => sum + (t.profit / averageLoss), 0) / group.trades.length : 0

          return {
            name: group.period,
            winRate: winRate * 100,
            riskRewardRatio,
            expectancy,
            rMultiple,
            tradeCount: group.trades.length,
            date: group.date
          }
        })
        .sort((a, b) => a.date.getTime() - b.date.getTime())
    }
  }, [filteredTrades, groupBy])

  // Custom tooltip for the charts
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload
      return (
        <div className="bg-background border rounded-md p-3 shadow-md">
          <p className="font-medium">{data.name}</p>
          <p className="text-sm text-muted-foreground">
            Win Rate: {data.winRate.toFixed(1)}%
          </p>
          <p className="text-sm text-muted-foreground">
            Risk/Reward: {data.riskRewardRatio.toFixed(2)}
          </p>
          <p className="text-sm text-muted-foreground">
            Expectancy: ${data.expectancy.toFixed(2)}
          </p>
          <p className="text-sm text-muted-foreground">
            R-Multiple: {data.rMultiple.toFixed(2)}R
          </p>
          <p className="text-sm text-muted-foreground">
            Trades: {data.tradeCount}
          </p>
        </div>
      )
    }
    return null
  }

  // Get chart data based on selected chart type
  const getChartData = () => {
    switch (chartType) {
      case "riskReward":
        return {
          dataKey: "riskRewardRatio",
          name: "Risk/Reward Ratio",
          color: "#3b82f6",
          formatter: (value: number) => value.toFixed(2)
        }
      case "expectancy":
        return {
          dataKey: "expectancy",
          name: "Expectancy ($)",
          color: "#10b981",
          formatter: (value: number) => `$${value.toFixed(2)}`
        }
      case "rMultiple":
        return {
          dataKey: "rMultiple",
          name: "R-Multiple",
          color: "#8b5cf6",
          formatter: (value: number) => `${value.toFixed(2)}R`
        }
      default:
        return {
          dataKey: "riskRewardRatio",
          name: "Risk/Reward Ratio",
          color: "#3b82f6",
          formatter: (value: number) => value.toFixed(2)
        }
    }
  }

  const chartConfig = getChartData()

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row gap-4 justify-between">
        <div className="flex flex-col sm:flex-row gap-2">
          <Tabs value={chartType} onValueChange={(value) => setChartType(value as ChartType)}>
            <TabsList className="grid w-full max-w-[500px] grid-cols-3 gap-2 rounded-none border-b bg-transparent">
              <TabsTrigger
                value="riskReward"
                className="rounded-none data-[state=active]:bg-background data-[state=active]:border-b data-[state=active]:border-primary data-[state=active]:font-medium"
              >
                <Scale className="mr-2 h-4 w-4" />
                Risk/Reward Ratio
              </TabsTrigger>
              <TabsTrigger
                value="expectancy"
                className="rounded-none data-[state=active]:bg-background data-[state=active]:border-b data-[state=active]:border-primary data-[state=active]:font-medium"
              >
                <DollarSign className="mr-2 h-4 w-4" />
                Expectancy
              </TabsTrigger>
              <TabsTrigger
                value="rMultiple"
                className="rounded-none data-[state=active]:bg-background data-[state=active]:border-b data-[state=active]:border-primary data-[state=active]:font-medium"
              >
                <BarChart3 className="mr-2 h-4 w-4" />
                R-Multiple
              </TabsTrigger>
            </TabsList>
          </Tabs>

          <Select value={groupBy} onValueChange={(value) => setGroupBy(value as any)}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Group By" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="month">Month</SelectItem>
              <SelectItem value="week">Week</SelectItem>
              <SelectItem value="symbol">Symbol</SelectItem>
            </SelectContent>
          </Select>

          <Select value={timeRange} onValueChange={(value) => setTimeRange(value as any)}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Time Range" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Time</SelectItem>
              <SelectItem value="1m">Last Month</SelectItem>
              <SelectItem value="3m">Last 3 Months</SelectItem>
              <SelectItem value="6m">Last 6 Months</SelectItem>
              <SelectItem value="1y">Last Year</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {chartData.length === 0 ? (
        <div className="flex justify-center items-center h-[400px] border rounded-md">
          <p className="text-muted-foreground">No data available for the selected time range</p>
        </div>
      ) : (
        <div className="h-[500px]">
          {groupBy !== "symbol" ? (
            <ResponsiveContainer width="100%" height="100%">
              <LineChart
                data={chartData}
                margin={{ top: 20, right: 30, left: 20, bottom: 70 }}
              >
                <CartesianGrid
                  strokeDasharray="3 3"
                  stroke="hsl(var(--border))"
                  opacity={0.4}
                  vertical={true}
                />
                <XAxis
                  dataKey="name"
                  angle={-45}
                  textAnchor="end"
                  height={70}
                  tick={{
                    fontSize: 11,
                    fill: 'hsl(var(--muted-foreground))',
                    opacity: 0.7
                  }}
                  axisLine={{ stroke: 'hsl(var(--border))', opacity: 0.5 }}
                />
                <YAxis
                  tickFormatter={(value) => chartConfig.formatter(value)}
                  label={{
                    value: chartConfig.name,
                    angle: -90,
                    position: 'insideLeft',
                    offset: -10,
                    style: {
                      textAnchor: 'middle',
                      fill: 'hsl(var(--muted-foreground))',
                      fontSize: 12
                    }
                  }}
                  tick={{
                    fontSize: 11,
                    fill: 'hsl(var(--muted-foreground))',
                    opacity: 0.7
                  }}
                  axisLine={{ stroke: 'hsl(var(--border))', opacity: 0.5 }}
                  width={60}
                />
                <Tooltip content={<CustomTooltip />} />
                <Legend
                  verticalAlign="top"
                  height={36}
                  wrapperStyle={{
                    paddingTop: '10px',
                    fontSize: '12px'
                  }}
                />
                {/* Add reference line based on chart type */}
                {chartType === "expectancy" && (
                  <ReferenceLine y={0} stroke="#666" />
                )}
                {chartType === "rMultiple" && (
                  <ReferenceLine y={0} stroke="#666" />
                )}
                <Line
                  type="monotone"
                  dataKey={chartConfig.dataKey}
                  name={chartConfig.name}
                  stroke={chartConfig.color}
                  strokeWidth={2}
                  dot={{ r: 4 }}
                  activeDot={{ r: 6 }}
                />
              </LineChart>
            </ResponsiveContainer>
          ) : (
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={chartData}
                margin={{ top: 20, right: 30, left: 20, bottom: 70 }}
              >
                <CartesianGrid
                  strokeDasharray="3 3"
                  stroke="hsl(var(--border))"
                  opacity={0.4}
                  vertical={true}
                />
                <XAxis
                  dataKey="name"
                  angle={-45}
                  textAnchor="end"
                  height={70}
                  tick={{
                    fontSize: 11,
                    fill: 'hsl(var(--muted-foreground))',
                    opacity: 0.7
                  }}
                  axisLine={{ stroke: 'hsl(var(--border))', opacity: 0.5 }}
                />
                <YAxis
                  tickFormatter={(value) => chartConfig.formatter(value)}
                  label={{
                    value: chartConfig.name,
                    angle: -90,
                    position: 'insideLeft',
                    offset: -10,
                    style: {
                      textAnchor: 'middle',
                      fill: 'hsl(var(--muted-foreground))',
                      fontSize: 12
                    }
                  }}
                  tick={{
                    fontSize: 11,
                    fill: 'hsl(var(--muted-foreground))',
                    opacity: 0.7
                  }}
                  axisLine={{ stroke: 'hsl(var(--border))', opacity: 0.5 }}
                  width={60}
                />
                <Tooltip content={<CustomTooltip />} />
                <Legend
                  verticalAlign="top"
                  height={36}
                  wrapperStyle={{
                    paddingTop: '10px',
                    fontSize: '12px'
                  }}
                />
                {/* Add reference line based on chart type */}
                {chartType === "expectancy" && (
                  <ReferenceLine y={0} stroke="#666" />
                )}
                {chartType === "rMultiple" && (
                  <ReferenceLine y={0} stroke="#666" />
                )}
                <Bar
                  dataKey={chartConfig.dataKey}
                  name={chartConfig.name}
                  fill={chartConfig.color}
                />
              </BarChart>
            </ResponsiveContainer>
          )}
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Risk/Reward Ratio Card */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-base">Risk/Reward Ratio</CardTitle>
            <CardDescription>
              Average win size vs. average loss
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-1">
              <div className="text-2xl font-bold">
                {riskMetrics.riskRewardRatio.toFixed(2)}
              </div>
              <div className="text-sm text-muted-foreground">
                Avg Win: ${riskMetrics.averageWin.toFixed(2)}
              </div>
              <div className="text-sm text-muted-foreground">
                Avg Loss: ${riskMetrics.averageLoss.toFixed(2)}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Expectancy Card */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-base">Expectancy</CardTitle>
            <CardDescription>
              Expected profit per trade
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-1">
              <div className={cn(
                "text-2xl font-bold",
                riskMetrics.expectancy >= 0 ? "text-emerald-500" : "text-rose-500"
              )}>
                ${riskMetrics.expectancy.toFixed(2)}
              </div>
              <div className="text-sm text-muted-foreground">
                Win Rate: {(riskMetrics.winRate * 100).toFixed(1)}%
              </div>
              <div className="text-sm text-muted-foreground">
                Profit Factor: {riskMetrics.profitFactor === Infinity ? "∞" : riskMetrics.profitFactor.toFixed(2)}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Kelly Percentage Card */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-base">Kelly Percentage</CardTitle>
            <CardDescription>
              Optimal position sizing
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-1">
              <div className={cn(
                "text-2xl font-bold",
                riskMetrics.kellyPercentage >= 0 ? "text-emerald-500" : "text-rose-500"
              )}>
                {(riskMetrics.kellyPercentage * 100).toFixed(1)}%
              </div>
              <div className="text-sm text-muted-foreground">
                Half Kelly: {(riskMetrics.kellyPercentage * 50).toFixed(1)}%
              </div>
              <div className="text-sm text-muted-foreground">
                Quarter Kelly: {(riskMetrics.kellyPercentage * 25).toFixed(1)}%
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Recovery Factor Card */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-base">Recovery Factor</CardTitle>
            <CardDescription>
              Net profit relative to drawdown
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-1">
              <div className="text-2xl font-bold">
                {riskMetrics.recoveryFactor.toFixed(2)}
              </div>
              <div className="text-sm text-muted-foreground">
                Max Consecutive Losses: {riskMetrics.maxConsecutiveLosses}
              </div>
              <div className="text-sm text-muted-foreground">
                Sharpe Ratio: {riskMetrics.sharpeRatio.toFixed(2)}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Risk Management Tips */}
      <Card>
        <CardHeader>
          <CardTitle>Risk Management Insights</CardTitle>
          <CardDescription>
            Recommendations based on your trading metrics
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {riskMetrics.riskRewardRatio < 1 && (
              <div className="flex items-start space-x-2">
                <div className="w-6 h-6 rounded-full bg-amber-500/10 flex items-center justify-center text-amber-500 mt-0.5">
                  !
                </div>
                <div>
                  <h4 className="font-medium">Improve Your Risk/Reward Ratio</h4>
                  <p className="text-sm text-muted-foreground">
                    Your average win is smaller than your average loss. Consider setting tighter stop losses or letting your winners run longer.
                  </p>
                </div>
              </div>
            )}

            {riskMetrics.winRate < 0.4 && (
              <div className="flex items-start space-x-2">
                <div className="w-6 h-6 rounded-full bg-amber-500/10 flex items-center justify-center text-amber-500 mt-0.5">
                  !
                </div>
                <div>
                  <h4 className="font-medium">Increase Your Win Rate</h4>
                  <p className="text-sm text-muted-foreground">
                    Your win rate is below 40%. Focus on improving your entry criteria or consider using smaller position sizes until your accuracy improves.
                  </p>
                </div>
              </div>
            )}

            {riskMetrics.expectancy <= 0 && (
              <div className="flex items-start space-x-2">
                <div className="w-6 h-6 rounded-full bg-rose-500/10 flex items-center justify-center text-rose-500 mt-0.5">
                  !
                </div>
                <div>
                  <h4 className="font-medium">Negative Expectancy</h4>
                  <p className="text-sm text-muted-foreground">
                    Your trading system currently has a negative expectancy. Consider pausing trading to review your strategy or significantly reduce position sizes.
                  </p>
                </div>
              </div>
            )}

            {riskMetrics.maxConsecutiveLosses >= 5 && (
              <div className="flex items-start space-x-2">
                <div className="w-6 h-6 rounded-full bg-amber-500/10 flex items-center justify-center text-amber-500 mt-0.5">
                  !
                </div>
                <div>
                  <h4 className="font-medium">Prepare for Drawdowns</h4>
                  <p className="text-sm text-muted-foreground">
                    You've experienced {riskMetrics.maxConsecutiveLosses} consecutive losses. Ensure your position sizing can withstand similar streaks in the future.
                  </p>
                </div>
              </div>
            )}

            {riskMetrics.kellyPercentage > 0.25 && (
              <div className="flex items-start space-x-2">
                <div className="w-6 h-6 rounded-full bg-emerald-500/10 flex items-center justify-center text-emerald-500 mt-0.5">
                  ✓
                </div>
                <div>
                  <h4 className="font-medium">Strong Edge Detected</h4>
                  <p className="text-sm text-muted-foreground">
                    Your Kelly percentage suggests a strong edge. Consider using the Quarter Kelly ({(riskMetrics.kellyPercentage * 25).toFixed(1)}%) for position sizing to balance growth and safety.
                  </p>
                </div>
              </div>
            )}

            {riskMetrics.recoveryFactor > 3 && (
              <div className="flex items-start space-x-2">
                <div className="w-6 h-6 rounded-full bg-emerald-500/10 flex items-center justify-center text-emerald-500 mt-0.5">
                  ✓
                </div>
                <div>
                  <h4 className="font-medium">Excellent Recovery Factor</h4>
                  <p className="text-sm text-muted-foreground">
                    Your recovery factor of {riskMetrics.recoveryFactor.toFixed(2)} indicates strong risk management. Your system generates good returns relative to drawdowns.
                  </p>
                </div>
              </div>
            )}

            {riskMetrics.totalTrades < 30 && (
              <div className="flex items-start space-x-2">
                <div className="w-6 h-6 rounded-full bg-blue-500/10 flex items-center justify-center text-blue-500 mt-0.5">
                  i
                </div>
                <div>
                  <h4 className="font-medium">Limited Sample Size</h4>
                  <p className="text-sm text-muted-foreground">
                    With only {riskMetrics.totalTrades} trades, these metrics may not be statistically significant yet. Continue trading consistently to gather more data.
                  </p>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
