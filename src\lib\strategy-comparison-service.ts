import { getSupabaseBrowser } from './supabase'
import { Strategy, StrategyPerformance } from '@/types/playbook'
import { format, subMonths, startOfMonth, endOfMonth } from 'date-fns'

// Fetch multiple strategies for comparison
export async function getStrategiesForComparison(userId: string, strategyIds: string[]): Promise<Strategy[]> {
  if (!strategyIds.length) return []

  const supabase = getSupabaseBrowser()

  try {
    const { data, error } = await supabase
      .from('strategies')
      .select('*')
      .eq('user_id', userId)
      .in('id', strategyIds)

    if (error) {
      console.error('Error fetching strategies for comparison:', error)
      return []
    }

    return data || []
  } catch (error) {
    console.error('Error in getStrategiesForComparison:', error)
    return []
  }
}

// Fetch performance data for multiple strategies
export async function getPerformanceForStrategies(
  userId: string,
  strategyIds: string[],
  timeframe: 'lastMonth' | 'last3months' | 'last6months' | 'lastYear' = 'last3months',
  accountId?: string | null
): Promise<Record<string, StrategyPerformance[]>> {
  if (!strategyIds.length) return {}

  const supabase = getSupabaseBrowser()
  const result: Record<string, StrategyPerformance[]> = {}

  // Calculate date range based on timeframe
  let startDate: Date
  let endDate = new Date()

  switch (timeframe) {
    case "lastMonth":
      startDate = startOfMonth(subMonths(new Date(), 1))
      endDate = endOfMonth(subMonths(new Date(), 1))
      break
    case "last3months":
      startDate = startOfMonth(subMonths(new Date(), 3))
      break
    case "last6months":
      startDate = startOfMonth(subMonths(new Date(), 6))
      break
    case "lastYear":
      startDate = startOfMonth(subMonths(new Date(), 12))
      break
    default:
      startDate = startOfMonth(subMonths(new Date(), 3))
  }

  const formattedStartDate = format(startDate, "yyyy-MM-dd")
  const formattedEndDate = format(endDate, "yyyy-MM-dd")

  try {
    let query = supabase
      .from('strategy_performance')
      .select('*')
      .eq('user_id', userId)
      .in('strategy_id', strategyIds)
      .gte('period_start', formattedStartDate)
      .lte('period_end', formattedEndDate)

    // Filter by account if specified
    if (accountId) {
      console.log(`Filtering performance by account: ${accountId}`)
      query = query.eq('account_id', accountId)
    } else {
      console.log('No account filter applied - showing performance from all accounts')
      query = query.is('account_id', null)
    }

    const { data, error } = await query.order('period_start', { ascending: false })

    if (error) {
      console.error('Error fetching performance data for strategies:', error)
      return {}
    }

    // Group performance data by strategy_id
    if (data) {
      strategyIds.forEach(id => {
        result[id] = data.filter(perf => perf.strategy_id === id)
      })
    }

    return result
  } catch (error) {
    console.error('Error in getPerformanceForStrategies:', error)
    return {}
  }
}

// Get the latest performance data for each strategy
export async function getLatestPerformanceForStrategies(
  userId: string,
  strategyIds: string[],
  accountId?: string | null
): Promise<Record<string, StrategyPerformance | null>> {
  if (!strategyIds.length) return {}

  const supabase = getSupabaseBrowser()
  const result: Record<string, StrategyPerformance | null> = {}

  try {
    // Initialize result with null values
    strategyIds.forEach(id => {
      result[id] = null
    })

    // Fetch the latest performance record for each strategy
    for (const strategyId of strategyIds) {
      let query = supabase
        .from('strategy_performance')
        .select('*')
        .eq('user_id', userId)
        .eq('strategy_id', strategyId)

      // Filter by account if specified
      if (accountId) {
        console.log(`Filtering performance by account: ${accountId}`)
        query = query.eq('account_id', accountId)
      } else {
        console.log('No account filter applied - showing performance from all accounts')
        query = query.is('account_id', null)
      }

      const { data, error } = await query
        .order('period_end', { ascending: false })
        .limit(1)
        .maybeSingle()

      if (error) {
        console.error(`Error fetching latest performance for strategy ${strategyId}:`, error)
        continue
      }

      if (data) {
        result[strategyId] = data
      }
    }

    return result
  } catch (error) {
    console.error('Error in getLatestPerformanceForStrategies:', error)
    return result
  }
}

// Prepare comparison data for charts
export function prepareComparisonData(
  strategies: Strategy[],
  performanceData: Record<string, StrategyPerformance | null>
) {
  return strategies.map(strategy => {
    const performance = performanceData[strategy.id]

    // Calculate profit factor if it's not available in the performance data
    let profitFactor = 0;
    if (performance) {
      if (performance.profit_factor !== undefined && performance.profit_factor !== null) {
        profitFactor = performance.profit_factor;
      } else if (performance.winning_trades > 0 && performance.losing_trades > 0) {
        // Calculate profit factor manually if not available in the database
        const grossProfit = performance.average_win * performance.winning_trades;
        const grossLoss = performance.average_loss * performance.losing_trades;
        profitFactor = grossLoss > 0 ? grossProfit / grossLoss : grossProfit > 0 ? 999 : 0;
      }
    }

    return {
      id: strategy.id,
      name: strategy.name,
      status: strategy.status,
      winRate: performance ? performance.win_rate * 100 : 0,
      profitLoss: performance ? performance.profit_loss : 0,
      expectancy: performance ? performance.expectancy : 0,
      averageWin: performance ? performance.average_win : 0,
      averageLoss: performance ? performance.average_loss : 0,
      totalTrades: performance ? performance.total_trades : 0,
      winningTrades: performance ? performance.winning_trades : 0,
      losingTrades: performance ? performance.losing_trades : 0,
      riskReward: performance ? performance.average_risk_reward : 0,
      profitFactor: profitFactor,
    }
  })
}
