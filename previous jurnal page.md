"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { format, isSameDay } from "date-fns"
import { DateRange } from "react-day-picker"
import { TableDateRangePicker } from "@/components/ui/table-date-range-picker"
import { Plus, Search, Filter, Tag, Calendar, X, Loader2, BookOpen, BarChart3, Check } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { Calendar as CalendarComponent } from "@/components/ui/calendar"
import { JournalEntryForm } from "@/components/journal-entry-form"
import { JournalEntryCard } from "@/components/journal-entry-card"
import { AutomatedDailyJournal } from "@/components/automated-daily-journal"
import { TradeJournalCard } from "@/components/trade-journal-card"
import { TradeDetailsEditDialog } from "@/components/trade-details-edit-dialog"
import { getSupabaseClient } from "@/lib/supabase-singleton"
import {
  getJournalEntries,
  getJournalEntryById,
  createJournalEntry,
  updateJournalEntry,
  deleteJournalEntry,
  getAllTags,
  type JournalEntry,
  type JournalEntryInsert
} from "@/lib/journal-service"
import { getUserTrades } from "@/lib/trade-service"
import { getDailyJournalEntries } from "@/lib/daily-journal-service"
import { useAccount } from "@/contexts/account-context"
import { toast } from "sonner"

export default function JournalPage() {
  const router = useRouter()
  const [entries, setEntries] = useState<JournalEntry[]>([])
  const [loading, setLoading] = useState(true)
  const [userId, setUserId] = useState<string | null>(null)
  const [isNewEntryDialogOpen, setIsNewEntryDialogOpen] = useState(false)
  const [isEditEntryDialogOpen, setIsEditEntryDialogOpen] = useState(false)
  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] = useState(false)
  const [currentEntry, setCurrentEntry] = useState<JournalEntry | null>(null)
  const [isTradeEditDialogOpen, setIsTradeEditDialogOpen] = useState(false)
  const [currentTrade, setCurrentTrade] = useState<any>(null)
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedTags, setSelectedTags] = useState<string[]>([])
  const [availableTags, setAvailableTags] = useState<string[]>([])
  const [dateRange, setDateRange] = useState<DateRange | undefined>(undefined)
  const [isFilterOpen, setIsFilterOpen] = useState(false)
  const [activeTab, setActiveTab] = useState("all")
  const [allTrades, setAllTrades] = useState<any[]>([])
  const [tradingDays, setTradingDays] = useState<Date[]>([])
  const [filteredDays, setFilteredDays] = useState<Date[]>([])
  const supabase = getSupabaseClient()
  const { selectedAccountId } = useAccount()

  useEffect(() => {
    const fetchUser = async () => {
      const { data: { session } } = await supabase.auth.getSession()
      // Only use authenticated user ID
      if (!session?.user?.id) {
        console.log('No authenticated user found')
        return
      }
      const currentUserId = session.user.id
      setUserId(currentUserId)

      await fetchEntries(currentUserId)
      await fetchTags(currentUserId)
    }

    fetchUser()
  }, [supabase.auth])

  // Separate effect to fetch trades when account changes or tags are selected
  useEffect(() => {
    const fetchTrades = async () => {
      if (!userId) return;

      try {
        console.log("Fetching trades for account:", selectedAccountId);
        // Pass selected tags to getUserTrades to filter by tags
        const trades = await getUserTrades(
          userId,
          selectedAccountId,
          undefined, // startDate
          undefined, // endDate
          undefined, // symbol
          undefined, // tradeType
          undefined, // minProfit
          undefined, // maxProfit
          selectedTags.length > 0 ? selectedTags : undefined // tags
        );
        console.log(`Retrieved ${trades?.length || 0} trades`);
        setAllTrades(trades || []);

        // Calculate trading days from the fetched trades
        const days: Date[] = [];
        trades.forEach(trade => {
          const tradeDate = new Date(trade.time_close);
          // Set time to midnight for proper comparison
          tradeDate.setHours(0, 0, 0, 0);

          // Check if this date is already in the array
          const dateExists = days.some(date => isSameDay(date, tradeDate));

          if (!dateExists) {
            days.push(tradeDate);
          }
        });

        // Sort dates in descending order (newest first)
        days.sort((a, b) => b.getTime() - a.getTime());
        console.log(`Found ${days.length} unique trading days`);
        setTradingDays(days);
      } catch (error) {
        console.error("Error fetching trades:", error);
      }
    };

    fetchTrades();

    // Listen for journal tag updates
    const handleJournalTagsUpdated = (event: Event) => {
      console.log("Journal tags updated, refreshing data");
      const customEvent = event as CustomEvent;
      console.log("Updated tags:", customEvent.detail?.tags);
      fetchTrades();
      if (userId) {
        fetchEntries(userId);
      }
    };

    // Listen for global data changes
    const handleGlobalDataChange = (event: Event) => {
      const customEvent = event as CustomEvent;
      console.log("Global data change:", customEvent.detail?.type);

      if (customEvent.detail?.type === 'trade-tags-updated') {
        console.log("Trade tags updated, refreshing data");
        fetchTrades();
        if (userId) {
          fetchEntries(userId);
        }
      }
    };

    document.addEventListener('journal-tags-updated', handleJournalTagsUpdated);
    window.addEventListener('global-data-change', handleGlobalDataChange);

    return () => {
      document.removeEventListener('journal-tags-updated', handleJournalTagsUpdated);
      window.removeEventListener('global-data-change', handleGlobalDataChange);
    };
  }, [userId, selectedAccountId, selectedTags]);

  const fetchEntries = async (userId: string, options?: {
    searchTerm?: string;
    tags?: string[];
    startDate?: string;
    endDate?: string;
  }) => {
    try {
      setLoading(true)
      const entries = await getJournalEntries(userId, {
        searchTerm: options?.searchTerm || searchTerm,
        tags: options?.tags || selectedTags,
        startDate: options?.startDate || (dateRange?.from ? format(dateRange.from, "yyyy-MM-dd") : undefined),
        endDate: options?.endDate || (dateRange?.to ? format(dateRange.to, "yyyy-MM-dd") : undefined),
        accountId: selectedAccountId
      })
      setEntries(entries)

      // Also refresh trades when entries are refreshed
      try {
        // Get start and end dates from options or dateRange
        const startDate = options?.startDate || (dateRange?.from ? format(dateRange.from, "yyyy-MM-dd") : undefined);
        const endDate = options?.endDate || (dateRange?.to ? format(dateRange.to, "yyyy-MM-dd") : undefined);

        // Pass date range and tags to getUserTrades
        const trades = await getUserTrades(
          userId,
          selectedAccountId,
          startDate,
          endDate,
          undefined, // symbol
          undefined, // tradeType
          undefined, // minProfit
          undefined, // maxProfit
          options?.tags || selectedTags
        );
        setAllTrades(trades || []);

        // Recalculate trading days
        const days: Date[] = [];
        trades.forEach(trade => {
          const tradeDate = new Date(trade.time_close);
          // Set time to midnight for proper comparison
          tradeDate.setHours(0, 0, 0, 0);

          // Check if this date is already in the array
          const dateExists = days.some(date => isSameDay(date, tradeDate));

          if (!dateExists) {
            days.push(tradeDate);
          }
        });

        // Sort dates in descending order (newest first)
        days.sort((a, b) => b.getTime() - a.getTime());
        setTradingDays(days);
      } catch (tradeError) {
        console.error("Error refreshing trades:", tradeError);
      }
    } catch (error) {
      console.error("Error fetching journal entries:", error)
      toast.error("Failed to load journal entries")
    } finally {
      setLoading(false)
    }
  }

  const fetchTags = async (userId: string) => {
    try {
      // Get tags from the database
      const tags = await getAllTags(userId)

      // Also get tags from daily journal entries in Supabase
      const dailyJournalTags = await getDailyJournalTags(userId)

      // Combine and deduplicate tags
      const allTags = Array.from(new Set([...tags, ...dailyJournalTags]))

      setAvailableTags(allTags)
    } catch (error) {
      console.error("Error fetching tags:", error)
    }
  }

  // Function to get tags from daily journal entries in Supabase
  const getDailyJournalTags = async (userId: string): Promise<string[]> => {
    try {
      // Fetch daily journal entries from Supabase
      const entries = await getDailyJournalEntries(userId, {
        accountId: selectedAccountId || undefined
      });

      const allTags: string[] = [];

      // Extract tags from each entry
      entries.forEach(entry => {
        if (Array.isArray(entry.tags)) {
          allTags.push(...entry.tags);
        }
      });

      // Return unique tags
      return Array.from(new Set(allTags));
    } catch (error) {
      console.error("Error getting daily journal tags from Supabase:", error);
      return [];
    }
  }

  const handleCreateEntry = async (entry: Omit<JournalEntryInsert, "user_id">) => {
    if (!userId) return

    try {
      await createJournalEntry(userId, entry)
      toast.success("Journal entry created successfully")
      setIsNewEntryDialogOpen(false)
      fetchEntries(userId)
      fetchTags(userId)
    } catch (error) {
      console.error("Error creating journal entry:", error)
      toast.error("Failed to create journal entry")
    }
  }

  const handleUpdateEntry = async (entry: Omit<JournalEntryInsert, "user_id">) => {
    if (!userId || !currentEntry) return

    try {
      await updateJournalEntry(userId, currentEntry.id, entry)
      toast.success("Journal entry updated successfully")
      setIsEditEntryDialogOpen(false)
      fetchEntries(userId)
      fetchTags(userId)
    } catch (error) {
      console.error("Error updating journal entry:", error)
      toast.error("Failed to update journal entry")
    }
  }

  const handleDeleteEntry = async () => {
    if (!userId || !currentEntry) return

    try {
      await deleteJournalEntry(userId, currentEntry.id)
      toast.success("Journal entry deleted successfully")
      setIsDeleteConfirmOpen(false)
      fetchEntries(userId)
    } catch (error) {
      console.error("Error deleting journal entry:", error)
      toast.error("Failed to delete journal entry")
    }
  }

  const handleEditEntry = (entry: JournalEntry) => {
    setCurrentEntry(entry)
    setIsEditEntryDialogOpen(true)
  }

  const handleDeleteConfirm = (entry: JournalEntry) => {
    setCurrentEntry(entry)
    setIsDeleteConfirmOpen(true)
  }

  const handleEditTrade = (trade: any) => {
    setCurrentTrade(trade)
    setIsTradeEditDialogOpen(true)
  }

  const handleTradeUpdate = () => {
    // Refresh trades after a trade is updated
    if (userId) {
      fetchEntries(userId)
    }
  }

  const applyFilters = async () => {
    if (!userId) return

    // If there's a search term that might be a tag, check if it matches any available tags
    if (searchTerm && searchTerm.trim() !== '' && selectedTags.length === 0) {
      const term = searchTerm.trim().toLowerCase();

      // Check if the search term exactly matches any available tag
      const matchingTag = availableTags.find(tag => tag.toLowerCase() === term);

      if (matchingTag) {
        // If it matches a tag, add it to selected tags
        console.log(`Search term "${term}" matches tag "${matchingTag}", adding to selected tags`);
        setSelectedTags([matchingTag]);
      }
    }

    fetchEntries(userId);
    setIsFilterOpen(false);
  }

  const resetFilters = () => {
    setSearchTerm("")
    setSelectedTags([])
    setDateRange(undefined)

    // Reset filtered days to show all trading days
    setFilteredDays(tradingDays)

    if (userId) {
      // Fetch all entries without any filters
      fetchEntries(userId, {
        searchTerm: "",
        tags: [],
        startDate: undefined,
        endDate: undefined,
      })

      // Force a refresh of the filtered days
      console.log("Clearing filters and resetting to all trading days");
    }

    setIsFilterOpen(false)
  }

  const toggleTag = (tag: string) => {
    if (selectedTags.includes(tag)) {
      setSelectedTags(selectedTags.filter(t => t !== tag))
    } else {
      setSelectedTags([...selectedTags, tag])
    }
  }

  // Function to get trades for a specific date, optionally applying filters
  const getTradesForDate = (date: Date, applyFilters = false) => {
    console.log(`Filtering ${allTrades.length} trades for date: ${format(date, "yyyy-MM-dd")}`)

    // When we're showing a daily journal that matched our tag filter,
    // we want to show ALL trades for that date, not just the ones that match the filter
    const isDateFromTagFilter = filteredDays.some(d => isSameDay(d, date));

    // First, get all trades for this date
    const tradesForDate = allTrades.filter(trade => {
      try {
        const tradeDate = new Date(trade.time_close);
        return isSameDay(tradeDate, date);
      } catch (error) {
        console.error(`Error comparing dates for trade:`, trade);
        return false;
      }
    });

    // If we're not applying filters or this date was included because of a tag filter,
    // return all trades for this date
    if (!applyFilters || (isDateFromTagFilter && selectedTags.length > 0)) {
      console.log(`Returning all ${tradesForDate.length} trades for ${format(date, "yyyy-MM-dd")}`);
      return tradesForDate;
    }

    // Otherwise, apply filters
    const filteredTrades = tradesForDate.filter(tradeMatchesFilters);

    console.log(`Found ${filteredTrades.length} filtered trades for ${format(date, "yyyy-MM-dd")}`);
    return filteredTrades;
  }

  // Function to check if a daily journal entry exists for a date and has matching tags
  const checkDailyJournalForDate = async (date: Date): Promise<boolean> => {
    if (!userId || selectedTags.length === 0) return true;

    try {
      // Fetch daily journal entry for this date
      const dateStr = format(date, 'yyyy-MM-dd');
      const supabase = getSupabaseClient();

      const { data, error } = await supabase
        .from('daily_journal_entries')
        .select('tags')
        .eq('user_id', userId)
        .eq('date', dateStr)
        .eq(selectedAccountId ? 'account_id' : 'account_id', selectedAccountId || null)
        .single();

      if (error || !data) return false;

      // Check if any of the selected tags match the journal entry tags
      if (!Array.isArray(data.tags) || data.tags.length === 0) return false;

      return selectedTags.some(tag => data.tags.includes(tag));
    } catch (error) {
      console.error(`Error checking daily journal for date ${format(date, 'yyyy-MM-dd')}:`, error);
      return false;
    }
  }

  // Simple function to check if a trade matches the current filters
  const tradeMatchesFilters = (trade: any) => {
    // Date range filter
    if (dateRange?.from) {
      const tradeDate = new Date(trade.time_close);
      const fromDate = new Date(dateRange.from);
      fromDate.setHours(0, 0, 0, 0);
      if (tradeDate < fromDate) return false;
    }

    if (dateRange?.to) {
      const tradeDate = new Date(trade.time_close);
      const toDate = new Date(dateRange.to);
      toDate.setHours(23, 59, 59, 999);
      if (tradeDate > toDate) return false;
    }

    // Tag filter
    if (selectedTags.length > 0) {
      // Make sure trade.tags is an array and has at least one matching tag
      if (!Array.isArray(trade.tags)) return false;
      if (trade.tags.length === 0) return false;
      // Check if any of the selected tags match any of the trade tags
      if (!selectedTags.some(selectedTag => trade.tags.includes(selectedTag))) return false;
    }

    // Search term filter
    if (searchTerm && searchTerm.trim() !== '') {
      const term = searchTerm.toLowerCase();

      // Search in notes
      const notesMatch = trade.notes && trade.notes.toLowerCase().includes(term);

      // Search in symbol
      const symbolMatch = trade.symbol && trade.symbol.toLowerCase().includes(term);

      // Search in tags - check if the search term exactly matches a tag
      // This allows searching for exact tag names
      const exactTagMatch = Array.isArray(trade.tags) &&
        trade.tags.some(tag => tag.toLowerCase() === term);

      // Also check if the search term is contained within any tag
      const partialTagMatch = Array.isArray(trade.tags) &&
        trade.tags.some(tag => tag.toLowerCase().includes(term));

      if (!notesMatch && !symbolMatch && !exactTagMatch && !partialTagMatch) return false;
    }

    return true;
  };

  // Get filtered trading days based on trades that match filters
  const getFilteredTradingDays = async () => {
    // First filter all trades by the current filters
    const filteredTrades = allTrades.filter(tradeMatchesFilters);

    // Then extract unique dates from the filtered trades
    const uniqueDates = new Set<string>();
    filteredTrades.forEach(trade => {
      const dateStr = format(new Date(trade.time_close), 'yyyy-MM-dd');
      uniqueDates.add(dateStr);
    });

    // If we're filtering by tags, include days that have daily journal entries with matching tags
    // ONLY if we're in the appropriate tabs
    if (selectedTags.length > 0 && userId) {
      // For "with-trades" tab, we only want trade-specific journals, not daily journals
      // For other tabs, we include daily journals with matching tags
      const includeDailyJournals = activeTab !== "with-trades";

      if (includeDailyJournals) {
        try {
          const supabase = getSupabaseClient();

          // Fetch all daily journal entries that have any of the selected tags
          const { data: journalEntries, error } = await supabase
            .from('daily_journal_entries')
            .select('date, tags, account_id')
            .eq('user_id', userId)
            .eq(selectedAccountId ? 'account_id' : 'account_id', selectedAccountId || null);

          if (!error && journalEntries) {
            // Filter entries that have at least one matching tag
            const entriesWithMatchingTags = journalEntries.filter(entry =>
              Array.isArray(entry.tags) &&
              entry.tags.some(tag => selectedTags.includes(tag))
            );

            console.log(`Found ${entriesWithMatchingTags.length} daily journal entries with matching tags`);

            // For each matching entry, add its date to our unique dates set
            for (const entry of entriesWithMatchingTags) {
              const dateStr = format(new Date(entry.date), 'yyyy-MM-dd');
              uniqueDates.add(dateStr);

              // Also fetch trades for this specific date and account
              const { data: tradesForDate, error: tradesError } = await supabase
                .from('trades')
                .select('*')
                .eq('user_id', userId)
                .eq('account_id', entry.account_id || selectedAccountId)
                .gte('time_close', `${dateStr}T00:00:00`)
                .lt('time_close', `${dateStr}T23:59:59.999`);

              if (!tradesError && tradesForDate && tradesForDate.length > 0) {
                console.log(`Found ${tradesForDate.length} trades for journal date ${dateStr}`);

                // Add these trades to our allTrades array if they're not already there
                tradesForDate.forEach(trade => {
                  if (!allTrades.some(t => t.id === trade.id)) {
                    console.log(`Adding trade ${trade.id} for ${dateStr} to allTrades`);
                    allTrades.push(trade);
                  }
                });
              } else {
                console.log(`No trades found for journal date ${dateStr}`);
              }
            }
          }
        } catch (error) {
          console.error('Error fetching daily journal entries with matching tags:', error);
        }
      }
    }

    // Convert back to Date objects and sort
    return Array.from(uniqueDates)
      .map(dateStr => new Date(dateStr))
      .sort((a, b) => b.getTime() - a.getTime()); // newest first
  }

  // Filter entries based on the active tab
  const filteredEntries = entries.filter(entry => {
    // Basic tab filtering
    if (activeTab === "all") return true
    if (activeTab === "with-trades") return !!entry.trade_id
    if (activeTab === "without-trades") return !entry.trade_id
    return true
  })

  // Special handling for the "Trade-Specific" tab when filtering by tags
  const showDailyJournalsInTradeSpecificTab = activeTab === "with-trades" && selectedTags.length > 0
    ? false // Never show daily journals in Trade-Specific tab when filtering by tags
    : true  // Otherwise, follow normal filtering rules

  // Filter trades based on whether they have journal content (notes or screenshots) and match filters
  const tradesWithJournalContent = allTrades.filter(trade => {
    // Check if the trade has notes or screenshots
    const hasContent = (
      (trade.notes && trade.notes.trim().length > 0) ||
      (Array.isArray(trade.screenshots) && trade.screenshots.length > 0) ||
      trade.has_journal_content === true
    )

    // If no content, exclude immediately
    if (!hasContent) return false

    // Apply all filters using our common filter function
    return tradeMatchesFilters(trade);
  })

  // State for strategy map
  const [strategyMap, setStrategyMap] = useState<Map<string, string>>(new Map())

  // Update filtered days when filters change
  useEffect(() => {
    const updateFilteredDays = async () => {
      if (!userId) return;

      try {
        // Get the days that match our filter criteria
        // This will also fetch trades for daily journal entries with matching tags
        const days = await getFilteredTradingDays();

        // Create a copy of allTrades to avoid mutation issues
        const currentTrades = [...allTrades];

        // Set the filtered days
        setFilteredDays(days);

        // If allTrades has been modified inside getFilteredTradingDays, update the state
        if (currentTrades.length !== allTrades.length) {
          console.log(`Updating allTrades state with ${allTrades.length} trades (was ${currentTrades.length})`);
          // Force a state update by creating a new array
          setAllTrades([...allTrades]);
        }
      } catch (error) {
        console.error("Error updating filtered days:", error);
      }
    };

    updateFilteredDays();
  }, [userId, selectedAccountId, selectedTags, dateRange, searchTerm, activeTab]);

  // Fetch strategy names for all trades with journal content
  useEffect(() => {
    const fetchStrategyNames = async () => {
      if (!userId) return

      // Get all trades that have strategy IDs
      const tradesWithStrategy = allTrades.filter(trade => trade.strategy_id)

      // Get unique strategy IDs from all trades
      const strategyIds = [...new Set(
        tradesWithStrategy.map(trade => trade.strategy_id)
      )]

      if (strategyIds.length === 0) return

      try {
        console.log('Fetching strategy names for IDs:', strategyIds)
        const supabase = getSupabaseClient()
        const { data, error } = await supabase
          .from('strategies')
          .select('id, name')
          .in('id', strategyIds)

        if (error) {
          console.error('Error fetching strategy names:', error)
          return
        }

        console.log('Fetched strategy data:', data)

        // Create a map of strategy ID to name
        const newStrategyMap = new Map<string, string>()
        data?.forEach(strategy => {
          newStrategyMap.set(strategy.id, strategy.name)
        })

        // Update the strategy map
        setStrategyMap(newStrategyMap)
        console.log('Updated strategy map:', Object.fromEntries(newStrategyMap))
      } catch (error) {
        console.error('Error in fetchStrategyNames:', error)
      }
    }

    fetchStrategyNames()
  }, [userId, allTrades])

  return (
    <div className="space-y-6">
      {selectedAccountId === null ? (
        <Card className="p-8 text-center">
          <div className="flex flex-col items-center justify-center space-y-4">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="h-12 w-12 text-muted-foreground mb-2"
            >
              <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
              <circle cx="9" cy="7" r="4" />
              <path d="M22 21v-2a4 4 0 0 0-3-3.87" />
              <path d="M16 3.13a4 4 0 0 1 0 7.75" />
            </svg>
            <h2 className="text-xl font-semibold">No Account Selected</h2>
            <p className="text-muted-foreground max-w-md">
              Please select an account from the dashboard to view your journal entries. No data will be displayed until an account is selected.
            </p>
            <Button
              variant="outline"
              onClick={() => router.push('/dashboard')}
              className="mt-4"
            >
              Go to Dashboard
            </Button>
          </div>
        </Card>
      ) : (
        <>
      {/* Header with search and filters */}
      <div className="flex flex-col space-y-4 border-b pb-4">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-semibold">Trading Journal</h1>
          <Button onClick={() => setIsNewEntryDialogOpen(true)}>
            <Plus className="mr-2 h-4 w-4" />
            New Entry
          </Button>
        </div>

        <div className="flex items-center space-x-4">
          <div className="relative flex-1">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search journal entries..."
              value={searchTerm}
              onChange={(e) => {
                const newSearchTerm = e.target.value;
                setSearchTerm(newSearchTerm);

                // Apply search after a short delay to avoid too many requests
                if (userId) {
                  const delayDebounceFn = setTimeout(() => {
                    // Check if the search term exactly matches any available tag
                    const term = newSearchTerm.trim().toLowerCase();
                    if (term && selectedTags.length === 0) {
                      const matchingTag = availableTags.find(tag => tag.toLowerCase() === term);
                      if (matchingTag) {
                        // If it matches a tag, add it to selected tags
                        console.log(`Search term "${term}" matches tag "${matchingTag}", adding to selected tags`);
                        setSelectedTags([matchingTag]);
                      }
                    }

                    fetchEntries(userId, {
                      searchTerm: newSearchTerm,
                      tags: selectedTags,
                      startDate: dateRange?.from ? format(dateRange.from, "yyyy-MM-dd") : undefined,
                      endDate: dateRange?.to ? format(dateRange.to, "yyyy-MM-dd") : undefined,
                    });
                  }, 500); // Increased delay to give user time to type
                  return () => clearTimeout(delayDebounceFn);
                }
              }}
              className="pl-8"
            />
          </div>

          <Popover open={isFilterOpen} onOpenChange={setIsFilterOpen}>
            <PopoverTrigger asChild>
              <Button variant="outline">
                <Filter className="mr-2 h-4 w-4" />
                Filters
                {(selectedTags.length > 0 || dateRange?.from || dateRange?.to) && (
                  <Badge variant="secondary" className="ml-2 px-1 py-0">
                    {selectedTags.length + (dateRange?.from ? 1 : 0) + (dateRange?.to ? 1 : 0)}
                  </Badge>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-80">
              <div className="space-y-4">
                <div className="space-y-2">
                  <h4 className="font-medium">Date Range</h4>
                  <TableDateRangePicker
                    dateRange={dateRange}
                    onDateRangeChange={setDateRange}
                    align="center"
                  />
                </div>

                <div className="space-y-2">
                  <h4 className="font-medium">Tags</h4>
                  <div className="flex flex-wrap gap-2 max-h-[150px] overflow-y-auto p-2 border rounded-md">
                    {availableTags.length > 0 ? (
                      availableTags.map((tag) => (
                        <Badge
                          key={tag}
                          variant={selectedTags.includes(tag) ? "default" : "outline"}
                          className="cursor-pointer"
                          onClick={() => toggleTag(tag)}
                        >
                          {tag}
                        </Badge>
                      ))
                    ) : (
                      <div className="text-sm text-muted-foreground">No tags available</div>
                    )}
                  </div>
                </div>

                <div className="flex justify-between">
                  <Button variant="outline" size="sm" onClick={resetFilters}>
                    Reset
                  </Button>
                  <Button size="sm" onClick={applyFilters}>
                    Apply Filters
                  </Button>
                </div>
              </div>
            </PopoverContent>
          </Popover>

          <Button
            variant="outline"
            onClick={() => {
              if (searchTerm || selectedTags.length > 0 || dateRange?.from || dateRange?.to) {
                resetFilters()
              }
            }}
            disabled={!searchTerm && selectedTags.length === 0 && !dateRange?.from && !dateRange?.to}
          >
            <X className="mr-2 h-4 w-4" />
            Clear
          </Button>
        </div>
      </div>

      {/* Tabs for filtering entries */}
      <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3 h-10 items-stretch">
          <TabsTrigger
            value="all"
            className="flex items-center justify-center gap-2 data-[state=active]:bg-background data-[state=active]:shadow-none data-[state=active]:border-b-2 data-[state=active]:border-primary rounded-none"
          >
            <BookOpen className="h-4 w-4" />
            <span>All Journals</span>
          </TabsTrigger>
          <TabsTrigger
            value="with-trades"
            className="flex items-center justify-center gap-2 data-[state=active]:bg-background data-[state=active]:shadow-none data-[state=active]:border-b-2 data-[state=active]:border-primary rounded-none"
          >
            <BarChart3 className="h-4 w-4" />
            <span>Trade-Specific</span>
          </TabsTrigger>
          <TabsTrigger
            value="without-trades"
            className="flex items-center justify-center gap-2 data-[state=active]:bg-background data-[state=active]:shadow-none data-[state=active]:border-b-2 data-[state=active]:border-primary rounded-none"
          >
            <Calendar className="h-4 w-4" />
            <span>Daily Journal</span>
          </TabsTrigger>
        </TabsList>
      </Tabs>

      {/* Active filters display */}
      {(selectedTags.length > 0 || dateRange?.from || dateRange?.to) && (
        <div className="flex flex-wrap items-center gap-2 text-sm">
          <span className="text-muted-foreground">Active filters:</span>

          {dateRange?.from && dateRange?.to && (
            <Badge variant="secondary" className="flex items-center gap-1">
              <Calendar className="h-3 w-3" />
              {format(dateRange.from, "MMM d, yyyy")} - {format(dateRange.to, "MMM d, yyyy")}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setDateRange(undefined)}
                className="h-4 w-4 p-0 ml-1 text-muted-foreground hover:text-foreground"
              >
                <X className="h-3 w-3" />
                <span className="sr-only">Remove date filter</span>
              </Button>
            </Badge>
          )}

          {dateRange?.from && !dateRange?.to && (
            <Badge variant="secondary" className="flex items-center gap-1">
              <Calendar className="h-3 w-3" />
              From: {format(dateRange.from, "MMM d, yyyy")}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setDateRange(undefined)}
                className="h-4 w-4 p-0 ml-1 text-muted-foreground hover:text-foreground"
              >
                <X className="h-3 w-3" />
                <span className="sr-only">Remove date filter</span>
              </Button>
            </Badge>
          )}

          {selectedTags.map(tag => (
            <Badge key={tag} variant="secondary" className="flex items-center gap-1">
              <Tag className="h-3 w-3" />
              {tag}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setSelectedTags(selectedTags.filter(t => t !== tag))}
                className="h-4 w-4 p-0 ml-1 text-muted-foreground hover:text-foreground"
              >
                <X className="h-3 w-3" />
                <span className="sr-only">Remove tag filter</span>
              </Button>
            </Badge>
          ))}
        </div>
      )}

      {/* Journals Section - Show different content based on active tab */}
      <div className="space-y-4 mb-8">
        <div className="flex flex-col space-y-2">
          <h2 className="text-lg font-medium">
            {activeTab === "with-trades" ? "Trade-Specific Journals" :
             activeTab === "without-trades" ? "Daily Trading Journals" :
             "All Trading Journals"}
          </h2>
          <p className="text-sm text-muted-foreground">
            {activeTab === "with-trades"
              ? "Journals created from individual trade details pages with notes and screenshots"
              : activeTab === "without-trades"
                ? "Daily journals summarizing all trading activity for each day"
                : "All journal entries including both trade-specific and daily journals"}
          </p>

          {/* Display active tag filters */}
          {selectedTags.length > 0 && (
            <div className="flex flex-wrap gap-2 mt-2">
              <span className="text-xs text-muted-foreground flex items-center">
                <Filter className="h-3 w-3 mr-1" />
                Filtered by tags:
              </span>
              {selectedTags.map(tag => (
                <Badge
                  key={tag}
                  variant="outline"
                  className="text-xs bg-purple-50 text-purple-700 border-purple-200 flex items-center gap-1"
                >
                  <Check className="h-3 w-3" />
                  {tag}
                </Badge>
              ))}
              <Button
                variant="ghost"
                size="sm"
                className="h-5 text-xs px-2 text-muted-foreground hover:text-foreground"
                onClick={resetFilters}
              >
                <X className="h-3 w-3 mr-1" />
                Clear
              </Button>
            </div>
          )}
        </div>

        {activeTab === "with-trades" ? (
          // Trade Journals Tab - Only show individual trades with notes or screenshots
          <div>
            <h3 className="text-sm font-medium text-muted-foreground mb-2">Trade-Specific Journals</h3>
            {tradesWithJournalContent.length > 0 ? (
              <div className="space-y-4">
                {/* Sort trades by date, newest first */}
                {tradesWithJournalContent
                  .sort((a, b) => new Date(b.time_close).getTime() - new Date(a.time_close).getTime())
                  .map((trade) => (
                    <TradeJournalCard
                      key={trade.id}
                      trade={trade}
                      strategyName={trade.strategy_id ? strategyMap.get(trade.strategy_id) : undefined}
                      onEdit={handleEditTrade}
                    />
                  ))
                }
              </div>
            ) : (
              <Card>
                <CardContent className="flex flex-col items-center justify-center py-8">
                  <p className="text-muted-foreground">No trade journals found</p>
                  <p className="text-xs text-muted-foreground mt-2">
                    Add notes or screenshots to trades from the trade details page to create trade journals.
                  </p>
                </CardContent>
              </Card>
            )}
          </div>
        ) : activeTab === "without-trades" ? (
          // Daily Journals Tab - Show only daily journals
          <div>
            {/* Today's Journal */}
            {getTradesForDate(new Date()).length > 0 &&
             // Only show today's journal if any trade from today passes all filters
             getTradesForDate(new Date()).some(tradeMatchesFilters) && (
              <div className="mb-6">
                <h3 className="text-sm font-medium text-muted-foreground mb-2">Today's Journal</h3>
                <AutomatedDailyJournal
                  date={new Date()}
                  trades={getTradesForDate(new Date(), false)}
                />
              </div>
            )}

            {/* Past Daily Journals */}
            <div>
              <h3 className="text-sm font-medium text-muted-foreground mb-2">Past Daily Journals</h3>
              {filteredDays.length > 0 ? (
                <div className="space-y-4">
                  {filteredDays
                    // Filter out today's date since it's already shown above
                    .filter(date => !isSameDay(date, new Date()))
                    .map((date, index) => {
                      // Get ALL trades for this date to ensure metrics are calculated correctly
                      // We're showing this card because it matches our filter criteria, but we need all trades for metrics
                      const trades = getTradesForDate(date, false);

                      return (
                        <AutomatedDailyJournal
                          key={format(date, "yyyy-MM-dd")}
                          date={date}
                          trades={trades}
                        />
                      );
                    })
                  }
                </div>
              ) : (
                <Card>
                  <CardContent className="flex flex-col items-center justify-center py-8">
                    <p className="text-muted-foreground">No daily journals found</p>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        ) : (
          // All Journals Tab - Show all journals (both daily and trade-specific)
          <div>
            {/* Today's Journal */}
            {getTradesForDate(new Date()).length > 0 &&
             // Only show today's journal if any trade from today passes all filters
             getTradesForDate(new Date()).some(tradeMatchesFilters) && (
              <div className="mb-6">
                <h3 className="text-sm font-medium text-muted-foreground mb-2">Today's Journal</h3>
                <AutomatedDailyJournal
                  date={new Date()}
                  trades={getTradesForDate(new Date(), false)}
                />
              </div>
            )}

            {/* Trade-Specific Journals */}
            {tradesWithJournalContent.length > 0 && (
              <div className="mb-6">
                <h3 className="text-sm font-medium text-muted-foreground mb-2">Trade-Specific Journals</h3>
                <div className="space-y-4">
                  {/* Sort trades by date, newest first */}
                  {tradesWithJournalContent
                    .sort((a, b) => new Date(b.time_close).getTime() - new Date(a.time_close).getTime())
                    .map((trade) => (
                      <TradeJournalCard
                        key={trade.id}
                        trade={trade}
                        strategyName={trade.strategy_id ? strategyMap.get(trade.strategy_id) : undefined}
                        onEdit={handleEditTrade}
                      />
                    ))
                  }
                </div>
              </div>
            )}

            {/* Past Trading Days */}
            <div>
              <h3 className="text-sm font-medium text-muted-foreground mb-2">Past Daily Journals</h3>
              {filteredDays.length > 0 ? (
                <div className="space-y-4">
                  {filteredDays
                    // Filter out today's date since it's already shown above
                    .filter(date => !isSameDay(date, new Date()))
                    .map((date, index) => {
                      // Get ALL trades for this date to ensure metrics are calculated correctly
                      // We're showing this card because it matches our filter criteria, but we need all trades for metrics
                      const trades = getTradesForDate(date, false);

                      return (
                        <AutomatedDailyJournal
                          key={format(date, "yyyy-MM-dd")}
                          date={date}
                          trades={trades}
                        />
                      );
                    })
                  }
                </div>
              ) : (
                <Card>
                  <CardContent className="flex flex-col items-center justify-center py-8">
                    <p className="text-muted-foreground">No past trading activity found</p>
                  </CardContent>
                </Card>
              )}
            </div>

            {/* Show message if no journals found */}
            {tradesWithJournalContent.length === 0 && filteredDays.length === 0 && (
              <Card>
                <CardContent className="flex flex-col items-center justify-center py-8">
                  <p className="text-muted-foreground">No journal entries found</p>
                  <p className="text-xs text-muted-foreground mt-2">
                    Try clearing your filters or add journal entries to see them here.
                  </p>
                </CardContent>
              </Card>
            )}
          </div>
        )}
      </div>

      {/* New Entry Dialog */}
      <Dialog open={isNewEntryDialogOpen} onOpenChange={setIsNewEntryDialogOpen}>
        <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>New Journal Entry</DialogTitle>
          </DialogHeader>
          {userId && (
            <JournalEntryForm
              onSubmit={handleCreateEntry}
              onCancel={() => setIsNewEntryDialogOpen(false)}
              userId={userId}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Edit Entry Dialog */}
      <Dialog open={isEditEntryDialogOpen} onOpenChange={setIsEditEntryDialogOpen}>
        <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Journal Entry</DialogTitle>
          </DialogHeader>
          {userId && currentEntry && (
            <JournalEntryForm
              entry={currentEntry}
              onSubmit={handleUpdateEntry}
              onCancel={() => setIsEditEntryDialogOpen(false)}
              userId={userId}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteConfirmOpen} onOpenChange={setIsDeleteConfirmOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Journal Entry</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <p>Are you sure you want to delete this journal entry? This action cannot be undone.</p>
          </div>
          <div className="flex justify-end space-x-2">
            <Button variant="outline" onClick={() => setIsDeleteConfirmOpen(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleDeleteEntry}>
              Delete
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Trade Details Edit Dialog */}
      {userId && currentTrade && (
        <TradeDetailsEditDialog
          isOpen={isTradeEditDialogOpen}
          onOpenChange={setIsTradeEditDialogOpen}
          trade={currentTrade}
          userId={userId}
          onUpdate={handleTradeUpdate}
        />
      )}
        </>
      )}
    </div>
  )
}

