import { cookies } from 'next/headers';
import { createServerClient } from '@supabase/ssr';
import { redirect } from 'next/navigation';
import type { Database } from '@/types/supabase';
import ClientWrapper from './client-wrapper';
import { format } from 'date-fns';

// Helper function to calculate statistics
function calculateStats(trades: any[]) {
  const totalTrades = trades.length;
  const winningTrades = trades.filter(t => t.profit > 0);
  const losingTrades = trades.filter(t => t.profit < 0);
  const winRate = totalTrades > 0 ? (winningTrades.length / totalTrades) * 100 : 0;
  const totalProfit = trades.reduce((sum, t) => sum + t.profit, 0);

  // Calculate average win and average loss
  const avgWin = winningTrades.length > 0 ?
    winningTrades.reduce((sum, t) => sum + t.profit, 0) / winningTrades.length : 0;
  const avgLoss = losingTrades.length > 0 ?
    Math.abs(losingTrades.reduce((sum, t) => sum + t.profit, 0) / losingTrades.length) : 0;
  const winLossRatio = avgLoss > 0 ? avgWin / avgLoss : 0;

  return {
    totalTrades,
    winRate: winRate.toFixed(2),
    totalProfit: totalProfit.toFixed(2),
    avgWin: avgWin.toFixed(2),
    avgLoss: avgLoss.toFixed(2),
    winLossRatio: winLossRatio.toFixed(2),
    winningTradesCount: winningTrades.length,
    losingTradesCount: losingTrades.length
  };
}

// Helper function to filter trades
function filterTrades(trades: any[], filters: any) {
  if (!filters) return trades;

  return trades.filter(trade => {
    // Symbol filter
    const matchesSymbol = !filters.symbol || trade.symbol.toLowerCase().includes(filters.symbol.toLowerCase());

    // Type filter
    const matchesType = !filters.type || trade.type.toLowerCase() === filters.type.toLowerCase();

    // Date filters
    const tradeDate = new Date(trade.time_close);
    const matchesDateFrom = !filters.dateFrom || tradeDate >= new Date(filters.dateFrom);
    const matchesDateTo = !filters.dateTo || tradeDate <= new Date(filters.dateTo + 'T23:59:59');

    // Profit filters
    const profit = trade.profit;
    const matchesProfitMin = !filters.profitMin || profit >= parseFloat(filters.profitMin);
    const matchesProfitMax = !filters.profitMax || profit <= parseFloat(filters.profitMax);

    // Volume filters
    const volume = trade.volume;
    const matchesVolumeMin = !filters.volumeMin || volume >= parseFloat(filters.volumeMin);
    const matchesVolumeMax = !filters.volumeMax || volume <= parseFloat(filters.volumeMax);

    // Duration filters
    const duration = new Date(trade.time_close).getTime() - new Date(trade.time_open).getTime();
    const durationHours = duration / (1000 * 60 * 60);
    const matchesDurationMin = !filters.durationMin || durationHours >= parseFloat(filters.durationMin);
    const matchesDurationMax = !filters.durationMax || durationHours <= parseFloat(filters.durationMax);

    // Status filter
    const matchesStatus = !filters.status || trade.status === filters.status;

    // Strategy filter
    const matchesStrategy = !filters.strategy ||
                           (filters.strategy === "none" ? !trade.strategy_id :
                           trade.strategy_id === filters.strategy);

    return matchesSymbol && matchesType && matchesDateFrom && matchesDateTo &&
           matchesProfitMin && matchesProfitMax && matchesVolumeMin && matchesVolumeMax &&
           matchesDurationMin && matchesDurationMax && matchesStatus && matchesStrategy;
  });
}

// Helper function to sort trades
function sortTrades(trades: any[], sortField: string, sortOrder: 'asc' | 'desc') {
  return [...trades].sort((a, b) => {
    const aValue = a[sortField];
    const bValue = b[sortField];
    const modifier = sortOrder === "asc" ? 1 : -1;

    if (typeof aValue === "string" && typeof bValue === "string") {
      return aValue.localeCompare(bValue) * modifier;
    }
    return ((aValue as number) - (bValue as number)) * modifier;
  });
}

// Helper function to paginate trades
function paginateTrades(trades: any[], page: number, pageSize: number) {
  const startIndex = (page - 1) * pageSize;
  return trades.slice(startIndex, startIndex + pageSize);
}

export default async function TradesPage({
  searchParams
}: {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>
}) {
  // Get search parameters for initial filtering
  const resolvedParams = await searchParams;

  const filterSymbol = typeof resolvedParams.symbol === 'string' ? resolvedParams.symbol : '';
  const filterType = typeof resolvedParams.type === 'string' ? resolvedParams.type : null;
  const filterDateFrom = typeof resolvedParams.dateFrom === 'string' ? resolvedParams.dateFrom : '';
  const filterDateTo = typeof resolvedParams.dateTo === 'string' ? resolvedParams.dateTo : '';
  const filterProfitMin = typeof resolvedParams.profitMin === 'string' ? resolvedParams.profitMin : '';
  const filterProfitMax = typeof resolvedParams.profitMax === 'string' ? resolvedParams.profitMax : '';
  const filterVolumeMin = typeof resolvedParams.volumeMin === 'string' ? resolvedParams.volumeMin : '';
  const filterVolumeMax = typeof resolvedParams.volumeMax === 'string' ? resolvedParams.volumeMax : '';
  const filterDurationMin = typeof resolvedParams.durationMin === 'string' ? resolvedParams.durationMin : '';
  const filterDurationMax = typeof resolvedParams.durationMax === 'string' ? resolvedParams.durationMax : '';
  const filterStatus = typeof resolvedParams.status === 'string' ? resolvedParams.status : '';
  const filterStrategy = typeof resolvedParams.strategy === 'string' ? resolvedParams.strategy : '';
  const sortField = typeof resolvedParams.sortField === 'string' ? resolvedParams.sortField : 'time_close';
  const sortOrder = typeof resolvedParams.sortOrder === 'string' ?
    (resolvedParams.sortOrder === 'asc' ? 'asc' : 'desc') : 'desc';
  const currentPage = typeof resolvedParams.page === 'string' ? parseInt(resolvedParams.page) : 1;
  const pageSize = typeof resolvedParams.pageSize === 'string' ? parseInt(resolvedParams.pageSize) : 20;
  const accountId = typeof resolvedParams.accountId === 'string' ? resolvedParams.accountId : null;

  // Create filters object
  const filters = {
    symbol: filterSymbol,
    type: filterType,
    dateFrom: filterDateFrom,
    dateTo: filterDateTo,
    profitMin: filterProfitMin,
    profitMax: filterProfitMax,
    volumeMin: filterVolumeMin,
    volumeMax: filterVolumeMax,
    durationMin: filterDurationMin,
    durationMax: filterDurationMax,
    status: filterStatus,
    strategy: filterStrategy
  };

  // Get cookies for server-side Supabase client
  const cookieStore = await cookies();
  const supabase = createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value;
        },
        set(_name: string, _value: string, _options: any) {
          // Server components can't set cookies directly
        },
        remove(_name: string, _options: any) {
          // Server components can't remove cookies directly
        }
      },
    }
  );

  // Get authenticated user
  const { data: { user }, error: userError } = await supabase.auth.getUser();
  if (userError || !user) {
    redirect('/login');
  }

  const userId = user.id;

  // Fetch user's accounts
  const { data: accounts, error: accountsError } = await supabase
    .from("trading_accounts")
    .select("*")
    .eq("user_id", userId)
    .order("updated_at", { ascending: false });

  if (accountsError) {
    console.error('Error fetching accounts:', accountsError);
  }

  // Get the selected account ID from the URL or the first account
  const selectedAccountId = accountId || (accounts && accounts.length > 0 ? accounts[0].id : null);

  // Fetch all trades for the user and selected account
  let tradesQuery = supabase
    .from("trades")
    .select("*")
    .eq("user_id", userId)
    .order("time_close", { ascending: false });

  // Apply account filter if provided
  if (selectedAccountId) {
    tradesQuery = tradesQuery.eq("account_id", selectedAccountId);
  }

  const { data: allTrades, error: tradesError } = await tradesQuery;

  if (tradesError) {
    console.error('Error fetching trades:', tradesError);
  }

  // Fetch strategies for trades with strategy_id
  const tradesWithStrategy = (allTrades || []).filter((trade) => trade.strategy_id);
  const strategyIds = [...new Set(tradesWithStrategy.map((trade) => trade.strategy_id))];

  let strategies: any[] = [];
  if (strategyIds.length > 0) {
    const { data: strategiesData, error: strategiesError } = await supabase
      .from('strategies')
      .select('*')
      .eq('user_id', userId);

    if (strategiesError) {
      console.error('Error fetching strategies:', strategiesError);
    } else {
      strategies = strategiesData || [];
    }
  }

  // Process trades with strategy information
  const processedTrades = (allTrades || []).map(trade => {
    // Find the strategy for this trade
    const strategy = strategies.find(s => s.id === trade.strategy_id);

    return {
      ...trade,
      status: "closed",
      strategy_name: strategy?.name || null
    };
  });

  // Apply server-side filtering, sorting, and pagination
  const filteredTrades = filterTrades(processedTrades, filters);
  const sortedTrades = sortTrades(filteredTrades, sortField, sortOrder as 'asc' | 'desc');
  const paginatedTrades = paginateTrades(sortedTrades, currentPage, pageSize);

  // Calculate statistics
  const stats = calculateStats(filteredTrades);

  // Calculate total pages
  const totalPages = Math.ceil(filteredTrades.length / pageSize);

  // Pass the processed data to the client component
  return (
    <ClientWrapper
      userId={userId}
      initialTrades={processedTrades}
      initialFilteredTrades={filteredTrades}
      initialPaginatedTrades={paginatedTrades}
      initialStrategies={strategies}
      initialStats={stats}
      initialFilters={filters}
      initialSortField={sortField}
      initialSortOrder={sortOrder as 'asc' | 'desc'}
      initialPage={currentPage}
      initialPageSize={pageSize}
      totalPages={totalPages}
      totalItems={filteredTrades.length}
      selectedAccountId={selectedAccountId}
    />
  );
}
