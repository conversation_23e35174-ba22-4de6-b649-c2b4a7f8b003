"use client"

import { useState, useEffect } from "react"
import { toast } from "sonner"
import { Strategy, Setup, StrategyRule } from "@/types/playbook"
import { createStrategy, createSetup, createStrategyRule } from "@/lib/server/playbook-service"

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import {
  <PERSON><PERSON>,
  <PERSON>bsContent,
  <PERSON>bsList,
  TabsTrigger,
} from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import { ArrowLeft, ArrowRight, Check, CheckCircle, Plus, Save } from "lucide-react"

// Import the form components
import { StrategyFormContent } from "./strategy-form-content"
import { SetupFormContent } from "./setup-form-content"
import { RuleFormContent } from "./rule-form-content"

interface StrategyWizardProps {
  userId: string
  onSuccess?: (strategy: Strategy) => void
  onCancel?: () => void
}

export function StrategyWizard({ userId, onSuccess, onCancel }: StrategyWizardProps) {
  // State for the current step
  const [currentStep, setCurrentStep] = useState<number>(1)
  const [isSubmitting, setIsSubmitting] = useState(false)

  // State for the created strategy, setup, and rules
  const [createdStrategy, setCreatedStrategy] = useState<Strategy | null>(null)
  const [createdSetups, setCreatedSetups] = useState<Setup[]>([])
  const [createdRules, setCreatedRules] = useState<StrategyRule[]>([])

  // State for form data
  const [strategyData, setStrategyData] = useState<any>(null)
  const [setupData, setSetupData] = useState<any>(null)
  const [ruleData, setRuleData] = useState<any>(null)

  // State for tracking completion of each step
  const [strategyComplete, setStrategyComplete] = useState(false)
  const [setupComplete, setSetupComplete] = useState(false)
  const [ruleComplete, setRuleComplete] = useState(false)

  // Calculate progress percentage
  const progressPercentage = ((currentStep - 1) / 3) * 100

  // Handle strategy form submission
  const handleStrategySubmit = async (data: any) => {
    setStrategyData(data)
    setStrategyComplete(true)

    // If we're creating the strategy for the first time
    if (!createdStrategy) {
      setIsSubmitting(true)
      try {
        const result = await createStrategy(data)
        if (result) {
          setCreatedStrategy(result)
          toast.success("Strategy created successfully")
          // Move to the next step
          setCurrentStep(2)
        }
      } catch (error) {
        console.error("Error creating strategy:", error)
        toast.error("Failed to create strategy")
      } finally {
        setIsSubmitting(false)
      }
    } else {
      // If we already created the strategy, just move to the next step
      setCurrentStep(2)
    }
  }

  // Handle setup form submission
  const handleSetupSubmit = async (data: any) => {
    if (!createdStrategy) {
      toast.error("Please create a strategy first")
      return
    }

    console.log("Setup form data received:", data)

    // Validate required fields
    if (!data.name) {
      toast.error("Setup name is required")
      return
    }

    setSetupData(data)
    setSetupComplete(true)

    // Check if we already created this setup (by name)
    const setupAlreadyExists = createdSetups.some(setup => setup.name === data.name);

    if (setupAlreadyExists) {
      console.log("Setup already exists, skipping creation:", data.name);
      toast.success("Proceeding to next step");
      setCurrentStep(3);
      return;
    }

    // Add the strategy_id to the setup data and ensure image_urls is an array
    const setupWithStrategy = {
      ...data,
      strategy_id: createdStrategy.id,
      image_urls: Array.isArray(data.image_urls) ? data.image_urls : []
    }

    console.log("Sending setup data to API:", setupWithStrategy)

    setIsSubmitting(true)
    try {
      const result = await createSetup(setupWithStrategy)
      if (result) {
        console.log("Setup created successfully:", result)
        setCreatedSetups([...createdSetups, result])
        toast.success("Setup created successfully")
        // Move to the next step
        setCurrentStep(3)
      } else {
        console.error("Setup creation returned null result")
        toast.error("Failed to create setup. Please check the console for details.")
      }
    } catch (error) {
      console.error("Error creating setup:", error)

      // Check if it's a duplicate name error
      const errorMessage = error instanceof Error ? error.message : String(error);
      if (errorMessage.includes('already exists for this strategy')) {
        // The setup might already exist in the database but not in our local state
        // Let's fetch the setup and add it to our local state
        try {
          const { getSetups } = await import('@/lib/server/playbook-service');
          const setups = await getSetups(createdStrategy.id);
          const existingSetup = setups.find(s => s.name === data.name);

          if (existingSetup) {
            // Add the existing setup to our local state if it's not already there
            if (!createdSetups.some(s => s.id === existingSetup.id)) {
              setCreatedSetups([...createdSetups, existingSetup]);
            }
            toast.success("Setup found. Proceeding to next step.");
            setCurrentStep(3);
          } else {
            toast.error(errorMessage);
          }
        } catch (fetchError) {
          console.error("Error fetching existing setups:", fetchError);
          toast.error(errorMessage);
        }
      } else {
        toast.error(`Failed to create setup: ${errorMessage}`);
      }
    } finally {
      setIsSubmitting(false)
    }
  }

  // Handle rule form submission
  const handleRuleSubmit = async (data: any) => {
    if (!createdStrategy) {
      toast.error("Please create a strategy first")
      return
    }

    setRuleData(data)
    setRuleComplete(true)

    // Check if we already created this rule (by name)
    const ruleAlreadyExists = createdRules.some(rule => rule.name === data.name);

    if (ruleAlreadyExists) {
      console.log("Rule already exists, skipping creation:", data.name);
      toast.success("Rule already exists. Strategy creation complete!");

      // Call handleFinish to return to the main playbook page
      if (createdStrategy && onSuccess) {
        // Add a small delay to allow the toast to be visible
        setTimeout(() => {
          toast.success("Strategy creation complete!")
          onSuccess(createdStrategy)
        }, 1000)
      }
      return;
    }

    // Add the strategy_id to the rule data
    const ruleWithStrategy = {
      ...data,
      strategy_id: createdStrategy.id
    }

    setIsSubmitting(true)
    try {
      const result = await createStrategyRule(ruleWithStrategy)
      if (result) {
        console.log("Rule created successfully:", result)
        setCreatedRules([...createdRules, result])
        toast.success("Rule created successfully")

        // Call handleFinish to return to the main playbook page
        if (createdStrategy && onSuccess) {
          // Add a small delay to allow the toast to be visible
          setTimeout(() => {
            toast.success("Strategy creation complete!")
            onSuccess(createdStrategy)
          }, 1000)
        }
      } else {
        console.error("Rule creation returned null result")
        toast.error("Failed to create rule. Please check the console for details.")
      }
    } catch (error) {
      console.error("Error creating rule:", error)

      // Check if it's a duplicate name error
      const errorMessage = error instanceof Error ? error.message : String(error);
      if (errorMessage.includes('already exists for this strategy')) {
        // The rule might already exist in the database but not in our local state
        // Let's fetch the rule and add it to our local state
        try {
          const { getStrategyRules } = await import('@/lib/server/playbook-service');
          const rules = await getStrategyRules(createdStrategy.id);
          const existingRule = rules.find(r => r.name === data.name);

          if (existingRule) {
            // Add the existing rule to our local state if it's not already there
            if (!createdRules.some(r => r.id === existingRule.id)) {
              setCreatedRules([...createdRules, existingRule]);
            }
            toast.success("Rule found. Strategy creation complete!");

            // Call handleFinish to return to the main playbook page
            if (createdStrategy && onSuccess) {
              // Add a small delay to allow the toast to be visible
              setTimeout(() => {
                toast.success("Strategy creation complete!")
                onSuccess(createdStrategy)
              }, 1000)
            }
          } else {
            toast.error(errorMessage);
          }
        } catch (fetchError) {
          console.error("Error fetching existing rules:", fetchError);
          toast.error(errorMessage);
        }
      } else {
        toast.error(`Failed to create rule: ${errorMessage}`);
      }
    } finally {
      setIsSubmitting(false)
    }
  }

  // Handle adding another setup
  const handleAddAnotherSetup = () => {
    // Show a success message about the setups created so far
    if (createdSetups.length > 0) {
      toast.success(`${createdSetups.length} setup${createdSetups.length !== 1 ? 's' : ''} created so far. Add another setup.`);
    }

    // Reset the form data to create a new setup
    setSetupData(null);
    setSetupComplete(false);

    // Focus on the setup name field if possible
    setTimeout(() => {
      const setupNameField = document.querySelector('[name="name"]');
      if (setupNameField instanceof HTMLElement) {
        setupNameField.focus();
      }
    }, 100);
  }

  // Handle adding another rule
  const handleAddAnotherRule = () => {
    // Show a success message about the rules created so far
    if (createdRules.length > 0) {
      toast.success(`${createdRules.length} rule${createdRules.length !== 1 ? 's' : ''} created so far. Add another rule.`);
    }

    // Reset the form data to create a new rule
    setRuleData(null);
    setRuleComplete(false);

    // Focus on the rule type field if possible
    setTimeout(() => {
      const ruleTypeField = document.querySelector('[name="rule_type"]');
      if (ruleTypeField instanceof HTMLElement) {
        ruleTypeField.focus();
      }
    }, 100);
  }

  // Handle finishing the wizard
  const handleFinish = () => {
    if (createdStrategy && onSuccess) {
      onSuccess(createdStrategy)
    }
  }

  // Handle navigation between steps
  const goToNextStep = () => {
    if (currentStep < 3) {
      setCurrentStep(currentStep + 1)
    }
  }

  const goToPreviousStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <CardTitle>Create Trading Strategy</CardTitle>
            <CardDescription>
              Complete all steps to create your trading strategy, setups, and rules
            </CardDescription>
          </div>
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            Step {currentStep} of 3
          </div>
        </div>
        <Progress value={progressPercentage} className="h-2 mt-2" />
      </CardHeader>

      <CardContent>
        <Tabs value={`step-${currentStep}`} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger
              value="step-1"
              onClick={() => strategyComplete && setCurrentStep(1)}
              disabled={!strategyComplete && currentStep !== 1}
              className={strategyComplete ? "text-green-500" : ""}
            >
              {strategyComplete && <Check className="mr-1 h-4 w-4" />}
              Strategy
            </TabsTrigger>
            <TabsTrigger
              value="step-2"
              onClick={() => setupComplete && setCurrentStep(2)}
              disabled={!setupComplete && currentStep !== 2}
              className={setupComplete ? "text-green-500" : ""}
            >
              {setupComplete && <Check className="mr-1 h-4 w-4" />}
              Setups
            </TabsTrigger>
            <TabsTrigger
              value="step-3"
              onClick={() => ruleComplete && setCurrentStep(3)}
              disabled={!ruleComplete && currentStep !== 3}
              className={ruleComplete ? "text-green-500" : ""}
            >
              {ruleComplete && <Check className="mr-1 h-4 w-4" />}
              Rules
            </TabsTrigger>
          </TabsList>

          <TabsContent value="step-1" className="py-4">
            <StrategyFormContent
              onSubmit={handleStrategySubmit}
              initialData={strategyData}
              isSubmitting={isSubmitting}
            />
          </TabsContent>

          <TabsContent value="step-2" className="py-4">
            {createdStrategy ? (
              <div className="space-y-6">
                {/* Show created setups if any */}
                {createdSetups.length > 0 && (
                  <div className="bg-muted/40 p-4 rounded-lg border border-border">
                    <h3 className="text-base font-medium mb-2 flex items-center">
                      <CheckCircle className="h-4 w-4 mr-2 text-green-500" />
                      Setups Created ({createdSetups.length})
                    </h3>
                    <div className="space-y-2">
                      {createdSetups.map((setup, index) => (
                        <div key={setup.id} className="flex items-start gap-2 p-2 bg-background/50 rounded border border-border/50">
                          <div className="flex-1">
                            <div className="font-medium">{setup.name}</div>
                            {setup.description && (
                              <div className="text-xs text-muted-foreground mt-1">{setup.description}</div>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                    <div className="mt-3 text-sm">
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-muted-foreground hover:text-foreground"
                        onClick={handleAddAnotherSetup}
                      >
                        <Plus className="h-3 w-3 mr-1" />
                        Add another setup
                      </Button>
                    </div>
                  </div>
                )}

                {/* Setup form */}
                {!setupComplete && (
                  <SetupFormContent
                    onSubmit={handleSetupSubmit}
                    initialData={setupData}
                    isSubmitting={isSubmitting}
                    strategyId={createdStrategy.id}
                    strategyName={createdStrategy.name}
                  />
                )}

                {/* Show a message if the form is hidden but we have setups */}
                {setupComplete && !setupData && createdSetups.length > 0 && (
                  <div className="text-center py-8">
                    <p className="text-muted-foreground">You've created {createdSetups.length} setup{createdSetups.length !== 1 ? 's' : ''}.</p>
                    <div className="flex justify-center gap-4 mt-4">
                      <Button onClick={handleAddAnotherSetup} variant="outline">
                        <Plus className="h-4 w-4 mr-2" />
                        Add Another Setup
                      </Button>
                      <Button onClick={() => setCurrentStep(3)}>
                        Continue to Rules
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="text-center py-8">
                <p className="text-muted-foreground">Please create a strategy first</p>
                <Button onClick={() => setCurrentStep(1)} variant="outline" className="mt-4">
                  Go to Strategy Step
                </Button>
              </div>
            )}
          </TabsContent>

          <TabsContent value="step-3" className="py-4">
            {createdStrategy ? (
              <div className="space-y-6">
                {/* Show created rules if any */}
                {createdRules.length > 0 && (
                  <div className="bg-muted/40 p-4 rounded-lg border border-border">
                    <h3 className="text-base font-medium mb-2 flex items-center">
                      <CheckCircle className="h-4 w-4 mr-2 text-green-500" />
                      Rules Created ({createdRules.length})
                    </h3>
                    <div className="space-y-2">
                      {createdRules.map((rule, index) => (
                        <div key={rule.id} className="flex items-start gap-2 p-2 bg-background/50 rounded border border-border/50">
                          <div className="flex-1">
                            <div className="font-medium">{rule.name}</div>
                            <div className="text-xs text-muted-foreground">
                              Type: {rule.rule_type.replace('_', ' ')}
                              {rule.description && (
                                <span className="block mt-1">{rule.description}</span>
                              )}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                    <div className="mt-3 text-sm">
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-muted-foreground hover:text-foreground"
                        onClick={handleAddAnotherRule}
                      >
                        <Plus className="h-3 w-3 mr-1" />
                        Add another rule
                      </Button>
                    </div>
                  </div>
                )}

                {/* Rule form */}
                {!ruleComplete && (
                  <RuleFormContent
                    onSubmit={handleRuleSubmit}
                    initialData={ruleData}
                    isSubmitting={isSubmitting}
                    strategyId={createdStrategy.id}
                    strategyName={createdStrategy.name}
                  />
                )}

                {/* Show a message if the form is hidden but we have rules */}
                {ruleComplete && !ruleData && createdRules.length > 0 && (
                  <div className="text-center py-8">
                    <p className="text-muted-foreground">You've created {createdRules.length} rule{createdRules.length !== 1 ? 's' : ''}.</p>
                    <div className="flex justify-center gap-4 mt-4">
                      <Button onClick={handleAddAnotherRule} variant="outline">
                        <Plus className="h-4 w-4 mr-2" />
                        Add Another Rule
                      </Button>
                      <Button onClick={handleFinish}>
                        Finish
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="text-center py-8">
                <p className="text-muted-foreground">Please create a strategy first</p>
                <Button onClick={() => setCurrentStep(1)} variant="outline" className="mt-4">
                  Go to Strategy Step
                </Button>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>

      <CardFooter className="flex justify-between border-t p-6">
        <div>
          {currentStep > 1 && (
            <Button
              variant="outline"
              onClick={goToPreviousStep}
              disabled={isSubmitting}
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back
            </Button>
          )}
          {currentStep === 1 && onCancel && (
            <Button
              variant="outline"
              onClick={onCancel}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
          )}
        </div>

        <div className="flex gap-2">
          {currentStep === 2 && setupComplete && (
            <div className="flex flex-col gap-2">
              <Button
                variant="outline"
                onClick={handleAddAnotherSetup}
                disabled={isSubmitting}
                className="flex items-center gap-2"
              >
                <Plus className="h-4 w-4" />
                Add Another Setup
              </Button>
              {createdSetups.length > 0 && (
                <div className="text-xs text-muted-foreground">
                  {createdSetups.length} setup{createdSetups.length !== 1 ? 's' : ''} created
                </div>
              )}
            </div>
          )}

          {currentStep === 3 && ruleComplete && (
            <div className="flex flex-col gap-2">
              <Button
                variant="outline"
                onClick={handleAddAnotherRule}
                disabled={isSubmitting}
                className="flex items-center gap-2"
              >
                <Plus className="h-4 w-4" />
                Add Another Rule
              </Button>
              {createdRules.length > 0 && (
                <div className="text-xs text-muted-foreground">
                  {createdRules.length} rule{createdRules.length !== 1 ? 's' : ''} created
                </div>
              )}
            </div>
          )}

          {currentStep === 1 && (
            <Button
              onClick={() => {
                const strategyForm = document.getElementById('strategy-form');
                if (strategyForm) {
                  strategyForm.dispatchEvent(new Event('submit', { bubbles: true }));
                }
              }}
              disabled={isSubmitting}
              style={{ backgroundColor: "#FAFAFA", color: "#000000", borderColor: "#E5E7EB" }}
            >
              Save & Continue
            </Button>
          )}

          {currentStep === 2 && (
            <Button
              onClick={() => {
                const setupForm = document.getElementById('setup-form');
                if (setupForm) {
                  setupForm.dispatchEvent(new Event('submit', { bubbles: true }));
                }
              }}
              disabled={isSubmitting}
              style={{ backgroundColor: "#FAFAFA", color: "#000000", borderColor: "#E5E7EB" }}
            >
              {setupComplete && createdSetups.some(s => s.name === setupData?.name)
                ? "Continue to Next Step"
                : "Save & Continue"}
            </Button>
          )}

          {currentStep === 3 && (
            <Button
              onClick={() => {
                if (ruleComplete) {
                  // If a rule has already been created, just finish the wizard
                  toast.success("Strategy creation complete!")
                  handleFinish();
                } else {
                  // Otherwise, submit the rule form
                  const ruleForm = document.getElementById('rule-form');
                  if (ruleForm) {
                    ruleForm.dispatchEvent(new Event('submit', { bubbles: true }));
                  } else {
                    handleFinish();
                  }
                }
              }}
              disabled={isSubmitting}
              style={{ backgroundColor: "#FAFAFA", color: "#000000", borderColor: "#E5E7EB" }}
            >
              {ruleComplete && createdRules.some(r => r.name === ruleData?.name)
                ? 'Finish'
                : ruleComplete
                  ? 'Finish'
                  : 'Save & Finish'}
            </Button>
          )}
        </div>
      </CardFooter>
    </Card>
  )
}
