import { cookies } from 'next/headers';
import { createServerClient } from '@supabase/ssr';
import { redirect } from 'next/navigation';
import { format } from 'date-fns';
import JournalClient from './new-client';
import { fetchFilteredJournalData } from './filter-actions';
import { Database } from '@/types/supabase';

export default async function JournalPage({
  searchParams,
}: {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}) {
  // Initialize Supabase client
  const cookieStore = await cookies();
  const supabase = createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value;
        },
      },
    }
  );

  // Get user session
  const {
    data: { user },
  } = await supabase.auth.getUser();

  // Redirect if not authenticated
  if (!user) {
    redirect('/login');
  }

  // Parse search parameters
  const resolvedSearchParams = await searchParams;
  const searchTerm = typeof resolvedSearchParams.searchTerm === 'string' ? resolvedSearchParams.searchTerm : '';

  // Handle tags parameter (can be string or array)
  let tags: string[] = [];
  if (resolvedSearchParams.tags) {
    if (Array.isArray(resolvedSearchParams.tags)) {
      tags = resolvedSearchParams.tags;
    } else {
      tags = [resolvedSearchParams.tags];
    }
  }

  const startDate = typeof resolvedSearchParams.startDate === 'string' ? resolvedSearchParams.startDate : undefined;
  const endDate = typeof resolvedSearchParams.endDate === 'string' ? resolvedSearchParams.endDate : undefined;
  const activeTab = typeof resolvedSearchParams.tab === 'string' ? resolvedSearchParams.tab : 'with-trades';

  // Fetch initial data
  try {
    console.log('Fetching initial journal data with parameters:', {
      userId: user.id,
      searchTerm,
      tags,
      startDate,
      endDate,
      activeTab,
    });

    const initialData = await fetchFilteredJournalData(
      user.id,
      null, // We'll let the client component handle account selection
      searchTerm,
      tags,
      startDate,
      endDate,
      activeTab
    );

    return (
      <div className="container mx-auto py-6">
        <JournalClient
          userId={user.id}
          initialData={initialData}
          initialSearchTerm={searchTerm}
          initialTags={tags}
          initialStartDate={startDate}
          initialEndDate={endDate}
          initialTab={activeTab}
        />
      </div>
    );
  } catch (error) {
    console.error('Error fetching initial journal data:', error);

    // Return error state
    return (
      <div className="container mx-auto py-6">
        <div className="bg-destructive/10 border border-destructive rounded-lg p-4 text-center">
          <h2 className="text-lg font-medium text-destructive">Error Loading Journal</h2>
          <p className="mt-2">
            There was an error loading your journal data. Please try refreshing the page.
          </p>
        </div>
      </div>
    );
  }
}
