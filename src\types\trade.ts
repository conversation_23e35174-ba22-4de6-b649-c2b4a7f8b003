export interface Trade {
  id: string;
  position_id: number;
  user_id: string;
  account_id: string;
  symbol: string;
  type: string;
  volume: number;
  price_open: number;
  price_close: number;
  time_open: string;
  time_close: string;
  profit: number;
  swap: number;
  commission: number;
  comment?: string;
  sl?: number | null;
  tp?: number | null;
  strategy_id?: string | null;
  setup_id?: string | null;
  created_at?: string;
  updated_at?: string;
  durationMinutes?: number;
  durationHours?: number;
  durationDays?: number;
  // Journal content fields
  notes?: string | null;
  screenshots?: string[] | null;
  followed_rules?: string[] | null;
  followed_setup_criteria?: string[] | null;
  tags?: string[] | null;
  has_journal_content?: boolean;
}
